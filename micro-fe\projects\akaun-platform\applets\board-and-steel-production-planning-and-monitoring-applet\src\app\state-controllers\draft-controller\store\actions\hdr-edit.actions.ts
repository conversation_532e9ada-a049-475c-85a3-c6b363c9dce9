import { createAction, props } from "@ngrx/store";
import { bl_mrp_job_order_hdr_RowClass } from "blg-akaun-ts-lib";
import { JobOrderNoDepartment, JobOrderMain  } from "../../../../models/internal-job-order.model";

export const updateMain = createAction('[Draft: HDR Edit] Update Main', props<{ form: JobOrderMain }>());
export const updateDepartment = createAction('[Draft: HDR Edit] Update Department', props<{ form: JobOrderNoDepartment }>());

export const resetHDRInit = createAction('[Draft: HDR Edit] Reset Init');
export const resetHDRSuccess = createAction('[Draft: HDR Edit] Reset Success', props<{hdr: bl_mrp_job_order_hdr_RowClass}>());
export const updateGroupTemplate = createAction('[Draft: HDR] Update Group Guid', props<{ groupGuid: string }>());
