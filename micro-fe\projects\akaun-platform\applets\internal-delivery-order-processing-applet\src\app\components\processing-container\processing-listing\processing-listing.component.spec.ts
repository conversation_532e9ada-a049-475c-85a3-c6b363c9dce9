import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProcessingListingComponent } from './processing-listing.component';

describe('ProcessingListingComponent', () => {
  let component: ProcessingListingComponent;
  let fixture: ComponentFixture<ProcessingListingComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ProcessingListingComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProcessingListingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
