import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';
import { ENTITY, EntityConstants, ENTITY_TYPE, STATUS } from '../constants/customer-constants';

export const customerSearchModel: SearchModel = {
  label: {
    entity: 'Entity Type',
    type: 'Txn Type',
    modifiedDate: 'Modified Date',
    status: 'Status',
    entityCode: 'Entity Code'
  },
  dataType: {
    entity: ['select', ENTITY],
    type: ['select', ENTITY_TYPE],
    modifiedDate: 'date',
    status: ['select', STATUS],
    entityCode: 'string'
  },
  form: new FormGroup({
    entity: new FormControl(),
    type: new FormControl(),
    modifiedDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    status: new FormControl(),
    entityCode: new FormControl()
  }),  
  // fix when customer code not in basic search
  query: (query) => 
  `(hdr.name ILIKE '%${query}%' 
    OR hdr.customer_code ILIKE '%${query}%'
    OR hdr.supplier_code ILIKE '%${query}%'
    OR hdr.merchant_code ILIKE '%${query}%'
    OR hdr.employee_code ILIKE '%${query}%')`,
  table: `bl_fi_mst_entity_hdr`,

  queryCallbacks: {
    entity: entity => {
      if (entity === "CUSTOMER")
        return `hdr.is_customer = true`;
      else if (entity === "SUPPLIER")
        return `hdr.is_supplier = true`;
      else if (entity === "MERCHANT")
        return `hdr.is_merchant = true`;
      else if (entity === "EMPLOYEE")
        return `hdr.is_employee = true`;
      else if (!entity)
        return "";
    },
    type: type => type ? ` hdr.txn_type = '${type}'` : '',
    status: status => status ? ` hdr.status = '${status}'` : '',
    modifiedDate: modifiedDate => {
      if (modifiedDate.from || modifiedDate.to) {
        const from = modifiedDate.from ? modifiedDate.from : modifiedDate.to;
        const to = modifiedDate.to ? modifiedDate.to : modifiedDate.from;
        return `hdr.updated_date BETWEEN '${from.format(EntityConstants.DateTimeFormat)}' AND '${to.format(EntityConstants.DateTimeFormat)}'`;
      }
      return '';
    },
    entityCode: entityCode => entityCode ? `(hdr.customer_code ILIKE '%${entityCode}%' OR 
                                            hdr.supplier_code ILIKE '%${entityCode}%' OR
                                            hdr.merchant_code ILIKE '%${entityCode}%' OR
                                            hdr.employee_code ILIKE '%${entityCode}%') ` 
                                          : ''
  },
};

