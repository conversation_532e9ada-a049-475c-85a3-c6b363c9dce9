import { Component, ChangeDetectionStrategy } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { bl_fi_mst_entity_ext_RowClass, GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SubSink } from 'subsink2';
import { internalDeliveryOrderSearchModel } from '../../../models/advanced-search-models/internal-delivery-order.model';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { Store } from '@ngrx/store';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { map } from 'rxjs/operators';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  deactivateReturn: boolean;
}

@Component({
  selector: 'app-internal-delivery-order-select-shipping-address',
  templateUrl: './internal-delivery-order-select-shipping-address.component.html',
  styleUrls: ['./internal-delivery-order-select-shipping-address.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalDeliveryOrderSelectShippingAddressComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Select Shipping Address';
  protected readonly index = 7;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);

  prevIndex: number;
  protected prevLocalState: any;

  searchModel = internalDeliveryOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    // {headerName: '', field: '', minWidth: 50, checkboxSelection: true},
    {headerName: 'Address', field: 'address', cellStyle: () => ({'text-align': 'left'}),
    valueGetter: (p) => {
      console.log("p", p);
      return p.data.address_line_1 + p.data.address_line_2 + p.data.address_line_3 + 
      p.data.address_line_4 + p.data.address_line_5}},
    {headerName: 'City', field: 'value_json.city', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Postal Code', field: 'value_json.postal_code'},
    {headerName: 'State', field: 'value_json.state', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Country', field: 'value_json.county', cellStyle: () => ({'text-align': 'left'})},
  ];

  rowData$ = this.store.select(InternalDeliveryOrderSelectors.selectCustomer).pipe(
    map(a => {
      console.log("a", a);
      return a.bl_fi_mst_entity_hdr.addresses_json.shipping_address;
    })
  );

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected store: Store<InternalDeliveryOrderStates>,
    protected readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onNext() {
    this.viewColFacade.gotoFourOhFour();
    // this.viewColFacade.updateInstance(this.index, {
    //   ...this.localState, deactivateAdd: true, deactivateList: false});
    // this.viewColFacade.onNextAndReset(this.index, 3);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(shipping_address: any) {
    if (shipping_address) {
      // create mode
      if (this.prevIndex === 3){
        this.store.dispatch(InternalDeliveryOrderActions.selectShippingAddress({shipping_address: shipping_address}));
      }
      // edit mode
      else if (this.prevIndex === 1) {
        this.store.dispatch(InternalDeliveryOrderActions.selectShippingAddressEdit({shipping_address: shipping_address}));
      }
    }
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false,
      deactivateCustomer: false,
      deactivateDeliveryAgent: false,
      deactivateShippingInfo: false,
      deactivateBillingInfo: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
