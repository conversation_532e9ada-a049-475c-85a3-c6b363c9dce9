import {
  bl_fi_generic_doc_line_RowClass,
  bl_fi_mst_entity_ext_RowClass,
  bl_fi_mst_entity_line_RowClass,
  EntityContainerModel,
  FinancialItemContainerModel,
  GenericDocContainerModel,
  PricingSchemeLinkContainerModel,
} from "blg-akaun-ts-lib";
import { JobDocContainerModel } from "../../../../models/job-container.model";

export interface InternalDeliveryOrderState {
  selectedGuid: string;
  selectedEntity: GenericDocContainerModel;
  totalRecords: number;
  errorLog: { timeStamp: Date; log: string }[];
  draft: GenericDocContainerModel;
  draftEdit: GenericDocContainerModel;
  entityType: string;
  selectedCustomer: EntityContainerModel;
  selectedShippingAddress: any;
  selectedBillingAddress: any;
  selectedContactPerson: bl_fi_mst_entity_line_RowClass;
  selectedDoc: GenericDocContainerModel;
  selectedItem: FinancialItemContainerModel;
  selectedLineItem: bl_fi_generic_doc_line_RowClass;
  pricingSchemeLink: PricingSchemeLinkContainerModel[];
  updateAgGrid: boolean;
  editMode: boolean;
  knockoffListingConfig: any;
  selectedCompGuid: string;

  // For Serial, Bin, Batch
  selectedInvItem: any;
  selectedSerial: any;
  selectedBin: any;
  selectedBatch: any;

  selectedPrintableFormatGuid: string;

  loadedExternalJobDocs: JobDocContainerModel[];
  loadedInternalJobDocs: JobDocContainerModel[];
  loadedPickupJobDocs: JobDocContainerModel[];
  jobListingStatus: boolean;
  deliveryDetailListingStatus: boolean;
  loadedGenDocs: GenericDocContainerModel[];
  refreshGenDocListing: boolean;
}

export const initState: InternalDeliveryOrderState = {
  selectedGuid: null,
  selectedEntity: null,
  totalRecords: 0,
  errorLog: [],
  draft: new GenericDocContainerModel(),
  draftEdit: null,
  selectedCustomer: null,
  selectedShippingAddress: null,
  selectedBillingAddress: null,
  selectedContactPerson: null,
  selectedDoc: null,
  selectedItem: null,
  selectedLineItem: null,
  pricingSchemeLink: null,
  updateAgGrid: false,
  editMode: false,
  knockoffListingConfig: null,
  selectedCompGuid: null,

  entityType: null,

  // For Serial, Bin, Batch
  selectedInvItem: null,
  selectedSerial: null,
  selectedBin: null,
  selectedBatch: null,

  selectedPrintableFormatGuid: null,

  loadedExternalJobDocs: [],
  loadedInternalJobDocs: [],
  loadedPickupJobDocs: [],
  jobListingStatus: false,
  deliveryDetailListingStatus: false,
  loadedGenDocs: null,
  refreshGenDocListing: false,
};
