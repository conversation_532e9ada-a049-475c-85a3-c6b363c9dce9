import { Injectable } from '@angular/core';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { ViewColumn } from 'projects/shared-utilities/view-column';
import { PickPackQueueListingComponent } from '../components/pick-pack-queue-container/pick-pack-queue-listing/pick-pack-queue-listing.component';

@Injectable()
export class PickPackQueuePagesService {

  private initialState: ViewColumnState = {
    firstColumn:  new ViewColumn(0, PickPackQueueListingComponent, 'Pick Pack Queue Listing', {
      deactivateList: false
    }),
    secondColumn: null,
    viewCol: [
      new ViewColumn(0, PickPackQueueListingComponent, 'Pick Pack Queue Listing', {
        deactivateList: false
      }),
    ],
    breadCrumbs: [],
    leftDrawer: [],
    rightDrawer: [],
    singleColumn: false,
    prevIndex: null
  };

  get pages() {
    return this.initialState;
  }

  constructor() { }
}
