<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
      [disabled]="deactivateReturn$ | async"
      (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Transfer From Sales Order
      </span>
    </div>
    <button mat-raised-button color="primary" type="button" [disabled]="disableAdd()" (click)="onAdd()">ADD</button>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <!-- <mat-tab label="Main">
    <app-internal-delivery-order-transfer-sales-order-main></app-internal-delivery-order-transfer-sales-order-main>
  </mat-tab>
  <mat-tab label="Customer Details">
    <app-internal-delivery-order-transfer-sales-order-customer-info></app-internal-delivery-order-transfer-sales-order-customer-info>
  </mat-tab> -->
  <mat-tab label="Line Items">
    <app-internal-delivery-order-transfer-sales-order-line-items
    [selectedDoc]="selectedDoc$ | async"
    (selectedRows)="onSelectedRows($event)"></app-internal-delivery-order-transfer-sales-order-line-items>
  </mat-tab>
</mat-tab-group>
