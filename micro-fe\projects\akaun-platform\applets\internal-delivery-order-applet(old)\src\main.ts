import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { LicenseManager } from 'ag-grid-enterprise';
import { environment } from 'src/environments/environment';

LicenseManager.setLicenseKey
  ("CompanyName=WAVELET SOLUTIONS SDN. BHD.,LicensedApplication=akaun.com,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-026234,ExpiryDate=23_April_2023_[v2]_MTY4MjIwNDQwMDAwMA==674cd70d9fb70c2f7af064e2539295d0")

if (environment.production) {
  enableProdMode();
} else {
  sessionStorage.setItem('appletCode', 'erp_internal_delivery_order_applet');
  sessionStorage.setItem('appletGuid', '08aec3c5-ad03-4a58-a0a4-5aabc63b1c3c');
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
