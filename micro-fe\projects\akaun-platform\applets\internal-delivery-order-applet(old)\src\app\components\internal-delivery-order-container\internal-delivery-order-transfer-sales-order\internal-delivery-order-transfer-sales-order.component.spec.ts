import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderTransferSalesOrderComponent } from './internal-delivery-order-transfer-sales-order.component';

describe('InternalDeliveryOrderTransferSalesOrderComponent', () => {
  let component: InternalDeliveryOrderTransferSalesOrderComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderTransferSalesOrderComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderTransferSalesOrderComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderTransferSalesOrderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
