import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { Store } from "@ngrx/store";
import { bl_fi_mst_entity_line_RowClass, FinancialItemService, GenericDocLineService, InternalJobsheetService, InternalSalesOrderService, InventoryItemHdrService, InventoryItemService, ItemHostPermissionService, ItemPermissionContainerModel, JobTemplateToProcessOptionProcessTemplateLinkService, MrpJobOrderGenDocLinkService, MrpJobOrderHdrService, MrpProcessInstanceContainerModel, MrpProcessInstanceService, MrpProcessTypeService, MrpProdsysService, Pagination, PagingResponseModel, SalesInvoiceService, ServiceNoteIssueHdrGenericDocLinkService, SubQueryService } from "blg-akaun-ts-lib";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { forkJoin, iif, Observable, of, ReplaySubject, Subject, zip } from "rxjs";
import { SubSink } from "subsink2";

import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { catchError, map, takeUntil } from "rxjs/operators";
import { FormControl } from "@angular/forms";
import { InternalJobOrderStates } from "../../../../../state-controllers/internal-job-order-controller/store/states";
import { ViewColumnFacade } from "../../../../../facades/view-column.facade";
import { InternalJobOrderSelectors } from "../../../../../state-controllers/internal-job-order-controller/store/selectors";
import { InternalJobOrderActions } from "../../../../../state-controllers/internal-job-order-controller/store/actions";
import { ProcessGroupOption } from "../../../../../models/internal-job-order.model";
import { DraftStates } from "../../../../../state-controllers/draft-controller/store/states";
import { HDRActions } from "../../../../../state-controllers/draft-controller/store/actions";
import { HDREditSelectors, JobOrderNoEditSelectors } from "../../../../../state-controllers/draft-controller/store/selectors";
import { SlideRendererComponent } from "../../../../utilities/slide-renderer/slide-renderer.component";

@Component({
  selector: "app-process-instance-listing",
  templateUrl: "./process-instance-listing.component.html",
  styleUrls: ["./process-instance-listing.component.scss"],
})
export class ProcessInstanceListingComponent
  extends ViewColumnComponent
  implements OnInit, OnDestroy {
  // @Input() itemCategory$: Observable<any>;
  deactivateAdd$;
  @Input() localState: any;

  @Output() updateGroupGuid = new EventEmitter();

  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    cellStyle: { textAlign: "left" },
  };

  gridApi;

  protected readonly index = 1;
  columnsDefs;
  rowData: any[] = [];
  apiVisa = AppConfig.apiVisa;
  labelGuids: any = [];
  tempCat$: Observable<boolean>;
  tryError$: Observable<any[]>;
  tempCatRow$: Observable<any>;
  protected subs = new SubSink();
  pinnedBottomRowData: any;
  packageListing: any = [];
  searchValue;
  pagination = new Pagination();
  SQLGuids: string[] = null;
  jobOrderHdrGuid;
  protected _onDestroy = new Subject<void>();

  processGroupOptions: ProcessGroupOption[] = [];
  filteredProcessGroupOptions: ReplaySubject<ProcessGroupOption[]> = new ReplaySubject<[]>(1);
  processGroupOptionsFilterCtrl: FormControl = new FormControl();

  public search = new FormControl();
  public processGroup = new FormControl();

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;
  editMode: boolean;
  isGroupAvailable : boolean = false;
  constructor(
    private mrpProcessInstanceService: MrpProcessInstanceService,
    private jobTemplateToProcessOptionProcessTemplateLinkService : JobTemplateToProcessOptionProcessTemplateLinkService,
    private mrpProcessTypeService : MrpProcessTypeService,
    private mrpProdsysService: MrpProdsysService,
    private subQueryService: SubQueryService,
    private jobOrderService: MrpJobOrderHdrService,
    private invItemService: InventoryItemHdrService,
    private readonly store: Store<InternalJobOrderStates>,
    protected readonly draftStore: Store<DraftStates>,
    private viewColFacade: ViewColumnFacade
  ) {
    super();
    const customComparator = (valueA, valueB) => {
      if (valueA != null && "" !== valueA && valueB != null && "" !== valueB) {
        return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
      }
    };
    this.columnsDefs = [
      {
        headerName: "Job Order No",
        field: "job_order_no",
        width: 250,
        checkboxSelection: true,
        comparator: customComparator,
      },
      {
        headerName: "Process",
        field: "process_type",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Machine Code",
        field: "machine_code",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Output Item Code",
        field: "output_item_code",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Output Item Name",
        field: "output_item_name",
        width: 250,
        comparator: customComparator,
      },
      // {
      //   headerName: "Length (m)",
      //   field: "length",
      //   width: 250,
      //   comparator: customComparator,
      // },
      {
        headerName: "Base Qty",
        field: "qty",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "UOM",
        field: "uom",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Sequence",
        field: "sequence",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Status",
        field: "status",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Completion Date",
        field: "completion_date",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: 'Active?', field: 'is_active', suppressSizeToFit: true, 
        cellRenderer: 'slideCellRenderer',
        onCellClicked: (params) => this.updateIsActive(params)
      },
    ];

    
  }

  frameworkComponents = {
    slideCellRenderer: SlideRendererComponent,
  };

  ngOnInit() {
    this.getProcessGroups();

    this.draftStore.select(JobOrderNoEditSelectors.selectJobOrderNo).subscribe(data => {
      console.log("isGroupAvailable",data);
      if(data.template_group_guid) {
        this.isGroupAvailable = true;
      }
    })

    this.subs.sink = this.store
      .select(InternalJobOrderSelectors.selectEntity)
      .subscribe((joborderdata) => {
        console.log("The joborderdata itselft", joborderdata);
        this.jobOrderHdrGuid = joborderdata.bl_mrp_job_order_hdr.guid;
      });

    console.log("Guest tennt guid", this.jobOrderHdrGuid);

    this.store.select(InternalJobOrderSelectors.selectEditMode).subscribe(data => {
      this.editMode = data;
  })


  this.processGroupOptionsFilterCtrl.valueChanges
  .pipe(takeUntil(this._onDestroy))
  .subscribe(() => {
    this.filterProcessGroupOptions();
  });

  }

  protected filterProcessGroupOptions() {
    if (!this.processGroupOptions) {
      return;
    }
    let search = this.processGroupOptionsFilterCtrl.value;
    if (!search) {
      this.filteredProcessGroupOptions.next(this.processGroupOptions.slice());
      return;
    } else {
      search = search.trim().toLowerCase();
      this.filteredProcessGroupOptions.next(
        this.processGroupOptions.filter(
          (option) => option.viewValue.toLowerCase().indexOf(search) > -1
        )
      );
    }
  }

  async getProcessGroups(): Promise<void> {
    this.processGroupOptions = [];
    let paging = new Pagination();
    paging.conditionalCriteria =  [
      { columnName: "calcTotalRecords", operator: "=", value: "true" },
    ]
    await this.jobTemplateToProcessOptionProcessTemplateLinkService
      .getByCriteriaPromise(paging, this.apiVisa)
      .then((resp: PagingResponseModel<any>) => {
        resp.data.forEach((eachBank, index) => {
            this.processGroupOptions.push({
              value: eachBank.bl_mrp_job_template_to_process_option_process_template_link.group_guid,
              viewValue: eachBank.bl_mrp_job_template_to_process_option_process_template_link.group_name ,
            });
        });
      });
    this.processGroupOptions.sort((a, b) => a.viewValue.localeCompare(b.viewValue));
    this.filteredProcessGroupOptions.next(this.processGroupOptions.slice());
  }


  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.store.dispatch(InternalJobOrderActions.loadProcessInstanceInit({ request: grid.request }));
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
          {
            columnName: "job_order_hdr_guid",
            operator: "=",
            value: this.jobOrderHdrGuid,
          },
          // { columnName: 'doc_type', operator: '=', value: 'INTERNAL_SALES_INVOICE' },
          {
            columnName: 'guids', operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
        ];
        let totalrec = 0;
        this.mrpProcessInstanceService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {

            totalrec = resolved.totalRecords;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                this.mrpProcessTypeService.getByGuid(itemperm.bl_mrp_process_instance_hdr.process_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                this.mrpProdsysService.getByGuid(itemperm.bl_mrp_process_instance_hdr.machine_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) ,
                itemperm.bl_mrp_process_instance_hdr.job_order_hdr_guid ?
                this.jobOrderService.getByGuid(itemperm.bl_mrp_process_instance_hdr.job_order_hdr_guid.toString(),apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),
                itemperm.bl_mrp_process_instance_hdr.output_inv_item_guid ?
                this.invItemService.getByGuid(itemperm.bl_mrp_process_instance_hdr.output_inv_item_guid.toString(),apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),
                ).pipe(
                  map(([b_a,b_b,b_c,b_d]) => {

                    console.log("B_a",b_a);
                    console.log("B_d",b_d);
                    let obj = {"guid":'', "job_order_no": '', "process_type": '', "machine_code": '',"output_item_code" : '',"output_item_name": '',"length":'', "qty": '', 'uom':'', "sequence" : '', "status" : '', "completion_date": '', "process_template_hdr_guid" : '', "is_active": true};
                    obj.guid = itemperm.bl_mrp_process_instance_hdr.guid.toString(),
                    obj.job_order_no = b_c ? b_c.data.bl_mrp_job_order_hdr.server_doc_1 : '',
                    obj.process_type =b_a ? b_a.data.bl_mrp_process_type_hdr.name : '',
                    obj.machine_code = b_b ? b_b.data.bl_mrp_prodsys_hdr.code : '',
                    obj.uom = b_d ? b_d.data.bl_inv_mst_item_hdr?.uom : '';
                    obj.sequence = b_a ? b_a.data.bl_mrp_process_type_hdr.sequence : '',
                    obj.process_template_hdr_guid = itemperm.bl_mrp_process_instance_hdr.process_template_hdr_guid ? itemperm.bl_mrp_process_instance_hdr.process_template_hdr_guid.toString() : '';
                    obj.output_item_code =  b_d ? b_d.data.bl_inv_mst_item_hdr?.code : '';
                    obj.output_item_name =  b_d ? b_d.data.bl_inv_mst_item_hdr?.name : '';
                    obj.is_active = itemperm.bl_mrp_process_instance_hdr.is_active;
                    obj.completion_date =  b_c ? b_c.data.bl_mrp_job_order_hdr.completion_date : '',
                    obj.status =  b_c ? b_c.data.bl_mrp_job_order_hdr.process_status : '',
                    obj.qty =  b_c ? b_c.data.bl_mrp_job_order_hdr.quantity_base : '',

                    // obj.guid = itemperm.bl_t2t_fi_item_to_tenant_link.guid.toString();
                    // obj.basic_type = b_a.data.bl_fi_mst_item_hdr.txn_type;
                    // obj.sub_item_type = b_a.data.bl_fi_mst_item_hdr.sub_item_type;

                    console.log("Object ->>>>>",obj);
                    return obj;
                  })
                )
            )
            );
            return iif(() => {
              console.log("Resolved",resolved);
              return resolved.totalRecords > 0},
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              this.store.dispatch(InternalJobOrderActions.loadGenDocLinkSuccess({ totalRecords: totalrec }));
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
            this.store.dispatch(InternalJobOrderActions.loadGenDocLinkFailed({ error: err.message }));
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
      if (resolved) {
        this.gridApi.refreshServerSideStore({ purge: true });
        this.store.dispatch(InternalJobOrderActions.resetAgGrid());
      }
    });
  }

  quickSearch() {
    this.gridApi.setQuickFilter(this.searchValue);
  }

  onSearch() {
    let searchValue = this.search.value;
    let query = `SELECT link.guid as requiredGuid FROM bl_svc_issue_gendoc_link AS link INNER JOIN bl_fi_generic_doc_line AS line ON link.generic_doc_line_guid = line.guid INNER JOIN bl_fi_generic_doc_hdr AS hdr ON link.generic_doc_hdr_guid = hdr.guid WHERE ((line.item_name = '${searchValue}') OR (line.item_code = '${searchValue}') OR (hdr.server_doc_1 = '${searchValue}')) and link.hdr_guid = '${this.jobOrderHdrGuid}' and link.doc_type = 'INTERNAL_SALES_INVOICE' and line.server_doc_type ='INTERNAL_SALES_INVOICE'`;
    if (query) {
      const sql = {
        subquery: query,
        table: 'bl_svc_issue_gendoc_link'
      };
      this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({
        next: resolve => {
          console.log("Searchhh",resolve);
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }
      });
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  updateIsActive(e){
    console.log("Event called",e);
    const processInstanceContainer = new MrpProcessInstanceContainerModel();
    this.mrpProcessInstanceService.getByGuid(e.data.guid,this.apiVisa).subscribe(data => {
      console.log("Process instance data",data);
      processInstanceContainer.bl_mrp_process_instance_hdr = data.data.bl_mrp_process_instance_hdr;
      processInstanceContainer.bl_mrp_process_instance_hdr.is_active = e.data.is_active;
    })
   

    console.log("Process instance container",processInstanceContainer)
    setTimeout(() => {
      this.store.dispatch(InternalJobOrderActions.updateProcessInstanceInit({processInstance: processInstanceContainer}))

    }, 1000);
  }

  onRowClicked(entity) {
    if(this.gridApi.getFocusedCell().column.getColId() !== 'is_active'){

    this.store.dispatch(InternalJobOrderActions.selectProcessInstance({ processInstance: entity }));

      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateList: true,
      });
      this.viewColFacade.onNextAndReset(this.index, 11);
    }
  }

  onDeleteCall(guid){
    this.store.dispatch(InternalJobOrderActions.selectGenericDocLinkGuid({guid :guid}));
    this.store.dispatch(InternalJobOrderActions.deleteGenericDocLinkInit());
  }
  onDelete(){
    this.gridApi.getSelectedRows().forEach(row => {
      console.log("Row guid",row.guid);
    
      setTimeout(() => {
        this.onDeleteCall(row.guid);
      }, 1000);
    })
  }
  onNext() {
    // this.viewColFacade.startDraft();
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
    });
    this.viewColFacade.onNextAndReset(this.index, 12);
  }

  onGroupChange(e) {
    this.updateGroupGuid.emit(e.value)
  }

  generateProcessInstance() {
    this.store.dispatch(InternalJobOrderActions.generateProcessInstanceInit({jobOrderGuid : this.jobOrderHdrGuid}));
    setTimeout(() => {
      this.setGridData();
    }, 1000);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
  callRefreshAfterMillis(params, millis, gridApi) {
    setTimeout(function () {
      gridApi.refreshCells(params);
    }, millis);
  }
}
