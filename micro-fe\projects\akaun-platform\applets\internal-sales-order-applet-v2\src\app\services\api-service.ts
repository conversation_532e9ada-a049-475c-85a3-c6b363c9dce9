import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  ApiResponseModel,
  ApiVisa, Core2Config
} from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';
import { ViewColumnFacade } from '../facades/view-column.facade';
import { StockAvailabilityInputModel } from '../models/stock-availability-model';


@Injectable({ providedIn: 'root' })
export class ApiService {
  readonly url =
    Core2Config.LOCAL_HOST +
    Core2Config.TENANT_URL_PREFIX +
    Core2Config.DOMAIN_PREFIX +
    Core2Config.ERP_PREFIX +
    + Core2Config.INV_PREFIX + 'stock-availability';

  protected apiUrl: string;
  protected api_domain_url: string;
  protected endpoint_path: string;

  protected httpClient: HttpClient;

  constructor(http: HttpClient,
    protected viewColFacade: ViewColumnFacade,
  ) {
    this.apiUrl = this.url;
    this.endpoint_path = Core2Config.TENANT_URL_PREFIX + Core2Config.DOMAIN_PREFIX + Core2Config.ERP_PREFIX + Core2Config.INV_PREFIX + 'stock-availability';
    this.httpClient = http;
  }

  public getApiUrl(apiVisa: ApiVisa) {
    let url = this.apiUrl;
    if (this.endpoint_path && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + this.endpoint_path;
    }
    return url;
  }

  public getHttpHeader(apiVisa: ApiVisa) {
    apiVisa.applet_code = apiVisa.applet_code ? apiVisa.applet_code : 'none';
    apiVisa.tenantCode = apiVisa.tenantCode ? apiVisa.tenantCode : '';

    const httpOptions = {
      headers: new HttpHeaders({
        authorization: apiVisa.jwt_secret,
        tenantCode: apiVisa.tenantCode, /// this will be removed in the future
        appId: apiVisa.applet_code, /// this will be removed in the future
      })
    };

    return httpOptions;
  }

  public getHttpHeaderBlob(apiVisa: ApiVisa, params?: HttpParams) {
    const httpOptions = {
      headers: new HttpHeaders({
        authorization: apiVisa.jwt_secret,
        responseType: 'blob',
        tenantCode: apiVisa.tenantCode
      }),
      params: params,
      responseType: 'blob' as 'json'
    };
    return httpOptions;
  }


  public printJasperPdf(
    guid: string,
    printServiceClassName: string,
    printableFormatGuid: string,
    apiVisa: ApiVisa,
    customParam?: string): Observable<Blob> {
    let params;
    let url;
    if (customParam) {
      params = new HttpParams()
        .set('printServiceClassName', printServiceClassName)
        .set('printableFormatHdrGuid', printableFormatGuid)
       .set('customParam', customParam);
    } else {
      params = new HttpParams()
        .set('printServiceClassName', printServiceClassName)
        .set('printableFormatHdrGuid', printableFormatGuid);
    }
    url = apiVisa.api_domain_url + this.endpoint_path + '/print-jasper-pdf/' + guid;
    return this.httpClient.get<Blob>(
      url,
      this.getHttpHeaderBlob(apiVisa, params)
    );
  }

  public getStockAvailability(inputModel: StockAvailabilityInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
      //"inventory_item_guids": ["ad733ce3-25f6-4c53-ad1c-84f4fdc639ce"],
      //"location_guids" : ["a4359b44-158c-4357-8cf2-a35da184e68c"]
    };
    if (inputModel) formData = inputModel;

    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getApiUrlPosMTO(apiVisa: ApiVisa) {
    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX +  'pos/mto-items';
    let url2 = Core2Config.TENANT_DOMAIN_URL_PREFIX + 'pos/mto-items';
    if (url2 && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + url2;
    }
    return url;
  }

  public getMTOByOptionLine(code: string, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrlPosMTO(apiVisa);


    url += "/option-line/backoffice-ep/"+code;

    return this.httpClient.get<ApiResponseModel<any>>(
      url,
      this.getHttpHeader(apiVisa)
    );
  }


  public draftGenericDocument(body: any,guid:any, apiVisa: ApiVisa) {
    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.GENERIC_PREFIX + 'internal-sales-orders/draft/backoffice-ep';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.GENERIC_PREFIX + 'internal-sales-orders/draft/backoffice-ep';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath+"/"+guid;
    }

    return this.httpClient.put<ApiResponseModel<any>>(
      url,
      body,
      this.getHttpHeader(apiVisa)
    );
  }

  public grossProfit(container: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_URL_PREFIX + Core2Config.DOMAIN_PREFIX + Core2Config.ERP_PREFIX + 'gen-doc/reports/gross-profits/backoffice-ep';
    let ep = Core2Config.TENANT_DOMAIN_URL_PREFIX +  Core2Config.ERP_PREFIX + 'gen-doc/reports/gross-profits/backoffice-ep';
    if (ep && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + ep;
    }

    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      container,
      this.getHttpHeader(apiVisa)
    );
  }
}
