import {
  bl_fi_generic_doc_line_RowClass,
  EntityContainerModel,
  FinancialItemContainerModel,
  GenericDocContainerModel,
  GenericDocSingleLineContainer,
  MrpJobOrderHdrContainerModel,
} from 'blg-akaun-ts-lib';

export interface InternalJobOrderState {
  selectedEntity: MrpJobOrderHdrContainerModel;
  selectedJobOrder: MrpJobOrderHdrContainerModel;
  totalRecords: number;
  selectedCustomer: EntityContainerModel;
  selectedItem: FinancialItemContainerModel;
  selectedLineItem: bl_fi_generic_doc_line_RowClass;
  updateAgGrid: boolean;
  genDocLinkGuid : string;
  editMode: boolean;
  totalContainerQty: number;
  processInstance: any;
}

export const initState: InternalJobOrderState = {
  selectedEntity: null,
  selectedJobOrder: null,
  totalRecords: 0,
  selectedCustomer: null,
  selectedItem: null,
  selectedLineItem: null,
  updateAgGrid: false,
  genDocLinkGuid: null,
  editMode: false,
  totalContainerQty: 0.00,
  processInstance: null
};
