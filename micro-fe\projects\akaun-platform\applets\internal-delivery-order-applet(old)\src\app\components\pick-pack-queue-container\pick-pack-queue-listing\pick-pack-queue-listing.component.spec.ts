import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { PickPackQueueListingComponent } from './pick-pack-queue-listing.component';

describe('PickPackQueueListingComponent', () => {
  let component: PickPackQueueListingComponent;
  let fixture: ComponentFixture<PickPackQueueListingComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ PickPackQueueListingComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PickPackQueueListingComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
