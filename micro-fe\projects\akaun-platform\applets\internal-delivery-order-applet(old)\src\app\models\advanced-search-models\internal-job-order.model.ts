import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const internalJobOrderSearchModel: SearchModel = {
  label: {
    jobOrderNo: 'Job Order No',
    creationDate: 'Creation Date',
    itemCode: 'Company',
    location: 'Location',
    category: 'Category'
  },
  dataType: {
    jobOrderNo: 'string',
    creationDate: 'date',
    itemCode: 'string',
    location: 'string',
    category: 'string'
  },
  form: new FormGroup({
    jobOrderNo: new FormControl(),
    creationDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    itemCode: new FormControl(),
    location: new FormControl(),
    category: new FormControl()
  }),
  query: () => '',
  queryCallbacks: {}
};

