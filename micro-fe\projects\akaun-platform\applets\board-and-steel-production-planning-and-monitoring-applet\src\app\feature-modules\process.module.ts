import { NgModule } from '@angular/core';
import { StoreModule } from '@ngrx/store';
import { processReducers } from '../state-controllers/process-controller/store/reducers';
import { processFeatureKey } from '../state-controllers/process-controller/store/reducers/process.reducers';


@NgModule({
    imports: [
        StoreModule.forFeature(processFeatureKey, processReducers.process)
    ],
})
export class ProcessModule { }
