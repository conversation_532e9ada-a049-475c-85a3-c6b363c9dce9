import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import {
  DimensionContainerModel,
  DimensionService,
  GenericDocContainerModel,
  Pagination,
  ProfitCenterContainerModel,
  ProfitCenterService,
  ProjectCoaContainerModel,
  ProjectCoaService,
  SegmentCoaContainerModel,
  SegmentCoaService
} from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-internal-delivery-order-create-department',
  templateUrl: './internal-delivery-order-create-department.component.html',
  styleUrls: ['./internal-delivery-order-create-department.component.css']
})
export class InternalDeliveryOrderCreateDepartmentComponent implements OnInit, OnD<PERSON>roy {

  @Input() draft$: Observable<GenericDocContainerModel>;

  @Output() updateDepartment = new EventEmitter();

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Segment', formControl: 'segment', type: 'segment', readonly: false, hint: ''},
    {label: 'Profit Centre', formControl: 'profitCenter', type: 'profitCenter', readonly: false, hint: ''},
  ];

  rightColControls = [
    {label: 'Dimension', formControl: 'dimension', type: 'dimension', readonly: false, hint: ''},
    {label: 'Project', formControl: 'project', type: 'project', readonly: false, hint: ''},
  ];

  dimension: DimensionContainerModel[];
  profitCenter: ProfitCenterContainerModel[];
  project: ProjectCoaContainerModel[];
  segment: SegmentCoaContainerModel[];

  apiVisa = AppConfig.apiVisa;

  constructor(
    private dimensionService: DimensionService,
    private profitCenterService: ProfitCenterService,
    private projectCOAService: ProjectCoaService,
    private segmentCOAService: SegmentCoaService
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      segment: new FormControl(),
      profitCenter: new FormControl(),
      dimension: new FormControl(),
      project: new FormControl(),
    });
    this.subs.sink = this.dimensionService.getByCriteria(new Pagination(0, 100), this.apiVisa).subscribe(
      {next: resolve => this.dimension = resolve.data}
    );
    this.subs.sink = this.profitCenterService.getByCriteria(new Pagination(0, 100), this.apiVisa).subscribe(
      {next: resolve => this.profitCenter = resolve.data}
    );
    this.subs.sink = this.projectCOAService.getByCriteria(new Pagination(0, 100), this.apiVisa).subscribe(
      {next: resolve => this.project = resolve.data}
    );
    this.subs.sink = this.segmentCOAService.getByCriteria(new Pagination(0, 100), this.apiVisa).subscribe(
      {next: resolve => this.segment = resolve.data}
    );
    this.subs.sink = this.draft$.subscribe({next: resolve => {
      this.form.patchValue({
        segment: resolve.bl_fi_generic_doc_hdr.guid_segment,
        profitCenter: resolve.bl_fi_generic_doc_hdr.guid_profit_center,
        dimension: resolve.bl_fi_generic_doc_hdr.guid_dimension,
        project: resolve.bl_fi_generic_doc_hdr.guid_project,
      });
    }});
    this.subs.sink = this.form.valueChanges.subscribe({next: (form) => {
      this.updateDepartment.emit(form);
    }});
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
