import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { bl_fi_generic_doc_line_RowClass, bl_inv_bin_line_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-add-planned-input-main',
  templateUrl: './add-planned-input-main.component.html',
  styleUrls: ['./add-planned-input-main.component.css']
})
export class AddPlannedInputMainComponent implements OnInit, OnDestroy {

  @Input() binLine$: Observable<bl_inv_bin_line_RowClass>;

  @Output() jobOrderNo = new EventEmitter();

  apiVisa = AppConfig.apiVisa;

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Location', formControl: 'location', type: 'text', readonly: true},
    // {label: 'Branch', formControl: 'branch', type: 'text', readonly: true},
    {label: 'Job Order No', formControl: 'jobOrderNo', type: 'jobOrderNo', readonly: false},
    {label: 'Quantity Measure', formControl: 'quantityMeasure', type: 'number', readonly: true},
  ];

  rightColControls = [
    {label: 'Transaction Date', formControl: 'transactionDate', type: 'date', readonly: false},
    {label: 'Item Code', formControl: 'itemCode', type: 'text', readonly: true},
    {label: 'Quantity Container', formControl: 'quantityContainer', type: 'number', readonly: true},
  ];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      // branch: new FormControl(),
      location: new FormControl(),
      jobOrderNo: new FormControl('', Validators.required),
      quantityMeasure: new FormControl(),
      itemCode: new FormControl(),
      quantityContainer: new FormControl(),
      transactionDate: new FormControl(),
      remarks: new FormControl(),
    });
    this.subs.sink = this.binLine$.pipe(
    ).subscribe(a => {
      this.form.patchValue({
        branch: (<any>a)?.branch,
        location: (<any>a)?.location,
        jobOrderNo: (<any>a)?.code,
        quantityMeasure: (<any>a)?.container_measure,
        itemCode: (<any>a)?.item_code,
        quantityContainer: (<any>a)?.container_qty,
        transactionDate: a?.created_date ?  new Date(a?.created_date).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
        // remarks: a?.item_remarks
      });
    });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
