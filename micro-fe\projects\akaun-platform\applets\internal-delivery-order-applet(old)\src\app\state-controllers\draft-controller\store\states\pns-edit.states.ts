import { createEntity<PERSON><PERSON>pter, EntityA<PERSON>pter, EntityState } from "@ngrx/entity";
import { bl_fi_generic_doc_line_RowClass } from "blg-akaun-ts-lib";

export interface PNSEditState extends EntityState<bl_fi_generic_doc_line_RowClass> {}

export const pnsAdapter: EntityAdapter<bl_fi_generic_doc_line_RowClass> = createEntityAdapter<bl_fi_generic_doc_line_RowClass>({
    selectId: a => a.guid.toString()
});

export const initState: PNSEditState = pnsAdapter.getInitialState();