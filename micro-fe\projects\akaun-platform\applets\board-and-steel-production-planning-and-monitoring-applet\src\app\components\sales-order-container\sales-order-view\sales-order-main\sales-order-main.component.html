<form [formGroup]="form">
  <div fxLayout="column" class="view-col-forms">
    <div fxLayout="row wrap" fxFlexAlign="center" class="view-col-forms">    

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Sales Order No</mat-label>
          <input readonly matInput placeholder="Sales Order No"
            [formControl]="form.controls['so_no']" type="text" autocomplete="off">
        </mat-form-field>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Txn Date</mat-label>
          <input matInput placeholder="Txn Date" [matDatepicker]="date_txn"
            [formControl]="form.controls['date_txn']" autocomplete="off" readonly
            (click)="date_txn.open()">
          <mat-error>Job Order Date is <strong>not valid</strong></mat-error>
          <mat-datepicker-toggle matSuffix [for]="date_txn"></mat-datepicker-toggle>
          <mat-datepicker touchUi="true" #date_txn></mat-datepicker>
        </mat-form-field>
        
        <div fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <blg-select-branch-drop-down-v2
          [apiVisa]="apiVisa"
          [(branch)]="form.controls['branch']"
          (branchSelected)="onBranchSelected($event); updateMain.emit(form.value)"
          [branchGuids]="branchGuids"
        ></blg-select-branch-drop-down-v2> 
        </div>

        <div fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <blg-select-location-drop-down-v2
          [branchSelectedGuid]="selectedBranch"
            [apiVisa]="apiVisa"
            [(location)]="form.controls['location']"
            (locationSelected)="onLocationSelected($event); updateMain.emit(form.value)"
          >
          </blg-select-location-drop-down-v2>
        </div>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Entity Id</mat-label>
        <input style="cursor: pointer" matInput placeholder="Entity Id" [formControl]="form.controls['entityId']" type="text"
         >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Entity Name</mat-label>
        <input style="cursor: pointer" matInput placeholder="Entity Name" [formControl]="form.controls['customerName']" type="text"
         >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Expected Delivery Date</mat-label>
        <input matInput placeholder="Expected Delivery Date" [matDatepicker]="expectedDelivery"
          [formControl]="form.controls['expected_delivery']" autocomplete="off" readonly
          (click)="expectedDelivery.open()">
        <mat-error>Expected Delivery Date is <strong>not valid</strong></mat-error>
        <mat-datepicker-toggle matSuffix [for]="expectedDelivery"></mat-datepicker-toggle>
        <mat-datepicker touchUi="true" #expectedDelivery></mat-datepicker>
      </mat-form-field>
      
      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
        <mat-label>Scenario Option</mat-label>
        <mat-select matNativeControl formControlName="scenario_option" (selectionChange)="updateForm.emit(form.value)">
          <mat-option *ngFor="let status1 of Scenario_Option" [value]="status1">
            {{ status1 }}
          </mat-option>
        </mat-select>
      </mat-form-field>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Job Order No</mat-label>
          <input matInput placeholder="Job Order No" [formControl]="form.controls['job_order_no']" type="text"
           >
        </mat-form-field>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
          <mat-label>Job Doc Status</mat-label>
          <mat-select matNativeControl formControlName="status" (selectionChange)="updateForm.emit(form.value)">
            <mat-option *ngFor="let status1 of STATUS" [value]="status1">
              {{ status1 }}
            </mat-option>
          </mat-select>
        </mat-form-field>

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label>Process Status</mat-label>
      <mat-select matNativeControl formControlName="process_status">
        <mat-option *ngFor="let status1 of PROCESS_STATUS" [value]="status1">
          {{ status1 }}
        </mat-option>
      </mat-select>
    </mat-form-field>

   
    </div>
  </div>
</form>