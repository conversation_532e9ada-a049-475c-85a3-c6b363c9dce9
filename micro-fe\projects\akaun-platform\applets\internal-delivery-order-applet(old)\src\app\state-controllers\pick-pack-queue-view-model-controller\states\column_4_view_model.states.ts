export interface Column4ViewModelState {
  itemDetailsTab_Color: string;
  serialNumberTab_Color: string;
  itemDetailsTab_qtyBaseField_Color: string;
  itemDetailsTab_qtyBaseField_Value: number;
  itemDetailsTab_itemType_Value: string;
  serialNumberTab_ScanTab_SerialNumbersListing: string[];
}

export const initialState: Column4ViewModelState = {
  itemDetailsTab_Color: null,
  serialNumberTab_Color: null,
  itemDetailsTab_qtyBaseField_Color: null,
  itemDetailsTab_qtyBaseField_Value: 0,
  itemDetailsTab_itemType_Value: null,
  serialNumberTab_ScanTab_SerialNumbersListing: [],
};
