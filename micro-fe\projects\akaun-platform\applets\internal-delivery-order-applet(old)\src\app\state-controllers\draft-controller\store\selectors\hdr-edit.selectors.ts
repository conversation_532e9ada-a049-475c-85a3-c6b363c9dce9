import { createSelector } from "@ngrx/store";
import { selectDraftState } from "../..";

export const selectHdr = createSelector(
  selectDraftState,
  (s1) => s1.hdrEdit
)

export const selectSegment = (state) => state.draft.hdrEdit.guid_segment
export const selectProfitCenter = (state) => state.draft.hdrEdit.guid_profit_center
export const selectProject = (state) => state.draft.hdrEdit.guid_project
export const selectDimension = (state) => state.draft.hdrEdit.guid_dimension