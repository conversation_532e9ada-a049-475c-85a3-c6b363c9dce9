import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProcessingContainerComponent } from './processing-container.component';

describe('ProcessingContainerComponent', () => {
  let component: ProcessingContainerComponent;
  let fixture: ComponentFixture<ProcessingContainerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ProcessingContainerComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProcessingContainerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
