import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-account-address',
  templateUrl: './account-address.component.html',
  styleUrls: ['./account-address.component.css']
})
export class AccountAddressComponent implements OnInit, OnDestroy {

  @Input() draft$: Observable<GenericDocContainerModel>;

  @Output() shippingInfo = new EventEmitter();
  @Output() billingInfo = new EventEmitter();

  private subs = new SubSink();

  shippingForm: FormGroup;
  billingForm: FormGroup;

  shippingLeftColControls = [
    {label: 'Shipping Address', formControl: 'shippingAddress', type: 'shippingAddress', readonly: false},
    {label: 'Address Line 2', formControl: 'addressLine2', type: 'text', readonly: true},
    {label: 'Address Line 4', formControl: 'addressLine4', type: 'text', readonly: true},
    {label: 'Country', formControl: 'country', type: 'text', readonly: true},
    {label: 'City', formControl: 'city', type: 'text', readonly: true},
  ];

  shippingRightColControls = [
    {label: 'Address Line 1', formControl: 'addressLine1', type: 'text', readonly: true},
    {label: 'Address Line 3', formControl: 'addressLine3', type: 'text', readonly: true},
    {label: 'Address Line 5', formControl: 'addressLine5', type: 'text', readonly: true},
    {label: 'State', formControl: 'state', type: 'text', readonly: true},
    {label: 'Postcode', formControl: 'postcode', type: 'text', readonly: true},
  ];

  billingLeftColControls = [
    {label: 'Billing Address', formControl: 'billingAddress', type: 'billingAddress', readonly: false},
    {label: 'Address Line 2', formControl: 'addressLine2', type: 'text', readonly: true},
    {label: 'Address Line 4', formControl: 'addressLine4', type: 'text', readonly: true},
    {label: 'Country', formControl: 'country', type: 'text', readonly: true},
    {label: 'City', formControl: 'city', type: 'text', readonly: true},
  ];

  billingRightColControls = [
    {label: 'Address Line 1', formControl: 'addressLine1', type: 'text', readonly: true},
    {label: 'Address Line 3', formControl: 'addressLine3', type: 'text', readonly: true},
    {label: 'Address Line 5', formControl: 'addressLine5', type: 'text', readonly: true},
    {label: 'State', formControl: 'state', type: 'text', readonly: true},
    {label: 'Postcode', formControl: 'postcode', type: 'text', readonly: true},
  ];

  constructor() { }

  ngOnInit() {
    this.shippingForm = new FormGroup({
      shippingAddress: new FormControl('', Validators.required),
      addressLine1: new FormControl(),
      addressLine2: new FormControl(),
      addressLine3: new FormControl(),
      addressLine4: new FormControl(),
      addressLine5: new FormControl(),
      country: new FormControl(),
      city: new FormControl(),
      state: new FormControl(),
      postcode: new FormControl(),
      remarks: new FormControl(),
    });
    this.billingForm = new FormGroup({
      billingAddress: new FormControl(''),
      addressLine1: new FormControl(),
      addressLine2: new FormControl(),
      addressLine3: new FormControl(),
      addressLine4: new FormControl(),
      addressLine5: new FormControl(),
      country: new FormControl(),
      city: new FormControl(),
      state: new FormControl(),
      postcode: new FormControl(),
      remarks: new FormControl(),
    });
    this.shippingForm.controls['shippingAddress'].disable();
    this.billingForm.controls['billingAddress'].disable();
    // this.subs.sink = this.store.select(InternalSalesOrderSelectors.selectCustomer).subscribe({next: resolve => {
    //   if (resolve) {
    //     this.shippingForm.controls['shippingAddress'].enable();
    //     this.billingForm.controls['billingAddress'].enable();
    //   }
    // }});
    this.subs.sink = this.draft$.subscribe({
      next: (resolve: any) => {
        if (resolve.bl_fi_generic_doc_hdr.doc_entity_hdr_guid) {
          this.shippingForm.controls['shippingAddress'].enable();
          this.billingForm.controls['billingAddress'].enable();
        }
        this.shippingForm.patchValue({
          shippingAddress: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.name,
          addressLine1: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.address_line_1,
          addressLine2: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.address_line_2,
          addressLine3: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.address_line_3,
          addressLine4: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.address_line_4,
          addressLine5: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.address_line_5,
          country: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.country,
          city: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.city,
          state: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.state,
          postcode: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.postal_code,
          remarks: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shippingAddress?.remarks,
        });
        this.billingForm.patchValue({
          billingAddress: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.name,
          addressLine1: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.address_line_1,
          addressLine2: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.address_line_2,
          addressLine3: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.address_line_3,
          addressLine4: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.address_line_4,
          addressLine5: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.address_line_5,
          country: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.country,
          city: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.city,
          state: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.state,
          postcode: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.postal_code,
          remarks: resolve?.bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.billingAddress?.remarks,
        });
      }});
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}

