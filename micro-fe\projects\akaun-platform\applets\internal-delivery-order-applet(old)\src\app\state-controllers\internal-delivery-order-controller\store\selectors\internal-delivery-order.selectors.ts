import { createFeatureSelector } from '@ngrx/store';
import { internalDeliveryOrderFeatureKey } from '../reducers/internal-delivery-order.reducers';
import { InternalDeliveryOrderStates } from '../states';
import { InternalDeliveryOrderState } from '../states/internal-delivery-order.states';

export const selectInternalDeliveryOrderFeature = createFeatureSelector<InternalDeliveryOrderState>(internalDeliveryOrderFeatureKey);

export const selectGuid = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedGuid;
export const selectEntity = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedEntity;
export const selectTotalRecords = (state: InternalDeliveryOrderStates) => state.deliveryOrder.totalRecords;
export const selectErrorLog = (state: InternalDeliveryOrderStates) => state.deliveryOrder.errorLog;
export const selectDraft = (state: InternalDeliveryOrderStates) => state.deliveryOrder.draft;
export const selectDraftEdit = (state: InternalDeliveryOrderStates) => state.deliveryOrder.draftEdit;
export const selectCustomer = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedCustomer;

// export const selectEntityType = (state: InternalDeliveryOrderStates) => state.deliveryOrder.entityType;

export const selectShippingAddress = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedShippingAddress;
export const selectBillingAddress = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedBillingAddress;
export const selectContactPerson = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedContactPerson;
export const selectDoc = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedDoc;
export const selectItem = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedItem;
export const selectAgGrid = (state: InternalDeliveryOrderStates) => state.deliveryOrder.updateAgGrid;
export const selectLineItem = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedLineItem;
export const selectEditMode = (state: InternalDeliveryOrderStates) => state.deliveryOrder.editMode;
export const getPricingSchemeLinks = (state: InternalDeliveryOrderStates) => state.deliveryOrder.pricingSchemeLink;
export const selectKnockoffListingConfig = (state: InternalDeliveryOrderStates) => state.deliveryOrder.knockoffListingConfig;

// For Serial, Bin, Batch
export const selectInvItem = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedInvItem;
export const selectSerial = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedSerial;
export const selectBatch = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedBatch;
export const selectBin = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedBin;
export const selectCompanyGuid = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedCompGuid;
export const selectPrintableFormatGuid = (state: InternalDeliveryOrderStates) => state.deliveryOrder.selectedPrintableFormatGuid;

export const selectExternalJobDocs = (state: InternalDeliveryOrderStates) => state.deliveryOrder.loadedExternalJobDocs;
export const selectInternalJobDocs = (state: InternalDeliveryOrderStates) => state.deliveryOrder.loadedInternalJobDocs;
export const selectPickupJobDocs = (state: InternalDeliveryOrderStates) => state.deliveryOrder.loadedPickupJobDocs;

export const selectJobListingStatus = (state: InternalDeliveryOrderStates) => state.deliveryOrder.jobListingStatus;
export const selectDeliveryDetailListingStatus = (state: InternalDeliveryOrderStates) => state.deliveryOrder.deliveryDetailListingStatus;

export const selectDeliveryOrders = (state: InternalDeliveryOrderStates) => state.deliveryOrder.loadedGenDocs;

export const refreshGenDocListing = (state: InternalDeliveryOrderStates) => state.deliveryOrder.refreshGenDocListing;
