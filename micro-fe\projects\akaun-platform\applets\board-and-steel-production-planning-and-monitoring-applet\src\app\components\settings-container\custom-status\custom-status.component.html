<div style="text-align: right; margin-top: 16px;">
  <button mat-raised-button color="primary" type="button" (click)="onSave()">SAVE</button>
</div>
<div style="margin: 10px">
  <mat-accordion>
    <mat-expansion-panel expanded="true">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="field-header">Enable Custom Status</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ul>
        <li *ngFor="let f of [1,2,3,4,5]">
          <mat-slide-toggle [formControl]="form.controls['ENABLE_CUSTOM_STATUS_' + f]">Client Doc Status {{f}}</mat-slide-toggle>
          <ul style="margin-left: 5%;">
            <li>
              <mat-slide-toggle [formControl]="form.controls['ENABLE_CUSTOM_STATUS_HDR_' + f]">Header</mat-slide-toggle>
            </li>
            <li>
              <mat-slide-toggle [formControl]="form.controls['ENABLE_CUSTOM_STATUS_LINE_' + f]">Line</mat-slide-toggle>
            </li>
          </ul>
        </li>
      </ul>
    </mat-expansion-panel>
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="field-header">Header Custom Status Settings</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <app-custom-status-settings *ngFor="let i of [1,2,3,4,5]" fieldSet="HDR" [index]="i" [masterSettings$]="masterSettings$"></app-custom-status-settings>
    </mat-expansion-panel>
    <mat-expansion-panel>
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="field-header">Line Custom Status Settings</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <app-custom-status-settings *ngFor="let i of [1,2,3,4,5]" fieldSet="LINE" [index]="i" [masterSettings$]="masterSettings$"></app-custom-status-settings>
    </mat-expansion-panel>
  </mat-accordion>
</div>

  