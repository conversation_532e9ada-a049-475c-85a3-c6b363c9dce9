import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { InternalJobOrderSelectors } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-vl-details',
  templateUrl: './vl-details.component.html',
  styleUrls: ['./vl-details.component.scss']
})
export class ViewLengthBreakdownDetailsComponent extends ViewColumnComponent {
  @Input() editMode: boolean;
  @Input() selectedIndex: any;
  @Output() updateSalesInvoiceItemDetails = new EventEmitter();
  public form: FormGroup;
  protected subs = new SubSink();

  constructor(private readonly store: Store<InternalJobOrderStates>) { super(); }

  ngOnInit() {
    this.form = new FormGroup({
      doc_no: new FormControl(),
      customerName: new FormControl(),
      itemName: new FormControl(),
      itemCode: new FormControl(),
      qty: new FormControl(),
      uom: new FormControl(),
      estimatedDeliveryDate: new FormControl(),
      mrp_request_status_production: new FormControl(),
      mrp_request_status_schedule: new FormControl(),
      mrp_request_status_exists: new FormControl(),
      mrp_request_remarks: new FormControl(),
    });

    this.store.select(InternalJobOrderSelectors.selectLinkedSalesOrder).subscribe(data => {
      this.form.patchValue({
        doc_no: data.doc_no,
        customerName: data.customerName,
        itemName: data.item_name,
        itemCode: data.item_code,
        qty: data.qty,
        uom: data.uom,
        estimatedDeliveryDate: data.track_delivery_time_estimated,
        mrp_request_status_production: data.mrp_request_status_production,
        mrp_request_status_schedule: data.mrp_request_status_schedule,
        mrp_request_status_exists: data.mrp_request_status_exists,
        mrp_request_remarks: data.mrp_request_remarks,
      })
    })
  }

}