import { OnInit, Component, ViewChild, Input, AfterViewChecked } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { SubSink } from 'subsink2';
import { DeliveryInstructionComponent } from './delivery-instructions/delivery-instructions.component';
// import { InternalOutboundStockTransferEditLineItemDepartmentComponent } from '../../internal-outbound-stock-transfer-edit-line-item/internal-outbound-stock-transfer-edit-line-item-department/internal-outbound-stock-transfer-edit-line-item-department.component';
import { InternalDeliveryOrderAddLineItemMainComponent } from './main-details/internal-delivery-order-add-line-item-main.component';

@Component({
  selector: 'app-add-line-item-item-details',
  templateUrl: './add-line-item-item-details.component.html',
  styleUrls: ['./add-line-item-item-details.component.css']
})
export class AddLineItemItemDetailsComponent implements OnInit, AfterViewChecked {

  protected subs = new SubSink();

  // @Input() batch$;
  @Input() item$;
  @Input() tax$;
  @Input() dept$;
  @Input() line$;
  @Input() childSelectedIndex$;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(InternalDeliveryOrderAddLineItemMainComponent) main: InternalDeliveryOrderAddLineItemMainComponent;
  @ViewChild(DeliveryInstructionComponent) delivery: DeliveryInstructionComponent
  // @ViewChild(InternalOutboundStockTransferEditLineItemDepartmentComponent) dept: InternalOutboundStockTransferEditLineItemDepartmentComponent;

  ngOnInit() {}

  ngAfterViewChecked() {
    this.matTab.realignInkBar();
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
