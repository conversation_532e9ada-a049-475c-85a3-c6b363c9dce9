import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { bl_fi_generic_doc_hdr_RowClass, InternalJobOrderService } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-internal-job-order-view-export',
  templateUrl: './internal-job-order-view-export.component.html',
  styleUrls: ['./internal-job-order-view-export.component.css']
})
export class InternalJobOrderViewExportComponent implements OnInit {

  @Output() print = new EventEmitter();

  constructor() { }

  ngOnInit() {
  }

  onPrint() {
    this.print.emit();
  }

}
