import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { InventoryItemService, MrpProcessInstanceLinesService, MrpProcessInstanceService, Pagination, StockResarvationService, TenantUserProfileService, bl_fi_generic_doc_hdr_RowClass, bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable, forkJoin, iif, of, zip } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../../../../facades/view-column.facade';
import { SalesOrderStates } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/sales-order-controller/store/states';
import { SalesOrderSelectors } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/sales-order-controller/store/selectors';

@Component({
  selector: 'app-detailed-reservation-stock',
  templateUrl: './detailed-reservation-stock.component.html',
  styleUrls: ['./detailed-reservation-stock.component.css']
})
export class DetailedReservationStockComponent extends ViewColumnComponent implements OnInit {

  protected index = 3;
  @Input() localState: any;
  @Input() rowData: bl_fi_generic_doc_line_RowClass[] = [];

  @Output() next = new EventEmitter();
  @Output() lineItem = new EventEmitter<bl_fi_generic_doc_line_RowClass>();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    onCellClicked: (params) => this.onRowClicked(params)
  };

  gridApi;
  pagination = new Pagination();
  SQLGuids: string[] = null;
  private subs = new SubSink();
  itemGuid;
  invItemGuid;
  prevIndex: number;
  protected prevLocalState: any;

  columnsDefs = [
    // {headerName: 'Job Order No', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Size of Wire', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Required Length', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Priority', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Doc No', field: 'doc_no', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Reserved Quantity', field: 'reserved_qty'},
   

  ];

  selectedRowIndex = null;

  constructor(    
    private stockResarvationService: StockResarvationService,
    protected readonly store: Store<SalesOrderStates>,
    private mrpProcessInstanceLinesService: MrpProcessInstanceLinesService,
    private inventoryItemService : InventoryItemService,
    private profileService: TenantUserProfileService,
    // protected readonly processStore: Store<ProcessStates>,
    protected viewColFacade: ViewColumnFacade,

    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.store.select(SalesOrderSelectors.selectSalesOrder).subscribe(salesOrder => {
      this.itemGuid = salesOrder.item_guid;
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();  
    const apiVisa = AppConfig.apiVisa;
    let invPagination = new Pagination();
    invPagination.conditionalCriteria = [
      { columnName: 'guid_fi_mst_item', operator: '=', value: this.itemGuid },
    ];
    this.subs.sink = this.inventoryItemService.getByCriteria(invPagination,apiVisa).subscribe(data => {
     console.log("inv data",data);
     this.invItemGuid = data.data[0].bl_inv_mst_item_hdr.guid;
      this.setGridData();
    })
    this.setGridData();
  }

  setGridData() {
    
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: "calcTotalRecords", operator: "=", value: "true" },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
          // { columnName: "bl_mrp_process_template_hdr_guid", operator: "=", value: this.process_template_hdr_guid },
          { columnName: 'item_guid', operator: '=', value: this.invItemGuid },

          {
            columnName: "guids",
            operator: "=",
            value: this.SQLGuids
              ? this.SQLGuids.slice(
                grid.request.startRow,
                grid.request.endRow
              ).toString()
              : "",
          },
        ];
        let totalrec = 0;
        this.stockResarvationService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {
            console.log("Planned Input",resolved);
            totalrec = resolved.data.length;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                itemperm.bl_inv_stock_reservation_hdr.created_by_subject_guid ?
                this.profileService.getByGuid(itemperm.bl_inv_stock_reservation_hdr.created_by_subject_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),

                ).pipe(
                  map(([b_a]) => {
                    console.log('Ivn ->>>',b_a);
                    let obj = {"guid":'', "doc_no": '', "reserved_qty": ''};
                    obj.guid = itemperm.bl_inv_stock_reservation_hdr.guid ?  itemperm.bl_inv_stock_reservation_hdr.guid : '';
                    obj.doc_no = itemperm.bl_inv_stock_reservation_hdr.server_doc_1 ? itemperm.bl_inv_stock_reservation_hdr.server_doc_1 : '';
                    obj.reserved_qty = itemperm.bl_inv_stock_reservation_hdr.total_reserve_qty ? itemperm.bl_inv_stock_reservation_hdr.total_reserve_qty : '0'
                    return obj;
                  })
                )
            )
            );
            return iif(() => resolved.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              console.log("Objecr Input",data);

              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    // this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
    //   if (resolved) {
    //     this.gridApi.refreshServerSideStore({ purge: true });
    //   }
    // });
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onNext() {
    this.next.emit();
  }

  onRowClicked(e) {
    // this.selectedRowIndex = this.localState.selectedLineItemRowIndex === null ? e.rowIndex : null;
    this.lineItem.emit(e.data);
  }

}
