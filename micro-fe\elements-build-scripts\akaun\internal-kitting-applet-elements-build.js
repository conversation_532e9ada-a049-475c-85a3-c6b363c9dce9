const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/internal-kitting-applet/runtime.js',
    './dist/internal-kitting-applet/polyfills-es5.js',
    './dist/internal-kitting-applet/scripts.js',
    './dist/internal-kitting-applet/main.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/internal-kitting-applet');
  await concat(files, './elements/akaun-platform/applets/internal-kitting-applet/internal-sales-order-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
