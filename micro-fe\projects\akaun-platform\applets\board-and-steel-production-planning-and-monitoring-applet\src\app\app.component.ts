import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { IdentityTokenService, MrpProdsysService, Pagination } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { mainPath } from './app.routing';
import { menuItems } from './models/menu-items';
import { ProcessPagesService } from './services/process-pages.service';
import { ProcessActions } from './state-controllers/process-controller/store/actions';
import { ProcessStates } from './state-controllers/process-controller/store/states';
import { ViewCacheActions } from './state-controllers/view-cache-controller/store/actions';
import { ViewCacheStates } from './state-controllers/view-cache-controller/store/states';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  providers: [ProcessPagesService]
})
export class AppComponent implements OnInit {

  readonly appletName = 'Board And Steel Production Planning And Monitoring Applet';
  readonly menuItems = menuItems;
  readonly mainPath = mainPath;

  constructor(
    private tokenService: IdentityTokenService,
    private mrpService: MrpProdsysService,
    private operatorService: ProcessPagesService,
    private readonly viewCacheStore: Store<ViewCacheStates>,
    private readonly processStore: Store<ProcessStates>,
    private router: Router) {
  }

  async ngOnInit() {
    await this.tokenService.getSingleRefreshToken({
      tenantCode: sessionStorage.getItem('tenantCode'),
      appletCode: sessionStorage.getItem('appletCode')
    }, AppConfig.apiVisa).toPromise().then(resolve => {
      sessionStorage.setItem('appletToken', resolve.data.token);
      AppConfig.setAppletToken();
    });
    // await this.mrpService.getByCriteria(new Pagination(0, 100,
    //   [
    //     {columnName: 'prodsys_type', operator: '=', value: 'PROCESS'}
    //   ]), AppConfig.apiVisa).toPromise().then(resolve => {
    //   this.menuItems.push(
    //     ...resolve.data.map(p => {
    //       // TODO: This part can be optimized
    //       this.viewCacheStore.dispatch(ViewCacheActions.addProcess({
    //         process: p.bl_mrp_prodsys_hdr.guid.toString(),
    //         cache: this.operatorService.pages
    //       }));
    //       this.processStore.dispatch(ProcessActions.mapProcess({
    //         guid: p.bl_mrp_prodsys_hdr.guid.toString(),
    //         process: p.bl_mrp_prodsys_hdr.name.toString()
    //       }));
    //       return {
    //         state: `issue-job-order/${p.bl_mrp_prodsys_hdr.guid}`,
    //         name: p.bl_mrp_prodsys_hdr.name.toString(),
    //         type: 'link',
    //         icon: 'list_alt'
    //       };
    //     })
    //   );
    // });
    this.router.initialNavigation();
  }

  onMenuClick(menu: string) {
    this.viewCacheStore.dispatch(ViewCacheActions.changeProcess({ process: menu }));
  }
}
