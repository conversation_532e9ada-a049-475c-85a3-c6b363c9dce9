import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const internalDeliveryOrderSearchModel: SearchModel = {
  label: {
    salesOrderNo: 'Sales Order No',
    creationDate: 'Creation Date',
    branch: 'Branch',
    location: 'Location',
    customerName: 'Customer Name',
    status: 'Sub Type'
  },
  dataType: {
    salesOrderNo: 'string',
    creationDate: 'date',
    branch: 'string',
    location: 'string',
    customerName: 'string',
    status: 'string'
  },
  form: new FormGroup({
    salesOrderNo: new FormControl(),
    creationDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    branch: new FormControl(),
    location: new FormControl(),
    customerName: new FormControl(),
    status: new FormControl()
  }),
  query: () => '',
  queryCallbacks: {}
};

export const internalOutboundDeliveryOrderSearchModel: SearchModel = {
  label: {
    createdDate: 'Creation Date',
    trackingId: 'Tracking Id' 
  },
  dataType: {
    createdDate: 'date',
    trackingId: 'string'
  },
  form: new FormGroup({
    createdDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    trackingId: new FormControl()
  }),
  query: (query) => 
  `line.tracking_id ILIKE '%${query.trim()}%'`,
  table: `bl_fi_generic_doc_line`,
  queryCallbacks: {
    trackingId: (trackingId) => trackingId ? `line.tracking_id ILIKE '%${trackingId.trim()}%'` : "",
    createdDate: (createdDate) => {
      if (createdDate.from || createdDate.to) {
        var from = createdDate.from ? createdDate.from : createdDate.to;
        var to = createdDate.to ? createdDate.to : createdDate.from;
        return `line.created_date >= '${from.format(
          "YYYY-MM-DD HH:mm:ss"
        )}' AND line.created_date <= '${to.format("YYYY-MM-DD HH:mm:ss")}'`;
      }
      return "";
    },
  },
  additionalCondition: ` AND line.status = 'ACTIVE' AND line.server_doc_type = 'INTERNAL_OUTBOUND_DELIVERY_ORDER'`
};

export const internalInboundDeliveryOrderSearchModel: SearchModel = {
  label: {
    createdDate: 'Creation Date',
    trackingId: 'Tracking Id' 
  },
  dataType: {
    createdDate: 'date',
    trackingId: 'string'
  },
  form: new FormGroup({
    createdDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    trackingId: new FormControl()
  }),
  query: (query) => 
  `line.tracking_id ILIKE '%${query.trim()}%'`,
  table: `bl_fi_generic_doc_line`,
  queryCallbacks: {
    trackingId: (trackingId) => trackingId ? `line.tracking_id ILIKE '%${trackingId.trim()}%'` : "",
    createdDate: (createdDate) => {
      if (createdDate.from || createdDate.to) {
        var from = createdDate.from ? createdDate.from : createdDate.to;
        var to = createdDate.to ? createdDate.to : createdDate.from;
        return `line.created_date >= '${from.format(
          "YYYY-MM-DD HH:mm:ss"
        )}' AND line.created_date <= '${to.format("YYYY-MM-DD HH:mm:ss")}'`;
      }
      return "";
    },
  },
  additionalCondition: ` AND line.status = 'ACTIVE' AND line.server_doc_type = 'INTERNAL_INBOUND_STOCK_TRANSFER'`
};


