<form [formGroup]="form" #formDirectives="ngForm">
  <div class="view-col-forms">
    <mat-form-field appearance="outline" *ngIf="modeValue">
      <mat-label>Document type</mat-label>
      <input matInput [formControl]="form.controls['docType']" (change)="updateMain.emit(form.value)" />
    </mat-form-field>

    <mat-form-field appearance="outline" *ngIf="modeValue">
      <mat-label>Doc No (Tenant)</mat-label>
      <input matInput [formControl]="form.controls['tenantDocNo']" (change)="updateMain.emit(form.value)" />
    </mat-form-field>

    <mat-form-field appearance="outline" *ngIf="modeValue">
      <mat-label>Doc No (Company)</mat-label>
      <input matInput [formControl]="form.controls['companyDocNo']" (change)="updateMain.emit(form.value)" />
    </mat-form-field>

    <mat-form-field appearance="outline" *ngIf="modeValue">
      <mat-label>Doc No (Branch)</mat-label>
      <input matInput [formControl]="form.controls['branchDocNo']" (change)="updateMain.emit(form.value)" />
    </mat-form-field>
    <div fxLayout="row" fxLayoutGap="5px">
      <div fxFlex="50" fxLayout="column">
        <blg-select-branch-drop-down [apiVisa]="apiVisa" [(branch)]="form.controls['branch']"
          (branchSelected)="onBranchSelected($event); updateMain.emit(form.value)">
        </blg-select-branch-drop-down>
        <div *ngFor="let x of leftColControls; let i = index">
          <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
            <mat-label>{{x.label}}</mat-label>
            <input matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
          </mat-form-field>
          <ng-template #input>
            <ng-container [ngSwitch]="x.type">
              <mat-form-field *ngSwitchCase="'text'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input matInput [formControl]="form.controls[x.formControl]" autocomplete="off" type="text">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'number'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input matInput [formControl]="form.controls[x.formControl]" autocomplete="off" type="number">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'text-area'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <textarea #remarks matInput [formControl]="form.controls[x.formControl]"></textarea>
                <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'date'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input matInput [matDatepicker]="datepicker" [formControl]="form.controls[x.formControl]"
                  autocomplete="off" readonly (click)="datepicker.open()">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle>
                <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'customer'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input style="cursor: pointer" matInput readonly [formControl]="form.controls[x.formControl]"
                  autocomplete="off" type="text" (click)="customer.emit()">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'shippingInfo'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input style="cursor: pointer" matInput readonly [formControl]="form.controls[x.formControl]"
                  autocomplete="off" type="text" (click)="shippingInfo.emit()">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                <mat-hint><strong>Customer</strong> must be selected first</mat-hint>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'billingInfo'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input style="cursor: pointer" matInput readonly [formControl]="form.controls[x.formControl]"
                  autocomplete="off" type="text" (click)="billingInfo.emit()">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                <mat-hint><strong>Customer</strong> must be selected first</mat-hint>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'salesLead'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <mat-select [formControl]="form.controls[x.formControl]">
                  <mat-option value="Corporate">Corporate</mat-option>
                  <mat-option value="Non-Corporate">Non-Corporate</mat-option>
                </mat-select>
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <app-credit-terms *ngSwitchCase="'creditTerms'"
                [(creditTerms)]="form.controls[x.formControl]"></app-credit-terms>
              <app-sales-man *ngSwitchCase="'salesAgent'" [(salesAgent)]="form.controls[x.formControl]"></app-sales-man>
            </ng-container>
          </ng-template>
        </div>
      </div>
      <div fxFlex="50" fxLayout="column">
        <blg-select-location-drop-down [branchSelectedGuid]="selectedBranch" [apiVisa]="apiVisa"
          [(location)]="form.controls['location']"></blg-select-location-drop-down>
        <div *ngFor="let x of rightColControls; let i = index">
          <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
            <mat-label>{{x.label}}</mat-label>
            <input matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
          </mat-form-field>
          <ng-template #input>
            <ng-container [ngSwitch]="x.type">
              <mat-form-field *ngSwitchCase="'text'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input matInput [formControl]="form.controls[x.formControl]" autocomplete="off" type="text">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'number'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input matInput [formControl]="form.controls[x.formControl]" autocomplete="off" type="number">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'text-area'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <textarea #remarks matInput [formControl]="form.controls[x.formControl]"></textarea>
                <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'date'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input matInput [matDatepicker]="datepicker" [formControl]="form.controls[x.formControl]"
                  autocomplete="off" readonly (click)="datepicker.open()">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle>
                <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'customer'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input style="cursor: pointer" matInput readonly [formControl]="form.controls[x.formControl]"
                  autocomplete="off" type="text" (click)="customer.emit()">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'shippingInfo'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input style="cursor: pointer" matInput readonly [formControl]="form.controls[x.formControl]"
                  autocomplete="off" type="text" (click)="shippingInfo.emit()">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                <mat-hint><strong>Customer</strong> must be selected first</mat-hint>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'billingInfo'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <input style="cursor: pointer" matInput readonly [formControl]="form.controls[x.formControl]"
                  autocomplete="off" type="text" (click)="billingInfo.emit()">
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                <mat-hint><strong>Customer</strong> must be selected first</mat-hint>
              </mat-form-field>
              <app-currency [(currency)]="form.controls[x.formControl]" *ngSwitchCase="'currency'"></app-currency>
            </ng-container>
          </ng-template>
        </div>
      </div>
    </div>
    <mat-form-field appearance="outline">
      <mat-label>Remarks</mat-label>
      <textarea #remarks matInput [formControl]="form.controls['remarks']"></textarea>
      <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
    </mat-form-field>
  </div>
</form>