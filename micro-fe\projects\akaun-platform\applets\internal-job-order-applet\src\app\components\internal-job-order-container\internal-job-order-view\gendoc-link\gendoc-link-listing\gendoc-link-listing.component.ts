import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { Store } from "@ngrx/store";
import { bl_fi_mst_entity_line_RowClass, GenericDocLineService, InternalSalesOrderService,
  MrpJobOrderGenDocLinkService, Pagination, SubQueryService } from "blg-akaun-ts-lib";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { forkJoin, iif, Observable, of, zip, Subject, } from "rxjs";
import { SubSink } from "subsink2";
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { debounceTime, catchError, map } from "rxjs/operators";
import { FormControl } from "@angular/forms";
import { InternalJobOrderStates } from "../../../../../state-controllers/internal-job-order-controller/store/states";
import { ViewColumnFacade } from "../../../../../facades/view-column.facade";
import { InternalJobOrderSelectors } from "../../../../../state-controllers/internal-job-order-controller/store/selectors";
import { InternalJobOrderActions } from "../../../../../state-controllers/internal-job-order-controller/store/actions";
import { Column1ViewModelActions } from '../../../../../state-controllers/gen-doc-view-model-controller/actions';
import { ColumnViewModelStates } from '../../../../../state-controllers/gen-doc-view-model-controller/states';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { Column1ViewSelectors } from '../../../../../state-controllers/gen-doc-view-model-controller/selectors';
import { GridOptions } from 'ag-grid-enterprise';
import { SessionStates } from "projects/shared-utilities/modules/session/session-controller/states";
import { SessionSelectors } from "projects/shared-utilities/modules/session/session-controller/selectors";

@Component({
  selector: "app-gendoc-link-listing",
  templateUrl: "./gendoc-link-listing.component.html",
  styleUrls: ["./gendoc-link-listing.component.scss"],
})
export class InvoiceServiceNoteLinkListingComponent extends ViewColumnComponent implements OnInit, OnDestroy {
  deactivateAdd$;
  @Input() localState: any;
  @Output() lineItem = new EventEmitter<bl_fi_mst_entity_line_RowClass>();
  private columnMoveSubject: Subject<void> = new Subject<void>();
  private debounceTimeMs = 500;
  gridOptions: GridOptions| undefined;
  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    cellStyle: { textAlign: "left" },
  };

  gridApi;
  protected readonly index = 1;
  columnsDefs;
  rowData: any[] = [];
  apiVisa = AppConfig.apiVisa;
  labelGuids: any = [];
  tempCat$: Observable<boolean>;
  tryError$: Observable<any[]>;
  tempCatRow$: Observable<any>;
  protected subs = new SubSink();
  pinnedBottomRowData: any;
  packageListing: any = [];
  searchValue;
  pagination = new Pagination();
  SQLGuids: string[] = null;
  jobOrderHdrGuid;
  totalContainerQty = 0.00;
  public search = new FormControl();
  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    public readonly viewModelStore: Store<ColumnViewModelStates>,
    private readonly sessionStore: Store<SessionStates>,
    private mrpJobOrderGenDocLinkService : MrpJobOrderGenDocLinkService,
    private genericDocLineService: GenericDocLineService,
    private subQueryService: SubQueryService,
    private soService: InternalSalesOrderService,
    private readonly store: Store<InternalJobOrderStates>,
    private viewColFacade: ViewColumnFacade
  ) {
    super();
    const customComparator = (valueA, valueB) => {
      if (valueA != null && "" !== valueA && valueB != null && "" !== valueB) {
        return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
      }
    };
    this.columnsDefs = [
      {
        headerName: "Sales Order No",
        field: "doc_no",
        width: 250,
        checkboxSelection: true,
        comparator: customComparator,
      },
      {
        headerName: "Posting Status",
        field: "posting_status",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Item Code",
        field: "item_code",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Item Name",
        field: "item_name",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Order Quantity",
        field: "qty",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "JO Quantity",
        field: "jo_qty",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "UOM",
        field: "uom",
        width: 250,
        comparator: customComparator,
      },

    ];
  }

  ngOnInit() {
    this.gridOptions={};

    this.subs.sink = this.store
      .select(InternalJobOrderSelectors.selectEntity)
      .subscribe((joborderdata) => {
        this.jobOrderHdrGuid = joborderdata.bl_mrp_job_order_hdr.guid;
      });
  }


  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.store.dispatch(InternalJobOrderActions.loadGenDocLinkInit({ request: grid.request }));
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;
        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);
        this.pagination.conditionalCriteria = [
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
          {
            columnName: "job_order_guid",
            operator: "=",
            value: this.jobOrderHdrGuid,
          },
          {
            columnName: 'guids', operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          },
        ];
        let totalrec = 0;
        this.mrpJobOrderGenDocLinkService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {
            totalrec = resolved.totalRecords;
            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                this.soService.getByGuid(itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_hdr_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid ?
                this.genericDocLineService.getByGuid(itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),
                ).pipe(
                  map(([b_a,b_b]) => {
                    let obj = {"guid":'', "doc_no": '',"posting_status":'', "item_name": '', "item_code": '', "qty": '', 'uom':'' , 'salesOrderGuid':'','customerName':'','track_delivery_time_estimated':'','genDocLineGuid':'','bin':'', 'jo_qty': null};
                    obj.guid = itemperm.bl_mrp_job_order_generic_doc_link.guid.toString();
                    obj.salesOrderGuid = b_a.data.bl_fi_generic_doc_hdr.guid;
                    obj.genDocLineGuid = itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid.toString();
                    obj.doc_no = b_a.data.bl_fi_generic_doc_hdr.server_doc_1;
                    obj.posting_status = b_a.data.bl_fi_generic_doc_hdr.posting_status;
                    obj.item_name = b_b ? b_b.data.bl_fi_generic_doc_line.item_name : '';
                    obj.item_code =b_b ? b_b.data.bl_fi_generic_doc_line.item_code : '';
                    obj.qty =b_b ? b_b.data.bl_fi_generic_doc_line.quantity_base : '';
                    obj.uom = b_b ? b_b.data.bl_fi_generic_doc_line.uom : '';
                    obj.customerName = b_a.data ? b_a.data.bl_fi_generic_doc_hdr.sales_entity_hdr_name : ''
                    obj.track_delivery_time_estimated = b_a.data ? b_a.data.bl_fi_generic_doc_hdr.track_delivery_time_estimated : ''
                    obj.bin = b_b.data ? b_b.data.bl_fi_generic_doc_line.bin_no : ''
                    obj.jo_qty = +itemperm.bl_mrp_job_order_generic_doc_link.ad_hoc_quantity;
                    // obj.guid = itemperm.bl_t2t_fi_item_to_tenant_link.guid.toString();
                    // obj.basic_type = b_a.data.bl_fi_mst_item_hdr.txn_type;
                    // obj.sub_item_type = b_a.data.bl_fi_mst_item_hdr.sub_item_type;
                    this.totalContainerQty = this.totalContainerQty + <number>itemperm.bl_mrp_job_order_generic_doc_link.ad_hoc_quantity;
                    return obj;
                  })
                )
            )
            );
            return iif(() => resolved.totalRecords > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              this.store.dispatch(InternalJobOrderActions.loadGenDocLinkSuccess({ totalRecords: totalrec }));
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
            this.store.dispatch(InternalJobOrderActions.loadGenDocLinkFailed({ error: err.message }));
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
      if (resolved) {
        this.gridApi.refreshServerSideStore({ purge: true });
        this.store.dispatch(InternalJobOrderActions.resetAgGrid());
      }
    });
  }

  quickSearch() {
    this.gridApi.setQuickFilter(this.searchValue);
  }

  onSearch() {
    let searchValue = this.search.value;
    let query = `SELECT link.guid as requiredGuid FROM bl_svc_issue_gendoc_link AS link INNER JOIN bl_fi_generic_doc_line AS line ON link.generic_doc_line_guid = line.guid INNER JOIN bl_fi_generic_doc_hdr AS hdr ON link.generic_doc_hdr_guid = hdr.guid WHERE ((line.item_name = '${searchValue}') OR (line.item_code = '${searchValue}') OR (hdr.server_doc_1 = '${searchValue}')) and link.hdr_guid = '${this.jobOrderHdrGuid}' and link.doc_type = 'INTERNAL_SALES_INVOICE' and line.server_doc_type ='INTERNAL_SALES_INVOICE'`;
    if (query) {
      const sql = {
        subquery: query,
        table: 'bl_svc_issue_gendoc_link'
      };
      this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({
        next: resolve => {
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }
      });
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }
  onRowClicked(entity) {
    this.store.dispatch(InternalJobOrderActions.selectLinkedSalesOrder({linkedSalesOrder :entity}));
    if (true) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateList: true,
      });
      this.viewColFacade.onNextAndReset(this.index, 11);
    }
  }
  onDeleteCall(guid){
    this.store.dispatch(InternalJobOrderActions.selectGenericDocLinkGuid({guid }));
    this.store.dispatch(InternalJobOrderActions.deleteGenericDocLinkInit({ guid }));
    setTimeout(() => {
      this.setGridData()
    }, 1000);
  }
  onDelete(){
    this.gridApi.getSelectedRows().forEach(row => {
      setTimeout(() => {
        this.onDeleteCall(row.guid);
      }, 1000);
    })
  }
  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
    });
    this.viewColFacade.onNextAndReset(this.index, 10);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  callRefreshAfterMillis(params, millis, gridApi) {
    setTimeout(function () {
      gridApi.refreshCells(params);
    }, millis);
  }

  saveColumnStateToLocal(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.viewModelStore.dispatch(Column1ViewModelActions.setGenDocListing_State({genDocListingState:serializedColumnState}))
  }
  ngAfterViewInit() {
    // Listen for column movement and debounce the action dispatch
    this.subs.add(
      this.columnMoveSubject
        .pipe(debounceTime(this.debounceTimeMs))
        .subscribe(() => {
          this.saveColumnStateToLocal(this.gridOptions.columnApi);
          this.saveColumnStateToBackend(this.gridOptions.columnApi);
        })
    );

    this.subs.sink = this.viewModelStore
      .select(Column1ViewSelectors.selectGenDocListing_State)
      .subscribe((data) => {
        if (!data) {
          this.subs.sink = this.sessionStore
            .select(SessionSelectors.selectPersonalSettings)
            .subscribe((data) => {
              if (data.genDocListingState) {
                console.log("data",data.genDocListingState);
                this.viewModelStore.dispatch(
                  Column1ViewModelActions.setGenDocListing_State({
                    genDocListingState: data.genDocListingState,
                  })
                );
              }
            });
        }
      });

    setTimeout(()=>{
      let serializedColumnState;
      this.subs.sink = this.viewModelStore.select(Column1ViewSelectors.selectGenDocListing_State).subscribe(data=>{
        if(data){
          serializedColumnState=data;
        }
      })
      if (serializedColumnState) {
        const newColumnState = JSON.parse(serializedColumnState);
        const currentColumnState = this.gridOptions.columnApi.getColumnState();
        const currentColumnIds = new Set(currentColumnState.map(column => column.colId));

        const hiddenColumns = {};
          currentColumnState.forEach(column => {
            if (column.hide) {
              hiddenColumns[column.colId] = true;
            }
          });
        const filteredNewColumnState = newColumnState.filter(column => currentColumnIds.has(column.colId));
        const combinedColumnState = filteredNewColumnState.map(column => {
            if (hiddenColumns[column.colId] !== undefined) {
              column.hide = hiddenColumns[column.colId];
            }
            return column;
          });
          this.gridOptions.columnApi.applyColumnState({
            state: combinedColumnState,
            applyOrder: true, // Set this to true to forcefully apply the state
          });
          console.log('Column State Applied:', combinedColumnState);
      }

    },2000);



  }

  // Define a function to save column state to the backend
  saveColumnStateToBackend(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.sessionStore.dispatch(SessionActions.saveColumnStateInit({ settings: {genDocListingState:serializedColumnState} }));
   }
  ngAfterViewChecked() {
    // Listen for column visibility changes
    this.gridOptions.api.addEventListener("columnVisible", (event) => {
      this.columnMoveSubject.next();
    });
    // Listen for column movement
    this.gridOptions.api.addEventListener("columnMoved", (event) => {
      this.columnMoveSubject.next();
    });
  }
}
