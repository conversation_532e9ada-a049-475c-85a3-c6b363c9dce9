<div class="view-col-table no-tab" fxLayout="column">
  <mat-card-title class="column-title">
    <div fxLayout="row" fxLayoutAlign="space-between end">
      <div>
        <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async"
        (click)="onReturn()">
          <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
        </button>
        <span>
          Add Attachment
        </span>
      </div>
      <button mat-raised-button color="primary" type="button" [disabled]="!(files$ | async).length" (click)="onAdd()">ADD</button>
    </div>
  </mat-card-title>
  <div class="main-container">
    <div appDragDrop (dropEvent)="onChange($event.dataTransfer.files)" class="drop-zone">
      <input hidden #input type="file" multiple (change)="onChange($event.target.files)">
      <h3>
        Drag and drop your files anywhere or
      </h3>
      <label for="input" (click)="input.click()">Upload File(s)</label>
    </div>
  </div>
  <div class="grid-container">
    <div *ngFor="let f of files$ | async" class="img-container">
      <button style="position: absolute;" mat-icon-button color="warn" type="button" (click)="onDeleteFile(f)"><mat-icon>delete</mat-icon></button>
      <ng-container *ngIf="f.fileSRC === 'icon'; else preview">
        <mat-icon style="transform: translate(0px, 30px);" class="file-preview">description</mat-icon>
      </ng-container>
      <ng-template #preview>
        <img class="file-preview" [src]="f.fileSRC">
      </ng-template>
      <p class="file-name">{{f.fileAttributes.fileName}}</p>
    </div>
  </div>

</div>
