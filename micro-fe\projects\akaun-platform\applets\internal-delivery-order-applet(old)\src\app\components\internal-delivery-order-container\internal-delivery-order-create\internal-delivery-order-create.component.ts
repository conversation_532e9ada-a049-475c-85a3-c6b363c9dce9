import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { of } from 'rxjs';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { ShippingAddress, ShippingInfo } from '../../../models/internal-delivery-order.model';
import { HDRActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDRSelectors, PNSSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { InternalDeliveryOrderCreateAccountComponent } from './internal-delivery-order-create-account/internal-delivery-order-create-account.component';
import { InternalDeliveryOrderCreateDepartmentComponent } from './internal-delivery-order-create-department/internal-delivery-order-create-department.component';
import { InternalDeliveryOrderCreateLineItemsComponent } from './internal-delivery-order-create-line-items/internal-delivery-order-create-line-items.component';
import { InternalDeliveryOrderCreateMainComponent } from './internal-delivery-order-create-main/internal-delivery-order-create-main.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateReturn: boolean;
  deactivateEntity: boolean;
  deactivateSalesAgent: boolean;
  deactivateShippingInfo: boolean;
  deactivateBillingInfo: boolean;
  deactivateLineItem: boolean;
  deactivateSettlement: boolean;
  deactivateContactPerson: boolean;
  selectedIndex: number;
  childSelectedIndex: number;
  customFields: {
    label: string,
    formControl: string,
    type: string,
    readonly: boolean
  }[];
}

@Component({
  selector: 'app-internal-delivery-order-create',
  templateUrl: './internal-delivery-order-create.component.html',
  styleUrls: ['./internal-delivery-order-create.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalDeliveryOrderCreateComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Internal Delivery Order Create';
  protected index = 3;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deactivateEntity$ = this.componentStore.select(state => state.deactivateEntity);
  readonly deactivateSalesAgent$ = this.componentStore.select(state => state.deactivateSalesAgent);
  readonly deactivateShippingInfo$ = this.componentStore.select(state => state.deactivateShippingInfo);
  readonly deactivateBillingInfo$ = this.componentStore.select(state => state.deactivateBillingInfo);
  readonly deactivateLineItem$ = this.componentStore.select(state => state.deactivateLineItem);
  readonly deactivateSettlement$ = this.componentStore.select(state => state.deactivateSettlement);
  readonly deactivateContactPerson$ = this.componentStore.select(state => state.deactivateContactPerson);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly childSelectedIndex$ = this.componentStore.select(state => state.childSelectedIndex);
  readonly customFields$ = this.componentStore.select(state => state.customFields);

  toggleColumn$ = this.viewColFacade.toggleColumn$;

  prevIndex: number;
  protected prevLocalState: any;
  

  draft$ = this.store.select(InternalDeliveryOrderSelectors.selectDraft);
  lineItems$ = this.draftStore.select(PNSSelectors.selectAll)
  hdrDraft$ = this.draftStore.select(HDRSelectors.selectHdr);

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(InternalDeliveryOrderCreateAccountComponent) account: InternalDeliveryOrderCreateAccountComponent;
  @ViewChild(InternalDeliveryOrderCreateMainComponent) main: InternalDeliveryOrderCreateMainComponent;
  @ViewChild(InternalDeliveryOrderCreateLineItemsComponent) lines: InternalDeliveryOrderCreateLineItemsComponent;
  @ViewChild(InternalDeliveryOrderCreateDepartmentComponent) department: InternalDeliveryOrderCreateDepartmentComponent; // TODO: maybe change into its own child component instead of sharing

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected store: Store<InternalDeliveryOrderStates>,
    protected draftStore: Store<DraftStates>,
    protected readonly componentStore: ComponentStore<LocalState>
    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

  }

  onEntity() {
    if (!this.localState.deactivateEntity) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: true,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 6);
    }
  }

  onShippingInfo() {
    if (!this.localState.deactivateShippingInfo) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: true,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 7);
    }
  }

  onBillingInfo() {
    if (!this.localState.deactivateBillingInfo) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: true,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 8);
    }
  }

  onContactPerson() {
    if (!this.localState.deactivateContactPerson) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: true,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 9);
    }
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onNextAdd() {
    if (!this.localState.deactivateAdd) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: true,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 4);
    }
  }

  onReset() {
    this.store.dispatch(InternalDeliveryOrderActions.resetDraft());
    this.store.dispatch(InternalDeliveryOrderActions.resetDeliveryOrder());
  }

  onSave() {
    this.store.dispatch(InternalDeliveryOrderActions.createDeliveryOrderInit());
  }

  disableButton() {
    return this.main?.form?.invalid || !this.lines?.rowData.length || this.account?.invalid;
  }

  onLineItem(line: bl_fi_generic_doc_line_RowClass) {
    this.store.dispatch(InternalDeliveryOrderActions.selectLineItemInit({lineItem: line}));
    if (!this.localState.deactivateLineItem) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: true
      });
      this.viewColFacade.onNextAndReset(this.index, 2);
    }
  }

  onUpdateMain(form) {
    console.log("onUpdateMain", form);
    // if (form.value.branch != '' || form.value.branch != null){
    //   form.patchValue({company: form.value.branch.bl_fi_mst_branch.comp_guid});
    // }
    this.store.dispatch(InternalDeliveryOrderActions.updateMain({model: form}));
  }

  onUpdateDepartment(form) {
    this.store.dispatch(InternalDeliveryOrderActions.updateDepartment({model: form}));
  }

  onUpdateCustomFields(form) {
    this.store.dispatch(InternalDeliveryOrderActions.updateCustomFields({model: form}));
  }

  onNextSettlement() {
    if (!this.localState.deactivateSettlement) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: true,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 11);
    }
  }

  goToSelectShipping() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 7);
  }

  onUpdateShippingInfo(form: ShippingInfo) {
    this.draftStore.dispatch(HDRActions.updateShippingInfo({ form }));
  }

  onUpdateShippingAddress(form: ShippingAddress) {
    this.draftStore.dispatch(HDRActions.updateShippingAddress({ form }));
  }

  onChangeCustomFields(e) {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      customFields: e
    });
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
        childSelectedIndex: this.account.matTab.selectedIndex
      });
    }
    this.subs.unsubscribe();
  }

}
