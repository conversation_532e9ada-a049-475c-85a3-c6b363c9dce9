import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { ComponentStore } from "@ngrx/component-store";
import { Store } from "@ngrx/store";
import {
  ApiResponseModel,
  FinancialItemContainerModel,
  FinancialItemService,
  InternalInboundStockTransferService,
  InternalPurchaseGoodsReceivedNotes,
  Pagination,
  SubQueryService,
} from "blg-akaun-ts-lib";
import {
  pageFiltering,
  pageSorting,
} from "projects/shared-utilities/listing.utils";
import { SearchQueryModel } from "projects/shared-utilities/models/query.model";
import { PaginationComponent } from "projects/shared-utilities/utilities/pagination/pagination.component";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { forkJoin, iif, Observable, of, Subject } from "rxjs";
import { filter, map, mergeMap, switchMap, tap } from "rxjs/operators";
import { SubSink } from "subsink2";
import { ViewColumnFacade } from "../../../../facades/view-column.facade";
import { internalDeliveryOrderSearchModel } from "../../../../models/advanced-search-models/internal-delivery-order.model";
//import { ItemActions } from "../../../../state-controllers/draft-controller/store/actions";
import { DraftStates } from "../../../../state-controllers/draft-controller/store/states";
import { InternalDeliveryOrderActions } from "../../../../state-controllers/internal-delivery-order-controller/store/actions";
import { InternalDeliveryOrderStates } from "../../../../state-controllers/internal-delivery-order-controller/store/states";

interface LocalState {
  rowIndexList: any;
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRowIndex: number;
}

@Component({
  selector: 'app-internal-delivery-order-create-line-item-ST-GRN',
  templateUrl: './internal-delivery-order-create-line-item-ST-GRN.component.html',
  styleUrls: ['./internal-delivery-order-create-line-item-ST-GRN.component.css']
})

export class InternalDeliveryOrderCreateLineItemSTGRNComponent
  extends ViewColumnComponent
  implements OnInit
{
  @Input() localState: any;
  @Output() item = new EventEmitter<FinancialItemContainerModel>();
  // protected localState: LocalState;
  protected subs = new SubSink();
  // readonly localState$ = this.viewColFacade.selectLocalState(this.index);

  prevIndex: number;
  protected prevLocalState: any;

  // Need to change
  searchModel = internalDeliveryOrderSearchModel;

  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    floatingFilter: true,
  };

  gridApi;

  columnsDefs = [
    // { headerName: "No.", field: "no" },
    {
      headerName: "GRN No",
      field: "grn_no",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "SERVER DOC TYPE",
      field: "server_doc_type_line",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "TXN DATE",
      field: "txn_date",
      cellStyle: () => ({ "text-align": "middle" }),
    },
    {
      headerName: "Item Code",
      field: "item_code",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Item Name",
      field: "item_name",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "UOM",
      field: "uom",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Unit Price",
      field: "unit_price",
      cellStyle: () => ({ "text-align": "right" }),
    },
    { headerName: "Base Qty", 
      field: "order_qty", 
      // type: "numericColumn",
      cellStyle: () => ({ "text-align": "middle" }) 
    },
    { headerName: "Balance Qty", 
      field: "open_qty",
      cellStyle: () => ({ "text-align": "middle" }) 
    },
    {
      headerName: "Knock Off Qty",
      field: "knock_off_qty",
      cellStyle: () => ({ "text-align": "middle" }),
      editable: true
    },
    /*
    {
      headerName: "Tracking ID",
      field: "tracking_id",
      cellStyle: () => ({ "text-align": "left" }),
    },
    */
    
  ];

  SQLGuids: string[] = null;
  pagination = new Pagination();
  snapshot: any;
  totalCount: number;
  totalRecords$: Subject<number> = new Subject<number>();
  emptyGrid: boolean;
  criteriaList: { columnName: string; operator: string; value: string; }[];
 

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  arraySize = [];
  arrayPromise = [];
  arrayData = [];

  constructor(
    private sqlService: SubQueryService,
    private stgrnService: InternalInboundStockTransferService,
    private viewColFacade: ViewColumnFacade,
    private subQueryService: SubQueryService,
    private componentStore: ComponentStore<LocalState>,
    private store: Store<InternalDeliveryOrderStates>,
    private readonly draftStore: Store<DraftStates>
  ) {
    super();
  }

  ngOnInit() {
    // this.subs.sink = this.localState$.subscribe((a) => {
    //   this.localState = a;
    //   this.componentStore.setState(a);
    // });
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  searchQuery(query: string) {

    const query$ = this.subQueryService
      .post({ 'subquery': query, 'table': 'bl_fi_generic_doc_line' }, AppConfig.apiVisa)
      .pipe(
        switchMap(resp => of(resp))
      );
    this.subs.sink = query$.pipe(
      tap(a =>
        console.log(a, 'this is a')
      ), filter((resp: ApiResponseModel<any>) => resp.data.length > 0)
    ).subscribe(resp => {
      const criteria = [
        // combine all the guids together separated by commas
        { columnName: 'guids', operator: '=', value: resp.data.join(',') }
      ];
      this.criteriaList = criteria;
      this.setGridData(criteria);
      this.setTotalRecordCount(resp.data.length);
      this.emptyGrid = false;
    });
    this.subs.sink = query$.pipe(
      filter((resp: ApiResponseModel<any>) => resp.data.length === 0))
      .subscribe(resp => {
        this.criteriaList = [];
        this.emptyGrid = true;
        this.setTotalRecordCount(0);
        this.clear();
      });
  }

clear() {
  const dataSource = {
    getRows(params: any) {
      params.successCallback([], 0);
    }
  };
  this.gridApi.setServerSideDatasource(dataSource);
}

setTotalRecordCount(totalCount: number) {
  this.totalRecords$.next(totalCount);
}
    
setGridData(criteria?: any) {
  this.snapshot = null;
  const apiVisa = AppConfig.apiVisa;

  const datasource = {
    getRows: grid => {
      const sortModel = grid.request.sortModel;
      const sortCriteria = [];
      if (sortModel.length > 0) {
        sortModel.forEach(element => {
          const columnName = element.colId.split(".")[1]
          sortCriteria.push({ columnName: 'orderBy', value: columnName })
        });
        sortCriteria.push({ columnName: 'order', value: sortModel[0].sort.toUpperCase() })
      }

      const limit = grid.request.endRow - grid.request.startRow;
      const pagination = new Pagination(0, limit, criteria, sortCriteria, this.snapshot);

      this.subs.sink = this.stgrnService.getGenericDocHdrLineLinkByCriteria(
        pagination,
        apiVisa).subscribe(
          (res) => {
            if (res.data.length > 0) {
              this.snapshot = res.data[res.data.length - 1].bl_fi_generic_doc_hdr.guid.toString();
            }

            let newArrData = new Array;
            newArrData = [...res.data].map((doc) => {
              //console.log("original container", doc);
              let aggContra = 0;
              (<any>doc).bl_fi_generic_doc_links.forEach((link) => {
                if (
                  link.guid_doc_1_hdr ===
                    doc.bl_fi_generic_doc_hdr.guid &&
                  link.guid_doc_1_line ===
                    doc.bl_fi_generic_doc_line.guid &&
                  link.txn_type === "IIST_IODO"
                ) {
                  let contra =
                    Number(link.quantity_signum) *
                    Number(link.quantity_contra);
                  aggContra += contra;
                }
              });
              let newObj = null;  
              newObj = {
                tracking_id: doc.bl_fi_generic_doc_line.tracking_id,
                hdr_guid: doc.bl_fi_generic_doc_hdr.guid,
                server_doc_type_hdr: doc.bl_fi_generic_doc_hdr.server_doc_type,
                line_guid: doc.bl_fi_generic_doc_line.guid,
                server_doc_type_line: doc.bl_fi_generic_doc_line.server_doc_type,
                doc_number: doc.bl_fi_generic_doc_hdr.server_doc_1,
                grn_no: doc.bl_fi_generic_doc_hdr.server_doc_1,
                item_guid: doc.bl_fi_generic_doc_line.item_guid,
                item_code: doc.bl_fi_generic_doc_line.item_code,
                item_name: doc.bl_fi_generic_doc_line.item_name,
                item_type: 'inbound',
                order_qty: doc.bl_fi_generic_doc_line.quantity_base,
                open_qty: Number(doc.bl_fi_generic_doc_line.quantity_base) + aggContra,
                unit_price: Number(doc.bl_fi_generic_doc_line.unit_price_txn),
                uom: doc.bl_fi_generic_doc_line.uom,
                status: "OPEN",
                txn_type: "IIST_IODO",
                item_txn_type: doc.bl_fi_generic_doc_line.item_txn_type,
                knock_off_qty: Number(doc.bl_fi_generic_doc_line.quantity_base) + aggContra,
                txn_date: doc.bl_fi_generic_doc_line.date_txn? doc.bl_fi_generic_doc_line.date_txn.getDate : null

                // unit_price_std: doc.bl_fi_generic_doc_line.unit_price_std,
                // unit_price_txn: doc.bl_fi_generic_doc_line.unit_price_txn,
                // tax_gst_code: doc.bl_fi_generic_doc_line.tax_gst_code,
                // tax_gst_rate: doc.bl_fi_generic_doc_line.tax_gst_rate,
                // amount_tax_gst: doc.bl_fi_generic_doc_line.amount_tax_gst,
                // tax_wht_code: doc.bl_fi_generic_doc_line.tax_wht_code,
                // tax_wht_rate: doc.bl_fi_generic_doc_line.tax_wht_rate,
                // amount_tax_wht: doc.bl_fi_generic_doc_line.amount_tax_wht,
                
              }
              // console.log("GenDocHdrLine", newObj);
              return newObj;
            })

            if (this.paginationComp.currentPage > this.paginationComp.totalPage.value) {
              this.paginationComp.firstPage()
            }

            // Calculate totalRecords if end reached.
            const start = grid.request.startRow;
            const end = grid.request.endRow;
            const totalRecords = newArrData.length < (end - start) ? start + newArrData.length : null;

            if (!this.totalCount && totalRecords) {
              this.totalCount = totalRecords;
              this.setTotalRecordCount(totalRecords);
            }

            grid.successCallback(newArrData, totalRecords);

            this.gridApi.forEachNode(a => {
              // if (a.data.item_guid === this.localState.selectedItem) {
              //   a.setSelected(true);
              // }
            });
          }, err => {
            console.log(err);
            grid.failCallback();

          })
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }  

  // setGridData() {
  //   const apiVisa = AppConfig.apiVisa;
  //   const datasource = {
  //     getRows: (grid) => {
  //       this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
  //       this.pagination.limit = grid.request.endRow - grid.request.startRow;
  //       this.pagination.conditionalCriteria = [
  //         { columnName: "line_txn_type", operator: "=", value: "PNS" },
  //         { columnName: "calcTotalRecords", operator: "=", value: "true" },
  //         { columnName: "orderBy", operator: "=", value: "updated_date" },
  //         { columnName: "order", operator: "=", value: "DESC" },
  //         {
  //           columnName: "line_guids",
  //           operator: "=",
  //           value: this.SQLGuids
  //             ? this.SQLGuids.slice(
  //                 grid.request.startRow,
  //                 grid.request.endRow
  //               ).toString()
  //             : "",
  //         },
  //       ];
  //       const filter = pageFiltering(grid.request.filterModel);
  //       const sortOn = pageSorting(grid.request.sortModel);
  //       this.subs.sink = this.grnService
  //         .getLinesByCriteria(this.pagination, apiVisa)
  //         .pipe(
  //           mergeMap((b) => {
  //             const source: Observable<any>[] = [];
  //             b.data.forEach((doc) =>
  //               source.push(
  //                 this.grnService
  //                   .getByGuid(
  //                     doc.bl_fi_generic_doc_hdr.guid.toString(),
  //                     apiVisa
  //                   )
  //                   .pipe(
  //                     map((b_a) => {
  //                       let aggContra = 0; // aggregate Contra [[[its doing it for all items in the header!!! FIX THIS]]]
  //                       b_a.data.bl_fi_generic_doc_link.forEach((link) => {
  //                         if (
  //                           link.guid_doc_1_hdr ===
  //                             doc.bl_fi_generic_doc_hdr.guid &&
  //                           link.guid_doc_1_line ===
  //                             doc.bl_fi_generic_doc_line[0].guid &&
  //                           link.txn_type === "IOST_IPGRN"
  //                         ) {
  //                           let contra =
  //                             Number(link.quantity_signum) *
  //                             Number(link.quantity_contra);
  //                           aggContra += contra;
  //                         }
  //                       });
  //                       const data = {
  //                         // Tracking id of data should be tracking id of line, not hdr
  //                         //tracking_id: doc.bl_fi_generic_doc_hdr.tracking_id,
  //                         tracking_id: doc.bl_fi_generic_doc_line[0].tracking_id,
  //                         hdr_guid: doc.bl_fi_generic_doc_hdr.guid,
  //                         server_doc_type_hdr:
  //                           doc.bl_fi_generic_doc_hdr.server_doc_type,
  //                         line_guid: doc.bl_fi_generic_doc_line[0].guid,
  //                         server_doc_type_line:
  //                           doc.bl_fi_generic_doc_line[0].server_doc_type,
  //                         doc_number: doc.bl_fi_generic_doc_hdr.server_doc_1,
  //                         grn_no: doc.bl_fi_generic_doc_hdr.server_doc_1,
  //                         item_guid: doc.bl_fi_generic_doc_line[0].item_guid,
  //                         item_code: doc.bl_fi_generic_doc_line[0].item_code,
  //                         item_name: doc.bl_fi_generic_doc_line[0].item_name,
  //                         item_type: "grn",
  //                         order_qty:
  //                           doc.bl_fi_generic_doc_line[0].quantity_base,
  //                         open_qty:
  //                           Number(
  //                             doc.bl_fi_generic_doc_line[0].quantity_base
  //                           ) + aggContra,
  //                         unit_price: null,
  //                         uom: doc.bl_fi_generic_doc_line[0].uom,
  //                         status: "OPEN",
  //                         txn_type: "IOST_IPGRN",
  //                         item_txn_type:
  //                           doc.bl_fi_generic_doc_line[0].item_txn_type,
  //                       };
  //                       //console.log(data);
  //                       return data;
  //                     })
  //                   )
  //               )
  //             );
  //             return iif(
  //               () => b.data.length > 0,
  //               forkJoin(source).pipe(
  //                 map((b_inner) => {
  //                   b.data = <any>b_inner;
  //                   return b;
  //                 })
  //               ),
  //               of(b)
  //             );
  //           })
  //         )
  //         .subscribe(
  //           (resolved) => {
  //             const data = sortOn(resolved.data).filter((entity) =>
  //               filter.by(entity)
  //             );
  //             const totalRecords = filter.isFiltering
  //               ? this.SQLGuids
  //                 ? this.SQLGuids.length
  //                 : resolved.totalRecords
  //               : data.length;
  //             grid.success({
  //               rowData: data,
  //               rowCount: totalRecords,
  //             });
  //           },
  //           (err) => {
  //             console.error(err);
  //             grid.fail();
  //           }
  //         );
  //     },
  //   };
  //   this.gridApi.setServerSideDatasource(datasource);
  // }

  // onSearch(e: SearchQueryModel) {
  //   if (!e.isEmpty) {
  //     const sql = {
  //       subquery: e.queryString,
  //       table: e.table,
  //     };
  //     this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe({
  //       next: (resolve) => {
  //         this.SQLGuids = resolve.data;
  //         this.paginationComp.firstPage();
  //         this.gridApi.refreshServerSideStore();
  //       },
  //     });
  //   } else {
  //     this.SQLGuids = null;
  //     this.paginationComp.firstPage();
  //     this.gridApi.refreshServerSideStore();
  //   }
  // }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      this.searchQuery(e.queryString);
      console.log('searchQuery', e);

    } else {
      this.criteriaList = [];
      this.emptyGrid = false;
      this.setGridData();
      this.setTotalRecordCount(this.totalCount);
    }
  }

  
  onRowClicked(item) {
    console.log("selected grn item: ", item);
    // if (item) {
    //   this.draftStore.dispatch(ItemActions.resetAllItemsState());
    this.store.dispatch(
      InternalDeliveryOrderActions.selectItem({ entity: item })
    );
    this.viewColFacade.updateInstance(4, {
      ...this.localState,
      deactivateAdd: true,
      deactivateList: false,
      deactivateReturn: true,
    });
    this.viewColFacade.onNextAndReset(4, 12);
    // }
  }


  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
