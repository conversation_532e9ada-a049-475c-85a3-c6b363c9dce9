import { NgxMatDateAdapter, NgxMatDateFormats, NgxMatDatetimePickerModule, NgxMatNativeDateModule, NGX_MAT_DATE_FORMATS } from '@angular-material-components/datetime-picker';
import { NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular-material-components/moment-adapter';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { AgGridModule } from 'ag-grid-angular';
import { DropDownModule } from 'blg-akaun-ng-lib';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { AppletUtilitiesModule } from '../../feature-modules/applet-utilities.module';
import { CustomNgxDatetimeAdapter } from '../../models/custom-ngx-date-time-adapter';
import { InternalJobOrderEffects } from '../../state-controllers/internal-job-order-controller/store/effects';
import { reducers } from '../../state-controllers/internal-job-order-controller/store/reducers';
import { InternalJobOrderFeatureKey } from '../../state-controllers/internal-job-order-controller/store/reducers/internal-job-order.reducers';
import { SlideRendererComponent } from '../utilities/slide-renderer/slide-renderer.component';
import { InternalJobOrderAddAttachmentsComponent } from './internal-job-order-add-attachments/internal-job-order-add-attachments.component';
import { InternalJobOrderAddLineItemComponent } from './internal-job-order-add-line-item/internal-job-order-add-line-item.component';
import { InternalJobOrderContainerComponent } from './internal-job-order-container.component';
import { InternalJobOrderCreateLineItemComponent } from './internal-job-order-create-line-item/internal-job-order-create-line-item.component';
import { LineJobsheetItemComponent } from './internal-job-order-create-line-item/line-jobsheet-item/line-jobsheet-item.component';
import { LineQuotationItemComponent } from './internal-job-order-create-line-item/line-quotation-item/line-quotation-item.component';
import { LineSearchItemComponent } from './internal-job-order-create-line-item/line-search-item/line-search-item.component';
import { AccountBillingAddressComponent } from './internal-job-order-create/internal-job-order-create-account/account-billing-address/account-billing-address.component';
import { AccountEntityDetailsComponent } from './internal-job-order-create/internal-job-order-create-account/account-entity-details/account-entity-details.component';
import { AccountShippingAddressComponent } from './internal-job-order-create/internal-job-order-create-account/account-shipping-address/account-shipping-address.component';
import { InternalJobOrderCreateLineItemsComponent } from './internal-job-order-create/internal-job-order-create-line-items/internal-job-order-create-line-items.component';
import { InternalJobOrderCreateComponent } from './internal-job-order-create/internal-job-order-create.component';
import { EditIssueActivityComponent } from './internal-job-order-edit-issue/edit-issue-activity/edit-issue-activity.component';
import { EditIssueAttachmentComponent } from './internal-job-order-edit-issue/edit-issue-attachment/edit-issue-attachment.component';
import { EditIssueCommentComponent } from './internal-job-order-edit-issue/edit-issue-comment/edit-issue-comment.component';
import { EditIssueDetailsComponent } from './internal-job-order-edit-issue/edit-issue-details/edit-issue-details.component';
import { EditIssueLinkedIssuesComponent } from './internal-job-order-edit-issue/edit-issue-linked-issues/edit-issue-linked-issues.component';
import { EditIssueMainDetailsComponent } from './internal-job-order-edit-issue/edit-issue-main-details/edit-issue-main-details.component';
import { EditIssueSubstasksComponent } from './internal-job-order-edit-issue/edit-issue-substasks/edit-issue-substasks.component';
import { EditIssueWorklogComponent } from './internal-job-order-edit-issue/edit-issue-worklog/edit-issue-worklog.component';
import { InternalJobOrderEditIssueComponent } from './internal-job-order-edit-issue/internal-job-order-edit-issue.component';
import { InternalJobOrderEditLineItemComponent } from './internal-job-order-edit-line-item/internal-job-order-edit-line-item.component';
import { InternalJobOrderListingComponent } from './internal-job-order-listing/internal-job-order-listing.component';
import { InternalJobOrderSelectCustomerComponent } from './internal-job-order-select-customer/internal-job-order-select-customer.component';
import { InternalJobOrderViewComponent } from './internal-job-order-view/internal-job-order-view.component';
import { EditLineItemItemDetailsComponent } from './internal-job-order-edit-line-item/edit-line-item-item-details/edit-line-item-item-details.component';
import { InternalJobOrderCreateMainComponent } from './internal-job-order-create/internal-job-order-create-main/internal-job-order-create-main.component';
import { InternalJobOrderCreateAccountComponent } from './internal-job-order-create/internal-job-order-create-account/internal-job-order-create-account.component';
import { InternalJobOrderCreateDepartmentComponent } from './internal-job-order-create/internal-job-order-create-department/internal-job-order-create-department.component';
import { InternalJobOrderCreateAttachmentsComponent } from './internal-job-order-create/internal-job-order-create-attachments/internal-job-order-create-attachments.component';
import { InternalJobOrderAddLineItemMainComponent } from './internal-job-order-add-line-item/internal-job-order-item-details/internal-job-order-add-line-item-main/internal-job-order-add-line-item-main.component';
import { InternalJobOrderEditLineItemDepartmentComponent } from './internal-job-order-edit-line-item/edit-line-item-item-details/internal-job-order-edit-line-item-department/internal-job-order-edit-line-item-department.component';
import { InternalJobOrderEditLineItemMainComponent } from './internal-job-order-edit-line-item/edit-line-item-item-details/internal-job-order-edit-line-item-main/internal-job-order-edit-line-item-main.component';
import { InternalJobOrderEditRelatedDocumentsComponent } from './internal-job-order-edit-line-item/edit-line-item-item-details/internal-job-order-edit-related-documents/internal-job-order-edit-related-documents.component';
import { InternalJobOrderViewExportComponent } from './internal-job-order-view/internal-job-order-view-export/internal-job-order-view-export.component';
import { LinePreviousSalesOrderComponent } from './internal-job-order-create-line-item/line-previous-job-order/line-previous-sales-order.component';
import { AddLineItemItemDetailsComponent } from './internal-job-order-add-line-item/internal-job-order-item-details/add-line-item-item-details.component';
import { AddLineItemIssueLinkComponent } from './internal-job-order-add-line-item/internal-job-order-item-issue-link/add-line-item-issue-link.component';
import { InternalJobOrderAddRelatedDocumentsComponent } from './internal-job-order-add-related-documents/internal-job-order-add-related-documents.component';
import { InvoiceServiceNoteLinkListingComponent } from './internal-job-order-view/gendoc-link/gendoc-link-listing/gendoc-link-listing.component';
import { SalesOrderLinkAddComponent } from './internal-job-order-view/gendoc-link/gendoc-link-add/gendoc-link-add.component';
import { ProcessInstanceListingComponent } from './internal-job-order-view/process-instance/process-instance-listing/process-instance-listing.component';
import { ProcessInstanceEditComponent } from './internal-job-order-view/process-instance/process-instance-edit/process-instance-edit.component';
import { DetailsComponent } from './internal-job-order-view/process-instance/process-instance-edit/details/details.component';
import { ProcessTemplateListingComponent } from './internal-job-order-view/process-instance/process-instance-edit/process-template-listing/process-template-listing.component';
import { InputSummaryComponent } from './internal-job-order-view/process-instance/process-instance-edit/input-summary/input-summary.component';
import { OutputSummaryComponent } from './internal-job-order-view/process-instance/process-instance-edit/output-summary/output-summary.component';
import { ProcessInstanceCreateComponent } from './internal-job-order-view/process-instance/process-instance-create/process-instance-create.component';
import { SalesOrderContainerComponent } from '../sales-order-container/sales-order-container.component';

const CUSTOM_DATE_FORMATS: NgxMatDateFormats  = {
  parse: {
    dateInput: 'l, LTS'
  },
  display: {
    dateInput: 'YYYY-MM-DD HH:mm:ss',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  }
};

@NgModule({
  declarations: [
    InternalJobOrderContainerComponent,
    InternalJobOrderListingComponent,
    InternalJobOrderCreateComponent,
    InternalJobOrderCreateMainComponent,
    InternalJobOrderCreateAccountComponent,
    InternalJobOrderCreateLineItemsComponent,
    InternalJobOrderCreateLineItemComponent,
    InternalJobOrderCreateDepartmentComponent,
    InternalJobOrderCreateAttachmentsComponent,
    AccountEntityDetailsComponent,
    AccountBillingAddressComponent,
    AccountShippingAddressComponent,
    InternalJobOrderSelectCustomerComponent,
    InternalJobOrderAddLineItemComponent,
    InternalJobOrderAddLineItemMainComponent,
    InternalJobOrderAddAttachmentsComponent,
    InternalJobOrderAddRelatedDocumentsComponent,
    InternalJobOrderEditLineItemComponent,
    InternalJobOrderEditLineItemDepartmentComponent,
    InternalJobOrderEditLineItemMainComponent,
    InternalJobOrderEditRelatedDocumentsComponent,
    InternalJobOrderEditIssueComponent,
    InternalJobOrderViewComponent,
    InternalJobOrderViewExportComponent,
    LineSearchItemComponent,
    LineJobsheetItemComponent,
    LineQuotationItemComponent,
    LinePreviousSalesOrderComponent,
    AddLineItemItemDetailsComponent,
    AddLineItemIssueLinkComponent,
    EditIssueDetailsComponent,
    EditIssueAttachmentComponent,
    EditIssueCommentComponent,
    EditIssueSubstasksComponent,
    EditIssueLinkedIssuesComponent,
    EditIssueWorklogComponent,
    EditIssueActivityComponent,
    EditIssueMainDetailsComponent,
    EditLineItemItemDetailsComponent,
    InvoiceServiceNoteLinkListingComponent,
    SalesOrderLinkAddComponent,
    ProcessInstanceListingComponent,
    ProcessInstanceEditComponent,
    DetailsComponent,
    ProcessTemplateListingComponent,
    InputSummaryComponent,
    OutputSummaryComponent,
    ProcessInstanceCreateComponent
  ],
  imports: [
    CommonModule,
    UtilitiesModule,
    DropDownModule,
    AgGridModule.withComponents([SlideRendererComponent]),
    StoreModule.forFeature(InternalJobOrderFeatureKey, reducers.jobOrder),
    EffectsModule.forFeature([InternalJobOrderEffects]),
    NgxMatDatetimePickerModule,
    NgxMatNativeDateModule,
    AppletUtilitiesModule
  ],
  providers: [
    {
      provide: NgxMatDateAdapter,
      useClass: CustomNgxDatetimeAdapter,
      deps: [MAT_DATE_LOCALE, NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: NGX_MAT_DATE_FORMATS, useValue: CUSTOM_DATE_FORMATS },
  ],
  exports: [
    AddLineItemItemDetailsComponent,
    AddLineItemIssueLinkComponent,
    EditLineItemItemDetailsComponent,
    InternalJobOrderCreateDepartmentComponent
  ]
})
export class InternalJobOrderModule { }
