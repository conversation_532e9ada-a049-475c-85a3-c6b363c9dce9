<mat-card-title class="column-title">
    <div fxLayout="row" fxLayoutAlign="space-between end">
      <div>
        <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
          [disabled]="deactivateReturn$ | async" (click)="onReturn()">
          <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
            alt="add" width="40px" height="40px">
        </button>
        <span>
          Issue Job Order
        </span>
      </div>
      <button mat-raised-button color="primary" type="button" [disabled]="disableSave()" (click)="onIssue()">ISSUE</button>
    </div>
  </mat-card-title>
  <mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
    <mat-tab label="Main">
        <app-issue-job-order-main [hdr$]="hdr$" [appletSettings$]="appletSettings$" [userProfile$]="userProfile$" [process$]="process$" [machines$]="machines$" (updateMain)="onUpdateMain($event)" (selectTemplate)="onSelectTemplate()"></app-issue-job-order-main>
    </mat-tab>
    <mat-tab label="Planned Output">
      <app-issue-job-order-planned-output [localState]="localState$ | async" [rowData]="plannedOutput$ | async" (next)="onAddOutput()"></app-issue-job-order-planned-output>
    </mat-tab>
    <mat-tab label="Planned Input">
      <app-issue-job-order-planned-input [localState]="localState$ | async" [rowData]="plannedInput$ | async" (next)="onAddInput()"></app-issue-job-order-planned-input>
    </mat-tab>
    <mat-tab label="Department">
      <app-internal-job-order-create-department [appletSettings$]="appletSettings$" [dept$]="hdr$" (updateDepartment)="onUpdateDepartment($event)"></app-internal-job-order-create-department>
    </mat-tab>
  </mat-tab-group>