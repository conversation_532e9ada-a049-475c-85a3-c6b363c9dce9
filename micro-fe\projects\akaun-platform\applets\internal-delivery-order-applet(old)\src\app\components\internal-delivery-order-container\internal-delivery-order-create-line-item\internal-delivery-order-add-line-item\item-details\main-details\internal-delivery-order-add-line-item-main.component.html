<form [formGroup]="form">
  <div fxLayout="row wrap" fxFlexAlign="center" class="view-col-forms">
    <div class="p-10" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex="100">
      <ng-container *ngIf="itemType" [ngSwitch]="itemType">
        <mat-form-field *ngSwitchCase="'jobsheet'" class="example-full-width" appearance="outline">
          <mat-label>Jobsheet No.</mat-label>
          <input matInput readonly placeholder="Jobsheet No." [formControl]="form.controls['orderNo']" type="text">
        </mat-form-field>
        <mat-form-field *ngSwitchCase="'salesOrder'" class="example-full-width" appearance="outline">
          <mat-label>Sales Order No.</mat-label>
          <input matInput readonly placeholder="Sales Order No." [formControl]="form.controls['orderNo']" type="text">
        </mat-form-field>
        <mat-form-field *ngSwitchCase="'salesQuotation'" class="example-full-width" appearance="outline">
          <mat-label>Sales Quotation No.</mat-label>
          <input matInput readonly placeholder="Sales Quotation No." [formControl]="form.controls['orderNo']" type="text">
        </mat-form-field>
        <mat-form-field *ngSwitchCase="'deliveryOrder'" class="example-full-width" appearance="outline">
          <mat-label>Delivery Order No.</mat-label>
          <input matInput readonly placeholder="Delivery Order No." [formControl]="form.controls['orderNo']" type="text">
        </mat-form-field>
      </ng-container>

      <mat-form-field class="example-full-width" appearance="outline">
        <mat-label>Item Code</mat-label>
        <input matInput readonly placeholder="Item Code" [formControl]="form.controls['itemCode']" type="text">
      </mat-form-field>
      <mat-form-field class="example-full-width" appearance="outline">
        <mat-label>Item Name</mat-label>
        <input matInput placeholder="Item Name" [formControl]="form.controls['itemName']" type="text">
      </mat-form-field>

      <app-pricing-scheme-uom *ngIf="!appletSettings.HIDE_UNIT_PRICE_STD_PRICING_SCHEME || SHOW_UNIT_PRICE_STD_PRICING_SCHEME" [(uomGuid)]="form.controls['uomGuid']" [(pricingScheme)]="form.controls['pricingScheme']"
        [fiitem$]="item$" [pricing$]="pricing$" [unitPriceType]="'SALES'" (uomToBaseRatio)="onUOMSelected($event)"
        (unitPrice)="onPriceSelected($event)" [(isInclusiveTax)]="isInclusiveTax">
      </app-pricing-scheme-uom>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_PRICE_STD_EXCL_TAX || SHOW_UNIT_PRICE_STD_EXCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Unit Price STD (Exclusive of tax)</mat-label>
        <input matInput placeholder="Unit Price (Exclusive of tax)" [formControl]="form.controls['unitPriceStdWithoutTax']"
          type="number" step=".01" readonly (change)="nullHandler('unitPriceStdWithoutTax', $event.target.value);" >
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_PRICE_STD_INCL_TAX || SHOW_UNIT_PRICE_STD_INCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Unit Price STD (Inclusive of tax)</mat-label>
        <input matInput placeholder="Unit Price (Inclusive of tax)" [formControl]="form.controls['unitPriceStdWithTax']"
          type="number" step=".01" readonly (change)="nullHandler('unitPriceStdWithTax', $event.target.value);">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_DISCOUNT || SHOW_UNIT_DISCOUNT" class="example-full-width" appearance="outline">
        <mat-label>Unit Discount</mat-label>
        <input matInput placeholder="Unit Discount" [formControl]="form.controls['unitDiscount']" type="number"
          step=".01" (change)="nullHandler('unitDiscount', $event.target.value); onCalculateFromUnitDisc()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_QTY_BASE || SHOW_QTY_BASE" class="example-full-width" appearance="outline">
        <mat-label>Quantity Base</mat-label>
        <input matInput placeholder="Quantity" [formControl]="form.controls['qty']" type="number"
          (change)="nullHandler('qty', $event.target.value); onCalculate()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_QTY_UOM || SHOW_QTY_UOM" class="example-full-width" appearance="outline">
        <mat-label>Quantity by UOM</mat-label>
        <input matInput placeholder="Quantity by UOM" [formControl]="form.controls['qtyUom']" type="number"
          (change)="nullHandler('qtyUom', $event.target.value); onCalculateFromQtyByUom()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UOM_TO_BASE_RATIO || SHOW_UOM_TO_BASE_RATIO" class="example-full-width" appearance="outline">
        <mat-label>UOM to Base Ratio</mat-label>
        <input matInput placeholder="UOM to Base Ratio" [formControl]="form.controls['uomBaseRatio']" type="number"
           readonly>
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_PRICE_STD_UOM_EXCL_TAX || SHOW_UNIT_PRICE_STD_UOM_EXCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Unit Price STD by UOM (Exclusive of tax)</mat-label>
        <input matInput placeholder="Unit Price STD by UOM (Exclusive of tax)" [formControl]="form.controls['unitPriceStdUom']"
          type="number" step=".01" readonly (change)="nullHandler('unitPriceStdUom', $event.target.value);">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_PRICE_STD_UOM_INCL_TAX || SHOW_UNIT_PRICE_STD_UOM_INCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Unit Price STD by UOM (Inclusive of tax)</mat-label>
        <input matInput placeholder="Unit Price STD by UOM (Inclusiveof tax)" [formControl]="form.controls['unitPriceStdUomWithTax']"
          type="number" step=".01" readonly (change)="nullHandler('unitPriceStdUomWithTax', $event.target.value);">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_DISCOUNT_UOM_EXCL_TAX || SHOW_UNIT_DISCOUNT_UOM_EXCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Unit Discount by UOM (Exclusive of tax)</mat-label>
        <input matInput placeholder="Unit Discount by UOM" [formControl]="form.controls['unitDiscountUom']"
          type="number" step=".01"
          (change)="nullHandler('unitDiscountUom', $event.target.value); onCalculateFromUnitDiscountUom()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_PRICE_NET_UOM_EXCL_TAX || SHOW_UNIT_PRICE_NET_UOM_EXCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Unit Price Net by UOM (Exclusive of tax)</mat-label>
        <input matInput placeholder="Unit Price by UOM (Exclusive of tax)"
          [formControl]="form.controls['unitPriceNetUom']" type="number" step=".01" readonly
          (change)="nullHandler('unitPriceNetUom', $event.target.value);">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_PRICE_TXN_UOM_INCL_TAX || SHOW_UNIT_PRICE_TXN_UOM_INCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Unit Price Transaction by UOM (Inclusive of tax)</mat-label>
        <input matInput placeholder="Unit Price by UOM (Inclusive of tax)"
          [formControl]="form.controls['unitPriceTxnUom']" type="number" step=".01"
          (change)="nullHandler('unitPriceTxnUom', $event.target.value); onCalculateFromUnitPriceTxnUom()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_PRICE_NET_EXCL_TAX || SHOW_UNIT_PRICE_NET_EXCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Unit Price Net (Exclusive of tax)</mat-label>
        <input matInput placeholder="Unit Price (Exclusive of tax)" [formControl]="form.controls['unitPriceNet']"
          type="number" step=".01"
          (change)="nullHandler('unitPriceNet', $event.target.value); onCalculateFromUnitPriceNet()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_AMOUNT_STD_EXCL_TAX || SHOW_AMOUNT_STD_EXCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>STD Amount(Unit Price x Quantity)</mat-label>
        <input matInput placeholder="STD Amount" [formControl]="form.controls['stdAmt']" type="number" step=".01" readonly>
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_DISCOUNT_AMOUNT_EXCL_TAX || SHOW_DISCOUNT_AMOUNT_EXCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Discount Amount(Exclusive of tax)</mat-label>
        <input matInput placeholder="Discount Amount" [formControl]="form.controls['discountAmt']" type="number"
          step=".01" (change)="nullHandler('discountAmt', $event.target.value); onCalculateFromAmountDisc()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_AMOUNT_NET_EXCL_TAX || SHOW_AMOUNT_NET_EXCL_TAX" class="example-full-width" appearance="outline">
        <mat-label>Amount Net (Exclusive of tax)</mat-label>
        <input matInput [formControl]="form.controls['netAmt']" type="number" step=".01"
          (change)="nullHandler('netAmt', $event.target.value); onCalculateFromAmountNet()">
      </mat-form-field>

      <app-sst *ngIf="!appletSettings.HIDE_TAX_CONFIG_SELECTION || SHOW_TAX_CONFIG_SELECTION" [(sst)]="form.controls['taxCode']" (sstContainer)="onSST($event)"></app-sst>

      <mat-form-field *ngIf="!appletSettings.HIDE_TAX_CONFIG_SELECTION || SHOW_TAX_CONFIG_SELECTION" class="example-full-width" appearance="outline">
        <mat-label>SST/GST/VAT</mat-label>
        <input matInput readonly placeholder="SST/GST/VAT" [formControl]="form.controls['taxPercent']" type="number"
          step=".01" (change)="nullHandler('taxPercent', $event.target.value); onCalculate()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_TAX_CONFIG_SELECTION || SHOW_TAX_CONFIG_SELECTION" class="example-full-width" appearance="outline">
        <mat-label>Tax Amount</mat-label>
        <input matInput readonly placeholder="Tax Amount" [formControl]="form.controls['taxAmt']" type="number"
          step=".01" >
      </mat-form-field>

      <!-- <mat-form-field class="example-full-width" appearance="outline">
        <mat-label>Net Amount(SST/GST/VAT x Net Amount)</mat-label>
        <input matInput readonly [formControl]="form.controls['netAmt2']" type="number">
      </mat-form-field> -->

      <app-wht *ngIf="!appletSettings.HIDE_WHT_CONFIG_SELECTION || SHOW_WHT_CONFIG_SELECTION" [(wht)]="form.controls['whtCode']" (whtContainer)="onWHT($event)"></app-wht>

      <mat-form-field *ngIf="!appletSettings.HIDE_WHT_CONFIG_SELECTION || SHOW_WHT_CONFIG_SELECTION" class="example-full-width" appearance="outline">
        <mat-label>WHT</mat-label>
        <input matInput readonly placeholder="WHT" [formControl]="form.controls['whtPercent']" type="number" step=".01"
          (change)="nullHandler('whtPercent', $event.target.value); onCalculate()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_WHT_CONFIG_SELECTION || SHOW_WHT_CONFIG_SELECTION" class="example-full-width" appearance="outline">
        <mat-label>WHT Amount</mat-label>
        <input matInput readonly placeholder="WHT Amount" [formControl]="form.controls['whtAmt']" type="number"
          step=".01">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_UNIT_PRICE_TXN || SHOW_UNIT_PRICE_TXN" class="example-full-width" appearance="outline">
        <mat-label>Unit Price Transaction (Inclusive of tax)</mat-label>
        <input matInput placeholder="Unit Price (Inclusive of tax)" [formControl]="form.controls['unitPriceTxn']"
          type="number" step=".01"
          (change)="nullHandler('unitPriceTxn', $event.target.value); onCalculateFromUnitPriceTxn()">
      </mat-form-field>

      <mat-form-field *ngIf="!appletSettings.HIDE_AMOUNT_TXN || SHOW_AMOUNT_TXN" class="example-full-width" appearance="outline">
        <mat-label>Txn Amount</mat-label>
        <input matInput placeholder="Txn Amount" [formControl]="form.controls['txnAmt']" type="number" step=".01"
          (change)="nullHandler('txnAmt', $event.target.value); onCalculateFromAmountTxn()">
      </mat-form-field>

      <mat-form-field class="example-full-width" appearance="outline">
        <mat-label>Remarks</mat-label>
        <textarea #remarks matInput placeholder="Remarks" [formControl]="form.controls['remarks']"></textarea>
        <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
      </mat-form-field>

      <mat-form-field class="example-full-width" appearance="outline">
        <mat-label>Tracking ID</mat-label>
        <input matInput placeholder="Tracking ID" [formControl]="form.controls['trackingId']" type="text">
      </mat-form-field>

    </div>
  </div>
</form>
