import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { Store } from '@ngrx/store';
import { AppletService, bl_fi_generic_doc_line_RowClass, TaxCodeCfgService } from 'blg-akaun-ts-lib';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { iif, of, EMPTY, combineLatest } from 'rxjs';
import { delay, map, mergeMap } from 'rxjs/operators';
import { AppConfig } from 'projects/shared-utilities/visa';
import { MatTabGroup } from '@angular/material/tabs';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { BinSelectors, HDRSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { InternalJobOrderEditLineItemMainComponent } from './edit-line-item-item-details/internal-job-order-edit-line-item-main/internal-job-order-edit-line-item-main.component';
import { InternalJobOrderEditLineItemDepartmentComponent } from './edit-line-item-item-details/internal-job-order-edit-line-item-department/internal-job-order-edit-line-item-department.component';

interface LocalState {
  deactivateReturn: boolean;
  selectedIndex: number;
  deactivateAddDoc: boolean;
  deactivateIssueLink: boolean;
  deliveryDetailsEdit: boolean;
  itemDetailsSelectedIndex: number;
  serialNumberSelectedIndex: number;
  deleteConfirmation: boolean;
}

@Component({
  selector: 'app-internal-job-order-edit-line-item',
  templateUrl: './internal-job-order-edit-line-item.component.html',
  styleUrls: ['./internal-job-order-edit-line-item.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalJobOrderEditLineItemComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Edit Line Item';
  protected index = 9;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly deleteConfirmation$ = this.componentStore.select(state => state.deleteConfirmation);
  readonly itemDetailsSelectedIndex$ = this.componentStore.select(state => state.itemDetailsSelectedIndex);
  readonly serialNumberSelectedIndex$ = this.componentStore.select(state => state.serialNumberSelectedIndex);

  prevIndex: number;
  protected prevLocalState: any;

  dept$ = combineLatest(
    [
      this.draftStore.select(HDRSelectors.selectDimension),
      this.draftStore.select(HDRSelectors.selectProfitCenter),
      this.draftStore.select(HDRSelectors.selectProject),
      this.draftStore.select(HDRSelectors.selectSegment)
  ]).pipe(
    map(([a, b, c, d]) => ({guid_dimension: a, guid_profit_center: b, guid_project: c, guid_segment: d}))
  );
  line$ = this.store.select(InternalJobOrderSelectors.selectLineItem);
  bins$ = this.draftStore.select(BinSelectors.selectAll);
  item$ = this.store.select(InternalJobOrderSelectors.selectItem);
  tax$ = this.taxService.get(AppConfig.apiVisa).pipe(map(d => ({
    sst: d.data.filter(s => s.bl_fi_cfg_tax_code.tax_gst_type),
    wht: d.data.filter(s => s.bl_fi_cfg_tax_code.tax_wht_type)
  })));
  appletSettings$ = this.appletService.getByGuid(sessionStorage.getItem('appletGuid'), AppConfig.apiVisa).pipe(
    map(a => a.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json)
  );

  lineItem: bl_fi_generic_doc_line_RowClass;
  deleteConfirmation = false;

  @ViewChild(InternalJobOrderEditLineItemMainComponent) main: InternalJobOrderEditLineItemMainComponent;
  @ViewChild(InternalJobOrderEditLineItemDepartmentComponent) deparment: InternalJobOrderEditLineItemDepartmentComponent;
  @ViewChild(MatTabGroup) matTab: MatTabGroup;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    private taxService: TaxCodeCfgService,
    // TODO: maybe use pipe, ngrx and directive instead
    private appletService: AppletService,
    protected readonly store: Store<InternalJobOrderStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly componentStore: ComponentStore<LocalState>
  ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.line$.subscribe({next: resolve => this.lineItem = resolve});
    this.subs.sink = this.deleteConfirmation$.pipe(
      mergeMap(a => {
        return iif(() => a, of(a).pipe(delay(3000)), of(EMPTY));
      })
    ).subscribe(resolve => {
      if (resolve === true) {
        this.componentStore.patchState({deleteConfirmation: false});
        this.deleteConfirmation = false;
      }
    });
  }

  onSave() {
    const line = new bl_fi_generic_doc_line_RowClass();
    line.guid = this.lineItem.guid;
    line.item_guid = this.main.form.value.itemGuid;
    line.item_code = this.main.form.value.itemCode;
    line.item_name = this.main.form.value.itemName;
    line.amount_discount = this.main.form.value.discountAmount;
    line.amount_net = this.main.form.value.netAmount;
    line.amount_std = this.main.form.value.stdAmount;
    line.amount_tax_gst = this.main.form.value.taxAmount;
    line.amount_tax_wht = this.main.form.value.whtAmount;
    line.amount_txn = this.main.form.value.txnAmount;
    line.quantity_base = this.main.form.value.quantity;
    line.tax_gst_code = this.main.form.value.sstCode;
    line.tax_wht_code = this.main.form.value.whtCode;
    line.guid_dimension = this.deparment.form.value.dimension;
    line.guid_profit_center = this.deparment.form.value.profitCenter;
    line.guid_project = this.deparment.form.value.project;
    line.guid_segment = this.deparment.form.value.segment;
    line.item_remarks = this.main.form.value.remarks;
    line.item_property_json = {...this.main.form.value};
    this.viewColFacade.editLineItem(line, this.prevIndex);
  }

  disableSave() {
    return this.main?.form?.invalid;
  }

  onDelete() {
    if (this.deleteConfirmation) {
      const line = {...this.lineItem, status: 'DELETED'};
      this.viewColFacade.editLineItem(line, this.prevIndex);
      // this.viewColFacade.deleteLineItem(this.lineItem.guid.toString(), this.prevIndex);
      this.deleteConfirmation = false;
      this.componentStore.patchState({deleteConfirmation: false});
    } else {
      this.deleteConfirmation = true;
      this.componentStore.patchState({deleteConfirmation: true});
    }
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateLineItem: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAddRelatedDocuments() {
    if (!this.localState.deactivateAddDoc) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAddDoc: true,
        deactivateReturn: true,
        deliveryDetailsEdit: false
      });
      this.viewColFacade.onNextAndReset(this.index, 10);
    }
  }

  onIssueLink(e) {
    if (!this.localState.deactivateIssueLink) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateIssueLink: true,
        deactivateReturn: true
      });
      this.viewColFacade.onNextAndReset(this.index, 13);
    }
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex
      });
    }
    this.subs.unsubscribe();
  }

}

