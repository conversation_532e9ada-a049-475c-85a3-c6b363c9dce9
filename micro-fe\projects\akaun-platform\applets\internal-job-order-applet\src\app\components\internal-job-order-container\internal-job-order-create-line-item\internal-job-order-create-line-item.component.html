<!-- <div class="view-col-table no-tab" fxLayout="column">
  <mat-card-title class="column-title">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
      [disabled]="deactivateReturn$ | async"
      (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Select Line Item
      </span>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
      <div fxFlex="3 0 0">
        <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
          <app-advanced-search fxFlex fxFlex.lt-sm="100" [id]="'internal-so-line-item'" [advSearchModel]="searchModel"></app-advanced-search>
        </div>
      </div>
      <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
        <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
  </mat-card-title>
  <div style="height: 80%;">
    <ag-grid-angular #agGrid
    style="height: 100%;"
    class="ag-theme-balham"
    rowSelection="single"
    rowModelType="serverSide"
    serverSideStoreType="partial"
    [getRowClass]="pagination.getRowClass"
    [columnDefs]="columnsDefs"
    [rowData]="[]"
    [paginationPageSize]="pagination.rowPerPage"
    [cacheBlockSize]="pagination.rowPerPage"
    [pagination]="true"
    [animateRows]="true"
    [defaultColDef]="defaultColDef"
    [suppressRowClickSelection]="false"
    [sideBar]="true"
    (rowClicked)="onRowClicked($event.data)"
    (gridReady)="onGridReady($event)">
    </ag-grid-angular>
  </div>
</div> -->
<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
      [disabled]="deactivateReturn$ | async"
      (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Select Line Item
      </span>
    </div>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Search Item">
    <app-line-search-item (item)="onFinancialItem($event)"></app-line-search-item>
  </mat-tab>
  <!-- <mat-tab label="Jobsheet Item">
    <app-line-jobsheet-item (item)="onFinancialItem($event)"></app-line-jobsheet-item>
  </mat-tab>
  <mat-tab label="Quotation Item">
    <app-line-quotation-item (item)="onFinancialItem($event)"></app-line-quotation-item>
  </mat-tab>
  <mat-tab label="Previous Sales Order">
    <app-line-previous-sales-order [customer]="entity$ | async" (item)="onFinancialItem($event)"></app-line-previous-sales-order>
  </mat-tab> -->
</mat-tab-group>

