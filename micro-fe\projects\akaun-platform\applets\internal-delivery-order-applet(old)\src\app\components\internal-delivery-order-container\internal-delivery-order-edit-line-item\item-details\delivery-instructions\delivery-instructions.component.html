
<form [formGroup]="form" #formDirectives="ngForm">
  <div class="view-col-forms">
    <div class="inner-tab" fxLayout="column" fxLayoutGap="5px">
      <div fxLayout="column">
        <fieldset>
          <legend>Delivery Instructions</legend>
          <div *ngFor="let x of leftColControls; let i = index">
            <mat-form-field *ngIf="x.readonly; else input" appearance="outline">
              <mat-label>{{ x.label }}</mat-label>
              <input  matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
            </mat-form-field>
            <ng-template #input>
              <ng-container [ngSwitch]="x.type">
                <mat-form-field *ngSwitchCase="'text-area'" appearance = "outline">
                  <mat-label>{{ x.label }}</mat-label>
                  <textarea #remarks matInput [formControl]="form.controls[x.formControl]"></textarea>
                  <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'date'" appearance = "outline">
                  <mat-label>{{ x.label }}</mat-label>
                  <input matInput [matDatepicker]="datepicker" [formControl]="form.controls[x.formControl]" autocomplete="off" readonly (click)="datepicker.open()">
                  <mat-error>{{ x.label }} is <strong>not valid</strong></mat-error>
                  <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle>
                  <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
                </mat-form-field>
              </ng-container>
            </ng-template>
          </div>
        </fieldset>
      </div>
      <div fxLayout="column">
        <fieldset>
          <legend>Delivery Message Card</legend>
          <mat-checkbox style="margin-right: 10px; font-size: 90%;" [formControl]="copyEntity">Copy from Entity Name</mat-checkbox>
          <mat-checkbox style="font-size: 90%;" [formControl]="copyRecipient">Copy from Recipient Name</mat-checkbox>
          <br>
          <br>
          <mat-form-field appearance="outline">
              <mat-label>From</mat-label>
              <input matInput placeholder="From" [formControl]="form.controls['from']" type="text">
            </mat-form-field>
            <mat-form-field appearance="outline">
              <mat-label>To</mat-label>
              <input matInput placeholder="to" [formControl]="form.controls['to']" type="text">
            </mat-form-field>
            <mat-form-field appearance="outline">
              <mat-label>Message</mat-label>
              <textarea #message matInput placeholder="Message" [formControl]="form.controls['message']"></textarea>
              <mat-hint align="end">{{ message.value.length }} characters</mat-hint>
            </mat-form-field>
        </fieldset>
      </div>
    </div>
  </div>
</form>
    