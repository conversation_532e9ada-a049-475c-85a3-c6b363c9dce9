import { AfterViewChecked, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { StockBalanceSummaryService, bl_fi_generic_doc_single_line, bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../../models/applet-settings.model';
import { Store } from '@ngrx/store';
import { InternalJobOrderStates } from '../../../../../state-controllers/internal-job-order-controller/store/states';
import { JobOrderNoSelectors } from '../../../../../state-controllers/draft-controller/store/selectors';
import { InternalJobOrderSelectors } from '../../../../../state-controllers/internal-job-order-controller/store/selectors';
import { ViewColumnFacade } from '../../../../../facades/view-column.facade';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { ComponentStore } from '@ngrx/component-store';
import { SalesOrderStates } from '../../../../../state-controllers/sales-order-controller/store/states';
import { SalesOrderSelectors } from '../../../../../state-controllers/sales-order-controller/store/selectors';
interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRowGuid: string;
}
@Component({
  selector: 'app-stock-balance-summary',
  templateUrl: './stock-balance-summary.component.html',
  styleUrls: ['./stock-balance-summary.component.css'],
  providers: [ComponentStore],
})
export class StockBalanceSummaryComponent extends ViewColumnComponent {

  @Input() draft$: Observable<bl_mrp_job_order_hdr_RowClass>;
  @Input() appletSettings$: Observable<AppletSettings>;

  @Output() itemCode = new EventEmitter();
  @Output() updateMain = new EventEmitter();

  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);
  toggleColumn$: Observable<boolean>;

  apiVisa = AppConfig.apiVisa;
  prevIndex: number;
  protected prevLocalState: any;

  PROCESS_STATUS = ['PLANNED','IN_PROGRESS','COMPLETED','ON_HOLD','CANCELLED'];
  QC_OUTPUT_STATUS = ['PASSED','REJECTED'];

  protected subs = new SubSink();

  protected compName = 'Stock Bal Summary';
  protected index = 2;

  form: FormGroup;
  uom;
  editMode;


  constructor(
     protected readonly store: Store<SalesOrderStates>,
    protected viewColFacade: ViewColumnFacade,
    protected stockBalanceSummaryService : StockBalanceSummaryService,
    private readonly componentStore: ComponentStore<LocalState>
  ) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.form = new FormGroup({
      itemCode: new FormControl(''),
      itemName: new FormControl(''),
      system_stock_bal: new FormControl(0.00),
      total_sales_order_qty: new FormControl(0.00),
      total_reservation_qty: new FormControl(0.00),
      total_job_order_qty: new FormControl(0.00),
      availble_stock_bal_not_incl_rsv: new FormControl(0.00),
      availble_stock_bal_incl: new FormControl(0.00)

    });

    this.subs.sink = this.store.select(SalesOrderSelectors.selectSalesOrder).subscribe(salesOrder => {
      this.subs.sink = this.stockBalanceSummaryService.postStockSummary(
        {
          "financial_item_guids" : [salesOrder.item_guid],
          "sales_order_guids": [salesOrder.so_guid]
        },
        this.apiVisa
      ).subscribe(data => {
          console.log("Stock Balance Summary Data ->>>",data);
          const stockBal : any = data;
          this.form.patchValue({
            itemCode: salesOrder.item_code,
            itemName: salesOrder.item_name,
            system_stock_bal: stockBal.system_stock_balance ? stockBal.system_stock_balance : 0.00,
            total_sales_order_qty: stockBal.total_sales_order_qty ? stockBal.total_sales_order_qty : 0.00,
            total_reservation_qty: stockBal.reserved_qty ? stockBal.reserved_qty : 0.00,
            total_job_order_qty: stockBal.incomplete_job_order_qty ? stockBal.incomplete_job_order_qty : 0.00,
            availble_stock_bal_not_incl_rsv: stockBal.stock_availability_qty_available ? stockBal.stock_availability_qty_available : 0.00,
            availble_stock_bal_incl: stockBal.stock_availability_qty_balance ? stockBal.stock_availability_qty_balance : 0.00,       
          })
      })
    })



  }

  goToDetailedReservedStock() {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false});
    this.viewColFacade.onNextAndReset(this.index, 3);
  }

  goToDetailedJobOrderStock() {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false});
    this.viewColFacade.onNextAndReset(this.index, 4);
  }

  onReturn() {
    this.viewColFacade.updateInstance(1, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(1);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
