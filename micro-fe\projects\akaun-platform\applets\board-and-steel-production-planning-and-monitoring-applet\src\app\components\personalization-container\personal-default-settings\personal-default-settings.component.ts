import { Component, OnInit, Input, OnD<PERSON>roy } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { AppletContainerModel } from 'blg-akaun-ts-lib';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../models/applet-settings.model';

@Component({
  selector: 'app-personal-default-settings',
  templateUrl: './personal-default-settings.component.html',
  styleUrls: ['./personal-default-settings.component.css'],
})
export class PersonalDefaultSettingsComponent implements OnInit, On<PERSON><PERSON>roy {

  @Input() appletSettings$: Observable<AppletContainerModel>;

  private subs = new SubSink();

  form: FormGroup;

  apiVisa = AppConfig.apiVisa;

  constructor(
    private readonly store: Store<SessionStates>,
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      DEFAULT_BRANCH: new FormControl(),
      DEFAULT_LOCATION: new FormControl()
    });
    this.subs.sink = this.store.select(SessionSelectors.selectPersonalSettings).subscribe({next: (resolve: AppletSettings) => {
      this.form.patchValue({
        DEFAULT_BRANCH: resolve?.DEFAULT_BRANCH,
        DEFAULT_LOCATION: resolve?.DEFAULT_LOCATION
      });
    }});
  }

  onSave() {
    this.store.dispatch(SessionActions.savePersonalSettingsInit({settings: this.form.value}));
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
