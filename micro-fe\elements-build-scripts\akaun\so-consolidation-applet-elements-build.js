const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/so-consolidation-applet/runtime.js',
    './dist/so-consolidation-applet/polyfills-es5.js',
    './dist/so-consolidation-applet/scripts.js',
    './dist/so-consolidation-applet/main.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/so-consolidation-applet');
  await concat(files, './elements/akaun-platform/applets/so-consolidation-applet/so-consolidation-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
