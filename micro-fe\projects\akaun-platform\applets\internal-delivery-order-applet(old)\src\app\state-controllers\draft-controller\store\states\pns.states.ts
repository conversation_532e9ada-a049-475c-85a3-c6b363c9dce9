import { createEntityAdapter, EntityAdapter, EntityState } from '@ngrx/entity';
import { bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';

export interface PNSState extends EntityState<bl_fi_generic_doc_line_RowClass> {
    binData: {
        bin_hdr_guid: string,
        bin_line_guid: string,
        qty: number,
        container_measure: number,
        container_qty: number,
        bin_hdr_code: string,
    };
}

export const pnsAdapter: EntityAdapter<bl_fi_generic_doc_line_RowClass> = createEntityAdapter<bl_fi_generic_doc_line_RowClass>({
    selectId: a => a.guid.toString()
});

export const initState: PNSState = pnsAdapter.getInitialState({
    binData: {
        bin_hdr_guid: null,
        bin_line_guid: null,
        qty: null,
        container_measure: null,
        container_qty: null,
        bin_hdr_code: null,
    }
});
