import { createFeatureSelector } from '@ngrx/store';
import { processingFeatureKey } from '../reducers/processing.reducers';
import { ProcesssingStates } from '../states';
import { ProcessingState } from '../states/processing.states';

export const selectInternalDeliveryOrderFeature = createFeatureSelector<ProcessingState>(processingFeatureKey);

export const selectEntity = (state: ProcesssingStates) => state.processing.selectedEntity;
export const selectTotalRecords = (state: ProcesssingStates) => state.processing.totalRecords;
export const selectErrorLog = (state: ProcesssingStates) => state.processing.errorLog;
export const selectDraft = (state: ProcesssingStates) => state.processing.draft;
export const selectDraftEdit = (state: ProcesssingStates) => state.processing.draftEdit;
export const selectAgGrid = (state: ProcesssingStates) => state.processing.updateAgGrid;
