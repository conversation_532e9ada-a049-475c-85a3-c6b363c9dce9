import { createAction, props } from '@ngrx/store';
import { bl_fi_generic_doc_hdr_RowClass } from 'blg-akaun-ts-lib';
import { ShippingAddress } from '../../../../models/internal-delivery-order.model';
// import { InternalSOMain, InternalSODepartment, BillingInfo, ShippingInfo } from '../../../../models/internal-sales-order.model';

export const updateMain = createAction('[Draft: HDR Edit] Update Main', props<{ form: any }>());
export const updateDepartment = createAction('[Draft: HDR Edit] Update Department', props<{ form: any }>());
export const updateBillingInfo = createAction('[Draft: HDR Edit] Update Billing', props<{ form: any }>());
export const updateShippingInfo = createAction('[Draft: HDR Edit] Update Shipping', props<{ form: any }>());

export const resetHDRInit = createAction('[Draft: HDR Edit] Reset Init');
export const resetHDRSuccess = createAction('[Draft: HDR Edit] Reset Success', props<{hdr: bl_fi_generic_doc_hdr_RowClass}>());
export const updateShippingAddress = createAction('[Draft: HDR] Update Shipping Address', props<{ form: ShippingAddress }>());

export const updateDeliveryType = createAction('[Draft: HDR Edit] Update Delivery Type', props<{ deliveryType:string }>());
