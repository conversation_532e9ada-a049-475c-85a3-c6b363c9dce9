import { AfterViewChecked, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { StockAvailabilityInputModel, StockAvailabilityService, bl_fi_generic_doc_single_line, bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../models/applet-settings.model';
import { Store } from '@ngrx/store';
import { InternalJobOrderStates } from '../../../../state-controllers/internal-job-order-controller/store/states';
import { JobOrderNoSelectors } from '../../../../state-controllers/draft-controller/store/selectors';
import { InternalJobOrderSelectors } from '../../../../state-controllers/internal-job-order-controller/store/selectors';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { SalesOrderStates } from '../../../../state-controllers/sales-order-controller/store/states';
import { SalesOrderSelectors } from '../../../../state-controllers/sales-order-controller/store/selectors';

interface LocalState {
  deactivateAdd: boolean;
  deactivateReturn: boolean;
  deactivateCustomer: boolean;

}

@Component({
  selector: 'app-item-details',
  templateUrl: './item-details.component.html',
  styleUrls: ['./item-details.component.css']
})
export class ItemDetailsComponent {

  @Input() draft$: Observable<bl_mrp_job_order_hdr_RowClass>;
  @Input() appletSettings$: Observable<AppletSettings>;

  @Output() itemCode = new EventEmitter();
  @Output() updateMain = new EventEmitter();
  locationGuids;

  apiVisa = AppConfig.apiVisa;

  PROCESS_STATUS = ['PLANNED','IN_PROGRESS','COMPLETED','ON_HOLD','CANCELLED'];
  QC_OUTPUT_STATUS = ['PASSED','REJECTED'];

  private subs = new SubSink();

  form: FormGroup;
  uom;
  editMode;

  protected compName = 'Item Details';
  protected index = 1;
  protected localState: LocalState;

  constructor(
    protected readonly store: Store<SalesOrderStates>,
    protected readonly stockAvailabilityService : StockAvailabilityService,
    protected viewColFacade: ViewColumnFacade,
  ) { }

  ngOnInit() {

    this.form = new FormGroup({
      itemCode: new FormControl(''),
      itemName: new FormControl(''),
     
      uom: new FormControl(''),
      qty: new FormControl(0.00),
      
      availble_stock_bal: new FormControl(),
    });
    this.form.controls['itemCode'].disable();
    this.form.controls['itemName'].disable();
    this.form.controls['uom'].disable();
    this.form.controls['qty'].disable();    
    this.form.controls['availble_stock_bal'].disable();

    this.subs.sink = this.store.select(SalesOrderSelectors.selectSalesOrder).subscribe(salesOrder => {
      const inputModel = {} as StockAvailabilityInputModel;
      this.locationGuids = [salesOrder.location_guid];
      inputModel.location_guids = this.locationGuids;
      const filteredData = [];
      this.subs.sink = this.stockAvailabilityService.getStockAvailability(inputModel, this.apiVisa).subscribe(response => {
        // console.log("Available Stock Bal",response.data);
        for (let i = 0; i < response.data.length; i++) {
          if (response.data[i].fi_item_guid === salesOrder.item_guid) {
            this.form.patchValue({
              availble_stock_bal : response.data[i].qty_available
            })
            // filteredData.push(response.data[i]);
          }
        }
      })
        this.form.patchValue({
          itemCode : salesOrder.item_code,
          itemName : salesOrder.item_name,
          qty : salesOrder.qty,
          uom : salesOrder.uom,
        })
    })
  }


  goToSummary() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
    });
    this.viewColFacade.onNext( 2);
  }



  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
