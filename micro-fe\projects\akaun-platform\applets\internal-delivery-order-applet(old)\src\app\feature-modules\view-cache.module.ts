import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { StoreModule } from '@ngrx/store';
import { InternalDeliveryOrderModule } from '../components/internal-delivery-order-container/internal-delivery-order.module';
import { PickPackQueueModule } from '../components/pick-pack-queue-container/pick-pack-queue.module';
import { PrintableFormatSettingsModule } from '../components/settings-container/printable-format-settings-container/printable-format-settings-container.module';
import { ViewColumnFacade } from '../facades/view-column.facade';
import { reducers } from '../state-controllers/view-cache-controller/store/reducers';
import { viewCacheFeatureKey } from '../state-controllers/view-cache-controller/store/reducers/view-cache.reducers';

@NgModule({
  imports: [
    CommonModule,
    InternalDeliveryOrderModule,
    PrintableFormatSettingsModule,
    PickPackQueueModule,
    StoreModule.forFeature(viewCacheFeatureKey, reducers.viewCache)
  ],
  providers: [ViewColumnFacade]
})
export class ViewCacheModule { }
