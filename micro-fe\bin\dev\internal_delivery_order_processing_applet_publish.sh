#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=dev --project=internal-delivery-order-processing-applet --output-hashing none
node elements-build-scripts/akaun/internal-delivery-order-processing-applet-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://development-akaun-applets/bigledger/tonn-cable/internal-delivery-order-processing-applet/staging s3://development-akaun-applets/bigledger/tonn-cable/internal-delivery-order-processing-applet/staging/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile development-bigledger --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/internal-delivery-order-processing-applet/ s3://development-akaun-applets/bigledger/tonn-cable/internal-delivery-order-processing-applet/dev --profile development-bigledger --acl public-read --recursive
