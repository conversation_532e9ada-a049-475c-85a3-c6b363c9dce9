import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { map, withLatestFrom } from 'rxjs/operators';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { SnackBarConstants } from '../../../../models/constants/snack-bar.constants';
import { SettlementEditActions } from '../actions';
import { HDREditSelectors } from '../selectors';
import { DraftStates } from '../states';

@Injectable()
export class SettlementEditEffects {

    // addSettlement$ = createEffect(() => this.actions$.pipe(
    //     ofType(SettlementEditActions.addSettlementInit),
    //     withLatestFrom(this.draftStore.select(HDREditSelectors.selectHdr)),
    //     map(([a, b]) => {
    //         if (parseFloat(<any>a.settlement.amount_txn) <= parseFloat(<any>b.amount_open_balance)) {
    //             this.viewColFacade.showSnackBar(SnackBarConstants.addSettlementSuccess);
    //             this.viewColFacade.resetIndex(a.pageIndex);
    //             return SettlementEditActions.addSettlementSuccess({settlement: a.settlement});
    //         } else {
    //             this.viewColFacade.showSnackBar(SnackBarConstants.addSettlementFailed);
    //             return SettlementEditActions.addSettlementFailed();
    //         }
    //     })
    // ));

    // resetSettlement$ = createEffect(() => this.actions$.pipe(
    //     ofType(SettlementEditActions.resetSettlementInit),
    //     withLatestFrom(this.store.select(InternalSalesOrderSelectors.selectEntity)),
    //     map(([a, b]) => {
    //         return SettlementEditActions.resetSettlementSuccess({settlement: b.bl_fi_generic_doc_line.filter(l => l.txn_type === 'STL_MTHD')})
    //     })
    // ));

    constructor(
        private actions$: Actions,
        private viewColFacade: ViewColumnFacade,
        private readonly draftStore: Store<DraftStates>,
    ) { }
}
