import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import {
  BranchService,
  GenericDocSingleLineContainer,
  Pagination,
  SubQueryService,
  GenericDocSingleLineService,
  GenericDocLineContainerModel,
  MrpJobOrderHdrService,
  MrpJobOrderHdrContainerModel,
  MrpJobOrderGenDocLinkService,
  GenericDocLineService,
  InternalSalesOrderService} from 'blg-akaun-ts-lib';
import { forkJoin, iif, Observable, of, zip } from 'rxjs';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../models/advanced-search-models';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { Store } from '@ngrx/store';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SalesOrderStates } from '../../../state-controllers/sales-order-controller/store/states';
import { SalesOrderActions } from '../../../state-controllers/sales-order-controller/store/actions';
import { SalesOrderSelectors } from '../../../state-controllers/sales-order-controller/store/selectors';
import { internalSalesOrderSearchModel } from '../../../models/advanced-search-models/sales-order.model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRowGuid: string;
}

@Component({
  selector: 'app-sales-order-listing',
  templateUrl: './sales-order-listing.component.html',
  styleUrls: ['./sales-order-listing.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class SalesOrderListingComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Sales Order Listing';
  protected readonly index = 0;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  searchModel = internalSalesOrderSearchModel;
  SQLGuids: string[] = null;
  pagination = new Pagination();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Sales Order No', field: 'so_no'},
    {headerName: 'Txn Date', field: 'date_txn'},
    {headerName: 'Customer Name', field: 'customerName', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Expected Delivery', field: 'expected_delivery'},

    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},

    {headerName: 'Quantity', field: 'qty'},
    {headerName: 'UOM', field: 'uom'},
    // {headerName: 'Available Stock Balance', field: 'avl_stock_bal'},

    {headerName: 'Scenario Option', field: 'scenario_option'},
    {headerName: 'Process Status', field: 'process_status'},

    // {headerName: 'QC Output Status', field: 'bl_mrp_job_order_hdr.batch_no.bin_code'},
    {headerName: 'Status', field: 'job_doc_state', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Job Order No', field: 'job_order_no'},

  ];

  // columnsDefs$ = this.sessionStore.select(SessionSelectors.selectMasterSettings).pipe(
  //   map((a: any) => [
  //     ...this.columnsDefs,
  //     a.ENABLE_CUSTOM_STATUS_HDR_1 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_1 ? a.NAME_CUSTOM_STATUS_HDR_1 : 'client_doc_status_01',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_01',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //     a.ENABLE_CUSTOM_STATUS_HDR_2 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_2 ? a.NAME_CUSTOM_STATUS_HDR_2 : 'client_doc_status_02',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_02',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //     a.ENABLE_CUSTOM_STATUS_HDR_3 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_3 ? a.NAME_CUSTOM_STATUS_HDR_3 : 'client_doc_status_03',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_03',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //     a.ENABLE_CUSTOM_STATUS_HDR_4 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_4 ? a.NAME_CUSTOM_STATUS_HDR_4 : 'client_doc_status_04',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_04',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //     a.ENABLE_CUSTOM_STATUS_HDR_5 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_5 ? a.NAME_CUSTOM_STATUS_HDR_5 : 'client_doc_status_05',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_05',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //   ])
  // );

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private mrpJobOrderHdrService: MrpJobOrderHdrService,
    private brnchService: BranchService,
    private sqlService: SubQueryService,
    private mrpJobOrderGenDocLinkService : MrpJobOrderGenDocLinkService,
    private genericDocLineService: GenericDocLineService,
    private soService: InternalSalesOrderService,
    private readonly store: Store<SalesOrderStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {

        // this.store.dispatch(InternalJobOrderActions.loadGenDocLinkInit({ request: grid.request }));


        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
         
          // { columnName: 'doc_type', operator: '=', value: 'INTERNAL_SALES_INVOICE' },
          {
            columnName: 'guids', operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
        ];
        let totalrec = 0;
        this.mrpJobOrderGenDocLinkService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {

            totalrec = resolved.totalRecords;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                this.soService.getByGuid(itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_hdr_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid ?
                this.genericDocLineService.getByGuid(itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),
                this.mrpJobOrderHdrService.getByGuid(itemperm.bl_mrp_job_order_generic_doc_link.job_order_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                ).pipe(
                  map(([b_a,b_b,b_c]) => {
                    console.log("B_a",b_a);
                    console.log("B_b",b_b);
                    let obj = {"guid":'', "so_guid": '', "so_no": '',"branch_guid":'',"location_guid":'', "date_txn": '', "customerName": '',"entityId": '', "expected_delivery": '','item_guid':'', 'item_code':'', 'item_name':'', 'qty':'', 'uom':'', 'avl_stock_bal':'','scenario_option':'','process_status':'','job_doc_state':'' , 'job_order_no':''};
                    obj.guid = itemperm.bl_mrp_job_order_generic_doc_link.guid.toString();
                    obj.so_guid = b_a.data.bl_fi_generic_doc_hdr.guid;
                    obj.so_no = b_a.data.bl_fi_generic_doc_hdr.server_doc_1;
                    obj.branch_guid = b_a.data.bl_fi_generic_doc_hdr.guid_branch;
                    obj.location_guid = b_a.data.bl_fi_generic_doc_hdr.guid_store;

                    obj.date_txn = b_a.data.bl_fi_generic_doc_hdr.date_txn
                    obj.customerName = b_a.data.bl_fi_generic_doc_hdr.sales_entity_hdr_name ? b_a.data.bl_fi_generic_doc_hdr.sales_entity_hdr_name : ""
                    obj.entityId = b_a.data.bl_fi_generic_doc_hdr.doc_entity_hdr_json?.entityId ? b_a.data.bl_fi_generic_doc_hdr.doc_entity_hdr_json?.entityId : ""
                    obj.branch_guid = b_a.data.bl_fi_generic_doc_hdr.guid_branch
                    // obj.expected_delivery =  b_a.data.bl_fi_generic_doc_hdr.track_delivery_time_estimated
                    obj.item_guid = b_b ? b_b.data.bl_fi_generic_doc_line.item_guid : '';
                    obj.item_name = b_b ? b_b.data.bl_fi_generic_doc_line.item_name : '';
                    obj.item_code = b_b ? b_b.data.bl_fi_generic_doc_line.item_code : '';
                    obj.qty = b_b ? b_b.data.bl_fi_generic_doc_line.quantity_base : '';
                    obj.uom = b_b ? b_b.data.bl_fi_generic_doc_line.uom : '';
                    // obj.avl_stock_bal
                    obj.scenario_option = itemperm.bl_mrp_job_order_generic_doc_link.scenario_option ? itemperm.bl_mrp_job_order_generic_doc_link.scenario_option.toString() : "";
                    obj.job_doc_state = itemperm.bl_mrp_job_order_generic_doc_link.job_doc_state ? itemperm.bl_mrp_job_order_generic_doc_link.job_doc_state.toString() : "";

                    obj.process_status =  b_c.error ? "" : b_c.data.bl_mrp_job_order_hdr.process_status
                    // objj.status
                    obj.job_order_no = b_c.error ? "" : b_c.data.bl_mrp_job_order_hdr.server_doc_1
                   
                  

                    // obj.guid = itemperm.bl_t2t_fi_item_to_tenant_link.guid.toString();
                    // obj.basic_type = b_a.data.bl_fi_mst_item_hdr.txn_type;
                    // obj.sub_item_type = b_a.data.bl_fi_mst_item_hdr.sub_item_type;
                    return obj;
                  })
                )
            )
            );
            return iif(() => resolved.totalRecords > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              // this.store.dispatch(InternalJobOrderActions.loadGenDocLinkSuccess({ totalRecords: totalrec }));
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
            // this.store.dispatch(InternalJobOrderActions.loadGenDocLinkFailed({ error: err.message }));
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(SalesOrderSelectors.selectAgGrid).subscribe(resolved => {
      if (resolved) {
        this.gridApi.refreshServerSideStore({ purge: true });
        this.store.dispatch(SalesOrderActions.resetAgGrid());
      }
    });
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onAdd() {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false});
    this.viewColFacade.onNextAndReset(this.index, 2);
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe(
        {next: resolve => {
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }}
      );
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  onRowClicked(entity: any) {
    if (entity) {
      console.log("Entity Selected",entity);
      this.store.dispatch(SalesOrderActions.selectSalesOrder({salesOrder: entity}));
      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance<LocalState>(this.index, {
          ...this.localState, deactivateAdd: false, deactivateList: true
        });
        this.viewColFacade.onNextAndReset(this.index, 1);
      }
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
