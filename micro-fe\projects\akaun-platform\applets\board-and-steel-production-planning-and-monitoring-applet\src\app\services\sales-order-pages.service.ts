import { Injectable } from '@angular/core';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { ViewColumn } from 'projects/shared-utilities/view-column';
import { SalesOrderListingComponent } from '../components/sales-order-container/sales-order-listing/sales-order-listing.component';
import { SalesOrderViewComponent } from '../components/sales-order-container/sales-order-view/sales-order-view.component';
import { StockBalanceSummaryComponent } from '../components/sales-order-container/sales-order-view/item-details/stock-balance-summary/stock-balance-summary.component';
import { DetailedReservationStockComponent } from '../components/sales-order-container/sales-order-view/item-details/stock-balance-summary/detailed-reservation-stock/detailed-reservation-stock.component';
import { DetailedJobOrderStockComponent } from '../components/sales-order-container/sales-order-view/item-details/stock-balance-summary/detailed-job-order-stock/detailed-job-order-stock.component';

@Injectable()
export class SalesOrderPagesService {

  // TODO: thinking of using a hashmap and need to get rid of adding index manually into pages component
  private initialState: ViewColumnState = {
    firstColumn: new ViewColumn(0, SalesOrderListingComponent, 'Sales Order Listing', {
      deactivateAdd: false,
      deactivateList: false,
      selectedRowIndex: null,
    }),
    secondColumn: null,
    viewCol: [
      new ViewColumn(0, SalesOrderListingComponent, 'Sales Order Listing', {
        deactivateAdd: false,
        deactivateList: false,
        selectedRowIndex: null,
      }),
      new ViewColumn(1, SalesOrderViewComponent, 'Sales Order View', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateCustomer: false,
        deactivateJobAgent: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateLineItem: false,
        deactivateSettlement: false,
        deactivateAddContra: false,
        deactivateViewContra: false,
        deactivateAddAttachments: false,
        selectedIndex: 0,
        childSelectedIndex: 0,
        selectedLineItemRowIndex: null,
        deleteConfirmation: false
      }),
      
      new ViewColumn(2, StockBalanceSummaryComponent, 'Stock Balance Summary', {
        selectedRowIndex: null,
      }),
      new ViewColumn(3, DetailedReservationStockComponent, 'Stock Balance Summary', {
        selectedRowIndex: null,
      }),
      new ViewColumn(4, DetailedJobOrderStockComponent, 'Stock Balance Summary', {
        selectedRowIndex: null,
      }),
      
    ],
    breadCrumbs: [],
    leftDrawer: [],
    rightDrawer: [],
    singleColumn: false,
    prevIndex: null
  };

  get pages() {
    return this.initialState;
  }

  constructor() { }
}
