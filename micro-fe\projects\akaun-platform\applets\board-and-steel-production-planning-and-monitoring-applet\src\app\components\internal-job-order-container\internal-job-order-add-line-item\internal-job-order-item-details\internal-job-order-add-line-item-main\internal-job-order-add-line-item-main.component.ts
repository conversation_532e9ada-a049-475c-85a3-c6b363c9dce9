import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { FinancialItemContainerModel, TaxCodeContainerModel } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../../models/applet-settings.model';

@Component({
  selector: 'app-internal-job-order-add-line-item-main',
  templateUrl: './internal-job-order-add-line-item-main.component.html',
  styleUrls: ['./internal-job-order-add-line-item-main.component.css']
})
// TODO: Check if all the calculations are correct
export class InternalJobOrderAddLineItemMainComponent implements OnInit, OnD<PERSON>roy {

  @Input() item$: Observable<FinancialItemContainerModel>;
  @Input() tax$: Observable<{
    sst: TaxCodeContainerModel[],
    wht: TaxCodeContainerModel[],
  }>;
  @Input() appletSettings$: Observable<AppletSettings>;

  @Output() customer = new EventEmitter();
  @Output() shippingInfo = new EventEmitter();
  @Output() billingInfo = new EventEmitter();
  apiVisa = AppConfig.apiVisa;

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Item Code*', formControl: 'itemCode', type: 'text', readonly: true, hint: ''},
    {label: 'Item Name*', formControl: 'itemName', type: 'text', readonly: true, hint: ''},
    // {label: 'UOM*', formControl: 'uom', type: 'uom', readonly: false, hint: ''},
    {label: 'Pricing Scheme*', formControl: 'pricingScheme', type: 'pricingScheme', readonly: false, hint: ''},
    {label: 'Quantity Base*', formControl: 'quantity', type: 'quantity', readonly: false, hint: ''},
    {label: 'Quantity By UOM*', formControl: 'quantity', type: 'quantity', readonly: false, hint: ''},
    {label: 'UOM to Base Ratio', formControl: 'uomToBaseRatio', type: 'text', readonly: true, hint: ''},
    {label: 'Unit Price by UOM', formControl: 'unitPriceByUOM', type: 'text', readonly: true, hint: ''},
    {label: 'Unit Price*', formControl: 'unitPrice', type: 'unitPrice', readonly: false, hint: 'Unit Discount x Quantity'},
    {label: 'Unit Discount by UOM*', formControl: 'quantity', type: 'quantity', readonly: false, hint: ''},
    {label: 'Unit Discount', formControl: 'unitDiscount', type: 'unitDiscount', readonly: false, hint: ''},
    {label: 'Discount Amount', formControl: 'discountAmount', type: 'discountAmount', readonly: false, hint: ''},
    {label: 'STD Amount*', formControl: 'stdAmount', type: 'money', readonly: true, hint: 'Unit Price x Quantity'},
    {label: 'Net Amount*', formControl: 'netAmount', type: 'netAmount', readonly: false, hint: 'STD Amount - Discount Amount'},
    {label: 'SST/GST/VAT Code', formControl: 'sstCode', type: 'sstCode', readonly: false, hint: ''},
    {label: 'SST/GST/VAT', formControl: 'sst', type: 'money', readonly: true, hint: ''},
    {label: 'Tax Amount', formControl: 'taxAmount', type: 'money', readonly: true, hint: 'SST/GST/VAT x Net Amount'},
    {label: 'WHT Code', formControl: 'whtCode', type: 'whtCode', readonly: false, hint: ''},
    {label: 'WHT', formControl: 'wht', type: 'money', readonly: true, hint: ''},
    {label: 'WHT Amount', formControl: 'whtAmount', type: 'money', readonly: true, hint: 'WHT x Net Amount'},
    {label: 'Txn Amount*', formControl: 'txnAmount', type: 'txnAmount', readonly: false, hint: 'Net Amount + Tax Amount - WHT Amount'},
  ];

  filteredUnitPrice: Observable<string[]>;

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      itemGuid: new FormControl(),
      itemCode: new FormControl('', Validators.compose([Validators.required])),
      itemName: new FormControl('', Validators.compose([Validators.required])),
      uom: new FormControl(),
      pricingScheme: new FormControl(),
      quantity: new FormControl(0, Validators.compose([Validators.required, Validators.min(1)])),
      uomToBaseRatio: new FormControl(),
      unitPriceByUOM: new FormControl(),
      unitPrice: new FormControl('0.00', Validators.compose([Validators.required, Validators.min(0.01)])),
      unitDiscount: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      stdAmount: new FormControl('0.00', Validators.compose([Validators.required, Validators.min(0)])),
      discountAmount: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      netAmount: new FormControl('0.00', Validators.compose([Validators.required, Validators.min(0)])),
      sstCode: new FormControl(''),
      sst: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      whtCode: new FormControl(''),
      wht: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      taxAmount: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      whtAmount: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      txnAmount: new FormControl('0.00', Validators.compose([Validators.required, Validators.min(0.01)])),
      remarks: new FormControl(),
    });
    this.subs.sink = combineLatest([this.item$, this.tax$]).subscribe({next: ([r1, r2]) => {
      const sstCode = r1.bl_fi_mst_item_exts.find(x => x.param_code === 'TAX_CODE_INPUT')?.value_string;
      const sst = r2.sst?.find(s => s.bl_fi_cfg_tax_code.tax_code === sstCode);
      const whtCode = r1.bl_fi_mst_item_exts.find(x => x.param_code === 'TAX_CODE_WITHHOLDING')?.value_string;
      const wht = r2.sst?.find(s => s.bl_fi_cfg_tax_code.tax_code === sstCode);
      this.form.patchValue({
        itemGuid: r1.bl_fi_mst_item_hdr.guid,
        itemCode: r1.bl_fi_mst_item_hdr.code,
        itemName: r1.bl_fi_mst_item_hdr.name,
        sstCode,
        sst: sst ? (parseFloat(sst.bl_fi_cfg_tax_code.tax_rate_txn.toString()) / 100).toFixed(2) : '0.00',
        whtCode,
        wht: wht ? (parseFloat(wht.bl_fi_cfg_tax_code.tax_rate_txn.toString()) / 100).toFixed(2) : '0.00',
        uom: r1.bl_fi_mst_item_hdr.uom,
        pricingScheme: (<any>r1.bl_fi_mst_item_hdr.price_json)?.item_base_uom_pricing[0].price_name
      });
    }});
    this.filteredUnitPrice = combineLatest([this.form.controls['unitPrice'].valueChanges, this.item$]).pipe(
      map(([a, b]) => {
        if (a) {
          return (<any>b.bl_fi_mst_item_hdr.price_json)?.item_base_uom_pricing.filter(option => option.price_amount.includes(a.toString()));
        } else {
          return (<any>b.bl_fi_mst_item_hdr.price_json)?.item_base_uom_pricing;
        }
      })
    );
    this.subs.sink = this.appletSettings$.subscribe({next: resolve => {
      if (resolve?.ENABLE_SST) {
        this.form.controls['sstCode'].enable();
      } else {
        this.form.controls['sstCode'].disable();
        this.form.controls['sst'].patchValue('0.00');
      }
      if (resolve?.ENABLE_WHT) {
        this.form.controls['whtCode'].enable();
      } else {
        this.form.controls['whtCode'].disable();
        this.form.controls['wht'].patchValue('0.00');
      }
    }});
    this.onCalculate();
  }

  onCalculateFromUnitDisc() {
    const quantity = parseInt(this.form.controls['quantity'].value, 10);
    const sst = parseFloat(this.form.controls['sst'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const unitDiscount = parseFloat(this.form.controls['unitDiscount'].value);
    // const netAmount = parseFloat(this.form.controls['netAmount'].value);
    // const taxAmount = parseFloat(this.form.controls['taxAmount'].value);
    // const whtAmount = parseFloat(this.form.controls['whtAmount'].value);
    // const discountAmount = parseFloat(this.form.controls['discountAmount'].value);
    // const stdAmount = parseFloat(this.form.controls['stdAmount'].value);
    this.form.controls['discountAmount'].setValue((quantity * unitDiscount).toFixed(2));
    this.form.controls['netAmount'].setValue(((unitPrice - unitDiscount) * quantity).toFixed(2));
    this.form.controls['taxAmount'].setValue(((quantity * unitPrice - (quantity * unitDiscount)) * sst).toFixed(2));
    this.form.controls['whtAmount'].setValue(((quantity * unitPrice - (quantity * unitDiscount)) * wht).toFixed(2));
    this.form.controls['txnAmount'].setValue(((unitPrice - unitDiscount) * quantity * (1 + sst - wht)).toFixed(2));
    // this.form.controls['whtAmount'].setValue((netAmount * wht).toFixed(2));
    // this.form.controls['stdAmount'].setValue((quantity * unitPrice).toFixed(2));
  }

  onCalculateFromAmountDisc() {
    const quantity = parseInt(this.form.controls['quantity'].value, 10);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    // const taxAmount = parseFloat(this.form.controls['taxAmount'].value);
    // const whtAmount = parseFloat(this.form.controls['whtAmount'].value);
    const sst = parseFloat(this.form.controls['sst'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const discountAmount = parseFloat(this.form.controls['discountAmount'].value);
    if (quantity) {
      this.form.controls['unitDiscount'].setValue((discountAmount / quantity).toFixed(2));
    }
    this.form.controls['netAmount'].setValue((unitPrice * quantity - discountAmount).toFixed(2));
    this.form.controls['taxAmount'].setValue(((quantity * unitPrice - discountAmount) * sst).toFixed(2));
    this.form.controls['whtAmount'].setValue(((quantity * unitPrice - discountAmount) * wht).toFixed(2));
    // this.form.controls['txnAmount'].setValue(((unitPrice * quantity) - discountAmount + taxAmount - whtAmount).toFixed(2));
    this.form.controls['txnAmount'].setValue((
      (unitPrice * quantity) - discountAmount +
      ((quantity * unitPrice - discountAmount) * sst) -
      ((quantity * unitPrice - discountAmount) * wht)).toFixed(2));
  }

  onCalculateFromAmountNet() {
    const quantity = parseInt(this.form.controls['quantity'].value, 10);
    const sst = parseFloat(this.form.controls['sst'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const netAmount = parseFloat(this.form.controls['netAmount'].value);
    const stdAmount = parseFloat(this.form.controls['stdAmount'].value);
    if (quantity) {
      this.form.controls['unitDiscount'].setValue((unitPrice - (netAmount / quantity)).toFixed(2));
    }
    this.form.controls['discountAmount'].setValue((stdAmount - netAmount).toFixed(2));
    this.form.controls['txnAmount'].setValue((netAmount * (1 + sst - wht)).toFixed(2));
  }

  onCalculateFromAmountTxn() {
    const quantity = parseInt(this.form.controls['quantity'].value, 10);
    const sst = parseFloat(this.form.controls['sst'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const txnAmount = parseFloat(this.form.controls['txnAmount'].value);
    const taxAmount = parseFloat(this.form.controls['taxAmount'].value);
    const whtAmount = parseFloat(this.form.controls['whtAmount'].value);
    const stdAmount = parseFloat(this.form.controls['stdAmount'].value);
    if (quantity) {
      this.form.controls['unitDiscount'].setValue((unitPrice - (txnAmount - taxAmount + whtAmount) / quantity).toFixed(2));
    }
    this.form.controls['discountAmount'].setValue((stdAmount - (txnAmount - taxAmount + whtAmount)).toFixed(2));
    this.form.controls['netAmount'].setValue((txnAmount / (1 + sst - wht)).toFixed(2));
  }

  onCalculate() {
    const quantity = parseInt(this.form.controls['quantity'].value, 10);
    const sst = parseFloat(this.form.controls['sst'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const unitDiscount = parseFloat(this.form.controls['unitDiscount'].value);
    const discountAmount = parseFloat(this.form.controls['discountAmount'].value);
    this.form.controls['netAmount'].setValue((quantity * unitPrice - discountAmount).toFixed(2));
    this.form.controls['taxAmount'].setValue(((quantity * unitPrice - discountAmount) * sst).toFixed(2));
    this.form.controls['whtAmount'].setValue(((quantity * unitPrice - discountAmount) * wht).toFixed(2));
    this.form.controls['stdAmount'].setValue((quantity * unitPrice).toFixed(2));
    this.form.controls['discountAmount'].setValue((quantity * unitDiscount).toFixed(2));
    this.form.controls['txnAmount'].setValue(((1 + sst - wht) * ((quantity * unitPrice) - discountAmount)).toFixed(2));
  }

  nullHandler(x: string, value: string) {
    if (x === 'quantity') {
      value ? this.form.controls[x].setValue(value) : this.form.controls[x].setValue(0);
    } else {
      value ? this.form.controls[x].setValue(parseFloat(value).toFixed(2)) : this.form.controls[x].setValue('0.00');
    }
  }

  onParseDelay(x: FormControl, e: string) {
    setTimeout(() => {
      if (x.value === null) {
        x.setValue('0.00');
      }
      if (typeof(x.value) === 'number' && x.value.toFixed(2) === parseFloat(e).toFixed(2)) {
        e ? x.setValue(parseFloat(e).toFixed(2)) : x.setValue('0.00');
      }
      this.onCalculate();
    }, 100);
  }

  onSST(e: TaxCodeContainerModel) {
    this.form.controls['sst'].setValue(
      (parseFloat(e.bl_fi_cfg_tax_code.tax_rate_txn.toString())).toFixed(2)
    );
    this.onCalculate();
  }

  onWHT(e: TaxCodeContainerModel) {
    this.form.controls['wht'].setValue(
      (parseFloat(e.bl_fi_cfg_tax_code.tax_rate_txn.toString())).toFixed(2)
    );
    this.onCalculate();
  }

  onUOMSelected(e: number) {
    this.form.controls['uomToBaseRatio'].patchValue(e);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
