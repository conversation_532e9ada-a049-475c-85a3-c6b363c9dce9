export const menuItems = [
  {
    state: '',
    name: sessionStorage.getItem('tenantCode'),
    type: 'tenant',
    icon: 'https'
  },
  {
    state: `master-list`,
    name: 'Master List',
    type: 'link',
    icon: 'list_alt',
  },
  {
    state: `processes`,
    name: 'Processes',
    type: 'link',
    icon: 'list_alt',
  },
  {
    state: `sales-order`,
    name: 'Sales Order',
    type: 'link',
    icon: 'list_alt',
  }
];

export const settingItems = [
  {
    group: 'System Configuration',
    child: [
      {
        state: 'field-settings',
        name: 'Field Settings'
      },
      {
        state: 'default-selection',
        name: 'Default Selection'
      },
      {
        state: 'printables',
        name: 'Printables'
      },
      {
        state: 'custom-status',
        name: 'Custom Status'
      },
    ]
  }
];

export const personalizationItems = [
  {
    group: 'System Configuration',
    child: [
      {
        state: 'field-settings',
        name: 'Field Settings'
      },
      {
        state: 'personal-default-selection',
        name: 'Default Selection'
      },
    ]
  }
];
