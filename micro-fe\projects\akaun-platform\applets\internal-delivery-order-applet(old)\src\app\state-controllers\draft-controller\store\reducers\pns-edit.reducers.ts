import { Action, createReducer, on } from "@ngrx/store";
import { PNSEditActions } from "../actions";
import { initState, pnsAdapter, PNSEditState } from "../states/pns-edit.states";

export const pnsReducers = createReducer(
    initState,
    // on(InternalSalesOrderActions.selectEntityInit, (state, action) => pnsAdapter.setAll(
    //     action.entity.bl_fi_generic_doc_line.filter(l => l.txn_type === 'PNS'), state)),
    on(PNSEditActions.addPNS, (state, action) => pnsAdapter.addOne({
        guid: state.ids.length,
        ...action.pns
    }, state)),
    on(PNSEditActions.deletePNS, (state, action) => pnsAdapter.removeOne(action.guid, state)),
    on(PNSEditActions.editPNS, (state, action) => pnsAdapter.upsertOne(action.pns, state)),
    on(PNSEditActions.resetPNSSuccess, (state, action) => pnsAdapter.setAll(action.pns, state)),
)

export function reducers(state: PNSEditState | undefined, action: Action) {
    return pnsReducers(state, action);
}