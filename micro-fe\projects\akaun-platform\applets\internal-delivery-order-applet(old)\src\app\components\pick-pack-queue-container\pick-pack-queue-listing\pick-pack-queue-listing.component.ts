import { Component, ChangeDetectionStrategy, ViewChild, ViewEncapsulation } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import {
  Pagination,
  InternalSalesOrderService,
  CustomerService, BranchService, EmployeeService, GenericDocContainerModel, SubQueryService, FinancialEcomsyncItemService, FinancialItemService, PickPackQueueContainerModel, GenericDocSearchCriteriaDtoModel, PickPackQueueSearchDto, JobContainerModel, bl_del_job_dochdr_link_RowClass, bl_del_job_docline_link_RowClass
} from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SubSink } from 'subsink2';
import { pickPackQueueSearchModel } from '../../../models/advanced-search-models/pick-pack-queue.model';
import { Store } from '@ngrx/store';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { map} from 'rxjs/operators';
import * as moment from 'moment';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { RowGroupingDisplayType } from 'ag-grid-community';
import { GroupRowInnerRenderer } from './group-row-inner-renderer.component';
import { PickPackQueueActions } from '../../../state-controllers/pick-pack-queue-controller/store/actions';
import { PickPackQueueSelectors } from '../../../state-controllers/pick-pack-queue-controller/store/selectors';
import { PickPackQueueStates } from '../../../state-controllers/pick-pack-queue-controller/store/states';
import { DateCellRendererComponent } from '../../utilities/date-cell-renderer/date-cell-renderer.component';
import { Column1ViewSelectors } from '../../../state-controllers/pick-pack-queue-view-model-controller/selectors';
import { Column1ViewModelActions } from '../../../state-controllers/pick-pack-queue-view-model-controller/actions';
import { ColumnViewModelStates } from '../../../state-controllers/pick-pack-queue-view-model-controller/states';
import { FormControl, FormGroup } from '@angular/forms';
import { UUID } from 'angular2-uuid';
import { ToastrService } from 'ngx-toastr';
interface LocalState {
    deactivateList: boolean;
  }
  
  @Component({
    selector: 'app-pick-pack-queue-listing',
    templateUrl: './pick-pack-queue-listing.component.html',
    styleUrls: ['./pick-pack-queue-listing.component.css'],
    providers: [ComponentStore],
    encapsulation: ViewEncapsulation.None
  })
  export class PickPackQueueListingComponent extends ViewColumnComponent {
  
    protected subs = new SubSink();
    itemGuid;
    paging = new Pagination();
  
    protected compName = 'Pick Pack Queue Listing';
    protected readonly index = 0;
    protected localState: LocalState;
  
    readonly localState$ = this.viewColFacade.selectLocalState(this.index);
    readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);
  
    toggleColumn$: Observable<boolean>;
    searchModel = pickPackQueueSearchModel;
    pickPackQueues$: Observable<PickPackQueueContainerModel[]>;
    

    gridApi;
    gridColumnApi;
   
    ColumnViewSelectors = Column1ViewSelectors;
    ColumnViewActions = Column1ViewModelActions;

    deliveryType = new FormControl();

  public groupDisplayType: RowGroupingDisplayType = 'groupRows'
public groupRowRendererParams = {
  innerRenderer: GroupRowInnerRenderer,
    // puts a checkbox onto each group row
    checkbox: true,
    // puts a row dragger onto each group row
    //rowDrag: true
};

defaultColDef = {
  flex: 1,
  resizable: true,
  minWidth:150,
  wrapText: true,
  autoHeight: true,
  sortable: true,
  headerComponentParams: {
    template:
      '<div class="ag-cell-label-container" role="presentation">' +
      '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
      '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
      '    <span ref="eSortOrder" class="ag-header-icon ag-sort-order"></span>' +
      '    <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon"></span>' +
      '    <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon"></span>' +
      '    <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon"></span>' +
      '    <span ref="eText" class="ag-header-cell-text" role="columnheader" style="white-space: normal;"></span>' +
      '    <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>' +
      '  </div>' +
      '</div>',
  },
};

  columnsDefs = [
    {headerName: 'Delivery Order No', field: 'bl_fi_generic_doc_hdr.server_doc_1', rowGroup: true, cellStyle: () => ({ 'text-align': 'left' })},
   
    {
      headerName: 'Requested Delivery Date', field:'bl_fi_generic_doc_lines.track_delivery_date_requested', cellStyle: () => ({ "text-align": "left" }),
      valueGetter: (params) => {
        return params.data.bl_fi_generic_doc_lines.track_delivery_date_requested ? moment(params.data.bl_fi_generic_doc_lines.track_delivery_date_requested).format("DD/MM/YYYY") : '';
       },
    },
    { headerName: 'Delivery Region',field: 'bl_fi_generic_doc_lines.del_region_hdr_state', cellStyle: () => ({ 'text-align': 'left' })},
    { headerName: 'Shipping Location',field: 'bl_fi_generic_doc_lines.shippingLocation', cellStyle: () => ({ 'text-align': 'left' })},
    { headerName: 'Item Code',field: 'bl_fi_generic_doc_lines.item_code', cellStyle: () => ({ 'text-align': 'left' })},
    { headerName: 'Item Name',field: 'bl_fi_generic_doc_lines.item_name', cellStyle: () => ({ 'text-align': 'left' })},
    { headerName: 'Delivery Type',field: 'bl_fi_generic_doc_lines.track_delivery_logic', cellStyle: () => ({ 'text-align': 'left' })},
    { headerName: 'Base Quantity',field: 'bl_fi_generic_doc_lines.quantity_base', type: 'numericColumn'},
    { headerName: 'UOM',field: 'bl_fi_generic_doc_lines.uom', cellStyle: () => ({ 'text-align': 'left' })},
    { headerName: 'Qty Pending',field: 'bl_fi_pick_pack_queues.qty_balance', type: 'numericColumn',cellStyle: () => ({ 'text-align': 'left' })},
    {
      headerName: 'Qty To Deliver',
      type: 'numericColumn', editable: true, 
      valueGetter: (params) => {
        return params.data.qty_to_deliver;
      },
      valueSetter: (params) => {
        console.log("ABC",params)
        let newQtyToDeliver = parseInt(params.newValue);
        let valueChanged = newQtyToDeliver <= params.data.bl_fi_pick_pack_queues.qty_balance && newQtyToDeliver >= 0;
        if (valueChanged) {
          params.data.qty_to_deliver = newQtyToDeliver;
          console.log("newQtyToDeliver", newQtyToDeliver);
        }
        return valueChanged;
      },
      cellStyle: () => ({ "text-align": "left", backgroundColor: 'orange'}),
    },
    { headerName: 'Location Balance',field: 'bl_inv_current_location_stock_balances.qty_ledger', type: 'numericColumn', 
    valueFormatter: (params) => {
      return params.value ?? 0;
    }},
    { headerName: 'Company Balance',field: 'bl_inv_current_company_stock_balances.qty_ledger', type: 'numericColumn',  
    valueFormatter: (params) => {
      return params.value ?? 0;
    }},
    //{ headerName: 'Open Queue',field: '', type: 'numericColumn'},
    { headerName: 'Delivery Status',field: 'bl_fi_generic_doc_lines.track_delivery_status', cellStyle: () => ({ 'text-align': 'left' })},
    { headerName: 'Remarks',field: 'bl_fi_generic_doc_lines.track_delivery_remarks', cellStyle: () => ({ 'text-align': 'left' })},
    
  ];


  public rowSelection: 'single' | 'multiple' = 'multiple';

  frameworkComponents = {
    dateCellRenderer: DateCellRendererComponent,
  };

  columnsDefs$ = this.sessionStore.select(SessionSelectors.selectMasterSettings).pipe(
    map((a: any) => [
      ...this.columnsDefs,
      a.ENABLE_CUSTOM_STATUS_LINE_1 ? {
        headerName: a.NAME_CUSTOM_STATUS_LINE_1 ? a.NAME_CUSTOM_STATUS_LINE_1 : 'client_doc_status_01',
        field: 'client_doc_status_01',
        cellStyle: () => ({ 'text-align': 'left' })
      } : { minWidth: 0 },
      a.ENABLE_CUSTOM_STATUS_LINE_2 ? {
        headerName: a.NAME_CUSTOM_STATUS_LINE_2 ? a.NAME_CUSTOM_STATUS_LINE_2 : 'client_doc_status_02',
        field: 'client_doc_status_02',
        cellStyle: () => ({ 'text-align': 'left' })
      } : { minWidth: 0 },
      a.ENABLE_CUSTOM_STATUS_LINE_3 ? {
        headerName: a.NAME_CUSTOM_STATUS_LINE_3 ? a.NAME_CUSTOM_STATUS_LINE_3 : 'client_doc_status_03',
        field: 'client_doc_status_03',
        cellStyle: () => ({ 'text-align': 'left' })
      } : { minWidth: 0 },
      a.ENABLE_CUSTOM_STATUS_LINE_4 ? {
        headerName: a.NAME_CUSTOM_STATUS_LINE_4 ? a.NAME_CUSTOM_STATUS_LINE_4 : 'client_doc_status_04',
        field: 'client_doc_status_04',
        cellStyle: () => ({ 'text-align': 'left' })
      } : { minWidth: 0 },
      a.ENABLE_CUSTOM_STATUS_LINE_5 ? {
        headerName: a.NAME_CUSTOM_STATUS_LINE_5 ? a.NAME_CUSTOM_STATUS_LINE_5 : 'client_doc_status_05',
        field: 'client_doc_status_05',
        cellStyle: () => ({ 'text-align': 'left' })
      } : { minWidth: 0 },
    ])
);


  SQLGuids: string[] = null;
  form: FormGroup;

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    public readonly viewModelStore: Store<ColumnViewModelStates>,
    private viewColFacade: ViewColumnFacade,
    private toastr: ToastrService,
    private soService: InternalSalesOrderService,
    private brchService: BranchService,
    private empService: EmployeeService,
    private cstmrService: CustomerService,
    private sqlService: SubQueryService,
    private subQueryService: SubQueryService,
    private fiItemService: FinancialItemService,
    private ecomSyncItemService: FinancialEcomsyncItemService,
    private readonly store: Store<PickPackQueueStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
    }

    ngOnInit() {
      console.log('On INIT')
      this.toggleColumn$ = this.viewColFacade.toggleColumn$;
      this.subs.sink = this.localState$.subscribe(a => {
        this.localState = a;
        this.componentStore.setState(a);
      });

     
      this.pickPackQueues$ = this.store.select(PickPackQueueSelectors.selectPickPackQueues);
      this.pickPackQueues$.subscribe(resolve => 
        console.log("ppq",resolve)
      );

      this.subs.sink = this.store.select(PickPackQueueSelectors.refreshGenDocListing).subscribe(a => {
        if (a) {
          console.log('PICKPACKQUEUE')
          this.store.dispatch(PickPackQueueActions.loadPickPackQueuesInit({pagination: new Pagination(0,50)}));
          this.store.dispatch(PickPackQueueActions.resetAgGrid());
        }
      });
  
      this.subs.sink = this.store.select(PickPackQueueSelectors.selectGuid).subscribe(guid=>{
        if(guid){
          setTimeout(()=>{
            this.gridApi.forEachNode(a => {
              if (a.data?.bl_fi_generic_doc_hdr.guid === guid) {
              //  console.log("a.data::",a.data.bl_fi_generic_doc_hdr.guid)
                a.setSelected(true);
              }
            });
          },500)
        }
      })

      this.form = new FormGroup({
        deliveryType: new FormControl(),
        height: new FormControl(),
        width: new FormControl(),
        length: new FormControl(),
        weight: new FormControl(),
        remarks: new FormControl(),
        content: new FormControl(),
      });
    }

    onGridReady(params) {
      //params.api.sizeColumnsToFit();
      // params.api.autoSizeColumn = true;
      params.columnApi.autoSizeAllColumns();
      this.gridApi = params.api;
      this.gridColumnApi = params.columnApi;
      this.gridApi.closeToolPanel();
      console.log("Pick pack Queue", this.pickPackQueues$)
    }

    headerHeightSetter() {
      var padding = 20;
      var height = this.headerHeightGetter() + padding;
      this.gridApi.setHeaderHeight(height);
      //this.gridApi.resetRowHeights();
    }

    headerHeightGetter() {
      var columnHeaderTexts = document.querySelectorAll('.ag-header-cell-text');
    
      var columnHeaderTextsArray = [];
    
      columnHeaderTexts.forEach(node => columnHeaderTextsArray.push(node));
    
      var clientHeights = columnHeaderTextsArray.map(
        headerText => headerText.clientHeight
      );
      var tallestHeaderTextHeight = Math.max(...clientHeights);
      return tallestHeaderTextHeight;
    }
    
  
    onToggle(e: boolean) {
      this.viewColFacade.toggleColumn(e);
    }

    onReadyToShip(){
        let createJob:boolean = true;
        const deliveryType = this.form.value.deliveryType;

        let selectedRows = this.gridApi.getSelectedRows();
        console.log(selectedRows);

        let jobContainerArray:JobContainerModel[] = [];

        let GenericDocContainerArray:GenericDocContainerModel[]=[];

        selectedRows.forEach(record=>{
          let genericDocContainerModel = new GenericDocContainerModel();
          genericDocContainerModel.bl_fi_generic_doc_hdr = record.bl_fi_generic_doc_hdr;
          GenericDocContainerArray.push(genericDocContainerModel);
        })

        GenericDocContainerArray = GenericDocContainerArray.filter((value, index, self) =>
          index === self.findIndex((t) => (
            t.bl_fi_generic_doc_hdr.guid === value.bl_fi_generic_doc_hdr.guid
          ))
        )

        selectedRows.forEach(selectedRecord=>{
          GenericDocContainerArray.forEach(record=>{
            if(record.bl_fi_generic_doc_hdr.guid.toString()===selectedRecord.bl_fi_generic_doc_lines.generic_doc_hdr_guid){
              selectedRecord.bl_fi_generic_doc_lines.qty_open =  selectedRecord.qty_to_deliver;
              record.bl_fi_generic_doc_line.push(selectedRecord.bl_fi_generic_doc_lines);
            }
          })
        })

        console.log("GenericDocContainerArray",GenericDocContainerArray);
        
        GenericDocContainerArray.forEach(record=>{
          let jobContainerModel= new JobContainerModel();

          jobContainerModel.bl_del_job_hdr.guid = UUID.UUID();
          jobContainerModel.bl_del_job_hdr.delivery_logic = deliveryType;
          jobContainerModel.bl_del_job_hdr.pickup_date_estimated = record.bl_fi_generic_doc_hdr.track_delivery_time_estimated;
          jobContainerModel.bl_del_job_hdr.pickup_date_planned = record.bl_fi_generic_doc_hdr.track_delivery_time_planned;
          jobContainerModel.bl_del_job_hdr.pickup_date_actual = record.bl_fi_generic_doc_hdr.track_delivery_time_actual;
          jobContainerModel.bl_del_job_hdr.pickup_date_requested = record.bl_fi_generic_doc_hdr.track_delivery_date_requested;
          jobContainerModel.bl_del_job_hdr.delivery_date_estimated = record.bl_fi_generic_doc_hdr.track_delivery_time_estimated;
          jobContainerModel.bl_del_job_hdr.delivery_date_planned = record.bl_fi_generic_doc_hdr.track_delivery_time_planned;
          jobContainerModel.bl_del_job_hdr.delivery_date_actual = record.bl_fi_generic_doc_hdr.track_delivery_time_actual;
          jobContainerModel.bl_del_job_hdr.delivery_date_requested = record.bl_fi_generic_doc_hdr.track_delivery_date_requested;
          jobContainerModel.bl_del_job_hdr.recipient_entity_hdr_guid = record.bl_fi_generic_doc_hdr.doc_entity_hdr_guid;
          jobContainerModel.bl_del_job_hdr.recipient_customer_code = record.bl_fi_generic_doc_hdr.doc_entity_hdr_json['entityId'];
          jobContainerModel.bl_del_job_hdr.recipient_name = record.bl_fi_generic_doc_hdr.delivery_entity_json.shippingInfo['name'];
          jobContainerModel.bl_del_job_hdr.recipient_email = record.bl_fi_generic_doc_hdr.delivery_entity_json.shippingInfo['email'];
          jobContainerModel.bl_del_job_hdr.recipient_phone_no = record.bl_fi_generic_doc_hdr.delivery_entity_json.shippingInfo['phoneNo'];
          jobContainerModel.bl_del_job_hdr.recipient_address_1 = record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ? record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['address_line_1'] : '';
          jobContainerModel.bl_del_job_hdr.recipient_address_2 = record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ? record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['address_line_2'] : '';
          jobContainerModel.bl_del_job_hdr.recipient_address_3 = record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ? record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['address_line_3'] : '';
          jobContainerModel.bl_del_job_hdr.recipient_address_4 = record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ? record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['address_line_4'] : '';
          jobContainerModel.bl_del_job_hdr.recipient_address_5 = record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ? record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['address_line_5'] : '';
          jobContainerModel.bl_del_job_hdr.recipient_postcode = record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ? record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['postal_code'] : '';
          jobContainerModel.bl_del_job_hdr.recipient_city = record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ? record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['city'] : '';
          jobContainerModel.bl_del_job_hdr.recipient_state= record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ? record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['state'] : '';
          jobContainerModel.bl_del_job_hdr.recipient_country= record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress ?  record.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress['country'] : '';
          jobContainerModel.bl_del_job_hdr.to_branch_guid = record.bl_fi_generic_doc_hdr.guid_branch;
          jobContainerModel.bl_del_job_hdr.to_branch_code = record.bl_fi_generic_doc_hdr.code_branch;
          jobContainerModel.bl_del_job_hdr.to_location_guid = record.bl_fi_generic_doc_hdr.guid_store;
          jobContainerModel.bl_del_job_hdr.to_location_code = record.bl_fi_generic_doc_hdr.code_location;
          jobContainerModel.bl_del_job_hdr.descr = record.bl_fi_generic_doc_hdr.doc_desc;
          // jobContainerModel.bl_del_job_hdr.remarks = record.bl_fi_generic_doc_hdr.doc_remarks;
          jobContainerModel.bl_del_job_hdr.guid_region_hdr = record.bl_fi_generic_doc_hdr.del_region_hdr_guid;
          jobContainerModel.bl_del_job_hdr.delivery_status = "CREATED";
          jobContainerModel.bl_del_job_hdr.ext_shipment_height = this.form.value.height;
          jobContainerModel.bl_del_job_hdr.ext_shipment_weight = this.form.value.weight;
          jobContainerModel.bl_del_job_hdr.ext_shipment_length = this.form.value.length;
          jobContainerModel.bl_del_job_hdr.ext_shipment_width = this.form.value.width;
          jobContainerModel.bl_del_job_hdr.remarks = this.form.value.remarks;
          jobContainerModel.bl_del_job_hdr.ext_content = this.form.value.content;
          jobContainerModel.bl_del_job_hdr.entity_hdr_guid = record.bl_fi_generic_doc_hdr.doc_entity_hdr_guid;

          
          let bl_del_job_dochdr_link = new bl_del_job_dochdr_link_RowClass();
          bl_del_job_dochdr_link.delivery_logic = record.bl_fi_generic_doc_hdr.track_delivery_logic;
          bl_del_job_dochdr_link.guid_doc_hdr = record.bl_fi_generic_doc_hdr.guid;
          bl_del_job_dochdr_link.hdr_guid = jobContainerModel.bl_del_job_hdr.guid;
          bl_del_job_dochdr_link.guid = UUID.UUID();
          bl_del_job_dochdr_link.server_doc_type = record.bl_fi_generic_doc_hdr.server_doc_type;
          bl_del_job_dochdr_link.client_doc_type = record.bl_fi_generic_doc_hdr.client_doc_type;
          bl_del_job_dochdr_link.recipient_name = record.bl_fi_generic_doc_hdr.delivery_entity_json.shippingInfo['name'];
          bl_del_job_dochdr_link.recipient_email = record.bl_fi_generic_doc_hdr.delivery_entity_json.shippingInfo['email'];
          bl_del_job_dochdr_link.recipient_contact = record.bl_fi_generic_doc_hdr.delivery_entity_json.shippingInfo['phoneNo'];
          bl_del_job_dochdr_link.guid_region_hdr = record.bl_fi_generic_doc_hdr.del_region_hdr_guid;
          bl_del_job_dochdr_link.delivery_status = "CREATED";
          jobContainerModel.bl_del_job_dochdr_link = bl_del_job_dochdr_link;

          record.bl_fi_generic_doc_line.forEach(lineItem=>{
            if(lineItem.track_delivery_logic !== deliveryType){
              createJob=false;
            }
            let bl_del_job_docline_link = new bl_del_job_docline_link_RowClass();
            bl_del_job_docline_link.delivery_logic = lineItem.track_delivery_logic;
            bl_del_job_docline_link.guid_generic_doc_line = lineItem.guid;
            bl_del_job_docline_link.guid_job_hdr = jobContainerModel.bl_del_job_hdr.guid;
            bl_del_job_docline_link.guid_dochdr_link = bl_del_job_dochdr_link.guid;
            bl_del_job_docline_link.guid_fi_item_hdr = lineItem.item_guid;
            bl_del_job_docline_link.item_code = lineItem.item_code;
            bl_del_job_docline_link.item_name = lineItem.item_name;
            // bl_del_job_docline_link.guid_inv_item_hdr = record.bl_fi_generic_doc_lines.item_name;
            bl_del_job_docline_link.delivery_status = "CREATED";
            bl_del_job_docline_link.qty_to_deliver = lineItem.qty_open;
            bl_del_job_docline_link.recipient_name = record.bl_fi_generic_doc_hdr.delivery_entity_json.shippingInfo['name'];
            bl_del_job_docline_link.recipient_contact = record.bl_fi_generic_doc_hdr.delivery_entity_json.shippingInfo['phoneNo'];
  
            jobContainerModel.bl_del_job_docline_link.push(bl_del_job_docline_link);
          })

          jobContainerArray.push(jobContainerModel);
        })
      
      console.log("jobContainerArray",jobContainerArray);

      if(createJob){
        this.store.dispatch(PickPackQueueActions.createJobFromDeliveryOrderInit({jobContainer:jobContainerArray}));
      }
      else{
        this.toastr.error(
           'Delivery Types Do Not Match',
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 2000
          }
        );
      }
    }
    
    onSearch(e:any){
      console.log("Search Criteria",e);
      const searchDto = new PickPackQueueSearchDto();
      searchDto.server_docs_1 = e.serverDocs;
      searchDto.guid_entities = e.customerGuids;
      searchDto.guid_branches = e.branchGuids;
      searchDto.sales_entity_hdr_guids = e.salesAgentGuids;
      searchDto.created_date_from = e.created_date_from;
      searchDto.created_date_to = e.created_date_to;
      searchDto.date_txn_from = e.transaction_date_from;
      searchDto.date_txn_to = e.transaction_date_to;
      searchDto.server_doc_type = 'INTERNAL_OUTBOUND_DELIVERY_ORDER'
      // searchDto.limit = 50;
      this.store.dispatch(PickPackQueueActions.searchPickPackQueuesInit({searchDto}));
    }
    
    async onRowClicked(entity: GenericDocContainerModel) {
      this.store.dispatch(PickPackQueueActions.selectPricingSchemeLink({ item: entity.bl_fi_generic_doc_line[0] }));
      this.store.dispatch(PickPackQueueActions.setPickPackQueueInLineItem({ entity: entity })); // for serial, bin, batch
      if (entity) {
        if (entity.bl_fi_generic_doc_hdr.client_doc_type === "LAZADA_SALES_ORDER") {
          entity.bl_fi_generic_doc_line[0].unit_price_std = parseFloat(entity.bl_fi_generic_doc_line[0].amount_txn.toString());
          entity.bl_fi_generic_doc_line[0].unit_price_txn = parseFloat(entity.bl_fi_generic_doc_line[0].amount_txn.toString());
          entity.bl_fi_generic_doc_line[0].amount_std = entity.bl_fi_generic_doc_line[0].amount_txn;
          entity.bl_fi_generic_doc_line[0].amount_net = entity.bl_fi_generic_doc_line[0].amount_txn;
          entity.bl_fi_generic_doc_line[0].quantity_base = 1;
          console.log("entity: ", entity)
          this.store.dispatch(PickPackQueueActions.selectLineItemInit({ line: entity.bl_fi_generic_doc_line[0] }));
          this.store.dispatch(PickPackQueueActions.selectLineItem({ lineItem: entity.bl_fi_generic_doc_line[0] }));
          let trying = {
            branch_guid: entity.bl_fi_generic_doc_hdr.guid_branch,
            order_item_ids: entity.bl_fi_generic_doc_ext.find(x => x.param_code === "LAZADA_ORDER_ITEM_ID" && x.guid_doc_line === entity.bl_fi_generic_doc_line[0].guid)?.value_string || null,
            delivery_type: 'Dropship',
            shipment_provider: entity.bl_fi_generic_doc_ext.find(x => x.param_code === "LAZADA_ORDER_ITEM_SHIPMENT_PROVIDER" && x.guid_doc_line === entity.bl_fi_generic_doc_line[0].guid)?.value_string || null,
            shipping_provider: entity.bl_fi_generic_doc_ext.find(x => x.param_code === "LAZADA_ORDER_ITEM_SHIPPING_PROVIDER_TYPE" && x.guid_doc_line === entity.bl_fi_generic_doc_line[0].guid)?.value_string || null,
            tracking_number: entity.bl_fi_generic_doc_line[0].tracking_id || null,
            // tracking_number: entity.bl_fi_generic_doc_ext.find(x => x.param_code === "LAZADA_ORDER_ITEM_TRACKING_NUMBER" && x.guid_doc_line === entity.bl_fi_generic_doc_line[0].guid)?.value_string || null,
            mkt_status: null
          };
          this.store.dispatch(PickPackQueueActions.selectDetailsInit({ entity: trying }))
        }
        else if (entity.bl_fi_generic_doc_hdr.client_doc_type === "SHOPEE_SALES_ORDER") {
          this.store.dispatch(PickPackQueueActions.selectLineItemInit({ line: entity.bl_fi_generic_doc_line[0] }));
          this.store.dispatch(PickPackQueueActions.selectLineItem({ lineItem: entity.bl_fi_generic_doc_line[0] }));
          let trying = {
            branch_guid: entity.bl_fi_generic_doc_hdr.guid_branch,
            order_item_ids: entity.bl_fi_generic_doc_ext.find(x => x.param_code === "SHOPEE_ORDER_ITEM_ID" && x.guid_doc_line === entity.bl_fi_generic_doc_line[0].guid)?.value_string || null,
            delivery_type: 'Dropship',
            shipment_provider: entity.bl_fi_generic_doc_ext.find(x => x.param_code === "SHOPEE_ORDER_ITEM_SHIPMENT_PROVIDER" && x.guid_doc_line === entity.bl_fi_generic_doc_line[0].guid)?.value_string || null,
            shipping_provider: entity.bl_fi_generic_doc_ext.find(x => x.param_code === "SHOPEE_ORDER_ITEM_SHIPPING_PROVIDER_TYPE" && x.guid_doc_line === entity.bl_fi_generic_doc_line[0].guid)?.value_string || null,
            tracking_number: entity.bl_fi_generic_doc_line[0].tracking_id || null,
            mkt_status: null
          };
          this.store.dispatch(PickPackQueueActions.selectDetailsInit({ entity: trying }))
        }
        else {
          this.store.dispatch(PickPackQueueActions.selectLineItemInit({ line: entity.bl_fi_generic_doc_line[0] }));
          this.store.dispatch(PickPackQueueActions.selectLineItem({ lineItem: entity.bl_fi_generic_doc_line[0] }));
          let trying = {
            branch_guid: null,
            order_item_ids: null,
            delivery_type: null,
            shipment_provider: null,
            shipping_provider: null,
            tracking_number: null,
            mkt_status: null
          };
          this.store.dispatch(PickPackQueueActions.selectDetailsInit({ entity: trying }))
        }

        // if (!this.localState.deactivateList) {
        // this.viewColFacade.updateInstance(this.index, {
        //   ...this.localState, deactivateList: true});
        this.viewColFacade.onNextAndReset(this.index, 1);
        // }
      }
      }
    
    DateConvert(date) {
      if (date != null && '' !== date) {
        const moments = moment(date).format('YYYY-MM-DD HH:mm:ss');
        return moments;
      } else {
        const notEdited = ' ';
        return notEdited;
      }
    }

    
    
    getRowStyle = params => {
      if (params.node.footer || params.node.group) {
        return { fontWeight: 'bold' };
      }
    }

    onSortChanged(e) {
      e.api.refreshCells();
    }
    
    onFilterChanged(e) {
      e.api.refreshCells();
    }
    
    ngOnDestroy() {
        this.subs.unsubscribe();
      }
  }