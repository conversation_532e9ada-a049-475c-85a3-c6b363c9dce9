export const menuItems = [
  {
    state: '',
    name: sessionStorage.getItem('tenantCode'),
    type: 'tenant',
    icon: 'https'
  },
  {
    state: `internal-delivery-order`,
    name: 'Delivery Order',
    type: 'link',
    icon: 'list_alt',
  },
  {
    state: `pick-pack-queue`,
    name: 'Pick Pack Queue',
    type: 'link',
    icon: 'local_shipping',
  },
];

export const settingItems = [
  {
    group: 'System Configuration',
    child: [
      {
        state: 'field-settings',
        name: 'Application Settings'
      },
      {
        state: 'default-selection',
        name: 'Default Selection'
      },
      {
        state: 'printable-format-settings',
        name: 'Printable Format Settings'
      },
      // {
      //   state: 'knock-off-settings',
      //   name: 'Knock Off Settings'
      // },
      {
        state: 'custom-status',
        name: 'Custom Status'
      },
    ]
  }
];

export const personalizationItems = [
  {
    group: 'System Configuration',
    child: [
      // {
      //   state: 'field-settings',
      //   name: 'Field Settings'
      // },
      {
        state: 'personal-default-selection',
        name: 'Default Selection'
      },
    ]
  }
];
