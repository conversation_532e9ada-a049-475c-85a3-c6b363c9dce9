import { FormControl, FormGroup } from '@angular/forms';
import { SearchModelV2 } from '../search-model-V2';

export const pickPackQueueSearchModel: SearchModelV2 = {
  label: {
    customer: 'Customer',
    salesAgent: 'Sales Agent',
    branch: 'Branch',
    deliveryRegion: 'Delivery Region',
    creationDate: 'Creation Date',
    transactionDate: 'Transaction Date',
  },
  dataType: {
    customer: 'customer',
    branch: 'branch',
    deliveryRegion: 'deliveryRegion',
    salesAgent: 'salesAgent',
    creationDate: 'date',
    transactionDate: 'date'
  },

  form: new FormGroup({
    customer: new FormControl(),
    branch: new FormControl(),
    deliveryRegion: new FormControl(),
    salesAgent: new FormControl(),
    creationDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    transactionDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
  })
};