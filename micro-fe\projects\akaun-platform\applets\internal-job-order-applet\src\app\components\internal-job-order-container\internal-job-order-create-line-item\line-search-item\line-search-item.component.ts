import { Component, EventEmitter, OnInit, Output, ViewChild } from '@angular/core';
import { FinancialItemContainerModel, FinancialItemService, Pagination, SubQueryService } from 'blg-akaun-ts-lib';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { FISearchModel } from '../../../../models/advanced-search-models';

@Component({
  selector: 'app-line-search-item',
  templateUrl: './line-search-item.component.html',
  styleUrls: ['./line-search-item.component.css']
})
export class LineSearchItemComponent implements OnInit {

  @Output() item = new EventEmitter<FinancialItemContainerModel>();

  protected subs = new SubSink();

  prevIndex: number;
  protected prevLocalState: any;

  searchModel = FISearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Item Code', field: 'bl_fi_mst_item_hdr.code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'bl_fi_mst_item_hdr.name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Category', field: 'bl_fi_mst_item_hdr.category', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'UOM', field: 'bl_fi_mst_item_hdr.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Unit Price', field: 'bl_fi_mst_item_hdr.price_json', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'System Stock Balance', field: 'stockBalQty'},
  ];

  SQLGuids: string[] = null;
  pagination = new Pagination(0, 10, [
    {columnName: 'calcTotalRecords', operator: '=', value: 'true'},
    {columnName: 'orderBy', operator: '=', value: 'date_updated'},
    {columnName: 'order', operator: '=', value: 'DESC'}
  ]);

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private fiService: FinancialItemService,
    private sqlService: SubQueryService
    ) {
  }

  ngOnInit() {}

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        const sortModel = grid.request.sortModel;
        const filterModel = grid.request.filterModel;
        const sortOn = pageSorting(sortModel);
        const filter = pageFiltering(filterModel);
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;
        this.pagination.conditionalCriteria = [
          {columnName: 'calcTotalRecords', operator: '=', value: 'true'},
          {columnName: 'orderBy', operator: '=', value: 'date_updated'},
          {columnName: 'order', operator: '=', value: 'DESC'},
          {
            columnName: 'guids',
            operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          }
        ];
        // this.store.dispatch(InternalPackingOrderActions.loadPackingOrdersInit({request: grid.request}));
        this.subs.sink = this.fiService.getByCriteria(this.pagination, AppConfig.apiVisa).pipe(
        ).subscribe( resolved => {
          const data = sortOn(resolved.data).filter(entity => filter.by(entity));
          const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : resolved.totalRecords) : data.length;
          grid.success({
            rowData: data,
            rowCount: totalRecords
          });
        }, err => {
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderFailed({error: err.message}));
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe(
        {next: resolve => {
          // this.SQLGuids = resolve.data.length ? resolve.data : null;
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }}
      );
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  onRowClicked(entity: FinancialItemContainerModel) {
    if (entity) {
      this.item.emit(entity);
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
