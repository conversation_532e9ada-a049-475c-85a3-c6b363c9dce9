import { Action, createReducer, on } from "@ngrx/store";
import { PNSActions } from "../../draft-controller/store/actions";
import { Column1ViewModelActions } from "../actions";
import {
  initialState,
  Column1ViewModelState,
} from "../states/column_1_view_model.states";

export const Column1ViewModelReducer = createReducer(
  initialState,
  on(
    Column1ViewModelActions.setAdvanceSearch_CustomerField,
    (state, action) => ({
      ...state,
      advanceSearch_Customer_Field: action.customers,
    })
  ),
  on(
    Column1ViewModelActions.setAdvanceSearch_SalesAgentField,
    (state, action) => ({
      ...state,
      advanceSearch_SalesAgent_Field: action.salesAgents,
    })
  ),
  on(Column1ViewModelActions.setAdvanceSearch_BranchField, (state, action) => ({
    ...state,
    advanceSearch_Branch_Field: action.branches,
  })),
  on(
    Column1ViewModelActions.setAdvanceSearch_CreationDateFromField,
    (state, action) => ({
      ...state,
      advanceSearch_CreationDateFrom_Field: action.date,
    })
  ),
  on(
    Column1ViewModelActions.setAdvanceSearch_CreationDateToField,
    (state, action) => ({
      ...state,
      advanceSearch_CreationDateTo_Field: action.date,
    })
  ),
  on(
    Column1ViewModelActions.setAdvanceSearch_TransactionDateFromField,
    (state, action) => ({
      ...state,
      advanceSearch_TransactionDateFrom_Field: action.date,
    })
  ),
  on(
    Column1ViewModelActions.setAdvanceSearch_TransactionDateToField,
    (state, action) => ({
      ...state,
      advanceSearch_TransactionDateTo_Field: action.date,
    })
  ),
  on(
    Column1ViewModelActions.setAdvanceSearch_DeliveryRegionField,
    (state, action) => ({
      ...state,
      advanceSearch_DeliveryRegion_Field: action.region,
    })
  ),
  // on(Column1ViewModelActions.resetAdvanceSearch, (state, action) => {
  //   ...state,
  //   state.searchCriteria = null;
  //   state.advanceSearch_Customer_Field = [];
  //   state.advanceSearch_Branch_Field = [];
  //   state.advanceSearch_CreationDateFrom_Field = null;
  //   state.advanceSearch_CreationDateTo_Field = null;
  //   state.advanceSearch_TransactionDateFrom_Field = null;
  //   state.advanceSearch_TransactionDateTo_Field = null;
  // }),
  on(Column1ViewModelActions.resetAdvanceSearch, (state, action) => ({
    ...state,
    searchCriteria: null,
    advanceSearch_SalesAgent_Field: [],
    advanceSearch_Customer_Field: [],
    advanceSearch_Branch_Field: [],
    advanceSearch_CreationDateFrom_Field: null,
    advanceSearch_CreationDateTo_Field: null,
    advanceSearch_TransactionDateFrom_Field: null,
    advanceSearch_TransactionDateTo_Field: null,
    advanceSearch_DeliveryRegion_Field: null
  }))
);

export function reducer(
  state: Column1ViewModelState | undefined,
  action: Action
) {
  return Column1ViewModelReducer(state, action);
}
