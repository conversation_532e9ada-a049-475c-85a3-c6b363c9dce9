<div class="view-col-table no-tab" fxLayout="column">
    <mat-card-title class="column-title">
      <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
        <div fxFlex="3 0 0">
          <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
            <app-advanced-search-ido fxFlex fxFlex.lt-sm="100" [id]="'po-fi-item'" [advSearchModel]="searchModel" (search)="onSearch($event)"></app-advanced-search-ido>
          </div>
        </div>
        <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
          <mat-form-field class="example-full-width" appearance="outline" class="search-box">
            <mat-label>Search Serial Number</mat-label>
            <button mat-icon-button matSuffix (click)="searchBySerial()">
              <mat-icon>search</mat-icon>
            </button >
            <input matInput placeholder="Search Serial Number" [formControl]="search" type="text">
          </mat-form-field>
          <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
          <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
        </div>
      </div>
    </mat-card-title>
    <div style="height: 80%;">
      <ag-grid-angular #agGrid
      style="height: 100%;"
      class="ag-theme-balham"
      rowSelection="single"
      rowModelType="serverSide"
      serverSideStoreType="partial"
      [getRowClass]="pagination.getRowClass"
      [columnDefs]="columnsDefs"
      [rowData]="[]"
      [paginationPageSize]="pagination.rowPerPage"
      [cacheBlockSize]="pagination.rowPerPage"
      [pagination]="true"
      [animateRows]="true"
      [defaultColDef]="defaultColDef"
      [suppressRowClickSelection]="false"
      [sideBar]="true"
      (rowClicked)="onRowClicked($event.data)"
      (gridReady)="onGridReady($event)">
      </ag-grid-angular>
    </div>
  </div>