<div class="view-col-table no-tab" fxLayout="column">
  <mat-card-title class="column-title">
    <div style="margin-top: 20px">
      Processes Listing
    </div>
    <div style="padding: 10px;" fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
      <div fxFlex="3 0 0">
        <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
          <!-- <button ngClass.xs="blg-button-mobile" #navBtn class="blg-button-icon" mat-button matTooltip="Create" type="button"
          [disabled]="deactivateAdd$ | async"
          (click)="onAdd()">
          <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="assets/images/add.png" alt="add" width="40px" height="40px">
        </button> -->
          <app-advanced-search fxFlex fxFlex.lt-sm="100" [id]="'internal-so'" [advSearchModel]="searchModel" (search)="onSearch($event)"></app-advanced-search>
          <app-column-toggle [currentToggle]="toggleColumn$ | async" (toggleColumn)="onToggle($event)" fxHide.lt-sm></app-column-toggle>
        </div>
      </div>
      <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
        <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
      <mat-form-field class="p-10" fxFlex.gt-sm="20" fxFlex.gt-xs="20" fxFlex="100" appearance="outline">
        <mat-label>Process Type</mat-label>
        <mat-select placeholder="Process Type" formControlName="processType" (selectionChange)="OnProcessTypeChange($event)">
          <mat-option>
            <ngx-mat-select-search [placeholderLabel]="'Process Type'"
              [noEntriesFoundLabel]="'No matching records found'" [formControl]="processTypeOptionsFilterCtrl">
            </ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let eachOption of filteredProcessTypeOptions | async" [value]="eachOption.value">
            {{eachOption.viewValue}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>


  </mat-card-title>
  <div style="height: 80%;">
    <ag-grid-angular #agGrid
    style="height: 100%;"
    class="ag-theme-balham"
    rowSelection="single"
    rowModelType="serverSide"
    serverSideStoreType="partial"
    [getRowClass]="pagination.getRowClass"
    [columnDefs]="columnsDefs$ | async"
    [rowData]="[]"
    [paginationPageSize]="pagination.rowPerPage"
    [cacheBlockSize]="pagination.rowPerPage"
    [pagination]="true"
    [animateRows]="true"
    [defaultColDef]="defaultColDef"
    [suppressRowClickSelection]="false"
    [sideBar]="true"
    (rowClicked)="onRowClicked($event.data)"
    (gridReady)="onGridReady($event)">
    </ag-grid-angular>
  </div>
</div>
