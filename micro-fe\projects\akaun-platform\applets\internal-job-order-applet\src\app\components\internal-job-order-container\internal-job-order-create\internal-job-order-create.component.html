<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Create Job Order No
      </span>
    </div>
    <div fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="5px">
      <button mat-raised-button color="primary" type="button"
      (click)="onReset()">RESET</button>
      <button mat-raised-button color="primary" type="button"
      [disabled]="disableButton()"
      (click)="onSave()">CREATE</button>
    </div>
  </div>
</mat-card-title>
<mat-tab-group  [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Main Details">
    <app-internal-job-order-create-main [appletSettings$]="appletSettings$" [draft$]="draft$" (updateMain)="onUpdateMain($event)" (itemCode)="onItemCode()"></app-internal-job-order-create-main>
  </mat-tab>
  <mat-tab label="Process Instance">
    <app-process-instance-listing (updateGroupGuid)="onGoupNameUpdated($event)"></app-process-instance-listing>
   </mat-tab>
  <!-- <mat-tab label="Department  ">
    <app-internal-job-order-create-department [appletSettings$]="appletSettings$" [dept$]="draft$" (updateDepartment)="onUpdateDepartment($event)"></app-internal-job-order-create-department>
  </mat-tab> -->
  <!-- <mat-tab label="Attachments">
    <app-internal-job-order-create-attachments [localState]="localState$ | async" (next)="onAddAttachments()"></app-internal-job-order-create-attachments>
  </mat-tab> -->
</mat-tab-group>
