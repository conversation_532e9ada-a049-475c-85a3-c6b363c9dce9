import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Input } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { combineLatest } from 'rxjs';
import { SubSink } from 'subsink2';
import { DraftStates } from '../../../../../state-controllers/draft-controller/store/states';
import { HDRSelectors } from '../../../../../state-controllers/draft-controller/store/selectors'
import { InternalDeliveryOrderStates } from '../../../../../state-controllers/internal-delivery-order-controller/store/states';
import { InternalDeliveryOrderSelectors } from '../../../../../state-controllers/internal-delivery-order-controller/store/selectors';

@Component({
  selector: 'app-item-details-delivery-instructions',
  templateUrl: './delivery-instructions.component.html',
  styleUrls: ['./delivery-instructions.component.css'],
})
export class DeliveryInstructionsComponent implements OnInit, OnDestroy {

  @Input() editMode: boolean;

  private subs = new SubSink();

  lineItem$ = this.store.select(InternalDeliveryOrderSelectors.selectLineItem);

  form: FormGroup;
  copyEntity = new FormControl();
  copyRecipient = new FormControl();

  leftColControls = [
    { label: 'Instructions', formControl: 'instructions', type: 'text-area', readonly: false },
    { label: 'Delivery Date', formControl: 'deliveryDate', type: 'date', readonly: false },
  ];

  constructor(
    protected readonly store: Store<InternalDeliveryOrderStates>,
    protected readonly draftStore: Store<DraftStates>) { 
  }

  ngOnInit() {
    this.form = new FormGroup({
      instructions: new FormControl(),
      deliveryDate: new FormControl(),
      from: new FormControl(),
      to: new FormControl(),
      message: new FormControl()
    });
    this.subs.sink = combineLatest([this.copyEntity.valueChanges, this.draftStore.select(HDRSelectors.selectHdr)]).subscribe({
      next: ([a, draft]) => {
        if (a) {
          this.form.patchValue({
            from: draft.property_json?.purchaser?.purchaserName
          })
        } else {
          this.form.patchValue({
            from: null
          })
        }
      }
    });
    this.subs.sink = combineLatest([this.copyRecipient.valueChanges, this.draftStore.select(HDRSelectors.selectHdr)]).subscribe({
      next: ([a, draft]) => {
        if (a) {
          this.form.patchValue({
            to: (<any>draft.doc_entity_hdr_json)?.entityName
          })
        } else {
          this.form.patchValue({
            to: null
          })
        }
      }
    });
    if (this.editMode) {
      this.subs.sink = this.lineItem$.subscribe({ next: (b: any) => {
        this.form.patchValue({
          instructions: b.line_property_json?.delivery_instructions?.instructions,
          deliveryDate: b.line_property_json?.delivery_instructions?.deliveryDate,
          from: b.line_property_json?.delivery_instructions?.from,
          to: b.line_property_json?.delivery_instructions?.to,
          message: b.line_property_json?.delivery_instructions?.message
        });
      }});
      this.subs.sink = this.draftStore.select(HDRSelectors.selectHdr).subscribe({ next: (resolve: any) => {
        // no purchaser
        if (this.form.value.from === resolve.property_json?.purchaser?.purchaserName)
          this.copyEntity.patchValue(true);
        if (this.form.value.to === resolve.doc_entity_hdr_json?.entityName)
          this.copyRecipient.patchValue(true);
      }})
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
