import { Action, createReducer, on } from "@ngrx/store";
import { LinkActions } from "../actions";
import { initState, linkAdapter, LinkState } from "../states/link.states";
import { InternalDeliveryOrderActions } from "../../../internal-delivery-order-controller/store/actions";

export const linkReducers = createReducer(
	initState,
	on(LinkActions.addLink, (state, action) => linkAdapter.addOne({
		guid: state.ids.length,
		...action.link
	}, state)),
	on(LinkActions.deleteLink, (state, action) => linkAdapter.removeOne(action.guid, state)),
	on(LinkActions.editLink, (state, action) => linkAdapter.upsertOne(action.link, state)),
	on(LinkActions.resetLink, (state, action) => linkAdapter.removeAll(state)),
	on(InternalDeliveryOrderActions.resetDeliveryOrder, (state, action) => linkAdapter.removeAll(state)),
	on(InternalDeliveryOrderActions.selectEntityInit, (state, action) => linkAdapter.setAll(action.entity.bl_fi_generic_doc_link, state))
)

export function reducers(state: LinkState | undefined, action: Action) {
	return linkReducers(state, action);
}