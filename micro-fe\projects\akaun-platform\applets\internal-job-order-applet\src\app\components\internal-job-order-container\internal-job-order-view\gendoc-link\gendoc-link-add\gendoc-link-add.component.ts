import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
  ViewChild,
} from "@angular/core";
import { FormGroup } from "@angular/forms";
import { ComponentStore } from "@ngrx/component-store";
import { Store } from "@ngrx/store";
import {
  ApiResponseModel,
  CompanyService,
  FinancialItemContainerModel,
  FinancialItemService,
  InternalJobsheetService,
  InternalSalesOrderService,
  ItemPermissionContainerModel,
  MrpJobOrderGenDocLinkContainerModel,
  Pagination,
  SalesInvoiceService,
  ServiceIssueGenericDocLinkContainerModel,
  SubQueryService,
} from "blg-akaun-ts-lib";
import { Subject, forkJoin, iif, Observable, of } from "rxjs";
import { SubSink } from "subsink2";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";

import { SearchQueryModel } from "projects/shared-utilities/models/query.model";

import { PaginationComponent } from "projects/shared-utilities/utilities/pagination/pagination.component";
import { debounceTime, map, mergeMap, switchMap } from "rxjs/operators";
import { pageFiltering, pageSorting } from "projects/shared-utilities/listing.utils";
import { ToastrService } from "ngx-toastr";
import { InternalJobOrderStates } from "../../../../../state-controllers/internal-job-order-controller/store/states";
import { ViewColumnFacade } from "../../../../../facades/view-column.facade";
import { InternalJobOrderActions } from "../../../../../state-controllers/internal-job-order-controller/store/actions";
import { InternalJobOrderSelectors } from "../../../../../state-controllers/internal-job-order-controller/store/selectors";
import moment from "moment";
import { WholeNumberAGCellEditor } from "projects/shared-utilities/utilities/numeric-ag-cell-editor/whole-number-ag-cell-editor.component";
import { Column1ViewModelActions } from '../../../../../state-controllers/gen-doc-add-view-model-controller/actions';
import { ColumnViewModelStates } from '../../../../../state-controllers/gen-doc-add-view-model-controller/states';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { Column1ViewSelectors } from '../../../../../state-controllers/gen-doc-add-view-model-controller/selectors';
import { GridOptions } from 'ag-grid-enterprise';
import { SessionStates } from "projects/shared-utilities/modules/session/session-controller/states";
import { SessionSelectors } from "projects/shared-utilities/modules/session/session-controller/selectors";

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}
export class ItemSearchData {
  data: Array<{
    guid: string;
    name?: string;
    code?: string;
    status?: string;
    action?: string;
  }> = [];
}
@Component({
  selector: "app-gendoc-link-add",
  templateUrl: "./gendoc-link-add.component.html",
  styleUrls: ["./gendoc-link-add.component.scss"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore],
})
export class SalesOrderLinkAddComponent
  extends ViewColumnComponent
  implements OnInit, OnDestroy {
  bread = "Sales Order Link Add";
  private localState: LocalState;
  breadCrumbs: any[];
  ui: any;
  pagination = new Pagination();
  // extMap: Map<string, any> = new Map<string, any>();
  changePage = false;
  shippingAddress = false;
  billingAddress = false;
  deactivateReturn$;
  item_guid;
  jobOrderHdrGuid: string;
  private columnMoveSubject: Subject<void> = new Subject<void>();
  private debounceTimeMs = 500;
  protected readonly index = 14;
  prevIndex: number;
  private prevLocalState: any;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);

  apiVisa = AppConfig.apiVisa;

  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.localState.deactivateAdd
  );
  readonly deactivateList$ = this.componentStore.select(
    (state) => state.localState.deactivateList
  );

  paging = new Pagination();
  addSuccess = "Add";
  isClicked = "primary";
  getLabel$: Observable<any>;
  gridApi: any;
  toggleColumn$: Observable<boolean>;
  // searchModel = salesInvoiceSearchModel;

  columnsDefs;
  defaultColDef = {
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 150,
    flex: 1,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    floatingFilter: true,
    filter: "agTextColumnFilter",
    menuTabs: ["filterMenuTab"],
    cellStyle: () => ({ textAlign: "left" }),
  };
  serverSideStoreType = 'partial';
  rowModelType = 'serverSide';
  rowSelection;
  selectedItem: any;
  // users that are already added
  addedEntities = new Map<string, string>();
  itemCategory$: Observable<any[]>;
  selectedEntities = new Map<string, string>();
  protected subs = new SubSink();
  item$: Observable<any>;
  packageListing: any = [];
  financialItemContainer: FinancialItemContainerModel[] = [];

  aggFuncs;
  itemData$: Observable<FinancialItemContainerModel[]>;
  checkInv$: Observable<any>;
  checkUomSave$: Observable<boolean>;
  itemInv$: any;
  SQLGuids: string[] = null;
  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;
  gridOptions: GridOptions| undefined;

  constructor(
    public readonly viewModelStore: Store<ColumnViewModelStates>,
    private readonly sessionStore: Store<SessionStates>,
    private toastr: ToastrService,
    private soService: InternalSalesOrderService,
    private companyService : CompanyService,
    protected readonly store: Store<InternalJobOrderStates>,
    private subQueryService: SubQueryService,
    private viewColFacade: ViewColumnFacade,
    private readonly componentStore: ComponentStore<{ localState: LocalState }>
  ) {
    super();
    this.rowSelection = "multiple";
    const customComparator = (valueA, valueB) => {
      if (valueA != null && "" !== valueA && valueB != null && "" !== valueB) {
        return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
      }
    };
    this.columnsDefs = [
      { headerName: 'Sales Order No', field: 'doc_number',  comparator: customComparator, suppressSizeToFit: true, checkboxSelection: true },
      { headerName: 'Posting Status', field: 'posting_status' },
      { headerName: 'Item Code', field: 'item_code' },
      { headerName: 'UOM', field: 'uom' },
      { headerName: 'Order Qty', field: 'order_qty', cellStyle: () => ({ 'text-align': 'right' }) },
      { headerName: 'Qty To Add', field: 'qty_to_add', editable: true, cellEditor: 'agNumericCellEditor', filter: 'agNumberColumnFilter', cellStyle: params => params?.value > params?.data?.order_qty
        ? ({ 'text-align': 'right', 'border': '1px solid red' })
        : params.value === 0
          ? ({ 'text-align': 'right', 'border': '1px dashed blue' })
          : ({ 'text-align': 'right', 'border': '1px dashed green' }),
      },
      { headerName: 'Txn Date', field: 'date_txn', cellStyle: () => ({ 'text-align': 'right' }), type: 'rightAligned',
        valueFormatter: params => moment(params.value).format('YYYY-MM-DD'), filter: 'agDateColumnFilter'
      },
      { headerName: "(P) Production Status", field: "mrp_request_status_production" },
      { headerName: "(S) Schedule Status", field: "mrp_request_status_schedule" },
      { headerName: "(Ex) Existing Stock Status", field: "mrp_request_status_exists" },
      { headerName: "MRP Remarks", field: "mrp_request_remarks" },
    ].map((c) => ({
      ...c,
      width: 30,
      filterParams: {
        debounceMs: 1000,
      },
    }));
  }

  ngOnInit() {
    this.gridOptions={
      components: { agNumericCellEditor: WholeNumberAGCellEditor }

    }
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(
      (resolve) => (this.prevIndex = resolve)
    );
    this.store.select(InternalJobOrderSelectors.selectEntity).subscribe(data => {
      this.item_guid = data.bl_mrp_job_order_hdr.item_guid
      this.jobOrderHdrGuid = data.bl_mrp_job_order_hdr.guid?.toString();
    })
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false,
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  // sumFunction(params) {
  //   // var result = 0;
  //   const date = DateConvert(params);
  //   return date;
  // }

  onSave() {
    const selectedRows = this.gridApi.getSelectedRows();

    //validation
    for (const row of selectedRows) {
      if (!row.qty_to_add) {
        this.viewColFacade.showFailedToast({ message: "Pls fill the 'Qty to Add' column for all selected rows"});
        return
      }
      if (+row.qty_to_add > +row.order_qty) {
        this.viewColFacade.showFailedToast({ message: "Qty to Add cannot be more than Order Qty"});
        return
      }
    }
    selectedRows.forEach(item => {
      let genDocLinkContainer: MrpJobOrderGenDocLinkContainerModel = new MrpJobOrderGenDocLinkContainerModel();
      genDocLinkContainer.bl_mrp_job_order_generic_doc_link.generic_doc_hdr_guid = item.hdr_guid;
      genDocLinkContainer.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid = item.line_guid;
      genDocLinkContainer.bl_mrp_job_order_generic_doc_link.job_order_guid = this.jobOrderHdrGuid;
      genDocLinkContainer.bl_mrp_job_order_generic_doc_link.txn_type = "PNS";
      genDocLinkContainer.bl_mrp_job_order_generic_doc_link.ad_hoc_quantity = item.qty_to_add;
      this.store.dispatch(InternalJobOrderActions.createGenDocLinkInit({
        genDocLink: genDocLinkContainer
      }));
    })

    this.addSuccess = "Success";
    this.isClicked = "buttonSuccess";
    setTimeout(() => {
      this.addSuccess = "Add";
      this.isClicked = "primary";
      this.onReturn();
    }, 500);
  }

  pageFiltering(filterModel) {
    const noFilters = Object.keys(filterModel).length <= 0;
    if (noFilters) {
      return {
        by: () => true,
        isFiltering: noFilters,
      };
    }
    return {
      by: (viewModel) =>
        Object.keys(filterModel)
          .map((col) => {
            if (!viewModel[col]) {
              return false;
            }
            return typeof viewModel[col] === "number"
              ? +viewModel[col] === +filterModel[col].filter
              : viewModel[col]
                .toLowerCase()
                .includes(filterModel[col].filter.toLowerCase());
          })
          .reduce((p, c) => p && c),
      isFiltering: noFilters,
    };
  }

  pageSorting(sortModel) {
    return (data) => {
      if (sortModel.length <= 0) {
        return data;
      }
      let newData = data.map((o) => o);
      sortModel.forEach((model) => {
        const col = model.colId;
        newData =
          model.sort === "asc"
            ? newData.sort((p, c) => 0 - (p[col] > c[col] ? -1 : 1))
            : newData.sort((p, c) => 0 - (p[col] > c[col] ? 1 : -1));
      });
      return newData;
    };
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        // this.store.dispatch(PurchaseOrderActions.loadPurchaseOrderInit({request: grid.request}));
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;
        this.pagination.conditionalCriteria = [
          { columnName: 'line_txn_type', operator: '=', value: 'PNS' },
          { columnName: 'item_guid', operator: '=', value: this.item_guid },
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
          { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          { columnName: 'order', operator: '=', value: 'DESC' },
          { columnName: 'line_guids', operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          },
          { columnName: 'unfulilled_job_order', operator: '=', value: 'true' }
        ];
        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);
        this.subs.sink = this.soService.getLinesByCriteria(this.pagination, apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<any>[] = [];
            b.data.forEach(doc => source.push(
              this.soService.getByGuid(doc.bl_fi_generic_doc_hdr.guid.toString(), apiVisa).pipe(
                map((b_a) => {
                  let aggContra = 0; // aggregate Contra
                  let data;
                  doc.bl_fi_generic_doc_line.forEach(line => {
                    let companyName;
                    this.companyService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_comp.toString(),this.apiVisa).subscribe(data => {
                      companyName = data.data.bl_fi_mst_comp.name;
                    })
                    data = {
                      hdr_guid: doc.bl_fi_generic_doc_hdr.guid,
                      server_doc_type_hdr: doc.bl_fi_generic_doc_hdr.server_doc_type,
                      line_guid: line.guid,
                      server_doc_type_line: line.server_doc_type,
                      doc_number: doc.bl_fi_generic_doc_hdr.server_doc_1,
                      item_guid: line.item_guid,
                      item_code: line.item_code,
                      item_name: line.item_name,
                      item_type: line.item_sub_type,
                      order_qty: line.quantity_base,
                      open_qty: Number(line.quantity_base) + aggContra,
                      unit_price: null,
                      created_date: line.created_date,
                      date_txn: line.date_txn,
                      posting_status: doc.bl_fi_generic_doc_hdr.posting_status,
                      // uom: 'UNIT',
                      uom: line.uom,
                      serialNo : (<any>line).serial_no?.serialNumbers?.toString(),
                      expiryDate : doc.bl_fi_generic_doc_hdr.due_date,
                      company : doc.bl_fi_generic_doc_hdr.guid_comp,
                      branch : doc.bl_fi_generic_doc_hdr.guid_branch,
                      location : doc.bl_fi_generic_doc_hdr.guid_store,
                      customer: doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid,
                      salesInvoiceDate : line.date_txn,
                      // status: 'OPEN',
                      status: null,
                      mrp_request_status_production: line.mrp_request_status_production,
                      mrp_request_status_schedule: line.mrp_request_status_schedule,
                      mrp_request_status_exists: line.mrp_request_status_exists,
                      mrp_request_remarks: line.mrp_request_remarks,
                      qty_to_add: 0,
                    }
                  })
                  return data;
                })
              )
            ));
            return iif(() => b.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                b.data = <any>b_inner;
                return b
              })),
              of(b)
            );
          })
        ).subscribe( resolved => {
          // this.store.dispatch(PurchaseOrderActions.loadPurchaseOrderSuccess({ totalRecords: resolved.totalRecords }));
          const data = sortOn(resolved.data).filter(entity => filter.by(entity));
          const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : resolved.totalRecords) : data.length;
          grid.success({
            rowData: data,
            rowCount: totalRecords
          });
        }, err => {
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({
        next: resolve => {
          this.SQLGuids = resolve.data;
          if(this.SQLGuids.length!==0 || this.SQLGuids.length<=50){
            // this.gridApi.refreshServerSideStore();
            this.pagination.conditionalCriteria = [
              {
                columnName: 'guids', operator: '=',
                value: this.SQLGuids.toString()
              }
            ];

          }else
          {
            this.toastr.error("Result Set Too Large. Please Refine Search", "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 2000,
            });
          }
        }
      });
    } else {
      this.SQLGuids = null;
    }
  }


  setCriteria(columnName, value) {
    return { columnName, operator: '=', value }
  }

  clear() {
    const dataSource = {
      getRows(params: any) {
        params.successCallback([], 0);
      },
    };
    this.gridApi.setServerSideDatasource(dataSource);
  }
  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }
  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  saveColumnStateToLocal(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.viewModelStore.dispatch(Column1ViewModelActions.setGenDocAddListing_State({genDocAddListingState:serializedColumnState}))
  }
  ngAfterViewInit() {
    // Listen for column movement and debounce the action dispatch
    this.subs.add(
      this.columnMoveSubject
        .pipe(debounceTime(this.debounceTimeMs))
        .subscribe(() => {
          this.saveColumnStateToLocal(this.gridOptions.columnApi);
          this.saveColumnStateToBackend(this.gridOptions.columnApi);
        })
    );

    this.subs.sink = this.viewModelStore
      .select(Column1ViewSelectors.selectGenDocAddListing_State)
      .subscribe((data) => {
        if (!data) {
          this.subs.sink = this.sessionStore
            .select(SessionSelectors.selectPersonalSettings)
            .subscribe((data) => {
              if (data.genDocAddListingState) {
                this.viewModelStore.dispatch(
                  Column1ViewModelActions.setGenDocAddListing_State({
                    genDocAddListingState: data.genDocAddListingState,
                  })
                );
              }
            });
        }
      });

    setTimeout(()=>{
      let serializedColumnState;
      this.subs.sink = this.viewModelStore.select(Column1ViewSelectors.selectGenDocAddListing_State).subscribe(data=>{
        if(data){
          serializedColumnState=data;
        }
      })
      if (serializedColumnState) {
        const newColumnState = JSON.parse(serializedColumnState);
        const currentColumnState = this.gridOptions.columnApi.getColumnState();
        const currentColumnIds = new Set(currentColumnState.map(column => column.colId));

        const hiddenColumns = {};
          currentColumnState.forEach(column => {
            if (column.hide) {
              hiddenColumns[column.colId] = true;
            }
          });
        const filteredNewColumnState = newColumnState.filter(column => currentColumnIds.has(column.colId));
        const combinedColumnState = filteredNewColumnState.map(column => {
            if (hiddenColumns[column.colId] !== undefined) {
              column.hide = hiddenColumns[column.colId];
            }
            return column;
          });
          this.gridOptions.columnApi.applyColumnState({
            state: combinedColumnState,
            applyOrder: true, // Set this to true to forcefully apply the state
          });
      }

    },2000);



  }

  // Define a function to save column state to the backend
  saveColumnStateToBackend(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.sessionStore.dispatch(SessionActions.saveColumnStateInit({ settings: {genDocAddListingState:serializedColumnState} }));
   }
  ngAfterViewChecked() {
    // Listen for column visibility changes
    this.gridOptions.api.addEventListener("columnVisible", (event) => {
      this.columnMoveSubject.next();
    });
    // Listen for column movement
    this.gridOptions.api.addEventListener("columnMoved", (event) => {
      this.columnMoveSubject.next();
    });
  }
}
