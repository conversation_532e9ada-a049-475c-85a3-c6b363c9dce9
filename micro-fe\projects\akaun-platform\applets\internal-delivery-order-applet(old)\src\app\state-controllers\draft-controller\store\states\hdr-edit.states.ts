import { bl_fi_generic_doc_hdr_RowClass } from 'blg-akaun-ts-lib';

export const initState: bl_fi_generic_doc_hdr_RowClass = {
    guid: null,
    doc_source_type: 'INTERNAL',
    server_doc_type: 'INTERNAL_OUTBOUND_DELIVERY_ORDER',
    server_doc_location: null,
    server_doc_1: null,
    server_doc_2: null,
    server_doc_3: null,
    server_doc_4: null,
    server_doc_5: null,
    client_guid: null,
    client_doc_type: 'INTERNAL_OUTBOUND_DELIVERY_ORDER',
    client_doc_1: null,
    client_doc_2: null,
    client_doc_3: null,
    client_doc_4: null,
    client_doc_5: null,
    guid_comp: null,
    guid_branch: null,
    guid_store: null,
    guid_profit_center: null,
    guid_segment: null,
    guid_project: null,
    guid_dimension: null,
    foreign_ccy: null,
    base_doc_guid: null,
    base_doc_ccy: null,
    base_doc_xrate: null,
    doc_ccy: null,
    amount_cogs: null,
    amount_std: <any>'0.00',
    amount_net: <any>'0.00',
    amount_discount: <any>'0.00',
    amount_tax_gst: <any>'0.00',
    amount_tax_wht: <any>'0.00',
    amount_txn: <any>'0.00',
    amount_json: null,
    amount_open_balance: <any>'0.00',
    amount_gst_balance: null,
    amount_wht_balance: null,
    amount_signum: 0,
    doc_reference: null,
    doc_desc: null,
    doc_remarks: null,
    doc_reference_tax_num: null,
    doc_reference_tax_date: null,
    doc_entity_hdr_guid: null,
    doc_entity_hdr_json: null,
    doc_entity_line_guid: null,
    doc_entity_line_json: null,
    gst_entity_hdr_guid: null,
    gst_entity_hdr_json: null,
    gst_entity_tax_num: null,
    gst_entity_type: null,
    gst_entity_contact_json: null,
    display_entity_json: null,
    delivery_entity_json: null,
    foreign_references_json: null,
    label_json: null,
    status_json: null,
    property_json: null,
    status_client: null,
    status_server: null,
    billing_json: null,
    credit_terms_json: null,
    log_json: null,
    date_txn: new Date(),
    process: null,
    status: null,
    doc_level: null,
    revision: null,
    vrsn: null,
    created_by_subject_guid: null,
    updated_by_subject_guid: null,
    created_date: null,
    updated_date: null,
    due_date: null,
    module_guid: null,
    applet_guid: null,
    acl_config: null,
    acl_policy: null,
    code_company: null,
    code_branch: null,
    code_location: null,
    contact_key_guid: null,
    member_guid: null,
    client_doc_status_01: null,
    client_doc_status_02: null,
    client_doc_status_03: null,
    client_doc_status_04: null,
    client_doc_status_05: null,
    contact_hdr_guid: null,
    client_entity_code: null,
    client_doc_date_01: null,
    client_doc_date_02: null,
    client_doc_date_03: null,
    client_doc_date_04: null,
    client_doc_date_05: null,
    guid_store_2: null,
    tracking_id: null,
    client_value: null,
    pic_entity_01: null,
    pic_entity_02: null,
    pic_entity_03: null,
    arap_pns_amount: null,
    arap_stlm_amount: null,
    arap_doc_open: null,
    arap_contra: null,
    arap_bal: null,
    posting_cashbook: null,
    posting_inventory: null,
    posting_journal: null,
    posting_tax_gst: null,
    posting_tax_wht: null,
    posting_membership: null,
    posting_running_no: null,
    posting_status: null,
    client_key: null,
    cfg_production: null,
    cfg_delivery: null,
    del_region_hdr_guid: null,
    del_region_hdr_reg_code: null,
    del_region_hdr_state: null,
    track_production_logic: null,
    track_production_table: null,
    track_production_guid: null,
    track_production_id: null,
    track_production_time_estimated: null,
    track_production_time_actual: null,
    track_production_time_planned: null,
    track_production_pic_name: null,
    track_production_pic_contact: null,
    track_production_remarks: null,
    track_production_status: null,
    track_delivery_logic: null,
    track_delivery_table: null,
    track_delivery_guid: null,
    track_delivery_id: null,
    track_delivery_time_estimated: null,
    track_delivery_time_actual: null,
    track_delivery_time_planned: null,
    track_delivery_pic_name: null,
    track_delivery_pic_contact: null,
    track_delivery_remarks: null,
    track_delivery_status: null,
    reseller_entity_hdr_guid: null,
    reseller_member_hdr_guid: null,
    sales_entity_hdr_guid: null,
    sales_member_hdr_guid: null,
    far_hdr_guid: null,
    device_guid: null,
    track_delivery_date_requested: null,
    pickup_branch_guid: null,
    valid_days: null,
    end_datetime: null,
    delivery_branch_guid: null,
    delivery_branch_code: null,
    delivery_location_guid: null,
    delivery_location_code: null,
    doc_internal_remarks: null,
    custom_status: null,
    marketplace_status: null,
    landed_cost_amount: null,
    apportion_type: null,
    wf_process_hdr_guid: null,
    wf_process_status_guid: null,
    wf_resolution_guid: null,
    sales_entity_hdr_name: null
};
