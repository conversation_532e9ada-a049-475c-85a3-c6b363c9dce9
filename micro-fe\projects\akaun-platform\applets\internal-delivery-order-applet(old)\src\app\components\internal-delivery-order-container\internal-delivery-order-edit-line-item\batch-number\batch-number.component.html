<form [formGroup]="form">
    <div class="view-col-forms">
      <div fxLayout="row wrap" fxLayoutAlign="start center">
        <mat-form-field fxFlex="80" fxFlex.lt-sm="70" appearance="outline">
          <mat-label>Batch Number</mat-label>
          <input matInput [formControl]="form.controls['batch_no']" type="text" (click)="onSelectBatch()">
          <mat-hint class="text-danger" *ngIf="batchInvalid">The batch number is invalid</mat-hint>
        </mat-form-field>
        <div fxFlex="20" fxFlex.lt-sm="40" style="margin-bottom: 20px;">
          <button mat-raised-button color="primary" type="button" [disabled]="disableAdd()" (click)="onAdd()">ADD</button>
        </div>
        <mat-form-field fxFlex="80" fxFlex.lt-sm="70" appearance="outline">
          <mat-label>Quantity</mat-label>
          <input matInput [formControl]="form.controls['qty']" type="number" (change)="checkQtyInput($event.target.value)">
          <mat-hint class="text-danger" *ngIf="invalidQty">The quantity is invalid</mat-hint>
        </mat-form-field>
        <mat-form-field fxFlex="80" fxFlex.lt-sm="70" appearance="outline">
          <mat-label>Issue Date</mat-label>
          <input matInput placeholder="Issue Date" [matDatepicker]="issueDatePicker"
            [formControl]="form.controls['issue_date']" autocomplete="off" readonly
            (click)="issueDatePicker.open()">
          <mat-datepicker-toggle matSuffix [for]="issueDatePicker"></mat-datepicker-toggle>
          <mat-datepicker touchUi="true" #issueDatePicker></mat-datepicker>
        </mat-form-field>
        <mat-form-field fxFlex="80" fxFlex.lt-sm="70" appearance="outline">
          <mat-label>Expiry Date</mat-label>
          <input matInput placeholder="Issue Date" [matDatepicker]="expiryDatePicker"
            [formControl]="form.controls['expiry_date']" autocomplete="off" readonly
            (click)="expiryDatePicker.open()">
          <mat-hint class="text-danger" *ngIf="dateInvalid">The expiry date must be after the issue date</mat-hint>
          <mat-datepicker-toggle matSuffix [for]="expiryDatePicker"></mat-datepicker-toggle>
          <mat-datepicker touchUi="true" #expiryDatePicker></mat-datepicker>
        </mat-form-field>
      </div>
    </div>
  </form>
  <div>
    <div style="text-align: right;">
      <span style="float: left;">Quantity: {{ total }}</span>
      <mat-checkbox [formControl]="selectAll" (click)="onSelectAll()">Select All</mat-checkbox>
    </div>
    <fieldset>
      <legend>Batch Numbers</legend>
      <mat-selection-list #matList style="text-align: left; max-height: 30vh; overflow: auto;">
        <ng-container *ngFor="let b of batchNumbers">
          <mat-list-option [value]="b" (click)="onSelect($event)">
            <div matLine>
              <p fxFlex="40">{{ b.batch_no }}</p>
              <p fxFlex="10">{{ b.qty }}</p>
              <p fxFlex="25" style="text-align:center">{{ b.issue_date.toISOString().split('T')[0] }}</p>
              <p fxFlex="25" style="text-align:center">{{ b.expiry_date.toISOString().split('T')[0] }}</p>
            </div>
          </mat-list-option>
          <mat-divider></mat-divider>
        </ng-container>
      </mat-selection-list>
    </fieldset>
    <div style="text-align: right; margin-top: 10px;">
      <button mat-raised-button color="warn" type="button" (click)="onRemove()">REMOVE</button>
    </div>
  </div>