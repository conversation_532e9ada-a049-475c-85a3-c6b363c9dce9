import { bl_fi_generic_doc_single_line, bl_inv_bin_line_RowClass } from 'blg-akaun-ts-lib';

// TODO: This one should be shared across MRP applets
export interface SalesOrderState {
    currentProcess: string;
    selectedSalesOrder: any;
    updateAgGrid: boolean;
    updatedForm: any;
}

export const initState: SalesOrderState = {
    currentProcess: null,
    selectedSalesOrder: null,
    updateAgGrid: false,
    updatedForm: null
};
