const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/internal-sales-order-applet/runtime-es2015.js',
    './dist/internal-sales-order-applet/polyfills-es2015.js',
    './dist/internal-sales-order-applet/scripts.js',
    './dist/internal-sales-order-applet/main-es2015.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/internal-sales-order-applet');
  await concat(files, './elements/akaun-platform/applets/internal-sales-order-applet/internal-sales-order-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
