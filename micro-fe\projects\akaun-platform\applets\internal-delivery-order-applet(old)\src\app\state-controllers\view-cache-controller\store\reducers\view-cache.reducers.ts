import { Action, createReducer, on } from '@ngrx/store';
import { ViewCacheActions } from '../actions';
import { initialState, ViewCacheState } from '../states/view-cache.states';

export const viewCacheFeatureKey = 'viewCache';

export const viewCacheReducer = createReducer(
  initialState,
  on(ViewCacheActions.cacheInternalDO, (state, action) => ({ ...state, internalDO: action.cache })),
  on(ViewCacheActions.cachePrintableFormatSettings, (state, action) => ({ ...state, printableFormatSettings: action.cache })),
  on(ViewCacheActions.cachePickPackQueue, (state, action) => ({...state, pickPackQueue: action.cache})),
);

export function reducer(state: ViewCacheState | undefined, action: Action) {
  return viewCacheReducer(state, action);
}


