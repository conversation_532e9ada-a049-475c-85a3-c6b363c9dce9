// import { Component, OnInit, ChangeDetectionStrategy, Input, EventEmitter, Output, OnDestroy, ViewChild } from '@angular/core';
// import { Store } from '@ngrx/store';
// import {
//   BranchService,
//   CompanyService,
//   CustomerService,
//   GenericDocContainerModel,
//   LocationService,
//   Pagination,
//   InternalSalesOrderService, 
//   SubQueryService,
//   InternalInboundStockTransferService,
//   TenantUserProfileService} from 'blg-akaun-ts-lib';
// import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
// import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
// import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
// import { AppConfig } from 'projects/shared-utilities/visa';
// import { Observable, zip, forkJoin, <PERSON><PERSON><PERSON>, of, iif } from 'rxjs';
// import { catchError, map, mergeMap } from 'rxjs/operators';
// import { SubSink } from 'subsink2';
// import { internalDeliveryOrderSearchModel } from '../../../../models/advanced-search-models/internal-delivery-order.model';
// import { InternalDeliveryOrderActions } from '../../../../state-controllers/internal-delivery-order-controller/store/actions';
// import { InternalDeliveryOrderStates } from '../../../../state-controllers/internal-delivery-order-controller/store/states';

// @Component({
//   selector: 'app-internal-delivery-order-create-line-item-ST-GRN',
//   templateUrl: './internal-delivery-order-create-line-item-ST-GRN.component.html',
//   styleUrls: ['./internal-delivery-order-create-line-item-ST-GRN.component.css']
// })
// export class InternalDeliveryOrderCreateLineItemSTGRNComponent1 implements OnInit, OnDestroy {

//   @Input() localState: any;
//   pagination = new Pagination();
//   @Output() next = new EventEmitter<any>();
//   SQLGuids: string[] = null;
//   @ViewChild(PaginationComponent) paginationComp: PaginationComponent;
//   private subs = new SubSink();
//   @Output() addLineItem = new EventEmitter();
//   searchModel = internalDeliveryOrderSearchModel;

//   defaultColDef = {
//     filter: 'agTextColumnFilter',
//     floatingFilterComponentParams: {suppressFilterButton: true},
//     minWidth: 200,
//     flex: 2,
//     sortable: true,
//     resizable: true,
//     suppressCsvExport: true
//   };

//   gridApi;

//   columnsDefs = [
//     {headerName: 'Doc No', field: 'bl_fi_generic_doc_hdr.server_doc_1', cellStyle: () => ({'text-align': 'left'})},
//     {headerName: 'Location From', field: 'locationFrom', cellStyle: () => ({'text-align': 'left'})},
//     {headerName: 'Location To', field: 'locationTo', cellStyle: () => ({'text-align': 'left'})},
//     {headerName: 'Customer Name', field: 'createdByName', cellStyle: () => ({'text-align': 'left'}),
//     valueFormatter: params => params.value.entityName},
//   ];

//   constructor(
//     private readonly store: Store<InternalDeliveryOrderStates>,
//     private stgrnService: InternalInboundStockTransferService,
//     private subQueryService: SubQueryService,
//     private locationService: LocationService,
//     private profileService: TenantUserProfileService,
//     // private cstmrService: CustomerService
//     ) { }
//     ngOnInit() {}
//     onGridReady(params) {
//       this.gridApi = params.api;
//       this.gridApi.closeToolPanel();
//       this.setGridData();
//     }
  
//     setGridData(criteria?: any) {
//       const apiVisa = AppConfig.apiVisa;
//       const datasource = {
//         getRows: grid => {
//           this.pagination.conditionalCriteria = [
//             { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
//             { columnName: 'orderBy', operator: '=', value: 'updated_date' },
//             { columnName: 'order', operator: '=', value: 'DESC' },
//           ];
//           const filter = pageFiltering(grid.request.filterModel);
//           const sortOn = pageSorting(grid.request.sortModel);
//           this.subs.sink = this.stgrnService.getByCriteria(this.pagination, apiVisa).pipe(
//             mergeMap(b => {
//               const source: Observable<GenericDocContainerModel>[] = [];
//               b.data.forEach(doc => source.push(
//                 zip(
//                   this.locationService
//                     .getByGuid(
//                       doc.bl_fi_generic_doc_hdr.guid_store.toString(),
//                       apiVisa
//                     )
//                     .pipe(catchError((err) => of(err))),
//                   this.locationService
//                     .getByGuid(
//                       doc.bl_fi_generic_doc_hdr.guid_store_2.toString(), 
//                       apiVisa
//                       )
//                       .pipe(catchError((err) => of(err))),
//                   this.profileService
//                     .getProfileName(
//                       apiVisa,
//                       doc.bl_fi_generic_doc_hdr.created_by_subject_guid.toString(), 
//                       )
//                       .pipe(catchError((err) => of(err)))
//                   ).pipe(
//                     map(([b_a, b_b, b_c]) => {
//                       doc = Object.assign(
//                         {
//                           locationFrom: b_a.error
//                             ? b_a.error.code
//                             : b_a.data.bl_inv_mst_location.name,
//                           locationTo: b_b.error
//                             ? b_b.error.code
//                             : b_b.data.bl_inv_mst_location.name,
//                           createdByName: b_c.error
//                             ? b_c.error.code
//                             : b_c.data
//                         },doc);
//                       return doc;
//                     })
//                   )
//               )
//               );
//               return iif(() => b.data.length > 0,
//                 forkJoin(source).pipe(map((b_inner) => {
//                   b.data = b_inner;
//                   return b
//                 })),
//                 of(b)
//               );
//             })
//           ).subscribe(resolved => {
//             const data = sortOn(resolved.data).filter(entity => filter.by(entity));
//             const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : resolved.totalRecords) : data.length;
//             grid.success({
//               rowData: data,
//               rowCount: totalRecords
//             });
//             this.gridApi.forEachNode(a => {
//               if (a.data.item_guid === this.localState.selectedItem) {
//                 a.setSelected(true);
//               }
//             });
//           }, err => {
//             grid.fail();
//           });
//         }
//       };
//       this.gridApi.setServerSideDatasource(datasource);
//     }
  
//     onSearch(e: SearchQueryModel) {
//       if (!e.isEmpty) {
//         const sql = {
//           subquery: e.queryString,
//           table: e.table
//         };
//         this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({
//           next: resolve => {
//             this.SQLGuids = resolve.data;
//             this.paginationComp.firstPage();
//             this.gridApi.refreshServerSideStore();
//           }
//         });
//       } else {
//         this.SQLGuids = null;
//         this.paginationComp.firstPage();
//         this.gridApi.refreshServerSideStore();
//       }
//     }
  
//     // onRowClicked(lineItem) {
//     //   let column = this.gridApi.getFocusedCell().column.getColId();
//     //   if (lineItem && column != 'unit_price') {
//     //     this.addLineItem.emit(lineItem);
//     //   }
//     // }
//     onRowClicked(entity: GenericDocContainerModel) {
//       if (entity) {
//         this.store.dispatch(InternalDeliveryOrderActions.selectDoc({entity}));
//         this.next.emit();
//       }
//     }
  
//     ngOnDestroy() {
//       this.subs.unsubscribe();
//     }

// }
