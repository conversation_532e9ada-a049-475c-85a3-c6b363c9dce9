import { InternalJobOrderActions } from '../actions';
import { Action, createReducer, on } from '@ngrx/store';
import { initState } from '../states/internal-job-order.states';
import { InternalJobOrderState } from '../states/internal-job-order.states';
import { AttachmentActions } from '../../../draft-controller/store/actions';

export const InternalJobOrderFeatureKey = 'jobOrder';

export const InternalJobOrderReducer = createReducer(
  initState,
  on(InternalJobOrderActions.selectItem, (state, action) => ({
    ...state,
    selectedItem: action.entity
  })),
  on(InternalJobOrderActions.selectJobOrder, (state, action) => ({
    ...state,
    selectedJobOrder: action.entity
  })),
  on(InternalJobOrderActions.selectLineItemInit, (state, action) => ({
    ...state,
    selectedLineItem: action.line
  })),

  on(InternalJobOrderActions.createJobOrderNoSuccess, (state, action) => ({
    ...state, updateAgGrid: true
  })),
  on(InternalJobOrderActions.editJobOrderNoSuccess, (state, action) => ({
    ...state, updateAgGrid: true
  })),
  on(InternalJobOrderActions.deleteJobOrderNoSuccess, (state, action) => ({
    ...state, updateAgGrid: true
  })),
  on(InternalJobOrderActions.issueJobOrderSuccess, (state, action) => ({
    ...state, updateAgGrid: true
  })),
  on(InternalJobOrderActions.resetAgGrid, (state, action) => ({
    ...state, updateAgGrid: false
  })),
  on(InternalJobOrderActions.selectLineItemSuccess, (state, action) => ({
    ...state, selectedItem: action.entity
  })),
  on(InternalJobOrderActions.resetJobOrder, (state, action) => ({
    ...state,
    selectedCustomer: null,
    selectedBillingAddress: null,
    selectedShippingAddress: null,
  })),
  on(InternalJobOrderActions.resetJobOrderEdit, (state, action) => ({
    ...state,
    selectedCustomerEdit: null,
    selectedBillingAddressEdit: null,
    selectedShippingAddressEdit: null,
  })),
  // on(AttachmentActions.uploadAttachmentsSuccess, (state, action) => ({
  //   ...state,
  //   selectedEntity: {
  //     ...state.selectedEntity,
  //     bl_fi_generic_doc_ext: [...state.selectedEntity.bl_fi_generic_doc_ext, ...action.ext]}
  // })),
  on(InternalJobOrderActions.selectEntity, (state, action) => ({
    ...state,
    selectedEntity: action.entity
  })),
  on(InternalJobOrderActions.selectGenericDocLinkGuid, (state, action) => ({
    ...state, genDocLinkGuid: action.guid
  })),
  on(InternalJobOrderActions.selectEditMode, (state, action) => ({
    ...state, editMode: action.editMode
  })),
  on(InternalJobOrderActions.selectTotalContainerQty, (state, action) => ({
    ...state, totalContainerQty: action.totalContainerQty
  })),
  on(InternalJobOrderActions.selectProcessInstance, (state, action) => ({
    ...state, processInstance: action.processInstance
  })),
  
);

export function reducer(state: InternalJobOrderState | undefined, action: Action) {
  return InternalJobOrderReducer(state, action);
}
