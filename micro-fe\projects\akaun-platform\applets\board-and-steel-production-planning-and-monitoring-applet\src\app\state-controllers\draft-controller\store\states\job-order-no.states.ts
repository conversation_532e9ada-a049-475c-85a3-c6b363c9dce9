import { bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';

export const initState: bl_mrp_job_order_hdr_RowClass = {
    guid: null,
    namespace: null,
    module_guid: null,
    applet_guid: null,
    server_doc_type: null,
    client_doc_type: null,
    guid_parent: null,
    guid_comp: null,
    guid_branch: null,
    guid_store: null,
    guid_profit_center: null,
    guid_segment: null,
    guid_project: null,
    guid_dimension: null,
    guid_glcode: null,
    label_json: null,
    foreign_ccy: null,
    base_doc_line_guid: null,
    base_doc_line_ccy: false,
    base_doc_line_xrate: 0,
    doc_ccy: null,
    amount_cogs: 0,
    amount_std: 0,
    amount_discount: 0,
    amount_net: 0,
    tax_gst_code: null,
    tax_gst_type: null,
    tax_gst_rate: 0,
    amount_tax_gst: 0,
    tax_wht_type: null,
    tax_wht_code: null,
    tax_wht_rate: 0,
    amount_tax_wht: 0,
    amount_txn: 0,
    amount_json: null,
    amount_open_balance: 0,
    amount_signum: 0,
    item_guid: null,
    item_code: null,
    item_machine_code: null,
    item_name: null,
    item_desc: null,
    item_remarks: null,
    item_reference_no: null,
    item_custom_template_json: null,
    item_custom_data_json: null,
    item_property_json: null,
    item_child_json: null,
    item_to_item_link_json: null,
    sort_code: null,
    line_option_json: null,
    txn_type: null,
    txn_code: null,
    reference_no: null,
    quantity_signum: 0,
    quantity_base: 0,
    unit_price_by_uom: 0,
    qty_by_uom: 0,
    unit_disc_by_uom: 0,
    client_key: null,
    client_source: null,
    client_hashed_value: null,
    uom_json: null,
    line_property_json: null,
    position_id: null,
    date_txn: null,
    log_json: null,
    created_date: null,
    updated_date: null,
    created_by_subject_guid: null,
    updated_by_subject_guid: null,
    status: 'ACTIVE',
    revision: null,
    vrsn: null,
    acl_config: null,
    acl_policy: null,
    qty_open: 0,
    serial_no: null,
    batch_no: null,
    t2t_doc_hdr_guid: null,
    t2t_doc_line_guid: null,
    client_data_source: null,
    client_item_guid: null,
    client_item_code: null,
    entity_line_guid: null,
    point_type: null,
    point_amount: 0,
    point_currency: null,
    entity_fi_item_link_guid: null,
    entity_inv_item_link_guid: null,
    t2t_tenant_guid: null,
    coupon_hdr_guid: null,
    coupon_line_guid: null,
    item_txn_type: null,
    item_sub_type: null,
    client_value: null,
    client_doc_status_01: null,
    client_doc_status_02: null,
    client_doc_status_03: null,
    client_doc_status_04: null,
    client_doc_status_05: null,
    guid_store_2: null,
    arap_pns_amount: 0,
    arap_stlm_amount: 0,
    tracking_id: null,
    mrp_prodsys_hdr_guid: null,
    uom: null,
    container_qty: 0,
    ad_hoc_qty: 0,
    total_container_measure: 0,
    process_status: null,
    estimated_pkg_date: null,
    completion_date: null,
    remarks: null,
    qc_output_status: null,
    server_doc_1: null,
    template_group_guid: null
};

