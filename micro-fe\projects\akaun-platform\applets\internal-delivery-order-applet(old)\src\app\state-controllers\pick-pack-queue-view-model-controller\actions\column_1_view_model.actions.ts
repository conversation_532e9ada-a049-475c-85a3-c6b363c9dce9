import { createAction, props } from "@ngrx/store";

export const resetAdvanceSearch = createAction('[Search] Reset Advance Search');

export const setAdvanceSearch_CustomerField = createAction(
  "[Column 4 View Model] Set Advance Search Customer Field",
  props<{ customers: [] }>()
);

export const setAdvanceSearch_SalesAgentField = createAction(
  "[Column 4 View Model] Set Advance Search Sales Agent Field",
  props<{ salesAgents: [] }>()
);

export const setAdvanceSearch_BranchField = createAction(
  "[Column 4 View Model] Set Advance Search Branch Field",
  props<{ branches: [] }>()
);

export const setAdvanceSearch_CreationDateFromField = createAction(
  "[Column 4 View Model] Set Advance Search Creation Date From Field",
  props<{ date: string }>()
);

export const setAdvanceSearch_CreationDateToField = createAction(
  "[Column 4 View Model] Set Advance Search Creation Date To Field",
  props<{ date: string }>()
);

export const setAdvanceSearch_TransactionDateFromField = createAction(
  "[Column 4 View Model] Set Advance Search Transaction Date From Field",
  props<{ date: string }>()
);

export const setAdvanceSearch_TransactionDateToField = createAction(
  "[Column 4 View Model] Set Advance Search Transaction Date To Field",
  props<{ date: string }>()
);

export const setAdvanceSearch_DeliveryRegionField = createAction(
  "[Column 4 View Model] Set Advance Search Delivery Region Field",
  props<{ region: [] }>()
);
