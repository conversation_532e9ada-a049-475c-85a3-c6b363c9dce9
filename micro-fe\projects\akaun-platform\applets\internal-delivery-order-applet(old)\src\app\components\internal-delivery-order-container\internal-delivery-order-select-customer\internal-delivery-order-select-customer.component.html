<div class="view-col-table no-tab" fxLayout="column">
  <mat-card-title class="column-title">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
      [disabled]="deactivateReturn$ | async"
      (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Select Customer
      </span>
    </div>
    <div fxLayout="row" fxLayoutAlign="space-between left" fxLayoutGap="3px" [style.padding]="'10px 0px'">
      <mat-slide-toggle [(ngModel)]="toggleMode" (change)="toggle($event)" #slide >
        <span>{{slide.checked?'Create/Edit Mode':'Select Mode'}}</span>
      </mat-slide-toggle>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
      <div fxFlex="3 0 0">
        <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
          <!-- <button ngClass.xs="blg-button-mobile" #navBtn class="blg-button-icon" mat-button matTooltip="Create" type="button"
            [disabled]="deactivateAdd$ | async"
            (click)="onNext()">
            <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="assets/images/add.png" alt="add" width="40px" height="40px">
          </button> -->
          <button *ngIf="toggleMode" ngClass.xs="blg-button-mobile" #navBtn class="blg-button-icon" mat-button matTooltip="Create"
          type="button" [disabled]="deactivateAdd$ | async" (click)="onNext()" >
          <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="assets/images/add.png" alt="add"
            width="40px" height="40px">
        </button>
          <app-advanced-search-ido class="mobile" fxFlex fxFlex.lt-sm="100  " [id]="'customer'" [advSearchModel]="searchModel" (search)="onSearch($event)"></app-advanced-search-ido>
        </div>
      </div>
      <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
        <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
  </mat-card-title>
  <div style="height: 80%;">
    <ag-grid-angular
    #agGrid
    id="grid"
    style="height: 100%"
    class="ag-theme-balham"
    [getRowClass]="pagination.getRowClass"
    [columnDefs]="columnsDefs"
    [rowData]="[]"
    [paginationPageSize]="pagination.rowPerPage"
    [cacheBlockSize]="pagination.rowPerPage"
    [pagination]="true"
    [animateRows]="true"
    [defaultColDef]="defaultColDef"
    [suppressRowClickSelection]="false"
    [rowSelection]="'single'"
    [sideBar]="true"
    [rowModelType]="'serverSide'"
    [serverSideStoreType]="'partial'"
    [detailCellRendererParams]="detailCellRendererParams"
    [applyColumnDefOrder]="true"
    [masterDetail]="true"
    (rowClicked)="onRowClickedNoContact($event.data)"
    (gridReady)="onGridReady($event)"
    >
    </ag-grid-angular>
  </div>
</div>
