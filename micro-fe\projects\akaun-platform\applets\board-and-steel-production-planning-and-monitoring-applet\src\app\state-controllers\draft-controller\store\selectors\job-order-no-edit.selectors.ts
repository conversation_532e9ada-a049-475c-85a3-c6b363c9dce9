import { createSelector } from '@ngrx/store';
import { selectDraftState } from '../..';

export const selectJobOrderNo = createSelector(
  selectDraftState,
  (s1) => s1.jobOrderNoEdit
);

export const selectSegment = (state) => state.draft.jobOrderNoEdit.guid_segment;
export const selectProfitCenter = (state) => state.draft.jobOrderNoEdit.guid_profit_center;
export const selectProject = (state) => state.draft.jobOrderNoEdit.guid_project;
export const selectDimension = (state) => state.draft.jobOrderNoEdit.guid_dimension;
