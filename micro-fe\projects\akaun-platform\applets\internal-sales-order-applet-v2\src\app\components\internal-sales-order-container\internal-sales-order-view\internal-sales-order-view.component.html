<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        {{title | async}} Internal Sales Order
      </span>
    </div>
    <div fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="5px" *ngIf="isNotShadow()">
      <!-- <button mat-raised-button color="primary" type="button" *ngIf="showClone()" [disabled]="disableCloneBtn$ | async" (click)="clone()">CLONE</button> -->
      <button mat-raised-button color="primary" type="button"
      *ngIf="showDraft()" (click)="onDraft()"
      >DRAFT</button>
      <button mat-raised-button color="primary" type="button"
      *ngIf="showFinal()"
      (click)="onFinal()">FINAL</button>
      <button mat-raised-button color="primary" type="button"
      [disabled]="(disableButton() || !hasUpdatePermission) || (disableShopeePickUp$ | async)"
      (click)="onSave()" *ngIf="!appletSettings.HIDE_GENDOC_SAVE_BUTTON || !genDocLock">SAVE</button>
      <button #menuTrigger mat-flat-button color="primary" type="button" [matMenuTriggerFor]="menu"><mat-icon>arrow_drop_down</mat-icon></button>
      <mat-menu #menu="matMenu">
        <button mat-menu-item color="primary" type="button" *ngIf="showClone()" [disabled]="disableCloneBtn$ | async" (click)="clone()">CLONE</button>
        <button mat-menu-item color="primary" type="button" *ngIf="showDiscard()" (click)="onDiscard()">DISCARD</button>
      </mat-menu>
      <button mat-raised-button color="warn" type="button" (click)="onClose()" *ngIf="showClose()">CLOSE</button>
    </div>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async" *ngIf="!showPanels()">
  <mat-tab label="Search Document" *ngIf="(hdr$ | async).status==='TEMP'">
    <ng-template matTabContent>
      <app-search-documents [selectedIndex]="selectedSearchIndex$ | async" [orientation]="orientation"></app-search-documents>
    </ng-template>
  </mat-tab>
  <mat-tab label="Main">
    <div class="with-delete">
      <ng-template mat-tab-label>
        <span [style.color]="mainTabInvalid ? 'red' : 'inherit'" [style.font-weight]="mainTabInvalid ? 'bold' : 'normal'">Main Details</span>
      </ng-template>
      <app-internal-sales-order-create-main [draft$]="hdr$" [appletSettings$]="appletSettings$" (updateMain)="onUpdateMain($event)"
      (member)="onMember()" [parentComponent]="this" [pns$]="pns$" (formStatusChange)="onFormStatusChange($event)"></app-internal-sales-order-create-main>
      <!-- <button mat-raised-button color="primary" type="button" (click)="onDiscard()" *ngIf="showDiscard()">DISCARD</button> -->
    </div>
    <div fxLayout="row"
    fxLayoutAlign="center"
    fxLayoutGap="5px">
      <button mat-raised-button color="warn" *ngIf="showVoid()" (click)="onVoid()">
        VOID
      </button>
    </div>

    <div style="padding: 5px;">
      <button mat-raised-button color="warn" type="button" (click)="onDelete()" *ngIf="deleteCondition()">
        <span>{{ (deleteConfirmation$ | async) ? 'CLICK AGAIN TO CONFIRM' : 'DELETE'}}</span>
      </button>
    </div>
  </mat-tab>
  <mat-tab label="Account">
    <div class="with-delete">
      <ng-template mat-tab-label>
        <span [style.color]="accountTabInvalid ? 'red' : 'inherit'" [style.font-weight]="accountTabInvalid ? 'bold' : 'normal'">Account</span>
      </ng-template>
      <app-internal-sales-order-create-account
      [childSelectedIndex$]="childSelectedIndex$" [draft$]="hdr$"
      (entity)="onCustomer()" (shipping)="onShippingAddress()" (billing)="onBillingAddress()"
      (shippingInfo)="onUpdateShippingInfo($event)" (billingInfo)="onUpdateBillingInfo($event)"
      (updateShippingAddress)="onUpdateShippingAddress($event)" (updateBillingAddress)="onUpdateBillingAddress($event)"
      (accountStatusChange)="onAccountStatusChange($event)" ></app-internal-sales-order-create-account>
    </div>
  </mat-tab>
  <mat-tab label="Line Items">
    <div class="with-delete">
      <ng-template mat-tab-label>
        <span [style.color]="lineTabInvalid ? 'red' : 'inherit'" [style.font-weight]="lineTabInvalid ? 'bold' : 'normal'">Lines</span>
      </ng-template>
      <app-internal-sales-order-create-line-items [draft$]="hdr$" [localState]="localState$ | async" [rowData]="pnsForLineItem$ | async"  (next)="onNextAdd()" (lineItem)="onLineItem($event)"></app-internal-sales-order-create-line-items>
    </div>
  </mat-tab>

  <mat-tab label="ARAP" *ngIf="!appletSettings.HIDE_MAIN_ARAP_TAB">
    <app-arap [draft$]="hdr$"></app-arap>
  </mat-tab>
  <mat-tab label="Delivery Details" *ngIf="!appletSettings.HIDE_DELIVERY_DETAILS_TAB">
      <app-delivery-details [draft$]="hdr$" [rowData]="pns$ | async" (selectDeliveryEntity)="goToSelectDeliveryEntity()" [selectedIndex]="deliveryDetailSelectedIndex$ | async"></app-delivery-details>
  </mat-tab>
  <!-- <mat-tab label="Delivery Trips" *ngIf="!appletSettings.HIDE_DELIVERY_TRIPS_TAB">
    <div class="with-delete">
      <app-internal-sales-order-create-delivery-trips [draft$]="hdr$"></app-internal-sales-order-create-delivery-trips>
    </div>
  </mat-tab> -->
  <mat-tab label="Delivery Plans" *ngIf="!appletSettings.HIDE_DELIVERY_PLANS_TAB">
    <div class="with-delete">
      <add-delivery-plan-listing></add-delivery-plan-listing>
    </div>
  </mat-tab>
  <mat-tab label="Settlement" *ngIf="!appletSettings.HIDE_SETTLEMENT_TAB">
    <div class="with-delete">
      <app-internal-sales-order-create-settlement [localState]="localState$ | async" [draft$]="hdr$" [rowData]="settlement$ | async" [pns]="pns$ | async" (next)="onAddSettlement()" (lineItem)="goToSettlementEdit($event)"></app-internal-sales-order-create-settlement>
    </div>
  </mat-tab>
  <mat-tab label="KO For" *ngIf="(hdr$ | async).status==='TEMP'">
    <ng-template matTabContent>
        <app-import-knock-off></app-import-knock-off>
    </ng-template>
  </mat-tab>

  <mat-tab label="Receipt Voucher" *ngIf="!appletSettings.HIDE_RECEIPT_VOUCHER_TAB">
    <ng-template matTabContent>
    <app-internal-receipt-voucher-listing></app-internal-receipt-voucher-listing>
    </ng-template>
  </mat-tab>

  <mat-tab label="Department Hdr" *ngIf="!appletSettings.HIDE_DEPARTMENT_HDR_TAB">
    <div class="with-delete">
      <app-internal-sales-order-create-department [draft$]="hdr$" (updateDepartment)="onUpdateDepartment($event)"></app-internal-sales-order-create-department>
    </div>
  </mat-tab>

  <mat-tab label="Trace Document" *ngIf="!appletSettings.HIDE_TRACE_DOCUMENT_TAB">
    <app-generic-document-posting [genericDoc$]="draft$" [draft$]="hdr$" >
    </app-generic-document-posting>
  </mat-tab>

  <mat-tab label="Contra" *ngIf="!appletSettings.HIDE_MAIN_CONTRA_TAB">
    <div class="with-delete">
      <ng-template matTabContent>
        <app-internal-sales-order-create-contra [localState]="localState$ | async" [draft$]="hdr$" (next)="onAddContra()" (lineItem)="onViewContra($event)"></app-internal-sales-order-create-contra>
      </ng-template>
    </div>
  </mat-tab>

  <mat-tab label="Doc Link" *ngIf="!appletSettings.HIDE_DOC_LINK_TAB">
    <div class="with-delete">
      <ng-template matTabContent>
      <app-internal-sales-order-view-doc-link [pns$]="pns$"></app-internal-sales-order-view-doc-link>
      </ng-template>
    </div>
  </mat-tab>
  <mat-tab label="Attachments" *ngIf="!appletSettings.HIDE_ATTACHMENT_TAB">
    <div class="with-delete">
      <app-internal-sales-order-create-attachments [rowData]="attachments$ | async" [localState]="localState$ | async" (next)="onAddAttachments()"></app-internal-sales-order-create-attachments>
    </div>
  </mat-tab>
  <mat-tab label="Export" *ngIf="!appletSettings.HIDE_EXPORT_TAB">
    <div class="with-delete">
      <app-internal-sales-order-view-export></app-internal-sales-order-view-export>
    </div>
  </mat-tab>
  <!-- <mat-tab label="Convert" *ngIf="!appletSettings.HIDE_DELIVERY_DETAILS_TAB">
    <div class="with-delete">
      <app-internal-sales-order-view-convert (convert)="onConvert()"></app-internal-sales-order-view-convert>
    </div>
  </mat-tab> -->
  <mat-tab label="Ecomsync" *ngIf="!appletSettings.HIDE_ECOMSYNC_TAB">
    <app-ecom-sync [localState]="localState$" [draft$]="hdr$"></app-ecom-sync>
  </mat-tab>

  <mat-tab label="Status" *ngIf="!appletSettings.HIDE_STATUS_TAB">
    <app-internal-sales-order-custom-status [draft$]="hdr$" [appletSettings$]="appletSettings$" (updateStatus)="onUpdateStatus($event)"></app-internal-sales-order-custom-status>
  </mat-tab>

  <mat-tab label="Events" *ngIf="!appletSettings.HIDE_EVENTS_TAB">
    <ng-template matTabContent>
      <app-internal-sales-order-view-events [draft$]="hdr$"></app-internal-sales-order-view-events>
    </ng-template>
  </mat-tab>
  <mat-tab label="Gross Profit" *ngIf="showGP()">
    <ng-template matTabContent>
      <app-gross-profit-listing></app-gross-profit-listing>
    </ng-template>
  </mat-tab>
  <!-- <mat-tab label="Email">
    <app-internal-sales-order-view-email></app-internal-sales-order-view-email>
  </mat-tab> -->

</mat-tab-group>

<ng-container *ngIf="showPanels()">
  <div class="scrollable-container">
  <mat-accordion [multi]="true">
    <mat-expansion-panel *ngFor="let panel of getFilteredPanels(draft$ | async); let i = index"
                         #panel
                         [expanded]="appletSettings[panel.expandSetting] || (i === expandedPanelIndex)"
                         (opened)="onPanelOpened(i)"
                         [ngClass]="{'expanded-panel': expandedPanelIndex === i}">
        <mat-expansion-panel-header>
          <mat-panel-title>
            <span [style.color]="panel.invalid ? 'red' : 'inherit'" [style.font-weight]="panel.invalid ? 'bold' : 'normal'">
              {{ panel.title }}
            </span>
          </mat-panel-title>
        </mat-expansion-panel-header>

        <ng-container *ngIf="panel.content === 'search-documents'">
          <ng-template matExpansionPanelContent>
            <app-search-documents [orientation]="orientation"></app-search-documents>
          </ng-template>
        </ng-container>

        <ng-container *ngIf="panel.content === 'main'">
          <div class="with-delete">
            <app-internal-sales-order-create-main [draft$]="hdr$" [appletSettings$]="appletSettings$" (updateMain)="onUpdateMain($event)"
            (member)="onMember()" [parentComponent]="this" [pns$]="pns$" (formStatusChange)="onFormStatusChange($event)"></app-internal-sales-order-create-main>
            <button mat-raised-button color="primary" type="button" (click)="onDiscard()" *ngIf="showDiscard()">DISCARD</button>
        </div>

        <div fxLayout="row" fxLayoutAlign="center" fxLayoutGap="5px">
          <button mat-raised-button color="warn" *ngIf="showVoid()" (click)="onVoid()"> VOID </button>
        </div>

        <div style="padding: 5px;">
          <button mat-raised-button color="warn" type="button" (click)="onDelete()" *ngIf="deleteCondition()">
            <span>{{ (deleteConfirmation$ | async) ? 'CLICK AGAIN TO CONFIRM' : 'DELETE'}}</span>
          </button>
        </div>
        </ng-container>

        <ng-container *ngIf="panel.content === 'account'">
          <div class="with-delete">
          <app-internal-sales-order-create-account [childSelectedIndex$]="childSelectedIndex$" [draft$]="hdr$"
            (entity)="onCustomer()" (shipping)="onShippingAddress()" (billing)="onBillingAddress()"
            (shippingInfo)="onUpdateShippingInfo($event)" (billingInfo)="onUpdateBillingInfo($event)"
            (updateShippingAddress)="onUpdateShippingAddress($event)" (updateBillingAddress)="onUpdateBillingAddress($event)"
            (accountStatusChange)="onAccountStatusChange($event)"  [orientation]="orientation">
          </app-internal-sales-order-create-account>
        </div>
        </ng-container>

        <ng-container *ngIf="panel.content === 'line-items'">
          <div class="with-delete">
          <app-internal-sales-order-create-line-items [draft$]="hdr$" [localState]="localState$ | async"
            [rowData]="pnsForLineItem$ | async" (next)="onNextAdd()" (lineItem)="onLineItem($event)">
          </app-internal-sales-order-create-line-items>
          </div>
        </ng-container>

        <ng-container *ngIf="panel.content === 'arap'">
          <app-arap [draft$]="hdr$"></app-arap>
        </ng-container>

        <ng-container *ngIf="panel.content === 'delivery-details' ">
          <app-delivery-details [draft$]="hdr$" [rowData]="pns$ | async"
            (selectDeliveryEntity)="goToSelectDeliveryEntity()" [selectedIndex]="deliveryDetailSelectedIndex$ | async" [orientation]="orientation">
          </app-delivery-details>
        </ng-container>

        <ng-container *ngIf="panel.content === 'delivery-trips'">
          <div class="with-delete">
          <app-internal-sales-order-create-delivery-trips [draft$]="hdr$"></app-internal-sales-order-create-delivery-trips>
          </div>
        </ng-container>

        <ng-container *ngIf="panel.content === 'delivery-plans'">
          <div class="with-delete">
          <add-delivery-plan-listing [draft$]="hdr$"></add-delivery-plan-listing>
          </div>
        </ng-container>


        <ng-container *ngIf="panel.content === 'settlement'">
          <div class="with-delete">
          <app-internal-sales-order-create-settlement [localState]="localState$ | async" [draft$]="hdr$"
            [rowData]="settlement$ | async" [pns]="pns$ | async" (next)="onAddSettlement()"
            (lineItem)="goToSettlementEdit($event)">
          </app-internal-sales-order-create-settlement>
          </div>
        </ng-container>

        <ng-container *ngIf="panel.content === 'ko-for'">
          <ng-template matExpansionPanelContent>
            <app-import-knock-off></app-import-knock-off>
          </ng-template>
        </ng-container>


        <ng-container *ngIf="panel.content === 'receipt-voucher'">
          <ng-template matExpansionPanelContent>
          <app-internal-receipt-voucher-listing></app-internal-receipt-voucher-listing>
        </ng-template>
        </ng-container>

        <ng-container *ngIf="panel.content === 'department-hdr'">
          <div class="with-delete">
          <app-internal-sales-order-create-department [appletSettings$]="appletSettings$" [draft$]="hdr$"
            (updateDepartment)="onUpdateDepartment($event)">
          </app-internal-sales-order-create-department>
        </div>
        </ng-container>

        <ng-container *ngIf="panel.content === 'trace-document'">
          <app-generic-document-posting [genericDoc$]="draft$" [draft$]="hdr$" [orientation]="orientation"></app-generic-document-posting >
        </ng-container>

        <ng-container *ngIf="panel.content === 'contra'">
          <ng-template matExpansionPanelContent>
            <div class="with-delete">
              <app-internal-sales-order-create-contra [localState]="localState$ | async" [draft$]="hdr$" (next)="onAddContra()" (lineItem)="onViewContra($event)"></app-internal-sales-order-create-contra>
            </div>
            </ng-template>
        </ng-container>

        <ng-container *ngIf="panel.content === 'doc-link'">

          <ng-template matExpansionPanelContent>
            <div class="with-delete">
              <app-internal-sales-order-view-doc-link [pns$]="pns$" [orientation]="orientation"></app-internal-sales-order-view-doc-link>
            </div>
          </ng-template>
        </ng-container>

        <ng-container *ngIf="panel.content === 'attachments'">
          <div class="with-delete">
          <app-internal-sales-order-create-attachments [rowData]="attachments$ | async" [localState]="localState$ | async" (next)="onAddAttachments()"></app-internal-sales-order-create-attachments>
        </div>
        </ng-container>

        <ng-container *ngIf="panel.content === 'export'">
          <div class="with-delete">
            <app-internal-sales-order-view-export></app-internal-sales-order-view-export>
        </div>
        </ng-container>

        <ng-container *ngIf="panel.content === 'ecomsync'">
          <app-ecom-sync [localState]="localState$" [draft$]="hdr$"  [orientation]="orientation"></app-ecom-sync>
        </ng-container>


        <ng-container *ngIf="panel.content === 'status'">
          <app-internal-sales-order-custom-status [draft$]="hdr$" [appletSettings$]="appletSettings$"
            (updateStatus)="onUpdateStatus($event)">
          </app-internal-sales-order-custom-status>
        </ng-container>


        <ng-container *ngIf="panel.content === 'events'">
          <ng-template matExpansionPanelContent>
            <app-internal-sales-order-view-events [draft$]="hdr$"></app-internal-sales-order-view-events>
          </ng-template>
        </ng-container>

    </mat-expansion-panel>
  </mat-accordion>
  </div>
</ng-container>
