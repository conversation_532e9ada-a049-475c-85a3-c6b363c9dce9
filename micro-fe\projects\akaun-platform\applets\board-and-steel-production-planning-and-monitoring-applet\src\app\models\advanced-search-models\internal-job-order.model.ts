import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const internalJobOrderSearchModel: SearchModel = {
  label: {
    jobOrderDate: 'Creation Date',
    completionDate: 'Completion Date',
    // itemCode: 'Item Code',
    // itemName: 'Item Name',
    processStatus: 'Process Status',
  },
  dataType: {
    jobOrderDate: 'date',
    completionDate: 'date',

    // itemCode: 'string',
    // itemName: 'string',
    // machineCode: 'string',
    processStatus: ['select', ['PLANNED','IN_PROGRESS','COMPLETED','ON_HOLD','CANCELLED']]
  },
  form: new FormGroup({
    jobOrderDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    completionDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    // itemCode: new FormControl(),
    // itemName: new FormControl(),
    // machineCode: new FormControl(),
    processStatus: new FormControl()
  }),
  query: (query) => `(hdr.server_doc_1 ILIKE '%${query}%' OR hdr.item_code ILIKE '%${query}%' OR hdr.item_name ILIKE '%${query}%')  AND hdr.status = 'ACTIVE'`,
  table: 'bl_mrp_job_order_hdr',
  queryCallbacks: {
    // docNo: query => query ? `hdr.status='${query}'` : '',
    jobOrderDate: jobOrderDate => {
      if (jobOrderDate.from || jobOrderDate.to) {
          const from = jobOrderDate.from ? `hdr.date_txn >= '${jobOrderDate.from.format('YYYY-MM-DD')}'` : '';
          const to = jobOrderDate.to ? `hdr.date_txn <= '${jobOrderDate.to.format('YYYY-MM-DD')}'` : '';
          return `${from} ${(from && to) ? 'AND' : ''} ${to}`;
      }
      return '';
  },
  completionDate: completionDate => {
      if (completionDate.from || completionDate.to) {
          // assign completionDate.from to itself or completionDate.to if null
          const from = completionDate.from ? completionDate.from : completionDate.to;
          // assign creationDate.to to itself or creationDate.from if null
          const to = completionDate.to ? completionDate.to : completionDate.from;
          return `hdr.completion_date >= '${from.format('YYYY-MM-DD')}' AND hdr.completion_date <= '${to.format('YYYY-MM-DD')}'`;
      }
      return '';
  },
  processStatus: query => query ? `hdr.process_status='${query}'` : '',
  }
};

