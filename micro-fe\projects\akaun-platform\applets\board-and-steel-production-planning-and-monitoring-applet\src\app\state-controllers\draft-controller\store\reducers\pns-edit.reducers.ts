import { Action, createReducer, on } from '@ngrx/store';
import { InternalJobOrderActions } from '../../../internal-job-order-controller/store/actions';
import { PNSEditActions } from '../actions';
import { initState, pnsAdapter, PNSEditState } from '../states/pns-edit.states';

export const pnsReducers = createReducer(
    initState,
    on(PNSEditActions.addPNS, (state, action) => pnsAdapter.addOne({
        guid: state.ids.length,
        ...action.pns
    }, state)),
    on(PNSEditActions.deletePNS, (state, action) => pnsAdapter.removeOne(action.guid, state)),
    on(PNSEditActions.editPNS, (state, action) => pnsAdapter.upsertOne(action.pns, state)),
    on(PNSEditActions.resetPNSSuccess, (state, action) => pnsAdapter.setAll(action.pns, state)),
)

export function reducers(state: PNSEditState | undefined, action: Action) {
    return pnsReducers(state, action);
}