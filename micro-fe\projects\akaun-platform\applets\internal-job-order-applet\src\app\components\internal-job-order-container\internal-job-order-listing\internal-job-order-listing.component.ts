import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import {
  BranchService,
  GenericDocSingleLineContainer,
  Pagination,
  SubQueryService,
  GenericDocSingleLineService,
  GenericDocLineContainerModel,
  MrpJobOrderHdrService,
  MrpJobOrderHdrContainerModel,
  ProductionMasterScheduleService} from 'blg-akaun-ts-lib';
import { forkJoin, iif, Observable, of, zip, Subject, } from 'rxjs';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { debounceTime, catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../models/advanced-search-models';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { Store } from '@ngrx/store';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import * as moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { Column1ViewModelActions } from '../../../state-controllers/internal-job-order-view-model-controller/actions';
import { ColumnViewModelStates } from '../../../state-controllers/internal-job-order-view-model-controller/states';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { Column1ViewSelectors } from '../../../state-controllers/internal-job-order-view-model-controller/selectors';
import { GridOptions } from 'ag-grid-enterprise';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRowGuid: string;
}

@Component({
  selector: 'app-internal-job-order-listing',
  templateUrl: './internal-job-order-listing.component.html',
  styleUrls: ['./internal-job-order-listing.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class InternalJobOrderListingComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Job Order No Listing';
  protected readonly index = 0;
  protected localState: LocalState;
  private columnMoveSubject: Subject<void> = new Subject<void>();
  private debounceTimeMs = 500;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  searchModel = internalJobOrderSearchModel;
  SQLGuids: string[] = null;
  pagination = new Pagination();
  authToken = localStorage.getItem("authToken");
  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 130,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;
  gridOptions: GridOptions| undefined ;
  columnsDefs = [
    {headerName: 'Job Order No', field: 'bl_mrp_job_order_hdr.server_doc_1',cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Job Order Date', field: 'bl_mrp_job_order_hdr.date_txn',valueFormatter: params => moment(params.value).format('YYYY-MM-DD'),  type: "rightAligned",},
    {headerName: 'Item Code', field: 'bl_mrp_job_order_hdr.item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'bl_mrp_job_order_hdr.item_name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Customer Name', field: 'customerName', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Ad Hoc Quantity', field: 'bl_mrp_job_order_hdr.ad_hoc_qty',  type: "rightAligned",},
    {headerName: 'SO Quantity', field: 'bl_mrp_job_order_hdr.container_qty',  type: "rightAligned",},
    {headerName: 'Total Container Measure', field: 'bl_mrp_job_order_hdr.total_container_measure',  type: "rightAligned",},
    {headerName: 'UOM', field: 'bl_mrp_job_order_hdr.uom',cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Process Status', field: 'bl_mrp_job_order_hdr.process_status',cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'QC Output Status', field: 'bl_mrp_job_order_hdr.batch_no.bin_code'},
    {headerName: 'Status', field: 'bl_mrp_job_order_hdr.status', cellStyle: () => ({'text-align': 'left'})},
    { headerName: 'Completation Date', field: 'bl_mrp_job_order_hdr.completion_date', type: "rightAligned",
      valueFormatter: (params) => params.value ?  moment(params.value).format("YYYY-MM-DD") : null,
    },
    {headerName: 'Remarks', field: 'bl_mrp_job_order_hdr.remarks', cellStyle: () => ({'text-align': 'left'})},
  ];

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;
  label_hdr_0: any;
  label_hdr_1: any;
  label_hdr_2: any;
  label_hdr_3: any;
  label_hdr_4: any;
  label_hdr_5: any;
  label_hdr_6: any;
  label_hdr_7: any;
  label_hdr_8: any;
  label_hdr_9: any;
  label_hdr_10: any;
  label_hdr_11: any;
  label_hdr_12: any;
  label_hdr_13: any;
  label_hdr_14: any;
  label_hdr_15: any;
  label_hdr_16: any;
  label_hdr_17: any;
  label_hdr_18: any;
  label_hdr_19: any;
  label_hdr_20: any;
  processStatus: any;
  completionDateFrom: string;
  completionDateTo: string;
  creationDateFrom: string;
  creationDateTo: string;
  rowData: any;


  constructor(
    public readonly viewModelStore: Store<ColumnViewModelStates>,
    private viewColFacade: ViewColumnFacade,
    private mrpJobOrderHdrService: MrpJobOrderHdrService,
    private productionMasterSchedule: ProductionMasterScheduleService,
    private sqlService: SubQueryService,
    private toastr: ToastrService,
    private readonly store: Store<InternalJobOrderStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.gridOptions={};
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectRowData).subscribe(resolved => this.rowData = resolved);
    this.subs.sink = this.viewColFacade.rowData$.subscribe(rowData => this.rowData = rowData);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    if (this.rowData.length > 0) {
      this.setRowDataCache();
    } else {
      // this.clear();
      this.setGridData();
    }
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: (grid) => {
        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;
        this.pagination.conditionalCriteria = [
          { columnName: "calcTotalRecords", operator: "=", value: "true" },
          { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          { columnName: 'order', operator: '=', value: 'DESC' },
        ];

        for (let i = 0; i <= 20; i++) {
          this.pagination.conditionalCriteria.push({
          columnName: `label_hdr_${i}_guid`,
          operator: "=",
          value: this[`label_hdr_${i}`] ?? ""
          });
        }

        this.pagination.conditionalCriteria.push({
          columnName: 'process_status',
          operator: "=",
          value: this.processStatus ?? ""
        })

        this.pagination.conditionalCriteria.push({
          columnName: 'completion_date_from',
          operator: "=",
          value: this.completionDateFrom
        })

        this.pagination.conditionalCriteria.push({
          columnName: 'completion_date_to',
          operator: "=",
          value: this.completionDateTo
        })

        this.pagination.conditionalCriteria.push({
          columnName: 'date_txn_from',
          operator: "=",
          value: this.creationDateFrom
        })

        this.pagination.conditionalCriteria.push({
          columnName: 'date_txn_to',
          operator: "=",
          value: this.creationDateTo
        })

        this.subs.sink = this.mrpJobOrderHdrService
          .getByCriteria(this.pagination, apiVisa)
          .subscribe(
            (resolved) => {  
              const totalRecords = filter.isFiltering
                ? this.SQLGuids
                  ? this.SQLGuids.length
                  : resolved.totalRecords
                : resolved.totalRecords;
  
              grid.success({
                rowData: resolved.data,
                rowCount: totalRecords,
              });
              this.viewColFacade.selectRowData(resolved.data);
              this.gridApi.forEachNode((node) => {
                if (
                  node.data?.bl_mrp_job_order_hdr.guid === this.localState.selectedRowGuid
                )
                  node.setSelected(true);
              });
            },
            (err) => {
              grid.fail();
            }
          );
      },
    };
    this.gridApi.setServerSideDatasource(datasource);
   
    this.subs.sink = this.store
      .select(InternalJobOrderSelectors.selectAgGrid)
      .subscribe((resolved) => {
        if (resolved) {
          this.gridApi.refreshServerSideStore();
          this.store.dispatch(
            InternalJobOrderActions.resetAgGrid()
          );
        }
      });
  }

  getSortValue(processStatus) {
    const customSortingOrder = ['IN_PROGRESS','PLANNED','ON_HOLD','COMPLETED','CANCELLED'];
    const processStatusIndex = customSortingOrder.indexOf(processStatus);
    return processStatusIndex !== -1 ? processStatusIndex : customSortingOrder.length;
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onAdd() {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false});
    this.viewColFacade.onNextAndReset(this.index, 2);
  }

  onSearch(e: SearchQueryModel) {
    console.log("abd search model",e);
    if (!e.isEmpty) {
      this.label_hdr_0 = UtilitiesModule.checkNull(e.queryString['category0'],null);
      this.label_hdr_1 = UtilitiesModule.checkNull(e.queryString['category1'],null);
      this.label_hdr_2 = UtilitiesModule.checkNull(e.queryString['category2'],null);
      this.label_hdr_3 = UtilitiesModule.checkNull(e.queryString['category3'],null);
      this.label_hdr_4 = UtilitiesModule.checkNull(e.queryString['category4'],null);
      this.label_hdr_5 = UtilitiesModule.checkNull(e.queryString['category5'],null);
      this.label_hdr_6 = UtilitiesModule.checkNull(e.queryString['category6'], null);
      this.label_hdr_7 = UtilitiesModule.checkNull(e.queryString['category7'], null);
      this.label_hdr_8 = UtilitiesModule.checkNull(e.queryString['category8'], null);
      this.label_hdr_9 = UtilitiesModule.checkNull(e.queryString['category9'], null);
      this.label_hdr_10 = UtilitiesModule.checkNull(e.queryString['category10'], null);
      this.label_hdr_11 = UtilitiesModule.checkNull(e.queryString['category11'], null);
      this.label_hdr_12 = UtilitiesModule.checkNull(e.queryString['category12'], null);
      this.label_hdr_13 = UtilitiesModule.checkNull(e.queryString['category13'], null);
      this.label_hdr_14 = UtilitiesModule.checkNull(e.queryString['category14'], null);
      this.label_hdr_15 = UtilitiesModule.checkNull(e.queryString['category15'], null);
      this.label_hdr_16 = UtilitiesModule.checkNull(e.queryString['category16'], null);
      this.label_hdr_17 = UtilitiesModule.checkNull(e.queryString['category17'], null);
      this.label_hdr_18 = UtilitiesModule.checkNull(e.queryString['category18'], null);
      this.label_hdr_19 = UtilitiesModule.checkNull(e.queryString['category19'], null);
      this.label_hdr_20 = UtilitiesModule.checkNull(e.queryString['category20'], null);
      this.processStatus = UtilitiesModule.checkNull(e.queryString['processStatus'], null);
      const completionDateFrom = UtilitiesModule.checkNull(e.queryString['completionDate']['from'], null);
      const completionDateTo = UtilitiesModule.checkNull(e.queryString['completionDate']['to'], null);
      this.completionDateFrom = completionDateFrom ? moment(completionDateFrom).utc().format('YYYY-MM-DDTHH:mm:ss[Z]') : null;
      this.completionDateTo = completionDateTo ? moment(completionDateTo).utc().format('YYYY-MM-DDTHH:mm:ss[Z]') : null;
      const jobOrderFrom = UtilitiesModule.checkNull(e.queryString['jobOrderDate']['from'], null);
      const jobOrderTo = UtilitiesModule.checkNull(e.queryString['jobOrderDate']['to'], null);
      this.creationDateFrom = jobOrderFrom ? moment(jobOrderFrom).utc().format('YYYY-MM-DDTHH:mm:ss[Z]') : null;
      this.creationDateTo = jobOrderTo ? moment(jobOrderTo).utc().format('YYYY-MM-DDTHH:mm:ss[Z]') : null;
    }
    this.setGridData();
  }

  setRowDataCache() {
    const datasource = {
      getRows: (params) => {
        const rowsThisPage = this.rowData.slice(params.request.startRow, params.request.endRow);
        params.successCallback(rowsThisPage, this.rowData.length);
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onRowClicked(entity: MrpJobOrderHdrContainerModel) {
    if (entity) {
      this.store.dispatch(InternalJobOrderActions.selectEditMode({editMode: true}));
      this.store.dispatch(InternalJobOrderActions.selectEntity({entity: entity}));
      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance<LocalState>(this.index, {
          ...this.localState, deactivateAdd: false, deactivateList: true, selectedRowGuid: entity.bl_mrp_job_order_hdr.guid
        });
        this.viewColFacade.onNextAndReset(this.index, 1);
      }
    }
  }

  GenerateReport(){
    this.productionMasterSchedule.generateReport(this.authToken, AppConfig.apiVisa)
      .then((blob) => {
      const downloadURL = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadURL;
      link.download = "Production_Master_Schedule.csv";
      link.click();
      link.remove();
      this.toastr.success("Master Schedule Downloaded", "Success", {
        tapToDismiss: true,
        progressBar: true,
        timeOut: 1300,
      });
    });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  ngAfterViewInit() {
    // Listen for column movement and debounce the action dispatch
    this.subs.add(
      this.columnMoveSubject
        .pipe(debounceTime(this.debounceTimeMs))
        .subscribe(() => {
          this.saveColumnStateToLocal(this.gridOptions.columnApi);
          this.saveColumnStateToBackend(this.gridOptions.columnApi);
        })
    );
    
    this.subs.sink = this.viewModelStore
      .select(Column1ViewSelectors.selectInternalJobOrderListing_State)
      .subscribe((data) => {
        if (!data) {
          this.subs.sink = this.sessionStore
            .select(SessionSelectors.selectPersonalSettings)
            .subscribe((data) => {
              if (data.internalJobOrderListingState) {
                console.log("data",data.internalJobOrderListingState);
                this.viewModelStore.dispatch(
                  Column1ViewModelActions.setInternalJobOrderListing_State({
                    internalJobOrderListingState: data.internalJobOrderListingState,
                  })
                );
              }
            });
        }
      });
    
    setTimeout(()=>{
      let serializedColumnState;
      this.subs.sink = this.viewModelStore.select(Column1ViewSelectors.selectInternalJobOrderListing_State).subscribe(data=>{
        if(data){
          serializedColumnState=data;
        }
      })
      if (serializedColumnState) {
        const newColumnState = JSON.parse(serializedColumnState);
        const currentColumnState = this.gridOptions.columnApi.getColumnState();
        const currentColumnIds = new Set(currentColumnState.map(column => column.colId));
      
        const hiddenColumns = {};
          currentColumnState.forEach(column => {
            if (column.hide) {
              hiddenColumns[column.colId] = true;
            }
          });
        const filteredNewColumnState = newColumnState.filter(column => currentColumnIds.has(column.colId));
        const combinedColumnState = filteredNewColumnState.map(column => {
            if (hiddenColumns[column.colId] !== undefined) {
              column.hide = hiddenColumns[column.colId];
            }
            return column;
          });
          this.gridOptions.columnApi.applyColumnState({
            state: combinedColumnState,
            applyOrder: true, // Set this to true to forcefully apply the state
          });
          console.log('Column State Applied:', combinedColumnState);
      }
    
    },2000);
  }
  saveColumnStateToLocal(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.viewModelStore.dispatch(Column1ViewModelActions.setInternalJobOrderListing_State({internalJobOrderListingState:serializedColumnState}))
  }
  ngAfterViewChecked() {
    // Listen for column visibility changes
    this.gridOptions.api.addEventListener("columnVisible", (event) => {
      this.columnMoveSubject.next();
    });
    // Listen for column movement
    this.gridOptions.api.addEventListener("columnMoved", (event) => {
      this.columnMoveSubject.next();
    });
  }
  // Define a function to save column state to the backend
  saveColumnStateToBackend(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.sessionStore.dispatch(SessionActions.saveColumnStateInit({ settings: {internalJobOrderListingState:serializedColumnState} }));
  }
}
