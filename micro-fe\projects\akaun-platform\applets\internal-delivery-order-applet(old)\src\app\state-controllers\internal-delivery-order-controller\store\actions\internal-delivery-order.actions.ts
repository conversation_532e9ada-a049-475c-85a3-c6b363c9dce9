import { createAction, props } from '@ngrx/store';
import {
  bl_fi_generic_doc_line_RowClass,
  bl_fi_generic_doc_link_RowClass,
  bl_fi_mst_entity_ext_RowClass, bl_fi_mst_entity_line_RowClass,
  bl_inv_batch_hdr_RowClass,
  bl_inv_bin_hdr_RowClass,
  EntityContainerModel, FinancialItemContainerModel,
  GenericDocContainerModel,
  GenericDocSearchCriteriaDtoModel,
  Pagination,
  PickPackQueueLineDtoModel,
  PricingSchemeLinkContainerModel
} from 'blg-akaun-ts-lib';
import { InternalDOMain, InternalDODepartment } from '../../../../models/internal-delivery-order.model';
import { JobDocContainerModel } from '../../../../models/job-container.model';

// export const loadDeliveryOrdersInit = createAction('[Delivery Order] Load Init', props<{ request: any }>());
// export const loadDeliveryOrderSuccess = createAction('[Delivery Order] Load Success', props<{ totalRecords: number }>());
// export const loadDeliveryOrderFailed = createAction('[Delivery Order] Load Failed', props<{ error: string }>());

export const selectGuid = createAction('[Delivery Order] Select Guid', props<{ guid: string }>());
export const selectEntityInit = createAction('[Delivery Order] Select Entity Item Init', props<{ entity: GenericDocContainerModel, entityType: string }>());
export const selectEntitySuccess = createAction('[Delivery Order] Select Entity Success', props<{ entity: EntityContainerModel }>());
export const selectEntityFailed = createAction('[Delivery Order] Select Entity Failed', props<{ error: string }>());

// export const selectEntity = createAction('[Delivery Order] Select Entity', props<{ entity: EntityContainerModel, entityId: string }>());
export const selectEntity = createAction('[Purchase Order] Select Entity', props<{ entity: { entity: EntityContainerModel, contact: bl_fi_mst_entity_line_RowClass } }>());
export const selectShippingAddress = createAction('[Delivery Order] Select Shipping Address', props<{ shipping_address: any }>());
export const selectBillingAddress = createAction('[Delivery Order] Select Billing Address', props<{ billing_address: any }>());
export const selectContactPerson = createAction('[Delivery Order] Select Contact Person', props<{ line: bl_fi_mst_entity_line_RowClass }>());
export const selectDoc = createAction('[Delivery Order] Select Item', props<{ entity: GenericDocContainerModel }>());
export const selectItem = createAction("[Delivery Order] Select Item Init", props<{ entity: any }>());
export const updateMain = createAction('[Delivery Order] Update Main', props<{ model: InternalDOMain }>());
export const updateDepartment = createAction('[Delivery Order] Update Department', props<{ model: InternalDODepartment }>());
export const updateCustomFields = createAction('[Delivery Order] Update Custom Fields', props<{ model: any }>());
export const resetDraft = createAction('[Delivery Order] Reset Draft');

export const addLineItemToDraftEdit = createAction('[Delivery Order] Add Line Item to Draft Edit', props<{ line: bl_fi_generic_doc_line_RowClass }>());

// export const addLineItemToDraft = createAction('[Delivery Order] Add Line Item to Draft', props<{ line: bl_fi_generic_doc_line_RowClass[] }>());
export const addLineItemToDraft = createAction('[Delivery Order] Add Line Item to Draft', props<{ line: bl_fi_generic_doc_line_RowClass }>());
export const editLineItem = createAction('[Delivery Order] Edit View Line Item', props<{ line: bl_fi_generic_doc_line_RowClass }>());
export const deleteLineItem = createAction('[Delivery Order] Delete View Line Item', props<{ guid: string }>());

export const addLinkToDraft = createAction('[Delivery Order] Add Link to Draft', props<{ link: bl_fi_generic_doc_link_RowClass }>());
export const addLinkToDraftEdit = createAction('[Delivery Order] Add Link to Draft Edit', props<{ link: bl_fi_generic_doc_link_RowClass }>());
``
export const deleteLineItemFromDraftEdit = createAction('[Delivery Order] Delete View Line Item From Draft Edit', props<{ line: bl_fi_generic_doc_line_RowClass, link: bl_fi_generic_doc_link_RowClass }>());
export const removeLineItemFromDraft = createAction('[Delivery Order] Remove View Line Item From Draft', props<{ line: bl_fi_generic_doc_line_RowClass }>());
export const removeLineItemFromDraftEdit = createAction('[Delivery Order] Remove View Line Item From Draft Edit', props<{ line: bl_fi_generic_doc_line_RowClass }>());

export const selectLineItemInit = createAction('[Delivery Order] Select Line Item Init', props<{ lineItem: bl_fi_generic_doc_line_RowClass }>());
export const selectLineItemSuccess = createAction('[Delivery Order] Select Line Item Success', props<{ entity: FinancialItemContainerModel }>());
export const selectLineItemFailed = createAction('[Delivery Order] Select Line Item Failed', props<{ error: string }>());

export const createDeliveryOrderInit = createAction('[Delivery Order] Create Init');
export const createDeliveryOrderSuccess = createAction('[Delivery Order] Create Success', props<{ hdrGuid: string }>());
export const createDeliveryOrderFailed = createAction('[Delivery Order] Create Failed', props<{ error: string }>());

export const deleteDeliveryOrderInit = createAction('[Delivery Order] Delete Init');
export const deleteDeliveryOrderSuccess = createAction('[Delivery Order] Delete Success');
export const deleteDeliveryOrderFailed = createAction('[Delivery Order] Delete Failed', props<{ error: string }>());

export const setEditMode = createAction('[Delivery Order] Set Edit Mode', props<{ editMode: boolean }>());

export const editDeliveryOrderInit = createAction('[Delivery Order] Edit Init');
export const editDeliveryOrderSuccess = createAction('[Delivery Order] Edit Success', props<{ hdrGuid: string }>());
export const editDeliveryOrderFailed = createAction('[Delivery Order] Edit Failed', props<{ error: string }>());

export const resetAgGrid = createAction('[Delivery Order] Reset Ag Grid Update');
export const resetDeliveryOrder = createAction('[Delivery Order] Reset Delivery Order');
export const resetDeliveryOrderEdit = createAction('[Sales Quotation] Reset Edit');


export const updateMainEdit = createAction('[Delivery Order] Update Main Edit', props<{ model: InternalDOMain }>());
export const updateDepartmentEdit = createAction('[Delivery Order] Update Department Edit', props<{ model: InternalDODepartment }>());
export const updateCustomFieldsEdit = createAction('[Delivery Order] Update Custom Fields Edit', props<{ model: any }>());
export const resetDraftEdit = createAction('[Delivery Order] Reset Draft Edit');
// export const selectEntityEdit = createAction('[Delivery Order] Select Entity Edit', props<{ entity: EntityContainerModel, entityId: string }>());
export const selectEntityEdit = createAction('[Purchase Order] Select Entity Edit', props<{ entity: { entity: EntityContainerModel, contact: bl_fi_mst_entity_line_RowClass } }>());
export const selectShippingAddressEdit = createAction('[Delivery Order] Select Shipping Address Edit', props<{ shipping_address: any }>());
export const selectBillingAddressEdit = createAction('[Delivery Order] Select Billing Address Edit', props<{ billing_address: any }>());
export const selectContactPersonEdit = createAction('[Delivery Order] Select Contact Person Edit', props<{ line: bl_fi_mst_entity_line_RowClass }>());
export const addViewLineItem = createAction('[Delivery Order] Add View Line Item', props<{ line: bl_fi_generic_doc_line_RowClass }>());
export const editViewLineItem = createAction('[Delivery Order] Edit View Line Item', props<{ line: bl_fi_generic_doc_line_RowClass }>());
export const deleteViewLineItem = createAction('[Delivery Order] Delete View Line Item', props<{ guid: string }>());

export const selectPricingSchemeLink = createAction('[Delivery Order] Select Pricing Scheme Link', props<{ item: any }>());
export const selectPricingSchemeLinkSuccess = createAction('[Delivery Order] Select Pricing Link Scheme Success', props<{ pricing: PricingSchemeLinkContainerModel[] }>());
export const selectPricingSchemeLinkFailed = createAction('[Delivery Order] Select Pricing Link Scheme Failed', props<{ error: string }>());

export const updateKnockoffListingConfig = createAction('[Delivery Order] Update Knockoff Listing Config', props<{ settings: any }>());

// For Serial, Bin, batch
export const getInvItem = createAction('[Delivery Order] Get Inventory Item', props<{ entity: FinancialItemContainerModel }>());
export const selectInvItem = createAction('[Delivery Order] Select Inventory Item', props<{ invItem }>());
export const noInvItemFound = createAction('[Delivery Order] No Inventory Item Found');
export const selectSerial = createAction('[Delivery Order] Select Serial Number', props<{ serial }>());
export const selectBatch = createAction('[Delivery Order] Select Batch Number', props<{ batch: bl_inv_batch_hdr_RowClass }>());
export const selectBin = createAction('[Delivery Order] Select Bin Number', props<{ bin: bl_inv_bin_hdr_RowClass }>());

export const updatePostingStatusInit = createAction('[Delivery Order] Update Posting Status', props<{ status: any, doc: GenericDocContainerModel }>());
export const updatePostingStatusSuccess = createAction('[Delivery Order] Update Posting Status Success', props<{ doc: GenericDocContainerModel }>());
export const updatePostingStatusFailed = createAction('[Delivery Order] Update Posting Status Failed', props<{ error: string }>());

export const updateAgGrid = createAction('[Delivery Order] Update Ag Grid',props<{ update: boolean }>());

export const selectCompanyGuid = createAction('[Delivery Order] Select Company Guid', props<{ compGuid: string }>());

// export const selectedEntity = createAction('[Delivery Order] Selected Entity Type', props<{ entityType: string }>());
export const createDeliveryOrderGenDocLinkSuccess = createAction('[Delivery Order] Create Gen Doc Link Success');
export const createDeliveryOrderGenDocLinkFailed = createAction('[Delivery Order] Create Gen Doc Link Failed', props<{error: string}>());
export const editDeliveryOrderGenDocLinkSuccess = createAction('[Delivery Order] Edit Gen Doc Link Success');
export const editDeliveryOrderGenDocLinkFailed = createAction('[Delivery Order] Edit Gen Doc Link Failed', props<{error: string}>());

export const chosenEntity = createAction('[Delivery Order] Set Chosen Entity', props<{ chosenEntity: string }>());
export const chosenEntityEdit = createAction('[Delivery Order] Set Chosen Entity in Edit', props<{ chosenEntity: string }>());
//export
export const printJasperPdfInit = createAction('[Delivery Order] Print Jasper Pdf Init', props<{ guid: string }>());
export const printJasperPdfSuccess = createAction('[Delivery Order] Print Jasper Pdf Success');
export const printJasperPdfFailed = createAction('[Delivery Order] Print Jasper Pdf Failed');

export const pickPackQueueAllocationInit = createAction('[Sales Order] Pick Pack Queue Allocation Init', props<{ pickPackQueueAllocation: PickPackQueueLineDtoModel[]}>());
export const pickPackQueueAllocationSuccess = createAction('[Sales Order] Pick Pack Queue Allocation Success');
export const pickPackQueueAllocationFailed = createAction('[Sales Order] Pick Pack Queue Allocation Failed', props<{ error: string }>());

export const loadExternalJobDocsInit = createAction('[Job Docs] Load External Job Docs Init', props<{ pagination: Pagination, dto: any }>());
export const loadExternalJobDocsSuccess = createAction('[Job Docs] Load External Job Docs Success', props<{ jobDocs: JobDocContainerModel[] }>());
export const loadExternalJobDocsFailure = createAction('[Job Docs] Load External Job Docs Failure', props<{ error: string }>());

export const loadInternalJobDocsInit = createAction('[Job Docs] Load Internal Job Docs Init', props<{ pagination: Pagination, dto: any }>());
export const loadInternalJobDocsSuccess = createAction('[Job Docs] Load Internal Job Docs Success', props<{ jobDocs: JobDocContainerModel[] }>());
export const loadInternalJobDocsFailure = createAction('[Job Docs] Load Internal Job Docs Failure', props<{ error: string }>());

export const loadPickupJobDocsInit = createAction('[Job Docs] Load Pickup Job Docs Init', props<{ pagination: Pagination, dto: any }>());
export const loadPickupJobDocsSuccess = createAction('[Job Docs] Load Pickup Job Docs Success', props<{ jobDocs: JobDocContainerModel[] }>());
export const loadPickupJobDocsFailure = createAction('[Job Docs] Load Pickup Job Docs Failure', props<{ error: string }>());

export const cancelJobDocInit = createAction('[Jobs] Cancel Job Docs Init', props<{ jobGuids: string[] }>());
export const cancelJobDocSuccess = createAction('[Jobs] Cancel Job Docs Success');
export const cancelJobDocFailed = createAction('[Jobs] Cancel Job Docs Failure', props<{ error: string }>());

export const resetJobListing = createAction('[Sales Order] Reset Job Ag Grid Update');
export const resetDeliveryDetailsListing = createAction('[Sales Order] Reset Delivery Details Ag Grid Update');

export const loadDeliveryOrderInit = createAction('[Sales Invoice] Load Init', props<{ pagination: Pagination }>());
export const loadDeliveryOrderSuccess = createAction('[Sales Invoice] Load Success', props<{ deliveryOrders: GenericDocContainerModel [] }>());
export const loadDeliveryOrderFailed = createAction('[Sales Invoice] Load Failed', props<{ error: string }>());

export const searchDeliveryOrderInit = createAction('[Sales Invoice] Search Init', props<{ searchDto: GenericDocSearchCriteriaDtoModel }>());
