<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
         Stock Balance Summary
      </span>
    </div>

  </div>
</mat-card-title>
<form [formGroup]="form">
  <div fxLayout="column" class="view-col-forms">
    <div fxLayout="row wrap" fxFlexAlign="center" class="view-col-forms">

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Item Code</mat-label>
        <input style="cursor: pointer" matInput placeholder="Item Code" [formControl]="form.controls['itemCode']" type="text" (click)="itemCode.emit()"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Item Name</mat-label>
        <input style="cursor: pointer" matInput placeholder="Item Name" [formControl]="form.controls['itemName']" type="text" (click)="itemCode.emit()"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="100" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>System Stock Balance</mat-label>
        <input matInput placeholder="System Stock Balance" [formControl]="form.controls['system_stock_bal']" type="number"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="100" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Total Sales Order Quantity</mat-label>
        <input matInput placeholder="Total Sales Order Quantity" [formControl]="form.controls['total_sales_order_qty']" type="number"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="100" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Total Reservation Quantity</mat-label>
        <input matInput placeholder="Total Reservation Quantity" [formControl]="form.controls['total_reservation_qty']" type="number"
          >
      </mat-form-field>

      <button mat-raised-button class="details-btn" type="button" (click)="goToDetailedReservedStock()">
        <span>Details</span>
      </button>

     <mat-form-field fxFlex.gt-sm="100" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Total Job Order Quantity</mat-label>
        <input matInput placeholder="Total Job Order Quantity" [formControl]="form.controls['total_job_order_qty']" type="number"
          >
      </mat-form-field>

      <button mat-raised-button class="details-btn" type="button" (click)="goToDetailedJobOrderStock()">
        <span>Details</span>
      </button>

      <mat-form-field fxFlex.gt-sm="100" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Available Stock not Inclusive of Reserve</mat-label>
        <input matInput placeholder="Available Stock not Inclusive of Reserve" [formControl]="form.controls['availble_stock_bal_not_incl_rsv']" type="text"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="100" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Available Stock Inclusive</mat-label>
        <input matInput placeholder="Available Stock Inclusive" [formControl]="form.controls['availble_stock_bal_incl']" type="text"
          >
      </mat-form-field>

    </div>
  </div>
</form>
