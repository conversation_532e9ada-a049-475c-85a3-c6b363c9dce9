#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=dev --project=qc-applet --output-hashing none
node elements-build-scripts/akaun/qc-applet-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://development-akaun-applets/bigledger/tonn-cable/qc-applet/staging s3://development-akaun-applets/bigledger/tonn-cable/qc-applet/staging/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile development-bigledger --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/qc-applet/ s3://development-akaun-applets/bigledger/tonn-cable/qc-applet/dev --profile development-bigledger --acl public-read --recursive
