<form [formGroup]="form">
    <div fxLayout="column" class="view-col-forms">
      <div fxLayout="row wrap" fxFlexAlign="center" class="view-col-forms">

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Item Code</mat-label>
        <input matInput  placeholder="Item Code" 
        [formControl]="form.controls['itemCode']" 
        type="text"
        >
      </mat-form-field>
      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Item Name</mat-label>
        <input matInput placeholder="Item Name" 
        [formControl]="form.controls['itemName']" 
        type="text"
        >
      </mat-form-field>
      
      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>No of Process</mat-label>
        <input matInput placeholder="No of Process" 
        [formControl]="form.controls['no_of_process']" 
        type="text"
        >
      </mat-form-field>

    </div>
  </div>
</form>