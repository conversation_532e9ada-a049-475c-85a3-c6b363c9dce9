import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import {
  BranchService,
  CustomerService,
  EmployeeService,
  GenericDocContainerModel,
  Pagination,
  InternalJobOrderService,
  SubQueryService, 
  MrpJobOrderHdrContainerModel,
  PagingResponseModel,
  MrpProcessTypeService,
  MrpProcessInstanceService,
  MrpProdsysService,
  MrpJobOrderHdrService,
  MrpProcessInstanceContainerModel,
  MrpProcessTemplateService,
  TenantUserProfileService,
  InventoryItemHdrService} from 'blg-akaun-ts-lib';
import { forkJoin, iif, Observable, of, ReplaySubject, Subject, zip } from 'rxjs';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { catchError, map, mergeMap, switchMap, takeUntil } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../models/advanced-search-models';
import { Store } from '@ngrx/store';
import * as moment from 'moment';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { ProcessStates } from '../../../state-controllers/process-controller/store/states';
import { ProcessSelectors } from '../../../state-controllers/process-controller/store/selectors';
import { FormControl } from '@angular/forms';
import { ProcessActions } from '../../../state-controllers/process-controller/store/actions';
import { ProcessTypeOption } from '../../../models/internal-job-order.model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRowGuid: string;
}

@Component({
  selector: 'app-process-job-order-listing',
  templateUrl: './process-job-order-listing.component.html',
  styleUrls: ['./process-job-order-listing.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessJobOrderListingComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Job Order No Listing';
  protected readonly index = 0;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  searchModel = internalJobOrderSearchModel;

  processTypeOptions: ProcessTypeOption[] = [];
  filteredProcessTypeOptions: ReplaySubject<ProcessTypeOption[]> = new ReplaySubject<[]>(1);
  processTypeOptionsFilterCtrl: FormControl = new FormControl();
  protected _onDestroy = new Subject<void>();
  apiVisa = AppConfig.apiVisa;
  processType = '';

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Job Order No', field: 'job_order_no'},
    {headerName: 'Job Order Date', field: 'job_order_date', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Process Template', field: 'process_template'},

    {headerName: 'Issued By', field: 'issued_by'},
    {headerName: 'Machine Code', field: 'machine_code', cellStyle: () => ({'text-align': 'left'})},

    {headerName: 'Priority', field: 'priority', cellStyle: () => ({'text-align': 'left'})},

    // {headerName: 'Length', field: 'length'},
    {headerName: 'Quantity', field: 'qty'},
    {headerName: 'UOM', field: 'uom',cellStyle: () => ({'text-align': 'left'})},

    // {headerName: 'QC Input Status', field: 'bl_fi_generic_doc_hdr.server_doc_1'},
    {headerName: 'Process Status', field: 'processStatus'},

    // {headerName: 'Item Code', field: 'salesAgent'},
    // {headerName: 'Required Length', field: 'salesAgent'},
    {headerName: 'Remarks', field: 'remarks', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Customer Name', field: 'customerName', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Creation Date', field: 'bl_fi_generic_doc_hdr.created_date', type: 'rightAligned',
    // valueFormatter: params => moment(params.value).format('YYYY-MM-DD')},
    // {headerName: 'Amount Txn', field: 'bl_fi_generic_doc_hdr.amount_txn', type: 'numericColumn',
    // valueFormatter: params => formatMoneyInList(params.value)},
    // {headerName: 'Status', field: 'stockBalQty', cellStyle: () => ({'text-align': 'left'})},
  ];

  columnsDefs$ = this.sessionStore.select(SessionSelectors.selectMasterSettings).pipe(
    map((a: any) => [
      ...this.columnsDefs,
      // a.ENABLE_CUSTOM_STATUS_HDR_1 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_1 ? a.NAME_CUSTOM_STATUS_HDR_1 : 'client_doc_status_01',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_01',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_2 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_2 ? a.NAME_CUSTOM_STATUS_HDR_2 : 'client_doc_status_02',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_02',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_3 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_3 ? a.NAME_CUSTOM_STATUS_HDR_3 : 'client_doc_status_03',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_03',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_4 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_4 ? a.NAME_CUSTOM_STATUS_HDR_4 : 'client_doc_status_04',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_04',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_5 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_5 ? a.NAME_CUSTOM_STATUS_HDR_5 : 'client_doc_status_05',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_05',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
    ])
  );

  SQLGuids: string[] = null;
  pagination = new Pagination();

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private mrpProcessInstanceService: MrpProcessInstanceService,
    private mrpProcessTypeService : MrpProcessTypeService,
    private mrpProdsysService: MrpProdsysService,
    private jobOrderService: MrpJobOrderHdrService,
    private processTemplateService: MrpProcessTemplateService,
    private sqlService: SubQueryService,
    private profileService: TenantUserProfileService,
    private processService: MrpProcessTypeService,
    private invItemService: InventoryItemHdrService,
    private readonly store: Store<InternalJobOrderStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly processStore: Store<ProcessStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.getProcessTypes();

    this.processTypeOptionsFilterCtrl.valueChanges
    .pipe(takeUntil(this._onDestroy))
    .subscribe(() => {
      this.filterProcessTypeOptions();
    });
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.store.dispatch(InternalJobOrderActions.loadProcessInstanceInit({ request: grid.request }));
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
          // { columnName: 'doc_type', operator: '=', value: 'INTERNAL_SALES_INVOICE' },
          {
            columnName: 'guids', operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          },
          { columnName: 'process_guid', operator: '=', value: this.processType },

          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          { columnName: 'order', operator: '=', value: 'ASC' },
        ];
        let totalrec = 0;
        this.mrpProcessInstanceService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {

            totalrec = resolved.totalRecords;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                this.mrpProcessTypeService.getByGuid(itemperm.bl_mrp_process_instance_hdr.process_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                this.mrpProdsysService.getByGuid(itemperm.bl_mrp_process_instance_hdr.machine_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) ,
                this.jobOrderService.getByGuid(itemperm.bl_mrp_process_instance_hdr.job_order_hdr_guid.toString(),apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                this.processTemplateService.getByGuid(itemperm.bl_mrp_process_instance_hdr.process_template_hdr_guid.toString(),apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                this.profileService.getByGuid(itemperm.bl_mrp_process_instance_hdr.created_by_subject_guid.toString(),apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                itemperm.bl_mrp_process_instance_hdr.output_inv_item_guid ?
                this.invItemService.getByGuid(itemperm.bl_mrp_process_instance_hdr.output_inv_item_guid.toString(),apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),
                ).pipe(
                  map(([b_a,b_b,b_c,b_d,b_e,b_f]) => {
                    console.log("B_a",b_a);
                    console.log("B-f",b_f);
                    let obj = {"guid":'', "job_order_no": '',"job_order_date": '',"job_order_guid": '', "process_template": '', "machine_guid": '', "machine_code": '',"output_item_code" : '',"output_item_name": '',"length":'', "qty": '', 'uom':'', "sequence" : '', "processStatus" : '', "completion_date": '', "issued_by": '','process_code':'','priority':''};
                    obj.guid = itemperm.bl_mrp_process_instance_hdr.guid.toString();
                    obj.priority = itemperm.bl_mrp_process_instance_hdr.priority ? itemperm.bl_mrp_process_instance_hdr.priority.toString() : '';
                    obj.job_order_guid = itemperm.bl_mrp_process_instance_hdr.job_order_hdr_guid.toString();
                    obj.job_order_no = b_c ? b_c.data.bl_mrp_job_order_hdr.server_doc_1 : '';
                    obj.job_order_date = b_c ? b_c.data.bl_mrp_job_order_hdr.date_txn : '';
                    obj.processStatus = b_c ? b_c.data.bl_mrp_job_order_hdr.process_status : '';
                    obj.process_template =b_d ? b_d.data.bl_mrp_process_template_hdr.code : '';
                    obj.machine_guid = b_b ? b_b.data.bl_mrp_prodsys_hdr.guid : '';
                    obj.machine_code = b_b ? b_b.data.bl_mrp_prodsys_hdr.code : '';
                    obj.process_code = b_a ? b_a.data.bl_mrp_process_type_hdr.code : '';
                    obj.uom = b_f ? b_f.data.bl_inv_mst_item_hdr?.uom : '';
                    obj.sequence = b_a ? b_a.data.bl_mrp_process_type_hdr.sequence : '';
                    obj.issued_by = b_e ? b_e.data.appLoginSubject.name : '';
                    obj.qty =  b_c ? b_c.data.bl_mrp_job_order_hdr.quantity_base : '';
                    // obj.basic_type = b_a.data.bl_fi_mst_item_hdr.txn_type;
                    // obj.sub_item_type = b_a.data.bl_fi_mst_item_hdr.sub_item_type;
                    return obj;
                  })
                )
            )
            );
            return iif(() => resolved.totalRecords > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              this.store.dispatch(InternalJobOrderActions.loadGenDocLinkSuccess({ totalRecords: totalrec }));
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
            this.store.dispatch(InternalJobOrderActions.loadGenDocLinkFailed({ error: err.message }));
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
      if (resolved) {
        this.gridApi.refreshServerSideStore({ purge: true });
        this.store.dispatch(InternalJobOrderActions.resetAgGrid());
      }
    });
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onAdd() {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false});
    this.viewColFacade.onNextAndReset(this.index, 2);
  }

  protected filterProcessTypeOptions() {
    if (!this.processTypeOptions) {
      return;
    }
    let search = this.processTypeOptionsFilterCtrl.value;
    if (!search) {
      this.filteredProcessTypeOptions.next(this.processTypeOptions.slice());
      return;
    } else {
      search = search.trim().toLowerCase();
      this.filteredProcessTypeOptions.next(
        this.processTypeOptions.filter(
          (option) => option.viewValue.toLowerCase().indexOf(search) > -1
        )
      );
    }
  }


  async getProcessTypes(): Promise<void> {
    this.processTypeOptions = [];
    let paging = new Pagination();

    await this.processService
      .getByCriteriaPromise(paging, this.apiVisa)
      .then((resp: PagingResponseModel<any>) => {
        resp.data.forEach((eachMember, index) => {
            this.processTypeOptions.push({
              value: eachMember.bl_mrp_process_type_hdr.guid,
              viewValue: eachMember.bl_mrp_process_type_hdr.name,
            });
        });
      });
    this.processTypeOptions.sort((a, b) => a.viewValue.localeCompare(b.viewValue));
    this.filteredProcessTypeOptions.next(this.processTypeOptions.slice());
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe(
        {next: resolve => {
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }}
      );
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  OnProcessTypeChange(event){
    console.log("Event Process type",event.value);
    this.processType = event.value;
    this.setGridData();
  }

  onRowClicked(entity: any) {
    if (entity) {
      console.log("Entity bhai",entity);
      this.processStore.dispatch(ProcessActions.selectProcess({process : entity}));
      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance<LocalState>(this.index, {
          ...this.localState, deactivateAdd: false, deactivateList: false, selectedRowGuid: entity.guid.toString()
        });
        this.viewColFacade.onNextAndReset(this.index, 1);
      }
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
