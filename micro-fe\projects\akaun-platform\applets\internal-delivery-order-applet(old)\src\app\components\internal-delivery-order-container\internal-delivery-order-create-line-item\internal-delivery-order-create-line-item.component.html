<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Create Internal Delivery Order Line Item
      </span>
    </div>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <!-- <mat-tab label="Sales Order">
    <app-internal-delivery-order-create-line-item-sales-order [localState]="localState$ | async" (next)="onNextSOList($event)"></app-internal-delivery-order-create-line-item-sales-order>
  </mat-tab>
  <mat-tab label="ST GRN">
    <app-internal-delivery-order-create-line-item-ST-GRN [localState]="localState$ | async" (next)="onNextSOList($event)"></app-internal-delivery-order-create-line-item-ST-GRN>
  </mat-tab>
  <mat-tab label="KO By ST GRN">
    <app-internal-delivery-order-ko-by-ST-GRN [localState]="localState$ | async" (next)="onNextSOList($event)"></app-internal-delivery-order-ko-by-ST-GRN>
  </mat-tab>
  <mat-tab label="KO For ST GRN">
    <app-internal-delivery-order-ko-for-ST-GRN [localState]="localState$ | async" (next)="onNextSOList($event)"></app-internal-delivery-order-ko-for-ST-GRN>
  </mat-tab> -->
  <mat-tab label="Search Item">
    <app-line-search-item-listing [localState]="localState$ | async" (addLineItem)="goToAddLineItem($event)" (addSerialNumber)="saveSerialNumber($event)"></app-line-search-item-listing>
  </mat-tab>
  <!-- <mat-tab label="KO By Sales Order">
    <app-line-container-ko-by
      [localState]="localState$ | async"
      [hdr]="draft$ | async"
      [searchModel]="internalSalesOrderSearchModel"
      [advSearchId]=""
      [service]="InternalSalesOrderService"
      [serverDocTypeDoc2]="'INTERNAL_OUTBOUND_DELIVERY_ORDER'"
      [itemType]="'salesOrder'"
      [additionalColumnDefs]="soAdditionalColumns"
    ></app-line-container-ko-by>
  </mat-tab> -->
  <!-- <mat-tab label="KO For Sales Order" *ngIf="koForSalesOrder">
    <app-line-container-ko-for
      [localState]="localState$ | async"
      [hdr]="draft$ | async"
      [searchModel]="internalSalesOrderSearchModel"
      [advSearchId]=""
      [service]="InternalSalesOrderService"
      [serverDocTypeDoc1]="'INTERNAL_SALES_ORDER'"
      [itemType]="'salesOrder'"
      [LinkActions]="LinkActions"
      [LinkSelectors]="LinkSelectors"
      [PNSActions]="PNSActions"
      [PNSSelectors]="PNSSelectors"
      [AppletConstants]="AppletConstants"
      [draftStore]="draftStore"
      [additionalColumnDefs]="soAdditionalColumns"
      (addLineItem)="onAdd($event)"
      (onReturn)="onReturn()"
    ></app-line-container-ko-for>
  </mat-tab> -->

  <mat-tab label="KO For Sales Order" *ngIf="koForSalesOrder">
    <app-line-container-ko-for-queue
    [localState]="localState$ | async"
    [hdr]="draft$ | async"
    [searchModel]="internalSalesOrderSearchModel"
    [advSearchId]=""
    [service]="InternalSalesOrderService"
    [serverDocTypeDoc1]="'INTERNAL_SALES_ORDER'"
    [serverDocTypeDoc2]="'INTERNAL_OUTBOUND_DELIVERY_ORDER'"
    [itemType]="'salesOrder'"
    [LinkActions]="LinkActions"
    [LinkSelectors]="LinkSelectors"
    [PNSActions]="PNSActions"
    [PNSSelectors]="PNSSelectors"
    [AppletConstants]="AppletConstants"
    [additionalColumnDefs] = "additionalColDef"
    [draftStore]="draftStore"
    (addLineItem)="onAdd($event)"
    (onReturn)="onReturn()"
    >
    </app-line-container-ko-for-queue>
  </mat-tab>

  <!-- <mat-tab label="KO By ST GRN">
    <app-line-container-ko-by
      [localState]="localState$ | async"
      [hdr]="draft$ | async"
      [searchModel]="internalOutboundStockTransferSearchModel"
      [advSearchId]=""
      [serverDocTypeDoc2]="'INTERNAL_OUTBOUND_DELIVERY_ORDER'"
      [itemType]="'inbound'"
      [service]="InternalOutboundDeliveryOrderService"
      [additionalColumnDefs]="stgrnAdditionalColumns"
    ></app-line-container-ko-by>
  </mat-tab> -->
  <!-- <mat-tab label="KO For ST GRN" *ngIf="koForSTGRN">
    <app-line-container-ko-for
      [localState]="localState$ | async"
      [hdr]="draft$ | async"
      [searchModel]="internalInboundStockTransferSearchModel"
      [advSearchId]=""
      [service]="InternalInboundStockTransferService"
      [serverDocTypeDoc1]="'INTERNAL_INBOUND_STOCK_TRANSFER'"
      [itemType]="'inbound'"
      [LinkActions]="LinkActions"
      [LinkSelectors]="LinkSelectors"
      [PNSActions]="PNSActions"
      [PNSSelectors]="PNSSelectors"
      [AppletConstants]="AppletConstants"
      [additionalColumnDefs]="stgrnAdditionalColumns"
      [draftStore]="draftStore"
      (addLineItem)="onAdd($event)"
      (onReturn)="onReturn()"
    ></app-line-container-ko-for>
  </mat-tab> -->
  <mat-tab label="KO For ST GRN" *ngIf="koForSTGRN">
    <app-line-container-ko-for-queue
    [localState]="localState$ | async"
    [hdr]="draft$ | async"
    [searchModel]="internalInboundStockTransferSearchModel"
    [advSearchId]=""
    [service]="InternalInboundStockTransferService"
    [serverDocTypeDoc1]="'INTERNAL_INBOUND_STOCK_TRANSFER'"
    [serverDocTypeDoc2]="'INTERNAL_OUTBOUND_DELIVERY_ORDER'"
    [itemType]="'inbound'"
    [LinkActions]="LinkActions"
    [LinkSelectors]="LinkSelectors"
    [PNSActions]="PNSActions"
    [PNSSelectors]="PNSSelectors"
    [AppletConstants]="AppletConstants"
    [additionalColumnDefs]="stgrnAdditionalColumns"
    [draftStore]="draftStore"
    (addLineItem)="onAdd($event)"
    (onReturn)="onReturn()">
    </app-line-container-ko-for-queue>
  </mat-tab>

  <mat-tab label="KO For GRN" *ngIf="koForSTGRN">
    <app-line-container-ko-for-queue
    [localState]="localState$ | async"
    [hdr]="draft$ | async"
    [searchModel]="internalPurchaseGRNSearchModel"
    [advSearchId]=""
    [service]="InternalPurchaseGoodsReceivedNotes"
    [serverDocTypeDoc1]="'INTERNAL_PURCHASE_GOODS_RECEIVED_NOTE'"
    [serverDocTypeDoc2]="'INTERNAL_OUTBOUND_DELIVERY_ORDER'"
    [itemType]="'grn'"
    [LinkActions]="LinkActions"
    [LinkSelectors]="LinkSelectors"
    [PNSActions]="PNSActions"
    [PNSSelectors]="PNSSelectors"
    [AppletConstants]="AppletConstants"
    [additionalColumnDefs] = "additionalColDef"
    [draftStore]="draftStore"
    (addLineItem)="onAdd($event)"
    (onReturn)="onReturn()"
    >
    </app-line-container-ko-for-queue>
  </mat-tab>

  <!-- <mat-tab label="Quotation">
    <app-internal-delivery-order-create-line-item-quotation></app-internal-delivery-order-create-line-item-quotation>
  </mat-tab>
  <mat-tab label="Invoice">
    <app-internal-delivery-order-create-line-item-invoice></app-internal-delivery-order-create-line-item-invoice>
  </mat-tab> -->
</mat-tab-group>
