#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=senheng-staging --project=internal-job-order-applet --output-hashing none
node elements-build-scripts/akaun/internal-job-order-applet-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://senheng-applets/bigledger/wavelet-erp/internal-job-order-applet/staging s3://senheng-applets/bigledger/wavelet-erp/internal-job-order-applet/staging/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile senheng-staging --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/internal-job-order-applet/ s3://senheng-applets/bigledger/wavelet-erp/internal-job-order-applet/staging --profile senheng-staging --acl public-read --recursive
