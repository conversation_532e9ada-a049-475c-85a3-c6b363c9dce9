import { FormGroup } from '@angular/forms';
import { createAction, props } from '@ngrx/store';
import { bl_fi_entity_credit_limit_hdr_RowClass, bl_fi_mst_comp_branch_location_entity_link_RowCLass, bl_fi_mst_entity_ext_RowClass, bl_fi_mst_entity_line_RowClass, bl_fi_mst_entity_login_subject_link_RowClass, bl_fi_mst_entity_payment_method_RowClass, CreditLimitContainerModel, CreditTermContainerModel, EntityContainerModel, EntityLoginSubjectLinkContainerModel, EntityPaymentMethodContainerModel, LabelContainerModel, LinkSubjectToGroupContainerModel, Pagination } from 'blg-akaun-ts-lib';

export const loadEntityExtsInit = createAction('[Entity Applet] Load Init', props<{ request: any }>());
export const loadEntityExtSuccess = createAction('[Entity Applet] Load Success', props<{ totalRecords: number }>());
export const loadEntityExtFailed = createAction('[Entity Applet] Load Failed', props<{ error: string }>());

export const loadAddress = createAction('[Entity Applet] Load Address', props<{ allAddress: any }>());

export const startDraft = createAction('[Entity Applet] Draft Init');
export const updateDraft = createAction('[Entity Applet] Draft Update',
  props<{ entity?: EntityContainerModel, line?: bl_fi_mst_entity_line_RowClass }>());
export const resetDraft = createAction('[Entity Applet] Draft Reset');

export const selectBranchLine = createAction('[Entity] Select Contact Line Ext', props<{ ext: any }>());

export const startPMCDraft = createAction('[Entity Applet] PMC Draft Init', props<{ form: FormGroup }>());

export const updatePMCDraft = createAction('[Entity Applet] PMC Draft Update', props<{ initCheck?: FormGroup, record?: any }>());
export const resetPMCDraft = createAction('[Entity Applet] PMC Draft Reset');

export const createContactLogin = createAction('[Entity Applet] Create Login Init', props<{ entityContactLogin: EntityLoginSubjectLinkContainerModel }>());

export const createLogin = createAction('[Entity Applet] Create Login Init', props<{ entityLogin: EntityLoginSubjectLinkContainerModel }>());

export const editBranchLine = createAction('[Entity] Edit Branch Line', props<{ guid: string, ext: any }>());
export const createBranchLine = createAction('[Entity] Create Branch Line', props<{ ext: bl_fi_mst_entity_line_RowClass }>());

export const createLoginFailed = createAction('[Entity Applet] Create Login Failed', props<{ error: string }>());

export const createLoginSuccess = createAction('[Entity Applet] Create Login Success', props<{ entityLogin: EntityLoginSubjectLinkContainerModel }>());

export const containerDraftUpdateLoginInit = createAction('[Entity] Update Init Container', props<{ entityLogin: EntityLoginSubjectLinkContainerModel }>());

export const createEntity = createAction('[Entity Applet] Create Init', props<{ entityExt: EntityContainerModel, entityType: string}>());

export const createEntityFailed = createAction('[Entity Applet] Create Failed', props<{ error: string }>());

export const createEntitySuccess = createAction('[Entity Applet] Create Success', props<{ entityExt: EntityContainerModel }>());

export const selectEntityExtGuid = createAction('[Entity Applet] Select Guid', props<{ guid: string }>());

export const selectEntityExtEntity = createAction('[Entity Applet] Select Entity', props<{ entity: EntityContainerModel }>());

export const selectEntityExtLineItem = createAction('[Entity Applet] Select Line Item', props<{ line: bl_fi_mst_entity_line_RowClass }>());

export const selectEntityEditExt = createAction('[Entity] Select Edit Ext', props<{ ext: any }>());

export const resetEntityEditExt = createAction('[Entity Applet] Entity Ext Reset');

export const selectContactLine = createAction('[Entity] Select Contact Line Ext', props<{ ext: any }>());

export const createContactLoginExt = createAction('[Entity] Create Login Ext', props<{ ext: any }>());

export const editContactLoginExt = createAction('[Entity] Edit Login Ext', props<{ guid: string, ext: any }>());

export const createLoginExt = createAction('[Entity] Create Login Ext', props<{ ext: any }>());

export const editLoginExt = createAction('[Entity] Edit Login Ext', props<{ guid: string, ext: any }>());

export const createPaymentConfigExt = createAction('[Entity] Create Payment Ext', props<{ ext: bl_fi_mst_entity_ext_RowClass }>());

export const editPaymentConfigExt = createAction('[Entity] Edit Payment Ext', props<{ guid: string, ext: any }>());

export const createEntityTaxExt = createAction('[Entity] Create Tax Ext', props<{ ext: bl_fi_mst_entity_ext_RowClass }>());

export const editTaxExt = createAction('[Entity] Edit Tax Ext', props<{ guid: string, ext: any }>());

// export const createAddressExt = createAction('[Entity] Create Address Ext', props<{ ext: bl_fi_mst_entity_ext_RowClass }>());
export const createAddress = createAction('[Entity] Create Address', props<{ address: any }>());

// export const editAddressExt = createAction('[Entity] Edit Address Ext', props<{ guid: string, ext: any }>());
export const editAddress = createAction('[Entity] Edit Address', props<{ address: any }>());

export const resetAddress = createAction('[Entity] Reset Address');

export const createContactExt = createAction('[Entity] Create Contact Ext', props<{ ext: bl_fi_mst_entity_line_RowClass }>());

export const editContactLine = createAction('[Entity] Edit Contact Ext', props<{ guid: string, ext: any }>());

export const createContainerDraftInit = createAction('[Entity] Create Container', props<{ entity: any }>());

export const createContainerDraftLoginInit = createAction('[Entity] Create Login Container', props<{ entityLogin: any }>());

export const containerDraftUpdateInit = createAction('[Entity] Update Init Container', props<{ entity: EntityContainerModel, entityType:string }>());
export const containerDraftUpdateFailed = createAction('[Entity] Update Failed Container', props<{ status: boolean }>());
export const containerDraftUpdateSuccess = createAction('[Entity] Update Success Container', props<{ entity: any }>());

export const updateAgGridDone = createAction('[Entity] Update Ag Grid Done', props<{ status: boolean }>());

export const containerCatDraftUpdateInit = createAction('[Entity] Update Category Init Container', props<{ entityCat: LabelContainerModel }>());
export const containerCatDraftUpdateFailed = createAction('[Entity] Update Category Failed Container', props<{ status: boolean }>());
export const containerCatDraftUpdateSuccess = createAction('[Entity] Update Category Success Container', props<{ entityCat: any }>());
export const createCatEntity = createAction('[Entity Applet] Create Category Init', props<{ entityCat: LabelContainerModel }>());

export const createCatEntitySuccess = createAction('[Entity Applet] Create Category Success', props<{ entityCat: LabelContainerModel }>());
export const selectEntityCatGuid = createAction('[Entity Category Applet] Select Category Guid', props<{ guid: string }>());

export const createContainerCatDraftInit = createAction('[Entity Category] Create Category Container', props<{ entity: any }>());

//ACTIONS FOR ENTITY EDIT CREDIT TERM AND LIMIT
export const addNewCreditTermSuccess = createAction('[Entity] Create Success', props<{ entity: any }>());
export const addNewCreditTermFailed = createAction('[Entity] Create Failed', props<{ error: string }>());
export const addNewCreditTerm = createAction('[Entity] Create new credit term', props<{ addNewCreditTerm: CreditTermContainerModel }>());

export const addNewCreditLimit = createAction('[Entity] Create new credit limit', props<{ addNewCreditLimit: CreditLimitContainerModel }>());
export const addNewCreditLimitFailed = createAction('[Entity] Create Failed', props<{ error: string }>());
export const addNewCreditLimitSuccess = createAction('[Entity] Create Success', props<{ entity: any }>());

export const createTermExt = createAction('[Entity] Create Term Ext', props<{ ext: bl_fi_mst_entity_ext_RowClass }>());
export const createLimitExt = createAction('[Entity] Create Limit Ext', props<{ ext: bl_fi_mst_entity_ext_RowClass }>());
export const editTermLine = createAction('[Entity] Edit Term Ext', props<{ guid: string, ext: any }>());
export const editLimitLine = createAction('[Entity] Edit Limit Ext', props<{ guid: string, ext: any }>());

//ACTIONS FOR CREDIT LIMIT MODULE
export const containerLimitDraftUpdateInit = createAction('[Entity] Update Limit Init Container', props<{ entityLimit: CreditLimitContainerModel }>());
export const containerLimitDraftUpdateFailed = createAction('[Entity] Update Limit Failed Container', props<{ status: boolean }>());
export const containerLimitDraftUpdateSuccess = createAction('Entity] Update Limit Success Container', props<{ entityLimit: any }>());
export const createLimitEntity = createAction('[Entity] Create Limit Init', props<{ entityLimit: CreditLimitContainerModel }>());
export const createLimitEntitySuccess = createAction('[Entity] Create Limit Success', props<{ entityLimit: CreditLimitContainerModel }>());
export const selectEntityLimitGuid = createAction('[Entity] Select Limit Guid', props<{ guid: string }>());
export const createContainerLimitDraftInit = createAction('[Entity] Create Limit Container', props<{ entity: any }>());
export const containerDraftLimitUpdateSuccess = createAction('[Entity] Update Success Container', props<{ entityLimit: any }>());

//ACTIONS FOR CREDIT TERM MODULE
export const containerTermDraftUpdateInit = createAction('[Entity] Update Term Init Container', props<{ entityTerm: CreditTermContainerModel }>());
export const containerTermDraftUpdateFailed = createAction('[Entity] Update Term Failed Container', props<{ status: boolean }>());
export const containerTermDraftUpdateSuccess = createAction('[Entity] Update Limit Success Container', props<{ entityTerm: any }>());
export const createTermEntity = createAction('[Entity] Create Term Init', props<{ entityTerm: CreditTermContainerModel }>());
export const createTermEntitySuccess = createAction('[Entity] Create Term Success', props<{ entityTerm: CreditTermContainerModel }>());
export const selectEntityTermGuid = createAction('[Entity] Select Term Guid', props<{ guid: string }>());
export const createContainerTermDraftInit = createAction('[Entity] Create Term Container', props<{ entity: any }>());
export const containerDraftTermUpdateSuccess = createAction('[Entity] Update Success Container', props<{ entityTerm: any }>());

export const getCurrency = createAction('[Entity] Get Currency', props<{ currency: any }>());

export const getCurrencySuccess = createAction('[Entity Applet] Get Currency Success', props<{ currency: any }>());
export const getCurrencyFailed = createAction('[Entity Applet]  Get Currency  Failed', props<{ error: string }>());

export const getAppLoginCreatedBy
  = createAction('[Item Applet] Get App Login Principle CreatedBy Container Init', props<{ appLoginCreatedBy: any; }>());

export const getAppLoginModifiedBy
  = createAction('[Item Applet] Get App Login Principle AppLogin ModifiedBy Container Init', props<{ appLoginModifiedBy: any; }>());

export const itemCategory = createAction('[Item Applet] Get Item Category Container', props<{ category: any, updated: boolean }>());

export const addNewPaymentConfig = createAction('[Payment Config] Add New Payment Config', props<{ paymentConfig: bl_fi_mst_entity_payment_method_RowClass }>());
export const createNewPaymentConfig = createAction('[Payment Config] Create New Payment Config', props<{ model: EntityPaymentMethodContainerModel }>());
export const createNewPaymentConfigSuccess = createAction('[Payment Config] Create New Payment Config Success', props<{ model: EntityPaymentMethodContainerModel[] }>());
export const createNewPaymentConfigFail = createAction('[Payment Config] Create New Payment Config Fail', props<{ error: string }>());

export const selectEditPaymentConfig = createAction('[Payment Config] Select Payment Config for Edit', props<{ paymentConfig: any }>());
export const editSelectedPaymentConfig = createAction('[Payment Config] Edit Payment Config', props<{ paymentConfig: EntityPaymentMethodContainerModel }>());
export const editSelectedPaymentConfigSuccess = createAction('[Payment Config] Edit Payment Config Success', props<{ paymentConfig: EntityPaymentMethodContainerModel }>());
export const editSelectedPaymentConfigFail = createAction('[Payment Config] Edit Payment Config Fail', props<{ error: string }>());

export const selectedRow = createAction('[Entity Applet] Selected Row', props<{ row: any[] }>())

export const selectToggleMode = createAction('[Entity Applet] Toggle Mode Trigerred', props<{ SelectedToggleMode: boolean }>());

export const selectedEntity = createAction('[Entity] Selected Entity Type', props<{ entityType: string }>());

export const resetAgGrid = createAction('[Entity] Reset Ag Grid Update');
