<form [formGroup]="form">
    <div fxLayout="column" class="view-col-forms">
      <div fxLayout="row wrap" fxFlexAlign="center" class="view-col-forms">

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Sales Order No</mat-label>
          <input matInput  placeholder="Sales Order No" 
          [formControl]="form.controls['doc_no']" 
          type="text"
          >
        </mat-form-field>
        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Customer Name</mat-label>
          <input matInput placeholder="Customer Name" 
          [formControl]="form.controls['customerName']" 
          type="text"
          >
        </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Item Code</mat-label>
        <input matInput  placeholder="Item Code" 
        [formControl]="form.controls['itemCode']" 
        type="text"
        >
      </mat-form-field>
      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Item Name</mat-label>
        <input matInput placeholder="Item Name" 
        [formControl]="form.controls['itemName']" 
        type="text"
        >
      </mat-form-field>
      
      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Quantity</mat-label>
        <input matInput placeholder="Quantity" 
        [formControl]="form.controls['qty']" 
        type="text"
        >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>UOM</mat-label>
        <input matInput placeholder="UOM" 
        [formControl]="form.controls['uom']" 
        type="text"
        >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Expected Delivery Date</mat-label>
        <input matInput [matDatepicker]="estimatedDeliveryDate" [formControl]="form.controls['estimatedDeliveryDate']" autocomplete="off" readonly (click)="estimatedDeliveryDate.open()">
        <mat-error>Expected Delivery Date is <strong>not valid</strong></mat-error>
        <mat-datepicker-toggle matSuffix [for]="estimatedDeliveryDate"></mat-datepicker-toggle>
        <mat-datepicker touchUi="true" #estimatedDeliveryDate></mat-datepicker>
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>(P) Production Status</mat-label>
        <input matInput placeholder="(P) Production Status" 
        [formControl]="form.controls['mrp_request_status_production']" 
        type="text"
        readonly
        >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>(S) Schedule Status</mat-label>
        <input matInput placeholder="(S) Schedule Status" 
        [formControl]="form.controls['mrp_request_status_schedule']" 
        type="text"
        readonly
        >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>(Ex) Existing Stock Status</mat-label>
        <input matInput placeholder="(Ex) Existing Stock Status" 
        [formControl]="form.controls['mrp_request_status_exists']" 
        type="text"
        readonly
        >
      </mat-form-field>

      <mat-form-field fxFlex="100" class="p-10" appearance="outline">
        <mat-label>MRP Request Remarks</mat-label>
        <textarea #remarks matInput placeholder="MRP Request Remarks" formControlName="mrp_request_remarks" readonly></textarea>
        <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
      </mat-form-field>
    </div>
  </div>
</form>