<div class="view-col-table no-tab" fxLayout="column">
  <mat-card-title class="column-title">
    <div style="margin-top: 20px">Pick Pack Queue Listing</div>
    <div
      fxLayout="row wrap"
      fxLayoutAlign="space-between end"
      fxLayoutGap="10px"
    >
      <div fxFlex="3 0 0">
        <div
          fxLayout="row"
          fxLayoutAlign="space-between center"
          fxLayoutGap="3px"
        >
          <app-advanced-search-v2
            fxFlex
            fxFlex.lt-sm="100"
            [id]="'si'"
            [advSearchModel]="searchModel"
            (search)="onSearch($event)"
            [viewModelStore]="viewModelStore"
            [ColumnViewActions]="ColumnViewActions"
            [ColumnViewSelectors]="ColumnViewSelectors"
          ></app-advanced-search-v2>
          <app-column-toggle
            [currentToggle]="toggleColumn$ | async"
            (toggleColumn)="onToggle($event)"
            fxHide.lt-sm
          ></app-column-toggle>
        </div>
      </div>
      <div
        class="blg-accent"
        fxFlex="1 0 25"
        fxLayout="row"
        fxLayoutAlign="space-between center"
      >
        <app-pagination
          fxFlex
          #pagination
          [agGridReference]="agGrid"
        ></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
    <form [formGroup]="form">
      <div fxLayout="row wrap" fxFlexAlign="center">
        <div style="padding-right: 5px">
          <button
            mat-raised-button
            color="primary"
            type="button"
            (click)="onReadyToShip()"
            style="width: 110px; height: 40px"
            class="btn-work"
          >
            Create Delivery Job
          </button>
        </div>
        <div style="padding-right: 5px; width: 110px">
          <mat-form-field appearance="outline" class="input-field">
            <mat-select
              placeholder="Delivery Type"
              formControlName="deliveryType"
            >
              <mat-option value="INTERNAL_DELIVERY"
                >INTERNAL_DELIVERY</mat-option
              >
              <mat-option value="EXTERNAL_DELIVERY"
                >EXTERNAL_DELIVERY</mat-option
              >
              <mat-option value="PICKUP">PICKUP</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div style="padding-right: 5px; width: 110px">
          <mat-form-field appearance="outline" class="input-field">
            <mat-label>Width (cm)</mat-label>
            <input
              matInput
              placeholder="Width (cm)"
              type="text"
              formControlName="width"
            />
          </mat-form-field>
        </div>
        <div style="padding-right: 5px; width: 110px">
          <mat-form-field appearance="outline" class="input-field">
            <mat-label>Length (cm)</mat-label>
            <input
              matInput
              placeholder="Length (cm)"
              type="text"
              formControlName="length"
            />
          </mat-form-field>
        </div>
        <div style="padding-right: 5px; width: 110px">
          <mat-form-field appearance="outline" class="input-field">
            <mat-label>Height (cm)</mat-label>
            <input
              matInput
              placeholder="Height (cm)"
              type="text"
              formControlName="height"
            />
          </mat-form-field>
        </div>
        <div style="padding-right: 5px; width: 110px">
          <mat-form-field appearance="outline" class="input-field">
            <mat-label>Weight (kg)</mat-label>
            <input
              matInput
              placeholder="Weight (kg)"
              type="text"
              formControlName="weight"
            />
          </mat-form-field>
        </div>
      </div>
      <div fxLayout="row wrap" fxFlexAlign="center" style="height: 100px" class="view-col-forms">
        <div style="padding-right: 5px" class="p-10" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex="100">
          <mat-form-field
            style="height: 50px; width: 500px"
            appearance="outline"
            class="input-field"
          >
            <mat-label>Remarks</mat-label>
            <textarea
              #remarks
              matInput
              placeholder="Remarks"
              [formControl]="form.controls['remarks']"
            ></textarea>
          </mat-form-field>
        </div>
        <div style="padding-right: 5px" class="p-10" fxFlex.gt-sm="100" fxFlex.gt-xs="100" fxFlex="100">
          <mat-form-field
            style="height: 50px; width: 500px"
            appearance="outline"
            class="input-field"
          >
            <mat-label>Content</mat-label>
            <textarea
              #content
              matInput
              placeholder="Content"
              [formControl]="form.controls['content']"
            ></textarea>
          </mat-form-field>
        </div>
      </div>
    </form>
  </mat-card-title>
  <div style="height: 80%">
    <ag-grid-angular
      #agGrid
      style="height: 100%"
      class="ag-theme-balham"
      rowModelType="clientSide"
      serverSideStoreType="partial"
      [getRowClass]="pagination.getRowClass"
      [columnDefs]="columnsDefs"
      [rowData]="pickPackQueues$ | async"
      [paginationPageSize]="pagination.rowPerPage"
      [cacheBlockSize]="pagination.rowPerPage"
      [pagination]="true"
      [animateRows]="true"
      [defaultColDef]="defaultColDef"
      [suppressRowClickSelection]="true"
      [sideBar]="true"
      [groupSelectsChildren]="true"
      [groupDefaultExpanded]="-1"
      [rowHeight]="60"
      [groupDisplayType]="groupDisplayType"
      [groupRowRendererParams]="groupRowRendererParams"
      [frameworkComponents]="frameworkComponents"
      [getRowStyle]="getRowStyle"
      (gridReady)="onGridReady($event)"
      [rowSelection]="rowSelection"
      [suppressAggFuncInHeader]="true"
      (firstDataRendered)="headerHeightSetter()"
      (columnResized)="headerHeightSetter()"
    >
    </ag-grid-angular>
  </div>
</div>
