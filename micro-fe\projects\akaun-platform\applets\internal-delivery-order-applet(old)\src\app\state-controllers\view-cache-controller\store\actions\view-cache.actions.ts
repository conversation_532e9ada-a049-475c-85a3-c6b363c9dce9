import { createAction, props } from '@ngrx/store';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';

export const cacheInternalDO = createAction('[View Cache] Cache Internal Delivery Order', props<{ cache: ViewColumnState }>());
export const cachePrintableFormatSettings = createAction('[View Cache] Cache Printable Format Settings', props<{ cache: ViewColumnState }>());
export const cachePickPackQueue = createAction('[View Cache] Cache Pick Pack Queue', props<{cache: ViewColumnState}>());
