import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AppLoginContainerModel, InternalSalesOrderService, InventoryItemHdrService, MrpJobOrderGenDocLinkContainerModel, MrpJobOrderHdrService, MrpProcessInstanceContainerModel, MrpProcessInstanceService, MrpProcessTemplateService, MrpProcessTypeService, MrpProdsysService, Pagination, PagingResponseModel, SubQueryService, bl_fi_generic_doc_hdr_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable, ReplaySubject, Subject, forkJoin, iif, of, zip } from 'rxjs';
import { catchError, map, mergeMap, takeUntil, withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { Store } from '@ngrx/store';
import { InternalJobOrderStates } from '../../../../../state-controllers/internal-job-order-controller/store/states';
import { ViewColumnFacade } from '../../../../../facades/view-column.facade';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { MachineOption, ProcessTemplateOption, ProcessTypeOption } from '../../../../../models/internal-job-order.model';
import { InternalJobOrderSelectors } from '../../../../../state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderActions } from '../../../../../state-controllers/internal-job-order-controller/store/actions';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { ToastrService } from 'ngx-toastr';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { ComponentStore } from "@ngrx/component-store";

interface LocalState {
  deactivateReturn: boolean;
  deactivateList: boolean;
  selectedIndex: number;
  selectedIndexInvoiceItem: number;
  SalesRmaEdit: boolean;
  selectedItem: any;
  deactivateAdd: boolean;
}

@Component({
  selector: 'app-process-instance-create',
  templateUrl: './process-instance-create.component.html',
  styleUrls: ['./process-instance-create.component.css'],
  providers: [ComponentStore],

})
export class ProcessInstanceCreateComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  bread = "Create Process Instance";
  private localState: LocalState;
  breadCrumbs: any[];
  ui: any;
  pagination = new Pagination();
  // extMap: Map<string, any> = new Map<string, any>();
  changePage = false;
  shippingAddress = false;
  billingAddress = false;
  deactivateReturn$;
  item_guid;

  protected readonly index = 14;
  prevIndex: number;
  private prevLocalState: any;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);

  apiVisa = AppConfig.apiVisa;

  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.localState.deactivateAdd
  );
  readonly deactivateList$ = this.componentStore.select(
    (state) => state.localState.deactivateList
  );

  paging = new Pagination();
  addSuccess = "Add";
  isClicked = "primary";
  getLabel$: Observable<any>;
  gridApi: any;
  toggleColumn$: Observable<boolean>;
  // searchModel = salesInvoiceSearchModel;

  columnsDefs;
  defaultColDef;
  serverSideStoreType;
  rowModelType;
  rowSelection;
  selectedItem: any;
  // users that are already added
  addedEntities = new Map<string, string>();
  itemCategory$: Observable<any[]>;
  selectedEntities = new Map<string, string>();
  protected subs = new SubSink();
  item$: Observable<any>;
  packageListing: any = [];

  aggFuncs;
  checkInv$: Observable<any>;
  checkUomSave$: Observable<boolean>;
  itemInv$: any;
  SQLGuids: string[] = null;
  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private toastr: ToastrService,
    private processTemplateService: MrpProcessTemplateService,
    private mrpProcessTypeService : MrpProcessTypeService,
    private mrpProdsysService: MrpProdsysService,
    private invItemService: InventoryItemHdrService,
    protected readonly store: Store<InternalJobOrderStates>,
    private subQueryService: SubQueryService,
    private viewColFacade: ViewColumnFacade,
    private readonly componentStore: ComponentStore<{ localState: LocalState }>
  ) {
    super();
    this.rowSelection = "multiple";
    const customComparator = (valueA, valueB) => {
      if (valueA != null && "" !== valueA && valueB != null && "" !== valueB) {
        return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
      }
    };
    this.columnsDefs = [
      {
        headerName: 'Process Template', field: 'process_template', cellStyle: () => ({ 'text-align': 'left' }),  comparator: customComparator,
        filter: "agTextColumnFilter", suppressSizeToFit: true, checkboxSelection: true
      },
      {
        headerName: 'Machine', field: 'machine_code', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
       
      },
      {
        headerName: 'Process Type', field: 'process_type', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
       
      },
      {
        headerName: 'Output Inv Item', field: 'output_inv_item', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
       
      },
      // {
      //   headerName: 'Posting Status', field: 'posting_status', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
      //   valueFormatter: (params) => params.value ? params.value : 'DRAFT'
      // },
      // { headerName: 'Transaction Date', field: 'date_txn', cellStyle: () => ({ 'text-align': 'left' }),
      //   valueFormatter: params => moment(params.value).format('YYYY-MM-DD'), floatingFilter: true},
      // // { headerName: 'Branch', field: 'branch_name', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true },
      // // { headerName: 'Customer Name', field: 'customer_name', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true },
      // { headerName: 'Updated Date', field: 'updated_date', cellStyle: () => ({ 'text-align': 'left' }), valueFormatter: params => moment(params.value).format('YYYY-MM-DD')},
      // { headerName: 'Created Date', field: 'created_date', cellStyle: () => ({'text-align': 'left'}), valueFormatter: params => moment(params.value).format('YYYY-MM-DD')},
    ].map((c) => ({
      ...c,
      width: 30,
      filterParams: {
        debounceMs: 1000,
      },
    }));
  
    this.defaultColDef = {
      // floatingFilterComponentParams: { suppressFilterButton: true },
      minWidth: 200,
      flex: 1,
      sortable: true,
      resizable: true,
      suppressCsvExport: true,
      cellStyle: { textAlign: "left" },

      floatingFilter: true,
      filter: "agTextColumnFilter",
      // suppressMenu: true,
      floatingFilterComponentParams: { suppressFilterButton: true },
      menuTabs: ["filterMenuTab"],
    };
    // this.aggFuncs = {
    //   sum: this.sumFunction,
    // };
    this.rowModelType = "serverSide";
    this.serverSideStoreType = "partial";
  }

  ngOnInit() {
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(
      (resolve) => (this.prevIndex = resolve)
    );
    this.store.select(InternalJobOrderSelectors.selectEntity).subscribe(data => {
      this.item_guid = data.bl_mrp_job_order_hdr.item_guid
    })
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false,
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  // sumFunction(params) {
  //   // var result = 0;
  //   const date = DateConvert(params);
  //   return date;
  // }

  onSave() {
    ///dispatch actiond to save selected rows

    this.store.select(InternalJobOrderSelectors.selectEntity).subscribe(data => {
      this.gridApi.getSelectedRows().forEach(item => {
        console.log("Prcess instance create Items:--> ", item);
        let processInstanceContainer: MrpProcessInstanceContainerModel = new MrpProcessInstanceContainerModel();
        processInstanceContainer.bl_mrp_process_instance_hdr.job_order_hdr_guid = data.bl_mrp_job_order_hdr.guid;
        processInstanceContainer.bl_mrp_process_instance_hdr.process_template_hdr_guid = item.guid;
        processInstanceContainer.bl_mrp_process_instance_hdr.process_guid = item.process_type_guid;
        processInstanceContainer.bl_mrp_process_instance_hdr.machine_guid = item.machine_guid;
        processInstanceContainer.bl_mrp_process_instance_hdr.output_inv_item_guid = item.output_inv_item_guid;
        console.log("Container",processInstanceContainer);

        this.store.dispatch(InternalJobOrderActions.createProcessInstanceInit({
          processInstance: processInstanceContainer
        }));
      })
    })


    this.addSuccess = "Success";
    this.isClicked = "buttonSuccess";
    setTimeout(() => {
      this.addSuccess = "Add";
      this.isClicked = "primary";
      this.onReturn();
    }, 500);
  }


  pageFiltering(filterModel) {
    const noFilters = Object.keys(filterModel).length <= 0;
    if (noFilters) {
      return {
        by: () => true,
        isFiltering: noFilters,
      };
    }
    return {
      by: (viewModel) =>
        Object.keys(filterModel)
          .map((col) => {
            if (!viewModel[col]) {
              return false;
            }
            return typeof viewModel[col] === "number"
              ? +viewModel[col] === +filterModel[col].filter
              : viewModel[col]
                .toLowerCase()
                .includes(filterModel[col].filter.toLowerCase());
          })
          .reduce((p, c) => p && c),
      isFiltering: noFilters,
    };
  }

  pageSorting(sortModel) {
    return (data) => {
      if (sortModel.length <= 0) {
        return data;
      }
      let newData = data.map((o) => o);
      sortModel.forEach((model) => {
        const col = model.colId;
        newData =
          model.sort === "asc"
            ? newData.sort((p, c) => 0 - (p[col] > c[col] ? -1 : 1))
            : newData.sort((p, c) => 0 - (p[col] > c[col] ? 1 : -1));
      });
      return newData;
    };
  }

  // onGridReady(params) {
  //   this.gridApi = params.api;
  //   this.gridApi.closeToolPanel();


  //   // let guidsString = '';
  //   // guidsString = 'where guid not in (select host_fi_item_guid from bl_t2t_fi_item_to_tenant_link where status=\'ACTIVE\' )'

  //   // let query = 'Select guid as requiredGuid from bl_fi_mst_item_hdr ' + guidsString;

  //   // var query$ = this.subQueryService
  //   //   .post({ 'subquery': query, 'table': 'bl_fi_mst_item_hdr' }, this.apiVisa)
  //   //   .pipe(
  //   //     switchMap(res => of(res))
  //   //   );
  //   // query$
  //   //   .subscribe(res => {
     
  //   //   });

  //     this.retrieveData([
  //       // this.setCriteria("guids", res.data),
  //       this.setCriteria("calcTotalRecords", 'true')
  //     ])

  //   // CRUD will triggered this because of requiresUpdate state changed in reducer
  //   // this.subSink.sink = this.store.select(AttributeSetSelectors.selectRequiresUpdate)
  //   //   .subscribe((requiresUpdate) => {
  //   //     if (requiresUpdate)
  //   //       this.gridApi.purgeServerSideCache();
  //   //   })

  // }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
          // { columnName: 'doc_type', operator: '=', value: 'INTERNAL_SALES_INVOICE' },
          {
            columnName: 'guids', operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
        ];
        let totalrec = 0;
        this.processTemplateService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {

            totalrec = resolved.totalRecords;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                itemperm.bl_mrp_process_template_hdr.process_guid ? 
                this.mrpProcessTypeService.getByGuid(itemperm.bl_mrp_process_template_hdr.process_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),
                itemperm.bl_mrp_process_template_hdr.machine_guid ?
                this.mrpProdsysService.getByGuid(itemperm.bl_mrp_process_template_hdr.machine_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),
                itemperm.bl_mrp_process_template_hdr.output_inv_item_hdr_guid ? 
                  this.invItemService.getByGuid(itemperm.bl_mrp_process_template_hdr.output_inv_item_hdr_guid.toString(), apiVisa).pipe(
                    catchError((err) => of(err))
                  ) : of(null),
                ).pipe(
                  map(([b_a,b_b,b_c]) => {
                    console.log("B_a",b_a);
                    let obj = {"guid":'', "process_template": '',"process_type_guid" : '', "process_type": '',"machine_guid":'', "machine_code": '' ,"output_inv_item":'',"output_inv_item_guid":''};
                    obj.guid = itemperm.bl_mrp_process_template_hdr.guid.toString(),
                    obj.process_template = itemperm.bl_mrp_process_template_hdr.code ? itemperm.bl_mrp_process_template_hdr.code.toString() : '',
                    obj.process_type_guid = itemperm.bl_mrp_process_template_hdr.process_guid ? itemperm.bl_mrp_process_template_hdr.process_guid.toString() : '',
                    obj.process_type =b_a ? b_a.data.bl_mrp_process_type_hdr.name : '',
                    obj.machine_guid =itemperm.bl_mrp_process_template_hdr.machine_guid ? itemperm.bl_mrp_process_template_hdr.machine_guid.toString() : '',
                    obj.machine_code = b_b ? b_b.data.bl_mrp_prodsys_hdr.code : '',
                    obj.output_inv_item_guid = itemperm.bl_mrp_process_template_hdr.output_inv_item_hdr_guid ? itemperm.bl_mrp_process_template_hdr.output_inv_item_hdr_guid.toString() : '',
                    obj.output_inv_item = b_c ? b_c.data.bl_inv_mst_item_hdr.name : ''
                    // obj.uom = b_b ? b_b.data.bl_fi_generic_doc_line.uom : '';
               
               
                    // obj.guid = itemperm.bl_t2t_fi_item_to_tenant_link.guid.toString();
                    // obj.basic_type = b_a.data.bl_fi_mst_item_hdr.txn_type;
                    // obj.sub_item_type = b_a.data.bl_fi_mst_item_hdr.sub_item_type;

                    console.log("Object ->>>>>",obj);
                    return obj;
                  })
                )
            )
            );
            return iif(() => {
              console.log("Resolved",resolved);
              return resolved.totalRecords > 0},
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              this.store.dispatch(InternalJobOrderActions.loadGenDocLinkSuccess({ totalRecords: totalrec }));
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
            this.store.dispatch(InternalJobOrderActions.loadGenDocLinkFailed({ error: err.message }));
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
      if (resolved) {
        this.gridApi.refreshServerSideStore({ purge: true });
        this.store.dispatch(InternalJobOrderActions.resetAgGrid());
      }
    });
  }


  // searchQuery(query: string, table: string) {
  //   let flag = true;

  //   if (flag) {
  //     flag = false;
  //     var query$ = this.subQueryService
  //       .post({ 'subquery': query, 'table': table }, this.apiVisa)
  //       .pipe(
  //         switchMap(res => of(res))
  //       );
  //     query$
  //       //.filter((res: ApiResponseModel<any>) => res.data.length > 0)
  //       .subscribe(res => {
  //         this.retrieveData([
  //           this.setCriteria("guids", res.data),
  //           this.setCriteria("calcTotalRecords", 'true')
  //         ])
  //       });
  //     query$
  //       //.filter((res: ApiResponseModel<any>) => res.data.length === 0)
  //       .subscribe(res => {
  //         this.clear()
  //       });
  //   }
  // }

  // retrieveData(criteria) {
  //   if (criteria) {
  //     const datasource = {
  //       getRows: this.getRowsFactory(criteria)
  //     };
  //     this.gridApi.setServerSideDatasource(datasource);
  //   }
  // }

  // getRowsFactory(criteria) {
  //   let offset = 0;
  //   let limit = this.paginationComp.rowPerPage;
  //   let callbackTotal = 0;
  //   let callback = false;
  //   let temp_criteria = null;

  //   return grid => {
  //     var filter = this.pageFiltering(grid.request.filterModel);
  //     var sortOn = this.pageSorting(grid.request.sortModel);

  //     if (!Object.keys(grid.request.filterModel).length) {
  //       offset = grid.request.startRow;
  //       limit = grid.request.endRow - offset;

  //       if (criteria.find(x => x.columnName === 'guids')?.value.length > 100) {
  //         callbackTotal = criteria.find(x => x.columnName === 'guids')?.value.length;
  //         callback = true;
  //         let guids = criteria.find(x => x.columnName === 'guids').value.slice(offset, limit);
  //         temp_criteria = [
  //           this.setCriteria("guids", guids),
  //           this.setCriteria("calcTotalRecords", 'true')
  //         ];
  //       }
  //     }

  //     // this.store.dispatch(ServiceNoteActions.loadItemsInit({ request: grid.request }));


  //     this.subs.sink = this.jsService.getByCriteria(
  //       new Pagination(
  //         offset,
  //         limit,
  //         temp_criteria ? temp_criteria : criteria,
  //         [
  //           // { columnName: 'orderBy', value: 'date_updated' },
  //           { columnName: 'order', value: 'DESC' },
  //         ]
  //       ),
  //       this.apiVisa).subscribe(resolved => {

  //         const data = sortOn(resolved.data.filter(o => filter.by(o)));
  //         let totalRecords;
  //         if (!callback) {
  //           totalRecords = filter.isFiltering ? resolved.totalRecords : data.length;
  //         }
  //         else {
  //           totalRecords = callbackTotal;
  //         }

  //         if (this.paginationComp.currentPage > this.paginationComp.totalPage.value)
  //           this.paginationComp.firstPage();

  //         grid.successCallback(data, totalRecords);
  //         // this.store.dispatch(ServiceNoteActions.loadItemSuccess({ totalRecords }));
  //       }, (err) => {
  //         grid.failCallback();
  //         // this.store.dispatch(ServiceNoteActions.loadItemsFailed({ error: err.message }));
  //       });
  //   }
  // }

  // onSearch(e: SearchQueryModel) {
  //   // if (!e.isEmpty) {
  //   //   const sql = {
  //   //     subquery: e.queryString,
  //   //     table: e.table,
  //   //   };
  //   //   this.subs.sink = this.sqlService.post(sql, this.apiVisa).subscribe({
  //   //     next: (resolve) => {
  //   //       this.SQLGuids = resolve.data;
  //   //       this.paginationComp.firstPage();
  //   //       this.gridApi.refreshServerSideStore();
  //   //     },
  //   //   });
  //   // } else {
  //   //   this.SQLGuids = null;
  //   //   this.paginationComp.firstPage();
  //   //   this.gridApi.refreshServerSideStore();
  //   // }
  //   !e.isEmpty ? this.searchQuery(e.queryString, e.table) : this.retrieveData([this.setCriteria('calcTotalRecords', 'true')]);
  // }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({
        next: resolve => {
          this.SQLGuids = resolve.data;
          if(this.SQLGuids.length!==0 || this.SQLGuids.length<=50){
            // this.gridApi.refreshServerSideStore();
            this.pagination.conditionalCriteria = [
              {
                columnName: 'guids', operator: '=',
                value: this.SQLGuids.toString()
              }
            ];
          
          }else
          {
            this.toastr.error("Result Set Too Large. Please Refine Search", "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 2000,
            });
          }
        }
      });
    } else {
      this.SQLGuids = null;
      // this.store.dispatch(JobSheetActions.loadJobSheetInit({pagination: new Pagination(0,50)}))
      // this.gridApi.refreshServerSideStore();
    }
  }


  setCriteria(columnName, value) {
    return { columnName, operator: '=', value }
  }

  clear() {
    const dataSource = {
      getRows(params: any) {
        params.successCallback([], 0);
      },
    };
    this.gridApi.setServerSideDatasource(dataSource);
  }
  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }
  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
