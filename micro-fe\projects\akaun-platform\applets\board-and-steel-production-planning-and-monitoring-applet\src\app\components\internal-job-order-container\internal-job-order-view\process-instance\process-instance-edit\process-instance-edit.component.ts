import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { ViewColumnFacade } from '../../../../../facades/view-column.facade';
import { ComponentStore } from '@ngrx/component-store';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { MatTabGroup } from '@angular/material/tabs';

interface LocalState {
  deactivateReturn: boolean;
  deactivateList: boolean;
  selectedIndex: number;
  selectedIndexInvoiceItem: number;
  SalesRmaEdit: boolean;
  selectedItem: any;
}

@Component({
  selector: 'app-process-instance-edit',
  templateUrl: './process-instance-edit.component.html',
  styleUrls: ['./process-instance-edit.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessInstanceEditComponent extends ViewColumnComponent {

  protected subs = new SubSink();
  
  protected compName = 'Process Instance Edit';
  protected readonly index = 5;
  protected localState: LocalState;
  protected prevLocalState: any;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => (state.selectedIndex));
  readonly selectedIndexInvoiceItem$ = this.componentStore.select(state => (state.selectedIndexInvoiceItem));

  prevIndex: number;
  apiVisa = AppConfig.apiVisa;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;

  constructor(
    private viewColFacade: ViewColumnFacade,
    // protected readonly store: Store<ServiceNoteStates>,
    protected readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    // if (this.prevIndex === 2) {
    //   console.log('here')
    //   this.componentStore.patchState({ SalesRmaEdit: true });
    //   console.log(this.localState);
    // }
    // else 
    //   this.componentStore.patchState({ SalesRmaEdit: false });
  }

  onSubmit() {

  }

  goToAddLineItem(lineItem: any) {
    // this.store.dispatch(ServiceNoteActions.selectLineItem({ lineItem }));
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, 
      deactivateList: false,
      deactivateReturn: true,
      // selectedItem: lineItem.item_guid
    });
    this.viewColFacade.onNextAndReset(this.index, 3);
  }

  goToServiceAndRepairAddLineItem(lineItem: any) {
    console.log("Service and repair item",lineItem);
    // this.store.dispatch(ServiceNoteActions.selectLineItem({ lineItem }));
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, 
      deactivateList: false,
      deactivateReturn: true,
      selectedItem: lineItem.item_guid
    });
    this.viewColFacade.onNextAndReset(this.index, 4);
  }

  goTo3rdPartyLineItems(lineItem: any) {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, 
      deactivateList: false,
      deactivateReturn: true,
      // selectedItem: lineItem.item_guid
    });
    this.viewColFacade.onNextAndReset(this.index, 7);
  }

  goToSearchBySerialNoItem(lineItem: any) {
    console.log("Search by serial no : ",lineItem);
    // this.store.dispatch(ServiceNoteActions.selectLineItem({ lineItem }));
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, 
      deactivateList: false,
      deactivateReturn: true,
      selectedItem: lineItem.item_guid
    });
    this.viewColFacade.onNextAndReset(this.index, 8);
  }

  onReturn() {
    this.viewColFacade.updateInstance<LocalState>(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState, selectedIndex: this.matTab.selectedIndex
      });
    }
    this.subs.unsubscribe();  
  }

}