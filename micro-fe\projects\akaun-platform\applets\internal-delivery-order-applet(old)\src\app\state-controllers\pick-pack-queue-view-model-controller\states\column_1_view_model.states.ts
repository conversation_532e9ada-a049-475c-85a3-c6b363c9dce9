export interface Column1ViewModelState {
  refreshGenDocListing: boolean;
  searchCriteria: string;
  advanceSearch_Customer_Field: [];
  advanceSearch_SalesAgent_Field: [];
  advanceSearch_Branch_Field: [];
  advanceSearch_DeliveryRegion_Field: [];
  advanceSearch_CreationDateFrom_Field: string;
  advanceSearch_CreationDateTo_Field: string;
  advanceSearch_TransactionDateFrom_Field: string;
  advanceSearch_TransactionDateTo_Field: string;
}

export const initialState: Column1ViewModelState = {
  refreshGenDocListing: false,
  searchCriteria: "",
  advanceSearch_Customer_Field: [],
  advanceSearch_SalesAgent_Field: [],
  advanceSearch_Branch_Field: [],
  advanceSearch_DeliveryRegion_Field: [],
  advanceSearch_CreationDateFrom_Field: "",
  advanceSearch_CreationDateTo_Field: "",
  advanceSearch_TransactionDateFrom_Field: "",
  advanceSearch_TransactionDateTo_Field: "",
};
