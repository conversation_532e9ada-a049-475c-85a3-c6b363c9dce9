import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';
import { EntityConstants, ENTITY_TYPE, STATUS } from '../constants/customer-constants';

export const customerSearchModel: SearchModel = {
  label: {
    type: 'Entity Type',
    modifiedDate: 'Modified Date',
    status: 'Status',
  },
  dataType: {
    type: ['select', ENTITY_TYPE],
    modifiedDate: 'date',
    status: ['select', STATUS],
  },
  form: new FormGroup({
    type: new FormControl(),
    modifiedDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    status: new FormControl()
  }),

  query: (query) => {
    return `
   ( ( hdr.customer_code ilike '%${query}%' ) or ( hdr.name ilike '%${query}%' ) )`;
  },

  table: `bl_fi_mst_entity_hdr`,
  queryCallbacks: {
    type: type => {
      if (type) {
        return `  hdr.txn_type = '${type}'`;
      }
      return '';
    },
    status: status => {
      if (status) {
        return `  hdr.status = '${status}'`;
      } return '';
    },
    modifiedDate: modifiedDate => {
      if (modifiedDate.from || modifiedDate.to) {
        const from = modifiedDate.from ? modifiedDate.from : modifiedDate.to;
        const to = modifiedDate.to ? modifiedDate.to : modifiedDate.from;
        return `  hdr.updated_date BETWEEN '${from.format(EntityConstants.DateTimeFormat)}' AND '${to.format(EntityConstants.DateTimeFormat)}'`;
      }
      return '';
    },
  },
};

