import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { DeleteRendererComponent } from './delete-renderer.component';

describe('DeleteRendererComponent', () => {
  let component: DeleteRendererComponent;
  let fixture: ComponentFixture<DeleteRendererComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ DeleteRendererComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DeleteRendererComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
