import { FormGroup } from '@angular/forms';
import { EntityContainerModel } from 'blg-akaun-ts-lib';

import * as moment from 'moment';
import { EntityConstants } from './constants/customer-constants';

export class EntityViewModel {
  guid: string;
  customer_code: string;
  name: string;
  txn_type: string;
  status: string;
  created_date: string;
  updated_date: string;
  currency: string;
}

export function containerToViewModel(customer: EntityContainerModel): EntityViewModel {
  let currency;
  let code;
  let hdr = customer.bl_fi_mst_entity_hdr;
  // var extCode = customer.bl_fi_mst_entity_ext.filter(element =>
  //   element.param_code === CustomerConstants.CUSTOMER_CODE);
  // if (extCode.length != 0) {
  //   extCode.forEach(pt => {
  //     code = pt.value_string
  //   })
  // }
  // let currencyCode = customer.bl_fi_mst_entity_ext.filter(element =>
  //   element.param_code === CustomerConstants.CURRENCY);
  // if (currencyCode.length != 0) {
  //   currencyCode.forEach(pt => {
  //     if (pt.value_json != null) {
  //       currency = pt.value_json.currency;
  //     }

  //   });
  // }
  let create_date = moment(hdr.created_date).format(EntityConstants.DateTimeFormat);
  let updated_date = moment(hdr.updated_date).format(EntityConstants.DateTimeFormat);
  return {
    guid: hdr.guid,
    txn_type: hdr.txn_type,
    customer_code: hdr.customer_code,
    currency: currency,
    name: hdr.name,
    created_date: create_date,
    updated_date: updated_date,
    status: hdr.status,

  } as EntityViewModel;
}

