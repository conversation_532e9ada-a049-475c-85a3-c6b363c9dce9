import { createAction, props } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';

export const addPlannedOutput = createAction(
    '[Draft: Planned Output] Add Planned Output',
    props<{line: bl_fi_generic_doc_line_RowClass}>());
export const addManyPlannedOutput = createAction(
    '[Draft: Planned Output] Add Many Planned Output',
    props<{lines: bl_fi_generic_doc_line_RowClass[]}>());
export const editPlannedOutput = createAction(
    '[Draft: Planned Output] Edit Planned Output',
    props<{line: bl_fi_generic_doc_line_RowClass}>());
export const deletePlannedOutput = createAction('[Draft: Planned Output] Delete Planned Output', props<{guid: string}>());
export const resetPlannedOutput = createAction('[Draft: Planned Output] Reset');
