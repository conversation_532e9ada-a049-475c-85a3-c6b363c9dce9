import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { SubSink } from 'subsink2';
import { PNSSelectors } from '../../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../../state-controllers/draft-controller/store/states';
import { InternalDeliveryOrderActions } from '../../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderSelectors } from '../../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { InternalDeliveryOrderStates } from '../../../../state-controllers/internal-delivery-order-controller/store/states';

@Component({
  selector: 'app-internal-delivery-order-create-line-items',
  templateUrl: './internal-delivery-order-create-line-items.component.html',
  styleUrls: ['./internal-delivery-order-create-line-items.component.css']
})
export class InternalDeliveryOrderCreateLineItemsComponent implements OnInit {

  @Input() localState: any;
  @Input() rowData: bl_fi_generic_doc_line_RowClass[];

  @Output() next = new EventEmitter();
  @Output() lineItem = new EventEmitter<bl_fi_generic_doc_line_RowClass>();

  private subs = new SubSink();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;
  updatedRowData;
  editMode;

  columnsDefs = [
    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Tracking Id', field: 'tracking_id', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Delivery Date', field: 'item_property_json.fromCableLength'},
    {headerName: 'UOM', field: 'uom', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Quantity', field: 'item_property_json.quantity'},
    {headerName: 'Quantity', field: 'quantity_base'},
    // {headerName: 'Stock Bal Qty', field: 'item_property_json.drumSize'}
  ];
 
  constructor(
    private draftStore: Store<DraftStates>,
    private store: Store<InternalDeliveryOrderStates>
  ) { }

  ngOnInit() {

    this.updatedRowData = this.rowData.filter(x => x.status === 'ACTIVE');    
    this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.selectEditMode).subscribe(
      (mode) => {
        this.editMode = mode;
      });

    if (this.editMode){
      this.draftStore.select(PNSSelectors.selectAll).subscribe({
        next: resolve => {
          console.log(resolve);
          this.updatedRowData = resolve.filter(x => x.status === 'ACTIVE');
        }
      })
    }
    else{
      this.draftStore.select(PNSSelectors.selectAll).subscribe({
        next: resolve => {
          this.updatedRowData = resolve.filter(x => x.status === 'ACTIVE');
        }
      })
    }
    
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

  onNext() {
    this.next.emit();
  }

  onRowClicked(event: bl_fi_generic_doc_line_RowClass) {
    this.store.dispatch(InternalDeliveryOrderActions.selectLineItemInit({lineItem: event}));
    this.store.dispatch(InternalDeliveryOrderActions.selectPricingSchemeLink({ item: event }));
    console.log("event", event);
    this.lineItem.emit(event);
  }

  getRowClassForAgGrid = params => {
    if (params.node.rowIndex % 2 === 1) {
      return 'ag-grid-odd-rows';
    }
  }

}
