import { Component, OnInit, Input, OnDestroy, Output, EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { AppletContainerModel, AppletService, bl_applet_ext_RowClass } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { exhaustMap, map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ToastConstants } from '../../../models/constants/toast.constants';

@Component({
  selector: 'app-default-settings',
  templateUrl: './default-settings.component.html',
  styleUrls: ['./default-settings.component.css'],
})
export class DefaultSettingsComponent implements OnInit, OnDestroy {

  private subs = new SubSink();

  apiVisa = AppConfig.apiVisa;

  branch = new FormControl();
  location = new FormControl();

  appletContainer: AppletContainerModel;

  constructor(
    // TODO: change this to states after demo
    private appletService: AppletService,
    private toastr: ToastrService
  ) { }

  ngOnInit() {
    this.subs.sink = this.appletService.getByGuid(sessionStorage.getItem('appletGuid'), this.apiVisa).subscribe({next: resolve => {
      if (resolve) {
        this.appletContainer = resolve.data;
        const branch = resolve.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.value_json?.DEFAULT_BRANCH;
        const location = resolve.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.value_json?.DEFAULT_LOCATION;
        this.branch.patchValue(branch);
        this.location.patchValue(location);
      }
    }});
    this.subs.sink = this.branch.valueChanges.subscribe({next: resolve => {
      const exist = this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS');
      if (exist) {
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json = {
          ...this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json,
          DEFAULT_BRANCH: resolve
        };
      } else {
        const appletExt = new bl_applet_ext_RowClass();
        appletExt.param_code = 'APPLET_SETTINGS';
        appletExt.param_name = 'APPLET_SETTINGS';
        appletExt.param_type = 'JSON';
        appletExt.value_json = {
          DEFAULT_BRANCH: resolve
        };
        this.appletContainer.bl_applet_exts.push(appletExt);
      }
    }});
    this.subs.sink = this.location.valueChanges.subscribe({next: resolve => {
      const exist = this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS');
      if (exist) {
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json = {
          ...this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json,
          DEFAULT_LOCATION: resolve
        };
      } else {
        const appletExt = new bl_applet_ext_RowClass();
        appletExt.param_code = 'APPLET_SETTINGS';
        appletExt.param_name = 'APPLET_SETTINGS';
        appletExt.param_type = 'JSON';
        appletExt.value_json = {
          DEFAULT_LOCATION: resolve
        };
        this.appletContainer.bl_applet_exts.push(appletExt);
      }
    }});
  }

  onSave() {
    this.subs.sink = this.appletService.getByGuid(sessionStorage.getItem('appletGuid'), this.apiVisa).pipe(
      map(a => {
        const revision = a.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.revision;
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').revision = revision ? revision : null;
        this.appletContainer = {
          bl_applet_hdr: {
            ...this.appletContainer.bl_applet_hdr,
            revision: a.data.bl_applet_hdr.revision
          },
          bl_applet_exts: [
            ...this.appletContainer.bl_applet_exts
          ]
        };
        return this.appletContainer;
      }),
      exhaustMap(b => this.appletService.put(b, this.apiVisa))
    ).subscribe({next: resolve => {
      this.toastr.success(
        ToastConstants.settingsSavedSuccess,
        'Success',
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.appletContainer = resolve.data;
    }, error: err => {
      this.toastr.error(
        err.message,
        'Error',
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
    }});
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
