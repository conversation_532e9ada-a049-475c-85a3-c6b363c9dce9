import { createFeatureSelector, createSelector } from '@ngrx/store';
import { entityFeatureKey } from '../reducers/customer.reducers';
import { EntityStates } from '../states';
import { entityAdapter, EntitiesState } from '../states/customer.states';

export const selectEntityFeature = createFeatureSelector<EntitiesState>(entityFeatureKey);

// export const selectContainer = createSelector(
//   selectCustomerFeature,
//   (entities) => entities.draftContainer
// );
export const selectRowCat = (state: EntityStates) => state.entity.tempRow;
export const selectTempCat = (state: EntityStates) => state.entity.tempCat;
export const selectItemCategory = (state: EntityStates) => state.entity.itemCategory;
export const selectUpdatedCat = (state: EntityStates) => state.entity.updatedCat;

export const selectAppLoginCreatedBy = (state: EntityStates) => state.entity.appLoginCreatedBy;
export const selectAppLoginModifiedBy = (state: EntityStates) => state.entity.appLoginModifiedBy;

export const selectGuid = (state: EntityStates) => state.entity.selectedGuid;
export const selectEntity = (state: EntityStates) => state.entity.selectedEntity;
export const selectLineItem = (state: EntityStates) => state.entity.selectedLineItem;

export const selectContainer = (state: EntityStates) => state.entity.draftContainer;

export const selectPaymentConfig = (state: EntityStates) => state.entity.extPayment;
export const selectNewPaymentConfig = (state: EntityStates) => state.entity.paymentConfig;
export const selectLogin = (state: EntityStates) => state.entity.extLogin;
export const selectTax = (state: EntityStates) => state.entity.extTax;
// export const selectAddress = (state: EntityStates) => state.customerExt.extAddress;
// export const selectAddressExt = (state: EntityStates) => state.customerExt.extAddress;
export const selectAddress = (state: EntityStates) => state.entity.hdrAddress;
export const selectBranch = (state: EntityStates) => state.entity.extBranch;
export const selectContact = (state: EntityStates) => state.entity.extContact;
export const selectTerm = (state: EntityStates) => state.entity.extTerm;
export const selectLimit = (state: EntityStates) => state.entity.extLimit;

export const selectEntityType = (state: EntityStates) => state.entity.entityType;


export const selectExt = (state: EntityStates) => state.entity.selectExt;
export const selectAgGrid = (state: EntityStates) => state.entity.
  updateAgGrid;

export const onSaveFail = (state: EntityStates) => state.entity.
  onSaveFail;

// if callaggrid=false equal to default and savefail=true
// if callagrid =true equal to savefail false
// default savefail=true

export const selectContactLine = (state: EntityStates) => state.entity.selectContactLine;

export const selectDraft = (state: EntityStates) => state.entity.draft;
export const selectPMCDraft = (state: EntityStates) => state.entity.pmcDraft;

export const selectCatContainer = (state: EntityStates) => state.entity.draftCatContainer;
export const selectCatGuid = (state: EntityStates) => state.entity.selectedCatGuid;
//TERM
export const selectTermContainer = (state: EntityStates) => state.entity.draftTermContainer;
export const selectTermGuid = (state: EntityStates) => state.entity.selectedTermGuid;
//LIMIT
export const selectLimitContainer = (state: EntityStates) => state.entity.draftLimitContainer;
export const selectLimitGuid = (state: EntityStates) => state.entity.selectedLimitGuid;

export const selectCurrency = (state: EntityStates) => state.entity.currency;
export const selectedRow = (state: EntityStates) => state.entity.selectedRow;

export const selectToggleMode = (state: EntityStates) => state.entity.selectedToggleMode;
export const {
  selectIds,
  selectEntities,
  selectAll,
  selectTotal
} = entityAdapter.getSelectors(selectEntityFeature);
