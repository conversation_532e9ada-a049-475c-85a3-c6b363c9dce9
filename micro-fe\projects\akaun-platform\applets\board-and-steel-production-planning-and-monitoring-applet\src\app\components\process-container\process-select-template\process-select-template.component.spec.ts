import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProcessSelectTemplateComponent } from './process-select-template.component';

describe('ProcessSelectTemplateComponent', () => {
  let component: ProcessSelectTemplateComponent;
  let fixture: ComponentFixture<ProcessSelectTemplateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ProcessSelectTemplateComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ProcessSelectTemplateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
