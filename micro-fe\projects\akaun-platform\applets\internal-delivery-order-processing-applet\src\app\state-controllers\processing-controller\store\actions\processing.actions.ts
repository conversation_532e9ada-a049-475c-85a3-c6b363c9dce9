import { createAction, props } from '@ngrx/store';
import { GenericDocContainerModel } from 'blg-akaun-ts-lib';

export const loadDeliveryOrdersInit = createAction('[Delivery Order] Load Init', props<{ request: any }>());
export const loadDeliveryOrderSuccess = createAction('[Delivery Order] Load Success', props<{ totalRecords: number}>());
export const loadDeliveryOrderFailed = createAction('[Delivery Order] Load Failed', props<{error: string}>());

export const selectEntity = createAction('[Delivery Order] Select Entity', props<{ entity: GenericDocContainerModel }>());

export const editDeliveryOrderInit = createAction('[Delivery Order] Edit Init', props<{status: string}>());
export const editDeliveryOrderSuccess = createAction('[Delivery Order] Edit Success');
export const editDeliveryOrderFailed = createAction('[Delivery Order] Edit Failed', props<{error: string}>());

export const resetAgGrid = createAction('[Delivery Order] Reset Ag Grid Update');
