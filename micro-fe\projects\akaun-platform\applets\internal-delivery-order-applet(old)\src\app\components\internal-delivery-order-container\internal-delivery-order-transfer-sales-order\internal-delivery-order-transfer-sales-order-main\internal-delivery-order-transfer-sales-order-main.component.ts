import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-internal-delivery-order-transfer-sales-order-main',
  templateUrl: './internal-delivery-order-transfer-sales-order-main.component.html',
  styleUrls: ['./internal-delivery-order-transfer-sales-order-main.component.css']
})
export class InternalDeliveryOrderTransferSalesOrderMainComponent implements OnInit, OnDestroy {

  private subs = new SubSink();

  form: FormGroup;

  controls = [
    {label: 'Branch', formControl: 'branch', type: 'text', readonly: true},
    {label: 'Doc No', formControl: 'docNo', type: 'text', readonly: true},
    {label: 'Customer Name', formControl: 'customerName', type: 'text', readonly: true},
    {label: 'Customer Type', formControl: 'customerType', type: 'text', readonly: true},
    {label: 'Credit Term', formControl: 'creditTerm', type: 'text', readonly: true},
    {label: 'Sales Agent', formControl: 'salesAgent', type: 'text', readonly: true},
    {label: 'Shipping Info', formControl: 'shippingInfo', type: 'text', readonly: true},
    {label: 'Billing Info', formControl: 'billingInfo', type: 'text', readonly: true},
    {label: 'Ship Via', formControl: 'shipVia', type: 'text', readonly: true},
    {label: 'Remarks', formControl: 'remarks', type: 'text', readonly: true},
  ];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      branch: new FormControl(),
      docNo: new FormControl(),
      customerName: new FormControl(),
      customerType: new FormControl(),
      creditTerm: new FormControl(),
      salesAgent: new FormControl(),
      shippingInfo: new FormControl(),
      billingInfo: new FormControl(),
      shipVia: new FormControl(),
      remarks: new FormControl(),
    });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
