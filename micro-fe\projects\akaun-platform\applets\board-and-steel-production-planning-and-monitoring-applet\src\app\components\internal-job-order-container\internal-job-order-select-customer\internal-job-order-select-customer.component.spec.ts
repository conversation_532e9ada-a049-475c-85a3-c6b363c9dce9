import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalJobOrderSelectCustomerComponent } from './internal-job-order-select-customer.component';

describe('InternalJobOrderSelectCustomerComponent', () => {
  let component: InternalJobOrderSelectCustomerComponent;
  let fixture: ComponentFixture<InternalJobOrderSelectCustomerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalJobOrderSelectCustomerComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalJobOrderSelectCustomerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
