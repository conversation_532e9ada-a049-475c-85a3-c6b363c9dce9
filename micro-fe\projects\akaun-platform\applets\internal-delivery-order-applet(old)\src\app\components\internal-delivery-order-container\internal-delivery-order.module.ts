import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';

import { BlgAkaunNgLibModule } from 'blg-akaun-ng-lib';

import { InternalDeliveryOrderContainerComponent } from './internal-delivery-order-container.component';
import { InternalDeliveryOrderListingComponent } from './internal-delivery-order-listing/internal-delivery-order-listing.component';
import { InternalDeliveryOrderViewComponent } from './internal-delivery-order-view/internal-delivery-order-view.component';
import { InternalDeliveryOrderViewLineItemComponent } from './internal-delivery-order-view-line-item/internal-delivery-order-view-line-item.component';
import { InternalDeliveryOrderCreateComponent } from './internal-delivery-order-create/internal-delivery-order-create.component';
import { InternalDeliveryOrderCreateMainComponent } from './internal-delivery-order-create/internal-delivery-order-create-main/internal-delivery-order-create-main.component';
import { InternalDeliveryOrderCreateLineItemsComponent } from './internal-delivery-order-create/internal-delivery-order-create-line-items/internal-delivery-order-create-line-items.component';
import { InternalDeliveryOrderCreateLineItemComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-create-line-item.component';
import { InternalDeliveryOrderCreateCustomFieldsComponent } from './internal-delivery-order-create/internal-delivery-order-create-custom-fields/internal-delivery-order-create-custom-fields.component';
import { InternalDeliveryOrderCreateLineItemSalesOrderComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-create-line-item-sales-order/internal-delivery-order-create-line-item-sales-order.component';
import { InternalDeliveryOrderCreateLineItemQuotationComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-create-line-item-quotation/internal-delivery-order-create-line-item-quotation.component';
import { InternalDeliveryOrderCreateLineItemInvoiceComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-create-line-item-invoice/internal-delivery-order-create-line-item-invoice.component';
import { InternalDeliveryOrderTransferSalesOrderComponent } from './internal-delivery-order-transfer-sales-order/internal-delivery-order-transfer-sales-order.component';
import { InternalDeliveryOrderTransferSalesOrderMainComponent } from './internal-delivery-order-transfer-sales-order/internal-delivery-order-transfer-sales-order-main/internal-delivery-order-transfer-sales-order-main.component';
import { InternalDeliveryOrderTransferSalesOrderCustomerInfoComponent } from './internal-delivery-order-transfer-sales-order/internal-delivery-order-transfer-sales-order-customer-info/internal-delivery-order-transfer-sales-order-customer-info.component';
import { InternalDeliveryOrderTransferSalesOrderLineItemsComponent } from './internal-delivery-order-transfer-sales-order/internal-delivery-order-transfer-sales-order-line-items/internal-delivery-order-transfer-sales-order-line-items.component';
import { InternalDeliveryOrderCreateAccountComponent } from './internal-delivery-order-create/internal-delivery-order-create-account/internal-delivery-order-create-account.component';
import { AccountAddressComponent } from './internal-delivery-order-create/internal-delivery-order-create-account/account-address/account-address.component';
import { AccountContactPersonComponent } from './internal-delivery-order-create/internal-delivery-order-create-account/account-contact-person/account-contact-person.component';
import { AccountDeliveryDetailsComponent } from './internal-delivery-order-create/internal-delivery-order-create-account/account-delivery-details/account-delivery-details.component';
import { AccountEntityDetailsComponent } from './internal-delivery-order-create/internal-delivery-order-create-account/account-entity-details/account-entity-details.component';
import { InternalDeliveryOrderCreateDepartmentComponent } from './internal-delivery-order-create/internal-delivery-order-create-department/internal-delivery-order-create-department.component';
import { internalDeliveryOrderFeatureKey } from '../../state-controllers/internal-delivery-order-controller/store/reducers/internal-delivery-order.reducers';
import { reducers } from '../../state-controllers/internal-delivery-order-controller/store/reducers';
import { InternalDeliveryOrderEffects } from '../../state-controllers/internal-delivery-order-controller/store/effects/internal-delivery-order.effects';
import { InternalDeliveryOrderSelectCustomerComponent } from './internal-delivery-order-select-customer/internal-delivery-order-select-customer.component';
import { InternalDeliveryOrderSelectContactPersonComponent } from './internal-delivery-order-select-contact-person/internal-delivery-order-select-contact-person.component';
import { InternalDeliveryOrderSelectBillingAddressComponent } from './internal-delivery-order-select-billing-address/internal-delivery-order-select-billing-address.component';
import { InternalDeliveryOrderSelectShippingAddressComponent } from './internal-delivery-order-select-shipping-address/internal-delivery-order-select-shipping-address.component';

import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { AgGridModule } from 'ag-grid-angular';
import { InternalDeliveryOrderCreateLineItemSTGRNComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-create-line-item-ST-GRN/internal-delivery-order-create-line-item-ST-GRN.component';
import { InternalDeliveryOrderEditLineItemComponent } from './internal-delivery-order-edit-line-item/internal-delivery-order-edit-line-item.component';
import { ItemDetailsComponent } from './internal-delivery-order-edit-line-item/item-details/item-details.component';
import { MainDetailsComponent } from './internal-delivery-order-edit-line-item/item-details/main-details/main-details.component';
import { DeliveryInstructionsComponent } from './internal-delivery-order-edit-line-item/item-details/delivery-instructions/delivery-instructions.component';
import { SerialNumberComponent } from './internal-delivery-order-edit-line-item/serial-number/serial-number.component';
import { BinNumberComponent } from './internal-delivery-order-edit-line-item/bin-number/bin-number.component';
import { BatchNumberComponent } from './internal-delivery-order-edit-line-item/batch-number/batch-number.component';
import { SerialNumberImportComponent } from './internal-delivery-order-edit-line-item/serial-number/serial-number-import/serial-number-import.component';
import { SerialNumberListingComponent } from './internal-delivery-order-edit-line-item/serial-number/serial-number-listing/serial-number-listing.component';
import { SerialNumberScanComponent } from './internal-delivery-order-edit-line-item/serial-number/serial-number-scan/serial-number-scan.component';
import { BinNumberListingComponent } from './internal-delivery-order-edit-line-item/bin-number/bin-number-listing/bin-number-listing.component';
import { BatchNumberListingComponent } from './internal-delivery-order-edit-line-item/batch-number/batch-number-listing/batch-number-listing.component';

import { InternalDeliveryOrderAddLineItemComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-add-line-item/internal-delivery-order-add-line-item.component';
import { AddLineItemItemDetailsComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-add-line-item/item-details/add-line-item-item-details.component';
import { InternalDeliveryOrderKOForSTGRNComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-ko-for-ST-GRN/internal-delivery-order-ko-for-ST-GRN.component';
import { InternalDeliveryOrderKOBySTGRNComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-ko-by-ST-GRN/internal-delivery-order-ko-by-ST-GRN.component';
import { KnockoffAddComponent } from './internal-delivery-order-edit-line-item/knockoff-add/knockoff-add.component'
import { KnockoffEditComponent } from './internal-delivery-order-edit-line-item/knockoff-edit/knockoff-edit.component'
// import { DocLinkKoByComponent } from './internal-delivery-order-edit-line-item/doc-link-ko-by/doc-link-ko-by.component';
// import { DocLinkKoForComponent } from './internal-delivery-order-edit-line-item/doc-link-ko-for/doc-link-ko-for.component';
import { LineContainerKoByComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-line-container-ko-by/line-container-ko-by.component';
import { LineContainerKoForComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-line-container-ko-for/line-container-ko-for.component';
import { AdvancedSearchIdoModule } from '../utilities/advanced-search-ido/advanced-search-ido.module';
import { entityFeatureKey } from '../../state-controllers/customer-controller/store/reducers/customer.reducers';
import { CreditLimitEffect, CreditTermEffect, CustomerEffects } from '../../state-controllers/customer-controller/store/effects';
import { reducerss } from '../../state-controllers/customer-controller/store/reducers';
import { CustomerCreateComponent } from './internal-delivery-order-select-customer/customer-create/customer-create.component';
import { CustomerCreateMainComponent } from './internal-delivery-order-select-customer/customer-create/customer-create-main/customer-create-main.component';
import { CustomerEditComponent } from './internal-delivery-order-select-customer/customer-edit/customer-edit.component';
import { CustomerPaymentConfigComponent } from './internal-delivery-order-select-customer/customer-edit/customer-payment-config/customer-payment-config.component';
import { EditPaymentConfigComponent } from './internal-delivery-order-select-customer/customer-edit/customer-payment-config/payment-config-edit/payment-config-edit.component';
import { CreatePaymentConfigComponent } from './internal-delivery-order-select-customer/customer-edit/customer-payment-config/payment-config-create/payment-config-create.component';
import { CustomerLoginComponent } from './internal-delivery-order-select-customer/customer-edit/customer-login/customer-login.component';
import { CreateLoginComponent } from './internal-delivery-order-select-customer/customer-edit/customer-login/login-create/login-create.component';
import { EditLoginComponent } from './internal-delivery-order-select-customer/customer-edit/customer-login/login-edit/login-edit.component';
import { CreateTaxComponent } from './internal-delivery-order-select-customer/customer-edit/customer-tax/tax-create/customer-tax-create.component';
import { EditTaxComponent } from './internal-delivery-order-select-customer/customer-edit/customer-tax/tax-edit/customer-tax-edit.component';
import { CreateAddressComponent } from './internal-delivery-order-select-customer/customer-edit/customer-address/address-create/customer-address-create.component';
import { EditAddressComponent } from './internal-delivery-order-select-customer/customer-edit/customer-address/address-edit/customer-address-edit.component';
import { CustomerAddressComponent } from './internal-delivery-order-select-customer/customer-edit/customer-address/customer-address.component';
import { CreateContactComponent } from './internal-delivery-order-select-customer/customer-edit/customer-contact/contact-create/customer-contact-create.component';
import { EditContactComponent } from './internal-delivery-order-select-customer/customer-edit/customer-contact/contact-edit/customer-contact-edit.component';
import { CustomerContactComponent } from './internal-delivery-order-select-customer/customer-edit/customer-contact/customer-contact.component';
import { EditBranchComponent } from './internal-delivery-order-select-customer/customer-edit/customer-branch/branch-edit/branch-edit.component';
import { CreateBranchComponent } from './internal-delivery-order-select-customer/customer-edit/customer-branch/branch-create/branch-create.component';
import { CreditTermsMainComponent } from './internal-delivery-order-select-customer/customer-edit/credit-terms-main/credit-terms-main.component';
import { CreditTermsComponent } from './internal-delivery-order-select-customer/customer-edit/credit-terms-main/credit-terms/credit-terms.component';
import { CustomerBranchComponent } from './internal-delivery-order-select-customer/customer-edit/customer-branch/customer-branch.component';
import { CreditTermsEditComponent } from './internal-delivery-order-select-customer/customer-edit/credit-terms-main/credit-terms-edit/credit-terms-edit.component';
import { CreditLimitsMainComponent } from './internal-delivery-order-select-customer/customer-edit/credit-limits-main/credit-limits-main.component';
import { CreditLimitsComponent } from './internal-delivery-order-select-customer/customer-edit/credit-limits-main/credit-limits/credit-limits.component';
import { CreditLimitsEditComponent } from './internal-delivery-order-select-customer/customer-edit/credit-limits-main/credit-limits-edit/credit-limits-edit.component';
import { CustomerItemPricingComponent } from './internal-delivery-order-select-customer/customer-edit/customer-item-pricing/customer-item-pricing-listing.component';
import { EditItemPricingComponent } from './internal-delivery-order-select-customer/customer-edit/customer-item-pricing/item-pricing-edit/item-pricing-edit.component';
import { CustomerTaxComponent } from './internal-delivery-order-select-customer/customer-edit/customer-tax/customer-tax.component';
import { CustomerCategoryComponent } from './internal-delivery-order-select-customer/customer-edit/customer-category/customer-category.component';
import { EditCategoryComponent } from './internal-delivery-order-select-customer/customer-edit/customer-category/category-edit/category-edit.component';
import { AddCategoryComponent } from './internal-delivery-order-select-customer/customer-edit/customer-category/category-add/category-add.component';
import { FroalaEditorModule } from 'angular-froala-wysiwyg';
import { LineSearchItemListingComponent } from './internal-delivery-order-create-line-item/line-search-item-listing/line-search-item-listing.component';
import { AddLineItemCostingDetailsComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-add-line-item/costing-details/add-line-item-costing-details.component';
import { AddLineItemPricingDetailsComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-add-line-item/pricing-details/add-line-item-pricing-details.component';
import { AddLineItemIssueLinkComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-add-line-item/issue-link/add-line-item-issue-link.component';
import { InternalDeliveryOrderExportComponent } from './internal-delivery-order-export/internal-delivery-order-export.component';
import { InternalDeliveryOrderCustomStatusComponent } from './internal-delivery-order-custom-status/internal-delivery-order-custom-status.component';
import { DemoMaterialModule } from '../../demo-material-module';
import { MatCheckboxModule} from '@angular/material/checkbox';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { InternalDeliveryOrderAddLineItemMainComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-add-line-item/item-details/main-details/internal-delivery-order-add-line-item-main.component';
import { DeliveryInstructionComponent } from './internal-delivery-order-create-line-item/internal-delivery-order-add-line-item/item-details/delivery-instructions/delivery-instructions.component';
import { AccountShippingAddressComponent } from './internal-delivery-order-create/internal-delivery-order-create-account/account-shipping-address/account-shipping-address.component';
import { InternalDeliveryOrderDeliveryDetailsComponent } from './internal-delivery-order-delivery-details/internal-delivery-order-delivery-details.component';
import { ExternalDeliveryComponent } from './internal-delivery-order-delivery-details/external-delivery/external-delivery.component';
import { InternalDeliveryComponent } from './internal-delivery-order-delivery-details/internal-delivery/internal-delivery.component';
import { PickupComponent } from './internal-delivery-order-delivery-details/pickup/pickup.component';
import { NgxMatDateAdapter, NgxMatDateFormats, NgxMatDatetimePickerModule, NgxMatNativeDateModule, NGX_MAT_DATE_FORMATS } from '@angular-material-components/datetime-picker';
import { NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular-material-components/moment-adapter';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { CustomNgxDatetimeAdapter } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/models/custom-ngx-date-time-adapter';

const CUSTOM_DATE_FORMATS: NgxMatDateFormats = {
  parse: {
    dateInput: 'l, LTS'
  },
  display: {
    dateInput: 'YYYY-MM-DD HH:mm:ss',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  }
};

@NgModule({
  declarations: [
    InternalDeliveryOrderContainerComponent,
    InternalDeliveryOrderListingComponent,
    InternalDeliveryOrderViewComponent,
    InternalDeliveryOrderViewLineItemComponent,
    InternalDeliveryOrderCreateComponent,
    InternalDeliveryOrderCreateMainComponent,
    InternalDeliveryOrderCreateAccountComponent,
    InternalDeliveryOrderCreateDepartmentComponent,
    AccountEntityDetailsComponent,
    AccountDeliveryDetailsComponent,
    AccountContactPersonComponent,
    AccountAddressComponent,
    InternalDeliveryOrderCreateLineItemsComponent,
    InternalDeliveryOrderCreateLineItemComponent,
    InternalDeliveryOrderCreateCustomFieldsComponent,
    InternalDeliveryOrderCreateLineItemSalesOrderComponent,
    InternalDeliveryOrderCreateLineItemQuotationComponent,
    InternalDeliveryOrderCreateLineItemInvoiceComponent,
    InternalDeliveryOrderTransferSalesOrderComponent,
    InternalDeliveryOrderTransferSalesOrderMainComponent,
    InternalDeliveryOrderTransferSalesOrderCustomerInfoComponent,
    InternalDeliveryOrderTransferSalesOrderLineItemsComponent,
    InternalDeliveryOrderSelectBillingAddressComponent,
    InternalDeliveryOrderSelectContactPersonComponent,
    InternalDeliveryOrderSelectCustomerComponent,
    InternalDeliveryOrderSelectShippingAddressComponent,
    InternalDeliveryOrderCreateLineItemSTGRNComponent,
    InternalDeliveryOrderKOForSTGRNComponent,
    InternalDeliveryOrderKOBySTGRNComponent,
    InternalDeliveryOrderEditLineItemComponent,
    ItemDetailsComponent,
    MainDetailsComponent,
    DeliveryInstructionsComponent,
    SerialNumberComponent,
    BinNumberComponent,
    BatchNumberComponent,
    SerialNumberImportComponent,
    SerialNumberListingComponent,
    SerialNumberScanComponent,
    BinNumberListingComponent,
    BatchNumberListingComponent,
    InternalDeliveryOrderAddLineItemComponent,
    AddLineItemItemDetailsComponent,
    InternalDeliveryOrderAddLineItemMainComponent,
    KnockoffAddComponent,
    KnockoffEditComponent,
    LineContainerKoForComponent,
    LineContainerKoByComponent,
    CustomerCreateComponent,
    CustomerCreateMainComponent,
    CustomerEditComponent,
    CustomerPaymentConfigComponent,
    EditPaymentConfigComponent,
    CreatePaymentConfigComponent,
    CustomerLoginComponent,
    CreateLoginComponent,
    EditLoginComponent,
    CreateTaxComponent,
    EditTaxComponent,
    CreateAddressComponent,
    EditAddressComponent,
    CustomerAddressComponent,
    CreateContactComponent,
    EditContactComponent,
    CustomerContactComponent,
    EditBranchComponent,
    CreateBranchComponent,
    CreditTermsMainComponent,
    CreditTermsComponent,
    CustomerBranchComponent,
    CreditTermsEditComponent,
    CreditLimitsMainComponent,
    CreditLimitsComponent,
    CreditLimitsEditComponent,
    CustomerItemPricingComponent,
    EditItemPricingComponent,
    CustomerTaxComponent,
    CustomerCategoryComponent,
    EditCategoryComponent,
    AddCategoryComponent,
    LineSearchItemListingComponent,
    AddLineItemCostingDetailsComponent,
    AddLineItemPricingDetailsComponent,
    AddLineItemIssueLinkComponent,
    InternalDeliveryOrderExportComponent,
    InternalDeliveryOrderCustomStatusComponent,
    DeliveryInstructionComponent,
    AccountShippingAddressComponent,
    InternalDeliveryOrderDeliveryDetailsComponent,
    ExternalDeliveryComponent,
    InternalDeliveryComponent,
    PickupComponent,
  ],
  imports: [
    CommonModule,
    UtilitiesModule,
    BlgAkaunNgLibModule,
    AgGridModule,
    DemoMaterialModule,
    MatCheckboxModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    NgxMatDatetimePickerModule,
    NgxMatNativeDateModule,
    StoreModule.forFeature(internalDeliveryOrderFeatureKey, reducers.deliveryOrder),
    EffectsModule.forFeature([InternalDeliveryOrderEffects]),
    AdvancedSearchIdoModule,
    StoreModule.forFeature(entityFeatureKey, reducerss.entity),
    EffectsModule.forFeature([CustomerEffects, CreditLimitEffect, CreditTermEffect]),
    FroalaEditorModule.forRoot(),
  ],
  providers: [
    {
      provide: NgxMatDateAdapter,
      useClass: CustomNgxDatetimeAdapter,
      deps: [MAT_DATE_LOCALE, NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: NGX_MAT_DATE_FORMATS, useValue: CUSTOM_DATE_FORMATS },
  ],
})
export class InternalDeliveryOrderModule { }
