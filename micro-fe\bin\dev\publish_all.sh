#!/bin/bash
echo "~Running batch script DEV~"
echo "~Deploying QC applet~"
sh ./bin/dev/qc_applet_publish.sh 2>> ./bin/dev/log.txt
echo "~Deploying Operator applet~"
sh ./bin/dev/operator_applet_publish.sh 2>> ./bin/dev/log.txt
echo "~Deploying Supervisor applet~"
sh ./bin/dev/supervisor_applet_publish.sh 2>> ./bin/dev/log.txt
echo "~Deploying Tonn Cable Admin applet~"
sh ./bin/dev/tonn_cable_admin_applet_publish.sh 2>> ./bin/dev/log.txt
echo "~Deploying Internal Sales Order applet~"
sh ./bin/dev/internal_sales_order_applet_publish.sh 2>> ./bin/dev/log.txt
echo "~Deploying Internal Packing Order applet~"
sh ./bin/dev/internal_packing_order_applet_publish.sh 2>> ./bin/dev/log.txt
echo "~Deploying Stock Balance applet~"
sh ./bin/dev/stock_balance_applet_publish.sh 2>> ./bin/dev/log.txt
echo "~Deploying Stock Adjustment applet~"
sh ./bin/dev/stock_adjustment_applet_publish.sh 2>> ./bin/dev/log.txt
echo "~Finish running script~"
