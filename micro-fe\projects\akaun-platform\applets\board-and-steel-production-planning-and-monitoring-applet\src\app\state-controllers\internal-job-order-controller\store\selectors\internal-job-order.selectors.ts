import { createFeatureSelector } from '@ngrx/store';
import { InternalJobOrderFeatureKey } from '../reducers/internal-job-order.reducers';
import { InternalJobOrderStates } from '../states';
import { InternalJobOrderState } from '../states/internal-job-order.states';

export const selectInternalJobOrderFeature = createFeatureSelector<InternalJobOrderState>(InternalJobOrderFeatureKey);

export const selectEntity = (state: InternalJobOrderStates) => state.jobOrder.selectedEntity;
export const selectJobOrder = (state: InternalJobOrderStates) => state.jobOrder.selectedJobOrder;
export const selectTotalRecords = (state: InternalJobOrderStates) => state.jobOrder.totalRecords;
export const selectCustomer = (state: InternalJobOrderStates) => state.jobOrder.selectedCustomer;
export const selectItem = (state: InternalJobOrderStates) => state.jobOrder.selectedItem;
export const selectAgGrid = (state: InternalJobOrderStates) => state.jobOrder.updateAgGrid;
export const selectLineItem = (state: InternalJobOrderStates) => state.jobOrder.selectedLineItem;
export const selectGenDocLinkGuid = (state: InternalJobOrderStates) => state.jobOrder.genDocLinkGuid;
export const selectEditMode = (state: InternalJobOrderStates) => state.jobOrder.editMode;
export const selectTotalContainerQty = (state: InternalJobOrderStates) => state.jobOrder.totalContainerQty;
export const selectProcessInstance = (state: InternalJobOrderStates) => state.jobOrder.processInstance;
