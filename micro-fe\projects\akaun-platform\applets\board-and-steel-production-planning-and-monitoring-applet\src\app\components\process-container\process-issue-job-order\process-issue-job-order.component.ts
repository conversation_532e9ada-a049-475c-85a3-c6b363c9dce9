import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { Store } from '@ngrx/store';
import { iif, of, EMPTY, combineLatest } from 'rxjs';
import { delay, map, mergeMap, withLatestFrom } from 'rxjs/operators';
import { AppConfig } from 'projects/shared-utilities/visa';
import { MatTabGroup } from '@angular/material/tabs';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { BinSelectors, HDRSelectors, PlannedInputSelectors, PlannedOutputSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { JobOrderNoDepartment, JobOrderMain } from '../../../models/internal-job-order.model';
import { ActivatedRoute } from '@angular/router';
import { IssueJobOrderMainComponent } from './issue-job-order-main/issue-job-order-main.component';
import { InternalJobOrderEditLineItemDepartmentComponent } from '../../internal-job-order-container/internal-job-order-edit-line-item/edit-line-item-item-details/internal-job-order-edit-line-item-department/internal-job-order-edit-line-item-department.component';
import { ProcessStates } from '../../../state-controllers/process-controller/store/states';
import { ProcessSelectors } from '../../../state-controllers/process-controller/store/selectors';
import { IssueJobOrderPlannedOutputComponent } from './issue-job-order-planned-output/issue-job-order-planned-output.component';
import { IssueJobOrderPlannedInputComponent } from './issue-job-order-planned-input/issue-job-order-planned-input.component';
import { HDRActions } from '../../../state-controllers/draft-controller/store/actions';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';

interface LocalState {
  deactivateAddInput: boolean;
  deactivateAddOutput: boolean;
  deactivateReturn: boolean;
  selectedIndex: number;
}

@Component({
  selector: 'app-process-issue-job-order',
  templateUrl: './process-issue-job-order.component.html',
  styleUrls: ['./process-issue-job-order.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessIssueJobOrderComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Issue Job Order';
  protected index = 2;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);

  prevIndex: number;
  protected prevLocalState: any;

  dept$ = combineLatest(
    [
      this.draftStore.select(HDRSelectors.selectDimension),
      this.draftStore.select(HDRSelectors.selectProfitCenter),
      this.draftStore.select(HDRSelectors.selectProject),
      this.draftStore.select(HDRSelectors.selectSegment)
  ]).pipe(
    map(([a, b, c, d]) => ({guid_dimension: a, guid_profit_center: b, guid_project: c, guid_segment: d}))
  );
  hdr$ = this.draftStore.select(HDRSelectors.selectHdr);
  plannedOutput$ = this.draftStore.select(PlannedOutputSelectors.selectAll);
  plannedInput$ = this.draftStore.select(PlannedInputSelectors.selectAll);
  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({...a, ...b})));
  userProfile$ = this.sessionStore.select(SessionSelectors.selectUserProfile);
  process$ = this.route.params.pipe(
    map(a => a.process),
    withLatestFrom(this.processStore.select(ProcessSelectors.selectProcessMap)),
    map(([guid, process]) => process[guid])
  );
  machines$ = this.route.params.pipe(
    map(a => a.process),
    withLatestFrom(this.processStore.select(ProcessSelectors.selectMachineMap)),
    map(([guid, machine]) => machine[guid])
  );

  @ViewChild(IssueJobOrderMainComponent) main: IssueJobOrderMainComponent;
  @ViewChild(IssueJobOrderPlannedOutputComponent) output: IssueJobOrderPlannedOutputComponent;
  @ViewChild(IssueJobOrderPlannedInputComponent) input: IssueJobOrderPlannedInputComponent;
  @ViewChild(InternalJobOrderEditLineItemDepartmentComponent) department: InternalJobOrderEditLineItemDepartmentComponent;
  @ViewChild(MatTabGroup) matTab: MatTabGroup;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected readonly sessionStore: Store<SessionStates>,
    protected readonly store: Store<InternalJobOrderStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly processStore: Store<ProcessStates>,
    protected readonly componentStore: ComponentStore<LocalState>,
    private route: ActivatedRoute
  ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onIssue() {
    this.store.dispatch(InternalJobOrderActions.issueJobOrderInit());
  }

  disableSave() {
    return this.main?.form.invalid || !this.output?.rowData.length || !this.input?.rowData.length;
  }

  onUpdateMain(form: JobOrderMain) {
    this.draftStore.dispatch(HDRActions.updateMain({form}));
  }

  onUpdateDepartment(form: JobOrderNoDepartment) {
    this.draftStore.dispatch(HDRActions.updateDepartment({form}));
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAddInput() {
    if (!this.localState.deactivateAddInput) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAddInput: true,
      deactivateAddOutput: false
    });
      this.viewColFacade.onNextAndReset(this.index, 4);
    }
  }

  onAddOutput() {
    if (!this.localState.deactivateAddOutput) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAddInput: false,
      deactivateAddOutput: true
    });
      this.viewColFacade.onNextAndReset(this.index, 3);
    }
  }

  onSelectTemplate() {
    console.log("Test")
    if (!this.localState.deactivateAddOutput) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAddInput: false,
      deactivateAddOutput: true
    });
      this.viewColFacade.onNextAndReset(this.index, 7);
    }
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex
      });
    }
    this.subs.unsubscribe();
  }

}
