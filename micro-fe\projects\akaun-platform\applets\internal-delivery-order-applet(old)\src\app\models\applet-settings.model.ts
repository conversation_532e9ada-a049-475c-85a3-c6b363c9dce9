export interface CustomStatusParams {
    name: string;
    descr?: string;
    default?: boolean;
}

export interface AppletSettings {
    DEFAULT_BRANCH: string;
    DEFAULT_LOCATION: string;
    DEFAULT_COMPANY: string;
    ENABLE_DIMENSION: boolean;
    ENABLE_PROFIT_CENTER: boolean;
    ENABLE_PROJECT: boolean;
    ENABLE_SEGMENT: boolean;
    ENABLE_SST: boolean;
    ENABLE_WHT: boolean;
    PRINTABLE: string;
    ENABLE_CUSTOM_STATUS_1: boolean;
    ENABLE_CUSTOM_STATUS_2: boolean;
    ENABLE_CUSTOM_STATUS_3: boolean;
    ENABLE_CUSTOM_STATUS_4: boolean;
    ENABLE_CUSTOM_STATUS_5: boolean;
    ENABLE_CUSTOM_STATUS_HDR_1: boolean;
    ENABLE_CUSTOM_STATUS_HDR_2: boolean;
    ENABLE_CUSTOM_STATUS_HDR_3: boolean;
    ENABLE_CUSTOM_STATUS_HDR_4: boolean;
    ENABLE_CUSTOM_STATUS_HDR_5: boolean;
    ENABLE_CUSTOM_STATUS_LINE_1: boolean;
    ENABLE_CUSTOM_STATUS_LINE_2: boolean;
    ENABLE_CUSTOM_STATUS_LINE_3: boolean;
    ENABLE_CUSTOM_STATUS_LINE_4: boolean;
    ENABLE_CUSTOM_STATUS_LINE_5: boolean;
    NAME_CUSTOM_STATUS_HDR_1: CustomStatusParams[];
    NAME_CUSTOM_STATUS_HDR_2: CustomStatusParams[];
    NAME_CUSTOM_STATUS_HDR_3: CustomStatusParams[];
    NAME_CUSTOM_STATUS_HDR_4: CustomStatusParams[];
    NAME_CUSTOM_STATUS_HDR_5: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_1: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_2: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_3: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_4: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_5: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_1: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_2: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_3: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_4: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_5: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_1: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_2: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_3: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_4: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_5: CustomStatusParams[];
    KNOCK_OFF_BY_SALES_ORDER: boolean;
    KNOCK_OFF_BY_ST_GRN: boolean;
    KNOCK_OFF_FOR_SALES_ORDER: boolean;
    KNOCK_OFF_FOR_ST_GRN: boolean;
    HIDE_UNIT_PRICE_STD_PRICING_SCHEME: boolean;
    HIDE_UNIT_PRICE_STD_INCL_TAX: boolean;
    HIDE_UNIT_PRICE_STD_EXCL_TAX: boolean;
    HIDE_UNIT_PRICE_STD_UOM_INCL_TAX: boolean;
    HIDE_UNIT_PRICE_STD_UOM_EXCL_TAX: boolean;
    HIDE_UNIT_PRICE_NET_UOM_EXCL_TAX: boolean;
    HIDE_UNIT_PRICE_NET_EXCL_TAX: boolean;
    HIDE_UNIT_DISCOUNT: boolean;
    HIDE_QTY_BASE: boolean;
    HIDE_QTY_UOM: boolean;
    HIDE_UOM_TO_BASE_RATIO: boolean;
    HIDE_UNIT_DISCOUNT_UOM_EXCL_TAX: boolean;
    HIDE_UNIT_PRICE_TXN_UOM_INCL_TAX: boolean;
    HIDE_AMOUNT_STD_EXCL_TAX: boolean;
    HIDE_DISCOUNT_AMOUNT_EXCL_TAX: boolean;
    HIDE_AMOUNT_NET_EXCL_TAX: boolean;
    HIDE_TAX_CONFIG_SELECTION: boolean;
    HIDE_WHT_CONFIG_SELECTION: boolean;
    HIDE_UNIT_PRICE_TXN: boolean;
    HIDE_AMOUNT_TXN: boolean;
    HIDE_LAST_PURCHASE_PRICE: boolean;
    HIDE_COSTING_DETAILS: boolean;
}
