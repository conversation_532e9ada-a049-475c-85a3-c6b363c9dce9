import { createSelector } from '@ngrx/store';
import { selectDraftState } from '../..';

export const selectJobOrderNo = createSelector(
  selectDraftState,
  (s1) => s1.jobOrderNo
);

export const selectSegment = (state) => state.draft.jobOrderNo.guid_segment;
export const selectProfitCenter = (state) => state.draft.jobOrderNo.guid_profit_center;
export const selectProject = (state) => state.draft.jobOrderNo.guid_project;
export const selectDimension = (state) => state.draft.jobOrderNo.guid_dimension;
