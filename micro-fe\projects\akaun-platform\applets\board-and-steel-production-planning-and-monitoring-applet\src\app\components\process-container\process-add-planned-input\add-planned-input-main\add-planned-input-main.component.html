<form [formGroup]="form">
    <div class="view-col-forms">
        <div fxLayout="row" fxLayoutGap="5px">
            <div fxFlex="50" fxLayout="column">
                <div *ngFor="let x of leftColControls; let i = index">
                    <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
                        <mat-label>{{x.label}}</mat-label>
                        <input matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
                    </mat-form-field>
                    <ng-template #input>
                        <ng-container [ngSwitch]="x.type">
                            <blg-select-branch-drop-down *ngSwitchCase="'branch'" [apiVisa]="apiVisa"
                                [(branch)]="form.controls[x.formControl]"
                                (branchSelected)="updateMain.emit(form.value)"></blg-select-branch-drop-down>
                            <mat-form-field *ngSwitchCase="'text'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <input matInput [formControl]="form.controls[x.formControl]"
                                    autocomplete="off" type="text">
                                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                            </mat-form-field>
                            <mat-form-field *ngSwitchCase="'number'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <input matInput [formControl]="form.controls[x.formControl]"
                                    autocomplete="off" type="number">
                                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                            </mat-form-field>
                            <mat-form-field *ngSwitchCase="'text-area'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <textarea matInput [formControl]="form.controls[x.formControl]"
                                   ></textarea>
                                <mat-hint align="end">{{form.controls[x.formControl].value ?
                                    form.controls[x.formControl].value.length : 0}} characters</mat-hint>
                            </mat-form-field>
                            <mat-form-field *ngSwitchCase="'date'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <input matInput [matDatepicker]="datepicker"
                                    [formControl]="form.controls[x.formControl]"
                                    autocomplete="off" readonly>
                                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                                <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle>
                                <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
                            </mat-form-field>
                            <mat-form-field *ngSwitchCase="'jobOrderNo'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <input style="cursor: pointer" matInput readonly
                                    [formControl]="form.controls[x.formControl]" autocomplete="off" type="text"
                                    (click)="jobOrderNo.emit()">
                                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                            </mat-form-field>
                        </ng-container>
                    </ng-template>
                </div>
            </div>
            <div fxFlex="50" fxLayout="column">
                <div *ngFor="let x of rightColControls; let i = index">
                    <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
                        <mat-label>{{x.label}}</mat-label>
                        <input matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
                    </mat-form-field>
                    <ng-template #input>
                        <ng-container [ngSwitch]="x.type">
                            <blg-select-location-drop-down *ngSwitchCase="'location'" [apiVisa]="apiVisa"
                                [(location)]="form.controls[x.formControl]"
                                (locationSelected)="updateMain.emit(form.value)"></blg-select-location-drop-down>
                            <mat-form-field *ngSwitchCase="'text'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <input matInput [formControl]="form.controls[x.formControl]" autocomplete="off"
                                    type="text">
                                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                            </mat-form-field>
                            <mat-form-field *ngSwitchCase="'number'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <input matInput [formControl]="form.controls[x.formControl]" autocomplete="off"
                                    type="number">
                                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                            </mat-form-field>
                            <mat-form-field *ngSwitchCase="'text-area'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <textarea matInput [formControl]="form.controls[x.formControl]"
                                   ></textarea>
                                <mat-hint align="end">{{form.controls[x.formControl].value ?
                                    form.controls[x.formControl].value.length : 0}} characters</mat-hint>
                            </mat-form-field>
                            <mat-form-field *ngSwitchCase="'date'" appearance="outline">
                                <mat-label>{{x.label}}</mat-label>
                                <input matInput [matDatepicker]="datepicker"
                                    [formControl]="form.controls[x.formControl]" autocomplete="off" readonly
                                   >
                                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                                <!-- <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle> -->
                                <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
                            </mat-form-field>
                        </ng-container>
                    </ng-template>
                </div>
            </div>
        </div>
        <mat-form-field appearance="outline">
            <mat-label>Remarks</mat-label>
            <textarea matInput [formControl]="form.controls['remarks']"
               ></textarea>
            <mat-hint align="end">{{form.controls['remarks'].value ? form.controls['remarks'].value.length : 0}}
                characters</mat-hint>
        </mat-form-field>
    </div>
</form>