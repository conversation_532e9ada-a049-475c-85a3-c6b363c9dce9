import * as fromAttachmentStates from './atachment.states';
import * as fromPlannedOutputStates from './planned-output.states';
import * as fromPlannedInputStates from './planned-input.states';
import * as fromPNSEditStates from './pns-edit.states';
import * as fromBinStates from './bin.states';
import { bl_fi_generic_doc_hdr_RowClass, bl_fi_generic_doc_single_line, bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';

export interface DraftStates {
    hdr: bl_mrp_job_order_hdr_RowClass;
    hdrEdit: bl_mrp_job_order_hdr_RowClass;
    plannedOutput: fromPlannedOutputStates.PlannedOutputState;
    plannedInput: fromPlannedInputStates.PlannedInputState;
    pnsEdit: fromPNSEditStates.PNSEditState;
    bin: fromBinStates.BinState;
    jobOrderNo: bl_mrp_job_order_hdr_RowClass;
    jobOrderNoEdit: bl_mrp_job_order_hdr_RowClass;
    attachment: fromAttachmentStates.AttachmentState;
}
