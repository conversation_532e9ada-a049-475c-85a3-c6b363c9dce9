const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/tonn-cable-admin-applet/runtime-es2015.js',
    './dist/tonn-cable-admin-applet/polyfills-es2015.js',
    './dist/tonn-cable-admin-applet/scripts.js',
    './dist/tonn-cable-admin-applet/main-es2015.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/tonn-cable-admin-applet');
  await concat(files, './elements/akaun-platform/applets/tonn-cable-admin-applet/tonn-cable-admin-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
