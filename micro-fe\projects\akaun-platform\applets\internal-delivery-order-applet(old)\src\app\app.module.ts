import { NgxMatDatetimePickerModule, NgxMatTimepickerModule } from '@angular-material-components/datetime-picker';
import { CommonModule } from '@angular/common';
import { HttpClientModule } from '@angular/common/http';
import { Injector, NgModule } from '@angular/core';
import { createCustomElement } from '@angular/elements';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MAT_SNACK_BAR_DEFAULT_OPTIONS } from '@angular/material/snack-bar';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterModule } from '@angular/router';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { PerfectScrollbarConfigInterface, PerfectScrollbarModule, PERFECT_SCROLLBAR_CONFIG } from 'ngx-perfect-scrollbar';
import { ToastrModule } from 'ngx-toastr';
import { appReducers } from 'projects/shared-utilities/application-controller/store/reducers';
import { LayoutModule } from 'projects/shared-utilities/modules/layout/layout.module';
import { PermissionModule } from 'projects/shared-utilities/modules/permission/permission.module';
import { SessionModule } from 'projects/shared-utilities/modules/session/session.module';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { environment } from 'src/environments/environment';
import { AppComponent } from './app.component';
import { AppRoutes } from './app.routing';
import { AppletPersonalizationModule } from './components/personalization-container/applet-personalization.module';
import { AppletSettingsModule } from './components/settings-container/applet-settings.module';
import { DateCellRendererComponent } from './components/utilities/date-cell-renderer/date-cell-renderer.component';
import { DemoMaterialModule } from './demo-material-module';
import { ViewColumnFacade } from './facades/view-column.facade';
import { DraftModule } from './feature-modules/draft.module';
import { ViewCacheModule } from './feature-modules/view-cache.module';


export const MY_FORMATS = {
  parse: {
    dateInput: 'YYYY-MM-DD'
  },
  display: {
    dateInput: 'YYYY-MM-DD',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY'
  }
};

const DEFAULT_PERFECT_SCROLLBAR_CONFIG: PerfectScrollbarConfigInterface = {
  suppressScrollX: true,
  wheelSpeed: 2,
  wheelPropagation: true
};

@NgModule({
  declarations: [
    AppComponent,
    DateCellRendererComponent
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    CommonModule,
    HttpClientModule,
    SessionModule,
    UtilitiesModule,
    DemoMaterialModule,
    PerfectScrollbarModule,
    RouterModule.forRoot(AppRoutes, { useHash: true, relativeLinkResolution: 'legacy' }),
    ToastrModule.forRoot(
      {
        timeOut: 10000,
        positionClass: 'toast-top-center',
        preventDuplicates: true
      }
    ),
    StoreModule.forRoot(appReducers, {
      runtimeChecks: {
        strictStateImmutability: false,
        strictActionImmutability: false
      }}
    ),
    EffectsModule.forRoot([]),
    !environment.production ? StoreDevtoolsModule.instrument({
      maxAge: 25
    }) : [],
    LayoutModule,
    ViewCacheModule,
    PermissionModule,
    AppletSettingsModule,
    AppletPersonalizationModule,
    DraftModule,
    NgxMatDatetimePickerModule,
    NgxMatTimepickerModule
  ],
  providers: [
    {
      provide: PERFECT_SCROLLBAR_CONFIG,
      useValue: DEFAULT_PERFECT_SCROLLBAR_CONFIG
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    {provide: MAT_SNACK_BAR_DEFAULT_OPTIONS, useValue: {
      duration: 3500,
      horizontalPosition: 'start',
      verticalPosition: 'bottom'
    }},
  ],
  bootstrap: []
})
export class AppModule {

  mobileView = window.matchMedia('(max-width: 768px)');

  constructor(private injector: Injector, private viewColFacade: ViewColumnFacade) {

    this.mobileView.matches ? this.viewColFacade.toggleColumn(true) : this.viewColFacade.toggleColumn(false);
    this.mobileView.addEventListener('change', (e) => {
      this.mobileView.matches ? this.viewColFacade.toggleColumn(true) : this.viewColFacade.toggleColumn(false);
    });

    if (!customElements.get('internal-delivery-order-applet-elements-' +  sessionStorage.getItem('randomNumber'))) {
      const el2 = createCustomElement(AppComponent, {injector: this.injector});
      customElements.define('internal-delivery-order-applet-elements-' + sessionStorage.getItem('randomNumber'), el2);
      // to be able to run it the applet as the tag must be same as index.html
      if (document.getElementById('customtag')) {
        // tslint:disable-next-line:max-line-length
        this.changeTagName(document.getElementById('customtag'), 'internal-delivery-order-applet-elements-' + sessionStorage.getItem('randomNumber'));
      }
    }
  }

  changeTagName(el, newTagName) {
    const n = document.createElement(newTagName);
    const attr = el.attributes;
    for (let i = 0, len = attr.length; i < len; ++i) {
      n.setAttribute(attr[i].name, attr[i].value);
    }
    n.innerHTML = el.innerHTML;
    el.parentNode.replaceChild(n, el);
  }

  ngDoBootstrap() {
  }
}
