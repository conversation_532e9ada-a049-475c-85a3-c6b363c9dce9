import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-internal-delivery-order-create-custom-fields',
  templateUrl: './internal-delivery-order-create-custom-fields.component.html',
  styleUrls: ['./internal-delivery-order-create-custom-fields.component.css']
})
export class InternalDeliveryOrderCreateCustomFieldsComponent implements OnInit, OnDestroy {

  @Input() draft$: Observable<GenericDocContainerModel>;
  @Input() customFields: {
    label: string,
    formControl: string,
    type: string,
    readonly: boolean
  }[];

  @Output() updateCustomFields = new EventEmitter();
  @Output() changeCustomFields = new EventEmitter();

  private subs = new SubSink();

  form: FormGroup;
  customFieldForm: FormGroup;

  showCustomFieldForm = false;

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({});
    this.customFields.forEach(c => this.form.addControl(c.formControl, new FormControl()));
    this.customFieldForm = new FormGroup({
      fieldName: new FormControl('', Validators.required),
      fieldType: new FormControl('text')
    });
    this.subs.sink = this.draft$.subscribe({next: resolve => {
      const ext = resolve.bl_fi_generic_doc_ext.find(x => x.param_code === 'CUSTOM_FIELDS');
      if (ext) {
        this.form.patchValue({
          ...resolve.bl_fi_generic_doc_ext.find(x => x.param_code === 'CUSTOM_FIELDS')?.value_json
        });
      } else {
        this.form.reset();
      }
    }});
    this.subs.sink = this.form.valueChanges.subscribe({next: (form) => {
      this.updateCustomFields.emit(form);
    }});
  }

  onAddCustomField() {
    this.showCustomFieldForm = true;
  }

  onProcessCustomField() {
    this.form.addControl(this.customFieldForm.controls['fieldName'].value, new FormControl());
    this.customFields.push(
      {
        label: this.customFieldForm.controls['fieldName'].value,
        formControl: this.customFieldForm.controls['fieldName'].value,
        type: this.customFieldForm.controls['fieldType'].value,
        readonly: false
      },
    );
    this.showCustomFieldForm = false;
    this.customFieldForm.reset();
    this.changeCustomFields.emit(this.customFields);
  }

  onRemoveCustomField(control: string) {
    this.customFields = this.customFields.filter(c => c.formControl !== control);
    this.form.removeControl(control);
    this.changeCustomFields.emit(this.customFields);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
