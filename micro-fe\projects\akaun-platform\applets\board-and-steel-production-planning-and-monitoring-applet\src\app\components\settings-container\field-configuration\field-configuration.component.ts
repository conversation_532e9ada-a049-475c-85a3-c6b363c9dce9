import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../models/applet-settings.model';

@Component({
  selector: 'app-field-configuration',
  templateUrl: './field-configuration.component.html',
  styleUrls: ['./field-configuration.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class FieldConfigurationComponent implements OnInit, OnDestroy {

  private subs = new SubSink();

  form: FormGroup;

  constructor(
    private readonly store: Store<SessionStates>,
    ) { }

  ngOnInit() {
    this.form = new FormGroup({
      ENABLE_SST: new FormControl(),
      ENABLE_WHT: new FormControl(),
      ENABLE_SEGMENT: new FormControl(),
      ENABLE_DIMENSION: new FormControl(),
      ENABLE_PROFIT_CENTER: new FormControl(),
      ENABLE_PROJECT: new FormControl(),
    });
    this.subs.sink = this.store.select(SessionSelectors.selectMasterSettings).subscribe({next: (resolve: AppletSettings) => {
      this.form.patchValue({
        ENABLE_SST: resolve?.ENABLE_SST,
        ENABLE_WHT: resolve?.ENABLE_WHT,
        ENABLE_SEGMENT: resolve?.ENABLE_SEGMENT,
        ENABLE_DIMENSION: resolve?.ENABLE_DIMENSION,
        ENABLE_PROFIT_CENTER: resolve?.ENABLE_PROFIT_CENTER,
        ENABLE_PROJECT: resolve?.ENABLE_PROJECT,
      });
    }});
  }

  onSave() {
    this.store.dispatch(SessionActions.saveMasterSettingsInit({settings: this.form.value}));
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
