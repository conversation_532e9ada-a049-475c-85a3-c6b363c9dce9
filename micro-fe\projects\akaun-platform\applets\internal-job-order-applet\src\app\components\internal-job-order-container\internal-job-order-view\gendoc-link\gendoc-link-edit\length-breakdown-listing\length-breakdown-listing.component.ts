import { Component, ChangeDetectionStrategy, ViewChild } from "@angular/core";
import { Store } from "@ngrx/store";
import { ComponentStore } from "@ngrx/component-store";
import {
  CouponSerialNumberContainerModel,
  FinancialItemService,
  GenericDocLineContainerModel,
  GenericDocLineService,
  MrpProcessTemplateContainerModel,
  MrpProcessTemplateService,
  MrpProcessTypeService,
  MrpProdsysContainerModel,
  MrpProdsysService,
  Pagination,
  SubQueryService,
} from "blg-akaun-ts-lib";
import { forkJoin, iif, Observable, of, zip } from "rxjs";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { SubSink } from "subsink2";
import {
  pageFiltering,
  pageSorting,
} from "projects/shared-utilities/listing.utils";
import { catchError, concatMap, map, mergeMap } from "rxjs/operators";
import * as moment from "moment";
import { SearchQueryModel } from "projects/shared-utilities/models/query.model";
import { PaginationComponent } from "projects/shared-utilities/utilities/pagination/pagination.component";
import { ViewColumnFacade } from "projects/akaun-platform/applets/internal-job-order-applet/src/app/facades/view-column.facade";
import { InternalJobOrderStates } from "projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/states";
import { InternalJobOrderSelectors } from "projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/selectors";


interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: "app-length-breakdown-listing",
  templateUrl: "./length-breakdown-listing.component.html",
  styleUrls: ["./length-breakdown-listing.component.scss"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore],
})
export class ViewLengthBreakdownListingComponent extends ViewColumnComponent {
  protected compName = "Length Breakdown Listing";
  protected readonly index = 0;
  private localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.deactivateAdd
  );
  readonly deactivateList$ = this.componentStore.select(
    (state) => state.deactivateList
  );

  toggleColumn$: Observable<boolean>;
  // searchModel = processTemplateSearchModel;

  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
  };

  gridApi;
  SQLGuids: string[] = null;
  pagination = new Pagination();
  process_template_hdr_guid;

  columnsDefs = [
    {
      headerName: "Quantity Breakdown",
      field: "qty",
      suppressSizeToFit: true,
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Units per Drum / Coil",
      field: "container_measure",
      suppressSizeToFit: true,
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "No. of Drum / Coil",
      field: "container_qty",
      suppressSizeToFit: true,
      cellStyle: () => ({ "text-align": "left" }),
    },
    // {headerName: 'Machine Code', field: 'machine_code', suppressSizeToFit: false, cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Status', field: 'bl_mrp_process_template_hdr.status', suppressSizeToFit: true, cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Modified Date', field: 'bl_mrp_process_template_hdr.updated_date', type: 'rightAligned',
    // valueFormatter: params => moment(params.value).format('YYYY-MM-DD HH:MM:SS')},
    // {headerName: 'Created Date', field: 'bl_mrp_process_template_hdr.created_date', type: 'rightAligned',
    // valueFormatter: params => moment(params.value).format('YYYY-MM-DD HH:MM:SS')}
  ];

  rowData = [];

  private subs = new SubSink();
  genDocLineGuid;

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    // private readonly draftStore: Store<DraftStates>,
    private viewColFacade: ViewColumnFacade,
    private processTemplateService: MrpProcessTemplateService,
    private prodsysService: MrpProdsysService,
    private processTypeService: MrpProcessTypeService,
    private genDocLineService: GenericDocLineService,
    private readonly componentStore: ComponentStore<LocalState>,
    private subQueryService: SubQueryService,
    private readonly store: Store<InternalJobOrderStates>,
    // private readonly processTemplateStore: Store<ProcessTemplateStates>
  ) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });


  }

  onNext() {
    // this.draftStore.dispatch(ProcessTemplateActions.resetDraft());
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateList: false,
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();

    // this.subs.sink = this.processTemplateStore
    //   .select(ProcessTemplateSelectors.selectAgGrid)
    //   .subscribe((a) => {
    //     if (a) {
    //       this.gridApi.refreshServerSideStore();
    //       this.processTemplateStore.dispatch(
    //         ProcessTemplateActions.updateAgGridDone()
    //       );
    //     }
    //   });
  }

  setGridData() {
    this.store.select(InternalJobOrderSelectors.selectLinkedSalesOrder).subscribe(data => {
      this.genDocLineGuid = data.genDocLineGuid;
      console.log("BIN",data.bin);
      this.rowData = data.bin.bins
      this.gridApi.setServerSideDatasource(data.bin.bins);

    })
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(entity: MrpProcessTemplateContainerModel) {
    // this.processTemplateStore.dispatch(
    //   ProcessTemplateActions.selectProcessTemplateForEdit({
    //     processTemplate: entity,
    //   })
    // );
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateList: false,
    });
    this.viewColFacade.onNextAndReset(this.index, 2);
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table,
      };
      this.subs.sink = this.subQueryService
        .post(sql, AppConfig.apiVisa)
        .subscribe({
          next: (resolve) => {
            this.SQLGuids = resolve.data;
            this.paginationComp.firstPage();
            this.gridApi.refreshServerSideStore();
          },
        });
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
