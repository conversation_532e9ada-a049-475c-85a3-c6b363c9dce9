import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass, bl_fi_generic_doc_link_RowClass, bl_fi_mst_entity_line_RowClass, EntityContainerModel } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { ViewColActions } from 'projects/shared-utilities/application-controller/store/actions';
import { ViewColSelectors } from 'projects/shared-utilities/application-controller/store/selectors';
import { AppStates } from 'projects/shared-utilities/application-controller/store/states';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { Observable } from 'rxjs';
import { SnackBarConstants } from '../models/constants/snack-bar.constants';
import { EntityActions } from '../state-controllers/customer-controller/store/actions';
import { EntitySelectors } from '../state-controllers/customer-controller/store/selectors';
import { EntityStates } from '../state-controllers/customer-controller/store/states';
import { HDRActions, HDREditActions, LinkActions, PNSActions, PNSEditActions, SettlementActions, SettlementEditActions } from '../state-controllers/draft-controller/store/actions';
import { DraftStates } from '../state-controllers/draft-controller/store/states';
import { InternalDeliveryOrderActions } from '../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderStates } from '../state-controllers/internal-delivery-order-controller/store/states';
import { ViewCacheActions } from '../state-controllers/view-cache-controller/store/actions';
import { ViewCacheSelectors } from '../state-controllers/view-cache-controller/store/selectors';
import { ViewCacheStates } from '../state-controllers/view-cache-controller/store/states';

@Injectable()
export class ViewColumnFacade {

  entityType;
  viewColState: ViewColumnState;
  internalDOCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectInternalDOCache);
  printableFormatSettingsCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectPrintableFormatSettingsCache);
  pickPackQueueCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectPickPackQueueCache);

  firstCol$ = this.appStore.select(ViewColSelectors.selectFirstColComp);
  secondCol$ = this.appStore.select(ViewColSelectors.selectSecondColComp);
  breadCrumbs$ = this.appStore.select(ViewColSelectors.selectBreadCrumbs);
  leftDrawer$ = this.appStore.select(ViewColSelectors.selectLeftDrawer);
  rightDrawer$ = this.appStore.select(ViewColSelectors.selectRightDrawer);
  toggleColumn$ = this.appStore.select(ViewColSelectors.selectSingleColumn);
  prevIndex$ = this.appStore.select(ViewColSelectors.selectPrevIndex);
  customerExt$: Observable<EntityContainerModel>;
  customerExtLineItem$: Observable<bl_fi_mst_entity_line_RowClass>;
  draft$: Observable<any>;
  prevLocalState$ = () => {
    ViewColSelectors.selectPrevLocalState.release();
    return this.appStore.select(ViewColSelectors.selectPrevLocalState);
  }

  errorLog = [];

  constructor(
    private readonly doStore: Store<InternalDeliveryOrderStates>,
    private readonly store: Store<DraftStates>,
    private readonly draftStore: Store<DraftStates>,
    private readonly entityExtStore: Store<EntityStates>,
    private readonly viewCacheStore: Store<ViewCacheStates>,
    private readonly appStore: Store<AppStates>,
    private router: Router,
    private toastr: ToastrService,
    private snackBar: MatSnackBar
  ) {
    this.appStore.select(ViewColSelectors.selectViewColState).subscribe(resolve => this.viewColState = resolve);
  }

  setViewColState(state: ViewColumnState) {
    this.appStore.dispatch(ViewColActions.setViewColState({ state }));
  }

  onNext(index: number) {
    this.appStore.dispatch(ViewColActions.viewColNext({ index }));
  }

  onNextAndReset(curIndex: number, nextIndex: number) {
    this.appStore.dispatch(ViewColActions.viewColNextAndReset({ curIndex, nextIndex }));
  }

  onPrev(index: number) {
    this.appStore.dispatch(ViewColActions.viewColPrev({ index }));
  }

  updateInstance<T>(index: number, localState: T) {
    this.appStore.dispatch(ViewColActions.viewColUpdateInstance({ index, localState }));
  }

  goToIndex(index: number) {
    this.appStore.dispatch(ViewColActions.goToIndex({ index }));
  }

  goBackIndex(index: number) {
    this.appStore.dispatch(ViewColActions.viewColRvIndex({ index }));
  }

  goForwardIndex(index: number) {
    this.appStore.dispatch(ViewColActions.viewColFwIndex({ index }));
  }

  resetIndex(index: number) {
    this.appStore.dispatch(ViewColActions.resetIndex({ index }));
  }

  toggleColumn(toggle: boolean) {
    this.appStore.dispatch(ViewColActions.toggleColumn({ toggle }));
  }

  selectLocalState(index: number) {
    return this.appStore.select(ViewColSelectors.selectLocalState, index);
  }

  gotoFourOhFour() {
    this.router.navigate(['404']);
  }

  saveInternalDOState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cacheInternalDO({ cache: this.viewColState }));
  }

  savePrintableFormatSettingsState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cachePrintableFormatSettings({ cache: this.viewColState }));
  }

  savePickPackQueueState(){
    this.viewCacheStore.dispatch(ViewCacheActions.cachePickPackQueue({cache: this.viewColState}));
  }
  
  showSuccessToast(message: string) {
    this.toastr.success(
      message,
      'Success',
      {
        tapToDismiss: true,
        progressBar: true,
        timeOut: 1300
      }
    );
  }

  showFailedToast(err) {
    this.toastr.error(
      err.message,
      'Error',
      {
        tapToDismiss: true,
        progressBar: true,
        timeOut: 1300
      }
    );
  }

  showSnackBar(message: string) {
    this.snackBar.open(message, 'Close');
  }


  addLineItemToDraft(line: bl_fi_generic_doc_line_RowClass) {
    // this.doStore.dispatch(InternalDeliveryOrderActions.addLineItemToDraft({line}));
    this.store.dispatch(PNSActions.addPNS({ pns: line }));

    //this.resetIndex(3);
    this.showSnackBar(` ${SnackBarConstants.addLineItem}`);
  }

  addLineItemToDraftEdit(line: bl_fi_generic_doc_line_RowClass) {
    this.doStore.dispatch(InternalDeliveryOrderActions.addLineItemToDraftEdit({ line }));
    //this.resetIndex(3);
    this.showSnackBar(`${SnackBarConstants.addLineItem}`);
  }

  addLink(link: bl_fi_generic_doc_link_RowClass) {
    // this.doStore.dispatch(InternalDeliveryOrderActions.addLinkToDraft({link}));
    this.store.dispatch(LinkActions.addLink({ link }));
    console.log("Link added to draft", link);
  }
  createCustomer(entityExt: EntityContainerModel) {
    
    this.entityExtStore.select(EntitySelectors.selectEntityType).subscribe((entityType) => {
      this.entityType = entityType;
    })
    console.log("createcustomer loop???")
    this.entityExtStore.dispatch(EntityActions.createEntity({ entityExt, entityType: this.entityType}));

    this.entityExtStore.dispatch(EntityActions.resetDraft());
    this.updateInstance(0, {
      deactivateAdd: false,
      deactivateList: false
    });
    this.resetIndex(6);
  }

  startDraft() {
    this.entityExtStore.dispatch(EntityActions.startDraft());
  }

  updateDraftHdr(entity: EntityContainerModel) {
    this.entityExtStore.dispatch(EntityActions.updateDraft({ entity, line: null }));
  }

  resetDraft(pageIndex: number) {
    // 1: edit, 2: create
    switch (pageIndex) {
      case 1:
        this.draftStore.dispatch(HDREditActions.resetHDRInit());
        this.draftStore.dispatch(PNSEditActions.resetPNSInit());
        this.draftStore.dispatch(SettlementEditActions.resetSettlementInit());
        // this.draftStore.dispatch(BinActions.resetBins());
        this.draftStore.dispatch((LinkActions.resetLink()));
        this.doStore.dispatch(InternalDeliveryOrderActions.resetDeliveryOrderEdit());
        // added for customer
        this.entityExtStore.dispatch(EntityActions.resetDraft());

        break;
      case 2:
        this.draftStore.dispatch(HDRActions.resetHDR());
        this.draftStore.dispatch(PNSActions.resetPNS());
        this.draftStore.dispatch(SettlementActions.resetSettlement());
        // this.draftStore.dispatch(BinActions.resetBins());
        this.draftStore.dispatch((LinkActions.resetLink()));
        this.doStore.dispatch(InternalDeliveryOrderActions.resetDeliveryOrder());
        break;
    }
    this.showSnackBar(SnackBarConstants.resetSO);
  }

  selectEntity(entity: { entity: EntityContainerModel, contact: bl_fi_mst_entity_line_RowClass }) {
    this.doStore.dispatch(InternalDeliveryOrderActions.selectEntity({ entity }));
    this.snackBar.open(`Some fields have been reset`, 'Close');
  }
}
