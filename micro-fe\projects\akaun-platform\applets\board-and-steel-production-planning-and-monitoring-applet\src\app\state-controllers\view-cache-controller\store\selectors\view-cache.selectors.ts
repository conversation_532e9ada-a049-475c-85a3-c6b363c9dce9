import { ViewCacheStates } from '../states';

export const selectInternalSOCache = (state: ViewCacheStates) => state.viewCache.internalSO;
export const selectProcessMenu = (state: ViewCacheStates) => state.viewCache.processMenu;
export const selectProcessCache = (state: ViewCacheStates, process: string) => state.viewCache[process];
export const selecSalesOrderCache = (state: ViewCacheStates) => state.viewCache.salesOrder;

