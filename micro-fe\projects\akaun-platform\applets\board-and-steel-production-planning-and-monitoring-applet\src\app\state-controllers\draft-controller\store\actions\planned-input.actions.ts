import { createAction, props } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';

export const addPlannedInput = createAction(
    '[Draft: Planned Input] Add Planned Input',
    props<{line: bl_fi_generic_doc_line_RowClass}>());
export const editPlannedInput = createAction(
    '[Draft: Planned Input] Edit Planned Input',
    props<{line: bl_fi_generic_doc_line_RowClass}>());
export const deletePlannedInput = createAction('[Draft: Planned Input] Delete Planned Input', props<{guid: string}>());
export const resetPlannedInput = createAction('[Draft: Planned Input] Reset');
