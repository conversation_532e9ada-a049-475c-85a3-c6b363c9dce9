<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
              [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        View Length Breakdown
      </span>
    </div>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">


  <mat-tab label="Main Details">
    <app-vl-details></app-vl-details>
  </mat-tab>
  <mat-tab label="Length Breakdown">
    <app-length-breakdown-listing></app-length-breakdown-listing>
  </mat-tab>
</mat-tab-group>