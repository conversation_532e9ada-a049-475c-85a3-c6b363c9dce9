import { FormGroup } from "@angular/forms";
import { CreditTermContainerModel, LabelListContainerModel } from "blg-akaun-ts-lib";

import * as moment from "moment";
import { EntityConstants } from "./constants/customer-constants";



export class EntityViewModel {
  guid: string;
  code: string;
  name: string;
  status: string;
  //created_date: string;
  updated_date: string;

}

export function containerToViewModelCreditTerm(data: CreditTermContainerModel): EntityViewModel {
  var hdr = data.bl_fi_entity_credit_term_hdr;

  //var create_date = moment(hdr.created_date).format(EntityConstants.DateTimeFormat)
  var updated_date = moment(hdr.updated_date).format(EntityConstants.DateTimeFormat)
  return {
    guid: hdr.guid,
    code: hdr.code,
    name: hdr.name,
    status: hdr.status,
    //created_date:create_date,
    updated_date: updated_date,

  } as EntityViewModel
}

