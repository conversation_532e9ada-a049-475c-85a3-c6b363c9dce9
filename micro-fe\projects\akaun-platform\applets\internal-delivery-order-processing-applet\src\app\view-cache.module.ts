import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { StoreModule } from '@ngrx/store';
import { ProcessingModule } from './components/processing-container/processing.module';
import { reducers } from './state-controllers/view-cache-controller/store/reducers';
import { viewCacheFeatureKey } from './state-controllers/view-cache-controller/store/reducers/view-cache.reducers';

@NgModule({
  imports: [
    CommonModule,
    ProcessingModule,
    StoreModule.forFeature(viewCacheFeatureKey, reducers.viewCache)
  ]
})
export class ViewCacheModule {}
