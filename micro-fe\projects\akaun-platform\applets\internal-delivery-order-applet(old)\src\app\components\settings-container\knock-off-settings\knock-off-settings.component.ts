import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../models/applet-settings.model';

@Component({
  selector: 'app-knock-off-settings',
  templateUrl: './knock-off-settings.component.html',
  styleUrls: ['./knock-off-settings.component.css']
})
export class KnockOffSettingsComponent implements OnInit {

  private subs = new SubSink();
  form: FormGroup;
  constructor(private readonly store: Store<SessionStates>,) { }

  ngOnInit(): void {
    this.form = new FormGroup({
      KNOCK_OFF_BY_SALES_ORDER: new FormControl(),
      KNOCK_OFF_BY_ST_GRN: new FormControl(),
      KNOCK_OFF_FOR_SALES_ORDER: new FormControl(),
      KNOCK_OFF_FOR_ST_GRN:  new FormControl()
    });

    this.subs.sink = this.store.select(SessionSelectors.selectMasterSettings).subscribe({next: (resolve: AppletSettings) => {
      this.form.patchValue({
        KNOCK_OFF_BY_SALES_ORDER: resolve?.KNOCK_OFF_BY_SALES_ORDER,
        KNOCK_OFF_BY_ST_GRN: resolve?.KNOCK_OFF_BY_ST_GRN,
        KNOCK_OFF_FOR_SALES_ORDER: resolve?.KNOCK_OFF_FOR_SALES_ORDER,
        KNOCK_OFF_FOR_ST_GRN: resolve?.KNOCK_OFF_FOR_ST_GRN,
      });
    }});
  }

  onSave() {
    console.log(this.form.value);
    this.store.dispatch(SessionActions.saveMasterSettingsInit({settings: this.form.value}));
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
