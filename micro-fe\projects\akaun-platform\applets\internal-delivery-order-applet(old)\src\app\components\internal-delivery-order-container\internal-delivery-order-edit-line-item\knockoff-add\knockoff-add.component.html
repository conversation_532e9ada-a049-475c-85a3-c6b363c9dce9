<mat-card-title class="column-title">
	<div fxLayout="row" fxLayoutAlign="space-between end">
		<div>
			<button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
							[disabled]="deactivateReturn$ | async" (click)="onReturn()">
					<img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
			</button>
			<span>Add Knockoff For {{ getType((listingConfig$ | async).serverDocTypeDoc1) }}</span>
		</div>
	</div>
    <app-line-container-ko-for
      [localState]="localState$ | async"
      [hdr]="draft$ | async"
      [searchModel]="advSearchModel"
      [advSearchId]=""
      [service]="(listingConfig$ | async).service"
      [serverDocTypeDoc1]="(listingConfig$ | async).serverDocTypeDoc1"
      [itemType]="''"
      [itemGuid]="(listingConfig$ | async).itemGuid"
      [store]="(listingConfig$ | async).store"
      [GenDocActions]="(listingConfig$ | async).GenDocActions"
      [draftStore]="draftStore"
      [LinkActions]="LinkActions"
      [LinkSelectors]="LinkSelectors"
      [PNSActions]="PNSActions"
      [PNSSelectors]="PNSSelectors"
      [AppletConstants]="AppletConstants"
      [additionalColumnDefs]="additionalColumnDefs"
    ></app-line-container-ko-for>
</mat-card-title>
<br>
