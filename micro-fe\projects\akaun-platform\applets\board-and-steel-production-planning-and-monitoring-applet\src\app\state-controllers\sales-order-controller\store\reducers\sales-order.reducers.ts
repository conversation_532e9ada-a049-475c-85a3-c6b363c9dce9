import { Action, createReducer, on } from '@ngrx/store';
import { PlannedOutputActions } from '../../../draft-controller/store/actions';
import { SalesOrderActions } from '../actions';
import { initState, SalesOrderState } from '../states/sales-order.states';

export const salesOrderFeatureKey = 'salesOrder';

export const salesOrderReducer = createReducer(
    initState,

    on(SalesOrderActions.selectSalesOrder, (state, action) => ({
        ...state, selectedSalesOrder: action.salesOrder
    })),
    on(SalesOrderActions.selectBinLine, (state, action) => ({
        ...state, selectedBinLine: action.binLine
    })),
    on(SalesOrderActions.selectCurrentSalesOrder, (state, action) => ({
        ...state, currentProcess: action.process
    })),
    on(SalesOrderActions.updateForm, (state, action) => ({
        ...state, updatedForm: action.form
    })),
);

export function reducer(state: SalesOrderState | undefined, action: Action) {
    return salesOrderReducer(state, action);
}
