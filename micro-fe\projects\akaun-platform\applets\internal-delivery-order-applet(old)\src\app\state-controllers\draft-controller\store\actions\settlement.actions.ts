import { createAction, props } from "@ngrx/store";
import { bl_fi_generic_doc_line_RowClass } from "blg-akaun-ts-lib";

export const addSettlementInit = createAction('[Draft: Settlement] Add Settlement Init', props<{pageIndex: number, settlement: bl_fi_generic_doc_line_RowClass}>());
export const addSettlementSuccess = createAction('[Draft: Settlement] Add Settlement Success', props<{settlement: bl_fi_generic_doc_line_RowClass}>());
export const addSettlementFailed = createAction('[Draft: Settlement] Add Settlement Failed');

export const editSettlement = createAction('[Draft: Settlement] Edit Settlement', props<{settlement: bl_fi_generic_doc_line_RowClass}>());
export const deleteSettlement = createAction('[Draft: Settlement] Delete Settlement', props<{guid: string}>());
export const resetSettlement = createAction('[Draft: Settlement] Reset');
