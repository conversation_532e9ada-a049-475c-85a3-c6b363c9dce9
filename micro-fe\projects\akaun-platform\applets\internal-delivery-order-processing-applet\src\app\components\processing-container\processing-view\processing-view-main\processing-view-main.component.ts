import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { GenericDocContainerModel } from 'blg-akaun-ts-lib';
import * as moment from 'moment';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-processing-view-main',
  templateUrl: './processing-view-main.component.html',
  styleUrls: ['./processing-view-main.component.css']
})
export class ProcessingViewMainComponent implements OnInit, OnDestroy {

  @Input() draft$: Observable<GenericDocContainerModel>;

  @Output() customer = new EventEmitter();
  @Output() shippingInfo = new EventEmitter();
  @Output() billingInfo = new EventEmitter();
  @Output() updateMain = new EventEmitter();

  apiVisa = AppConfig.apiVisa;

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Branch', formControl: 'branch', type: 'text', readonly: true},
    {label: 'Reference', formControl: 'reference', type: 'text', readonly: true},
    {label: 'Ship Via', formControl: 'shipVia', type: 'text', readonly: true},
  ];

  rightColControls = [
    {label: 'Location', formControl: 'location', type: 'text', readonly: true},
    {label: 'Transaction Date', formControl: 'transactionDate', type: 'text', readonly: true},
    {label: 'Tracking ID', formControl: 'trackingID', type: 'text', readonly: true},
  ];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      branch: new FormControl(),
      location: new FormControl(),
      reference: new FormControl(),
      transactionDate: new FormControl(),
      shipVia: new FormControl(),
      trackingID: new FormControl(),
      remarks: new FormControl(),
    });
    this.subs.sink = this.draft$.subscribe(resolve => {
      this.form.patchValue({
        branch: resolve.bl_fi_generic_doc_hdr.code_branch,
        location: resolve.bl_fi_generic_doc_hdr.code_location,
        reference: resolve.bl_fi_generic_doc_hdr.doc_reference,
        // transactionDate: resolve.bl_fi_generic_doc_hdr.date_txn,
        shipVia: (<any>resolve).bl_fi_generic_doc_hdr.doc_entity_hdr_json?.shipVia,
        trackingID: (<any>resolve).bl_fi_generic_doc_hdr.doc_entity_hdr_json?.trackingID,
        remarks: resolve.bl_fi_generic_doc_hdr.doc_remarks,
      });
    });
    this.subs.sink = this.form.valueChanges.subscribe({next: (form) => {
      this.updateMain.emit(form);
    }});
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
