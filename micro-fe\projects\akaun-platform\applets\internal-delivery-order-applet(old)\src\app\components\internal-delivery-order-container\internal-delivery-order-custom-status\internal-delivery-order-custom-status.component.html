
<form [formGroup]="form">
    <div class="view-col-forms">
      <div fxLayout="row" fxLayoutGap="5px">
        <div fxFlex="100" fxLayout="column">
          <div *ngFor="let x of leftColControls; let i = index">
            <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
              <mat-label>{{x.label}}</mat-label>
              <input matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
            </mat-form-field>
            <ng-template #input>
                <ng-container [ngSwitch]="x.type">
                  <mat-form-field *ngSwitchCase="'dropdown'" appearance="outline">
                    <mat-label>{{x.label}}</mat-label>
                    <mat-select [formControl]="form.controls[x.formControl]" (selectionChange)="updateStatus.emit(form.value)">
                      <mat-option *ngFor="let status of statusList[i]" [value]="status.name">
                        {{status.name}}
                      </mat-option>
                    </mat-select>
                    <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                  </mat-form-field>
                </ng-container>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </form>