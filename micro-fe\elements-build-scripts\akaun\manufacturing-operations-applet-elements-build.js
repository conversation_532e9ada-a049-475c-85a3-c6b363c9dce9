const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/manufacturing-operations-applet/runtime.js',
    './dist/manufacturing-operations-applet/polyfills-es5.js',
    './dist/manufacturing-operations-applet/scripts.js',
    './dist/manufacturing-operations-applet/main.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/manufacturing-operations-applet');
  await concat(files, './elements/akaun-platform/applets/manufacturing-operations-applet/manufacturing-operations-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
