import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-account-contact-person',
  templateUrl: './account-contact-person.component.html',
  styleUrls: ['./account-contact-person.component.css']
})
export class AccountContactPersonComponent implements OnInit, OnDestroy {

  @Input() hdrDraft$: Observable<GenericDocContainerModel>;

  @Output() contactPerson = new EventEmitter();

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Contact Name', formControl: 'contactName', type: 'contactName', readonly: false},
    {label: 'Designation / Position', formControl: 'designation', type: 'text', readonly: true},
    {label: 'Phone Number', formControl: 'phoneNumber', type: 'number', readonly: true},
    {label: 'Office Number', formControl: 'officeNumber', type: 'number', readonly: true},
    {label: 'Fax Number', formControl: 'faxNumber', type: 'number', readonly: true},
  ];

  rightColControls = [
    {label: 'Contact ID', formControl: 'contactId', type: 'text', readonly: true},
    {label: 'Email', formControl: 'email', type: 'email', readonly: true},
    {label: 'Mobile Number', formControl: 'mobileNumber', type: 'number', readonly: true},
    {label: 'Extension Number', formControl: 'extensionNumber', type: 'number', readonly: true},
    {label: 'Other Number', formControl: 'otherNumber', type: 'number', readonly: true},
  ];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      contactName: new FormControl(),
      contactId: new FormControl(),
      designation: new FormControl(),
      email: new FormControl(),
      phoneNumber: new FormControl(),
      mobileNumber: new FormControl(),
      officeNumber: new FormControl(),
      faxNumber: new FormControl(),
      extensionNumber: new FormControl(),
      otherNumber: new FormControl(),
    });
    this.form.controls['contactName'].disable();
    // this.subs.sink = this.store.select(InternalSalesOrderSelectors.selectCustomer).subscribe({next: resolve => {
    //   if (resolve) {
    //     this.form.controls['contactName'].enable();
    //   }
    // }});
    this.subs.sink = this.hdrDraft$.subscribe({
      next: (resolve: any) => {
        console.log("Contact::",resolve);
        if (resolve.doc_entity_hdr_guid) {
          this.form.controls['contactName'].enable();
        }
        this.form.patchValue({
          contactName: resolve?.doc_entity_hdr_json?.contactPerson?.contactName,
          contactId: resolve?.doc_entity_hdr_json?.contactPerson?.contactId,
          designation: resolve?.doc_entity_hdr_json?.contactPerson?.designation,
          email: resolve?.doc_entity_hdr_json?.contactPerson?.email,
          phoneNumber: resolve?.doc_entity_hdr_json?.contactPerson?.phoneNumber,
          mobileNumber: resolve?.doc_entity_hdr_json?.contactPerson?.mobileNumber,
          officeNumber: resolve?.doc_entity_hdr_json?.contactPerson?.officeNumber,
          faxNumber: resolve?.doc_entity_hdr_json?.contactPerson?.faxNumber,
          extensionNumber: resolve?.doc_entity_hdr_json?.contactPerson?.extensionNumber,
          otherNumber: resolve?.doc_entity_hdr_json?.contactPerson?.otherNumber,
        });
      }});
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
