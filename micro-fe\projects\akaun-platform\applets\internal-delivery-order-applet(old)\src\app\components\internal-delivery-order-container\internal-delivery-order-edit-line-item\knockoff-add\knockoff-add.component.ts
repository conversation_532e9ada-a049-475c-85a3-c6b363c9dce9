import { ChangeDetectionStrategy, Component, Input, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { 
  InternalSalesOrderService,
  InternalInboundStockTransferService
 } from 'blg-akaun-ts-lib';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { internalInboundDeliveryOrderSearchModel } from '../../../../models/advanced-search-models/internal-delivery-order.model';
import { salesLineItemSearchModel } from '../../../../models/advanced-search-models/line-item.model';
import { AppletConstants } from '../../../../models/constants/applet-constants';
import { LinkActions, PNSActions } from '../../../../state-controllers/draft-controller/store/actions';
import { HDRSelectors, LinkSelectors, PNSSelectors } from '../../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../../state-controllers/draft-controller/store/states';
import { InternalDeliveryOrderActions } from '../../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderSelectors } from '../../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { InternalDeliveryOrderStates } from '../../../../state-controllers/internal-delivery-order-controller/store/states';

interface LocalState {
  deactivateReturn: boolean;
}

@Component({
  selector: 'app-knockoff-add',
  templateUrl: './knockoff-add.component.html',
  styleUrls: ['./knockoff-add.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class KnockoffAddComponent extends ViewColumnComponent {

  service;
  advSearchModel;
  itemType;

  protected subs = new SubSink();

  protected compName = 'Add Knockoff';
  protected readonly index = 13;
  protected localState: LocalState;
  protected prevLocalState: any;

  prevIndex: number;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly listingConfig$ = this.store.select(InternalDeliveryOrderSelectors.selectKnockoffListingConfig);

  draft$ = this.draftStore.select(HDRSelectors.selectHdr);  
  LinkSelectors = LinkSelectors;
  LinkActions = LinkActions;
  PNSSelectors = PNSSelectors;
  PNSActions = PNSActions;
  additionalColumnDefs;

  stgrnAdditionalColumns = [
    {
      headerName: "Location From",
      field: "location_from",
      cellStyle: () => ({ "text-align": "left"})
    },
    {
      headerName: "Location To",
      field: "location_to",
      cellStyle: () => ({ "text-align": "left"})
    },
    {
      headerName: "Tracking Id",
      field: "tracking_id",
      cellStyle: () => ({ "text-align": "left"})
    }
  ];

  soAdditionalColumns = [
    {
      headerName: "Branch",
      field: "branch",
      cellStyle: () => ({ "text-align": "left"})
    },
    {
      headerName: "Location From",
      field: "location_from",
      cellStyle: () => ({ "text-align": "left"})
    },
    {
      headerName: "Location To",
      field: "location_to",
      cellStyle: () => ({ "text-align": "left"})
    }
  ];

  AppletConstants = AppletConstants;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private readonly componentStore: ComponentStore<LocalState>,
    public readonly draftStore: Store<DraftStates>,
    protected readonly store: Store<InternalDeliveryOrderStates>,
    public InternalSalesOrderService: InternalSalesOrderService,
    public InternalInboundStockTransferService: InternalInboundStockTransferService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.listingConfig$.subscribe(console.log);
  }

  getType(serverDocType) {
    switch(serverDocType){
      case 'INTERNAL_SALES_ORDER':
        this.advSearchModel = salesLineItemSearchModel;
        this.additionalColumnDefs = this.soAdditionalColumns;
        return 'Sales Order';
      case 'INTERNAL_INBOUND_STOCK_TRANSFER':
        this.advSearchModel = internalInboundDeliveryOrderSearchModel;
        this.additionalColumnDefs = this.stgrnAdditionalColumns;
        return 'ST GRN';
      default:
        return '';
    }
  }

  // onAdd(lineItem: any){
  //   console.log("onAdd sec", lineItem);
    
  //   this.store.dispatch(InternalDeliveryOrderActions.selectLineItem({ lineItem: lineItem }));
  //   this.store.dispatch(InternalDeliveryOrderActions.selectPricingSchemeLink({ item: lineItem }));
  //   this.viewColFacade.updateInstance(this.index, {
  //     ...this.localState,
  //     deactivateReturn: true,
  //     deactivateList: false,
  //     selectedItem: lineItem.item_guid,
  //   });
  //   this.viewColFacade.onNextAndReset(this.index, 12);
  // }


  onReturn() {
    this.viewColFacade.updateInstance<LocalState>(this.prevIndex, {
      ...this.prevLocalState,
      deactivateReturn: false,
      deactivateAdd: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

}
