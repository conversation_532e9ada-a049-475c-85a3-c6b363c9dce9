import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AppLoginContainerModel, bl_alg_cc_agent_ext_RowClass, bl_fi_generic_doc_hdr_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../models/applet-settings.model';
import { Store } from '@ngrx/store';
import { ProcessStates } from '../../../../state-controllers/process-controller/store/states';
import { ProcessSelectors } from '../../../../state-controllers/process-controller/store/selectors';

@Component({
  selector: 'app-job-order-view-main',
  templateUrl: './job-order-view-main.component.html',
  styleUrls: ['./job-order-view-main.component.css']
})
export class JobOrderViewMainComponent implements OnInit, OnDestroy {

  @Input() hdr$: Observable<bl_fi_generic_doc_hdr_RowClass>;
  @Input() appletSettings$: Observable<AppletSettings>;
  @Input() userProfile$: Observable<AppLoginContainerModel>;
  @Input() process$: Observable<string>;
  @Input() machines$: Observable<string[]>;

  @Output() itemCode = new EventEmitter();
  @Output() updateMain = new EventEmitter();

  apiVisa = AppConfig.apiVisa;
  PROCESS_STATUS = ['PLANNED','IN_PROGRESS','COMPLETED','ON_HOLD','CANCELLED'];

  private subs = new SubSink();

  form: FormGroup;

  constructor(
    private readonly processStore: Store<ProcessStates>,
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      guid: new FormControl(''),
      jobOrderNo: new FormControl(''),
      jobOrderDate: new FormControl(''),
      processType: new FormControl(''),
      processTemplateName: new FormControl(''),
      issuedBy: new FormControl(),
      machineCode: new FormControl(''),
      priority: new FormControl(''),
      remarks: new FormControl(),
      processStatus: new FormControl()
    });

    this.processStore.select(ProcessSelectors.selectProcess).subscribe(data => {
      console.log("data process instance",data);
      // if(data.processStatus = "COMPLETED"){
      //   this.form.disable()
      // }
      this.form.patchValue({
        guid: data.guid,
        jobOrderNo: data.job_order_no,
        jobOrderDate: data.job_order_date,
        processType: data.process_guid,
        processTemplateName: data.process_template,
        issuedBy: data.issued_by,
        machineCode: data.machine_code,
        priority: data.priority,
        remarks: data.remarks,
        processStatus : data.processStatus
      })
    })

  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
