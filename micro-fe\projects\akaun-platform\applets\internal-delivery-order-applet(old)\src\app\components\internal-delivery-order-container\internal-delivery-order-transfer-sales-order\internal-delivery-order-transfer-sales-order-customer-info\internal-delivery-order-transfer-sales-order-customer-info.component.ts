import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-internal-delivery-order-transfer-sales-order-customer-info',
  templateUrl: './internal-delivery-order-transfer-sales-order-customer-info.component.html',
  styleUrls: ['./internal-delivery-order-transfer-sales-order-customer-info.component.css']
})
export class InternalDeliveryOrderTransferSalesOrderCustomerInfoComponent implements OnInit, OnDestroy {

  private subs = new SubSink();

  form: FormGroup;

  controls = [
    {label: 'Customer Code', formControl: 'customerCode', type: 'text', readonly: true},
    {label: 'ID/Registration No', formControl: 'id', type: 'text', readonly: true},
    {label: 'Mobile Phone No', formControl: 'mobilePhone', type: 'text', readonly: true},
    {label: 'Email', formControl: 'email', type: 'text', readonly: true},
    {label: 'D.O.B', formControl: 'dob', type: 'text', readonly: true},
    {label: 'Nickname', formControl: 'nickName', type: 'text', readonly: true},
  ];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      customerCode: new FormControl(),
      id: new FormControl(),
      mobilePhone: new FormControl(),
      email: new FormControl(),
      dob: new FormControl(),
      nickName: new FormControl(),
      shippingInfo: new FormControl(),
      billingInfo: new FormControl(),
      shipVia: new FormControl(),
      remarks: new FormControl(),
    });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
