import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderSelectContactPersonComponent } from './internal-delivery-order-select-contact-person.component';

describe('InternalDeliveryOrderSelectContactPersonComponent', () => {
  let component: InternalDeliveryOrderSelectContactPersonComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderSelectContactPersonComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderSelectContactPersonComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderSelectContactPersonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
