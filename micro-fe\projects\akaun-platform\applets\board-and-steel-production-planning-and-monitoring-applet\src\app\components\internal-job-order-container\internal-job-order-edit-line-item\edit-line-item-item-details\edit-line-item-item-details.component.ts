import { OnInit, Component, ViewChild, Input, AfterViewChecked } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { SubSink } from 'subsink2';
import { InternalJobOrderEditLineItemDepartmentComponent } from './internal-job-order-edit-line-item-department/internal-job-order-edit-line-item-department.component';
import { InternalJobOrderEditLineItemMainComponent } from './internal-job-order-edit-line-item-main/internal-job-order-edit-line-item-main.component';

@Component({
  selector: 'app-edit-line-item-item-details',
  templateUrl: './edit-line-item-item-details.component.html',
  styleUrls: ['./edit-line-item-item-details.component.css']
})
export class EditLineItemItemDetailsComponent implements OnInit, AfterViewChecked {

  protected subs = new SubSink();

  @Input() appletSettings$;
  @Input() item$;
  @Input() tax$;
  @Input() dept$;
  @Input() line$;
  @Input() childSelectedIndex$;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(InternalJobOrderEditLineItemMainComponent) main: InternalJobOrderEditLineItemMainComponent;
  @ViewChild(InternalJobOrderEditLineItemDepartmentComponent) dept: InternalJobOrderEditLineItemDepartmentComponent;

  ngOnInit() {}

  ngAfterViewChecked() {
    this.matTab.realignInkBar();
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
