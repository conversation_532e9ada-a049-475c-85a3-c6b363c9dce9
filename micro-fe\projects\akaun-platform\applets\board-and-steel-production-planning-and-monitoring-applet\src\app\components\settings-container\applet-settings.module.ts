import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { DropDownModule } from 'blg-akaun-ng-lib';
import { MaterialModule } from 'projects/shared-utilities/modules/material.module';
import { SettingsModule } from 'projects/shared-utilities/modules/settings/settings.module';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { DefaultSettingsComponent } from './default-settings/default-settings.component';
import { FieldConfigurationComponent } from './field-configuration/field-configuration.component';
import { SettingsContainerComponent } from './settings-container.component';
import { CustomStatusComponent } from './custom-status/custom-status.component';
import { PrintablesComponent } from './printables/printables.component';
import { AgGridModule } from 'ag-grid-angular';
import { CustomStatusSettingsComponent } from './custom-status/custom-status-settings/custom-status-settings.component';
import { DeleteRendererComponent } from '../utilities/delete-renderer/delete-renderer.component';
import { AppletUtilitiesModule } from '../../feature-modules/applet-utilities.module';

@NgModule({
  declarations: [
    SettingsContainerComponent,
    FieldConfigurationComponent,
    DefaultSettingsComponent,
    CustomStatusComponent,
    PrintablesComponent,
    CustomStatusSettingsComponent,
  ],
  imports: [
    CommonModule,
    DropDownModule,
    MaterialModule,
    SettingsModule,
    UtilitiesModule,
    AppletUtilitiesModule,
    AgGridModule.withComponents([DeleteRendererComponent])
  ]
})
export class AppletSettingsModule {}
