
<div class="view-col-forms">
  <div class="inner-tab">
    <form [formGroup]="shippingForm">
      <div fxLayout="row" fxLayoutGap="5px">
        <div fxFlex="50" fxLayout="column">
          <div *ngFor="let x of shippingLeftColControls; let i = index">
            <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
              <mat-label>{{x.label}}</mat-label>
              <input matInput readonly [formControl]="shippingForm.controls[x.formControl]" autocomplete="off">
            </mat-form-field>
            <ng-template #input>
              <ng-container [ngSwitch]="x.type">
                <mat-form-field *ngSwitchCase="'text'" appearance="outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [formControl]="shippingForm.controls[x.formControl]" autocomplete="off" type="text">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'number'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [formControl]="shippingForm.controls[x.formControl]" autocomplete="off" type="number">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'text-area'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <textarea #remarks matInput [formControl]="shippingForm.controls[x.formControl]"></textarea>
                  <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'date'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [matDatepicker]="datepicker" [formControl]="shippingForm.controls[x.formControl]" autocomplete="off" readonly (click)="datepicker.open()">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                  <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle>
                  <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'shippingAddress'" appearance="outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input style="cursor: pointer" matInput readonly [formControl]="shippingForm.controls[x.formControl]" autocomplete="off" type="text" (click)="shippingInfo.emit()">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                  <mat-hint><strong>Entity</strong> must be selected first</mat-hint>
                </mat-form-field>
              </ng-container>
            </ng-template>
          </div>
        </div>
        <div fxFlex="50" fxLayout="column">
          <div *ngFor="let x of shippingRightColControls; let i = index">
            <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
              <mat-label>{{x.label}}</mat-label>
              <input  matInput readonly [formControl]="shippingForm.controls[x.formControl]" autocomplete="off">
            </mat-form-field>
            <ng-template #input>
              <ng-container [ngSwitch]="x.type">
                <mat-form-field *ngSwitchCase="'text'" appearance="outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [formControl]="shippingForm.controls[x.formControl]" autocomplete="off" type="text">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'number'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [formControl]="shippingForm.controls[x.formControl]" autocomplete="off" type="number">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'text-area'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <textarea #remarks matInput [formControl]="shippingForm.controls[x.formControl]"></textarea>
                  <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'date'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [matDatepicker]="datepicker" [formControl]="shippingForm.controls[x.formControl]" autocomplete="off" readonly (click)="datepicker.open()">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                  <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle>
                  <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
                </mat-form-field>
              </ng-container>
            </ng-template>
          </div>
        </div>
      </div>
    </form>
    <mat-divider style="margin-bottom: 25px;"></mat-divider>
    <form [formGroup]="billingForm">
      <div fxLayout="row" fxLayoutGap="5px">
        <div fxFlex="50" fxLayout="column">
          <div *ngFor="let x of billingLeftColControls; let i = index">
            <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
              <mat-label>{{x.label}}</mat-label>
              <input matInput readonly [formControl]="billingForm.controls[x.formControl]" autocomplete="off">
            </mat-form-field>
            <ng-template #input>
              <ng-container [ngSwitch]="x.type">
                <mat-form-field *ngSwitchCase="'text'" appearance="outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [formControl]="billingForm.controls[x.formControl]" autocomplete="off" type="text">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'number'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [formControl]="billingForm.controls[x.formControl]" autocomplete="off" type="number">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'text-area'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <textarea #remarks matInput [formControl]="billingForm.controls[x.formControl]"></textarea>
                  <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'date'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [matDatepicker]="datepicker" [formControl]="billingForm.controls[x.formControl]" autocomplete="off" readonly (click)="datepicker.open()">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                  <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle>
                  <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'billingAddress'" appearance="outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input style="cursor: pointer" matInput readonly [formControl]="billingForm.controls[x.formControl]" autocomplete="off" type="text" (click)="billingInfo.emit()">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                  <mat-hint><strong>Entity</strong> must be selected first</mat-hint>
                </mat-form-field>
              </ng-container>
            </ng-template>
          </div>
        </div>
        <div fxFlex="50" fxLayout="column">
          <div *ngFor="let x of billingRightColControls; let i = index">
            <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
              <mat-label>{{x.label}}</mat-label>
              <input  matInput readonly [formControl]="billingForm.controls[x.formControl]" autocomplete="off">
            </mat-form-field>
            <ng-template #input>
              <ng-container [ngSwitch]="x.type">
                <mat-form-field *ngSwitchCase="'text'" appearance="outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [formControl]="billingForm.controls[x.formControl]" autocomplete="off" type="text">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'number'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [formControl]="billingForm.controls[x.formControl]" autocomplete="off" type="number">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'text-area'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <textarea #remarks matInput [formControl]="billingForm.controls[x.formControl]"></textarea>
                  <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
                </mat-form-field>
                <mat-form-field *ngSwitchCase="'date'" appearance = "outline">
                  <mat-label>{{x.label}}</mat-label>
                  <input matInput [matDatepicker]="datepicker" [formControl]="billingForm.controls[x.formControl]" autocomplete="off" readonly (click)="datepicker.open()">
                  <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
                  <mat-datepicker-toggle matSuffix [for]="datepicker"></mat-datepicker-toggle>
                  <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
                </mat-form-field>
              </ng-container>
            </ng-template>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>

