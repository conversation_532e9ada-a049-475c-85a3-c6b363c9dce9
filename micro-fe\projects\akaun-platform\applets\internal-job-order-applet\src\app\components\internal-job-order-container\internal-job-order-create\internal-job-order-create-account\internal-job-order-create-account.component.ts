import { AfterViewChecked, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { bl_fi_generic_doc_hdr_RowClass } from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { AccountBillingAddressComponent } from './account-billing-address/account-billing-address.component';
import { AccountEntityDetailsComponent } from './account-entity-details/account-entity-details.component';
import { AccountShippingAddressComponent } from './account-shipping-address/account-shipping-address.component';
@Component({
  selector: 'app-internal-job-order-create-account',
  templateUrl: './internal-job-order-create-account.component.html',
  styleUrls: ['./internal-job-order-create-account.component.css']
})
export class InternalJobOrderCreateAccountComponent implements OnInit, AfterViewChecked, On<PERSON><PERSON>roy {

  @Input() draft$: Observable<bl_fi_generic_doc_hdr_RowClass>;
  @Input() childSelectedIndex$;

  @Output() entity = new EventEmitter();
  @Output() shipping = new EventEmitter();
  @Output() billing = new EventEmitter();
  @Output() shippingInfo = new EventEmitter();
  @Output() billingInfo = new EventEmitter();

  private subs = new SubSink();

  invalid = true;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(AccountEntityDetailsComponent) entityDetails: AccountEntityDetailsComponent;
  @ViewChild(AccountBillingAddressComponent) billingAddress: AccountBillingAddressComponent;
  @ViewChild(AccountShippingAddressComponent) shippingAddress: AccountShippingAddressComponent;

  constructor() { }

  ngOnInit() {
  }

  ngAfterViewChecked() {
    this.matTab.realignInkBar();
    this.invalid = this.entityDetails?.form.invalid;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
