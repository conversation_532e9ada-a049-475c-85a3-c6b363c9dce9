import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';

@Component({
  selector: 'app-processing-view-line-items',
  templateUrl: './processing-view-line-items.component.html',
  styleUrls: ['./processing-view-line-items.component.css']
})
export class ProcessingViewLineItemsComponent implements OnInit {

  @Input() localState: any;
  @Input() rowData: bl_fi_generic_doc_line_RowClass[];

  @Output() next = new EventEmitter();
  @Output() lineItem = new EventEmitter<bl_fi_generic_doc_line_RowClass>();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Delivery Date', field: 'item_property_json.fromCableLength'},
    {headerName: 'UOM', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Quantity', field: 'item_property_json.quantity'},
  ];

  constructor() { }

  ngOnInit() {
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

}
