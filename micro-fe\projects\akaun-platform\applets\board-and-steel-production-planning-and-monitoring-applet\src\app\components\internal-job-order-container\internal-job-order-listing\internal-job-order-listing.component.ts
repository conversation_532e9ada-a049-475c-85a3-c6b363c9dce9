import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import {
  BranchService,
  GenericDocSingleLineContainer,
  Pagination,
  SubQueryService,
  GenericDocSingleLineService,
  GenericDocLineContainerModel,
  MrpJobOrderHdrService,
  MrpJobOrderHdrContainerModel} from 'blg-akaun-ts-lib';
import { forkJoin, iif, Observable, of, zip } from 'rxjs';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../models/advanced-search-models';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { Store } from '@ngrx/store';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRowGuid: string;
}

@Component({
  selector: 'app-internal-job-order-listing',
  templateUrl: './internal-job-order-listing.component.html',
  styleUrls: ['./internal-job-order-listing.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class InternalJobOrderListingComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Job Order No Listing';
  protected readonly index = 0;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  searchModel = internalJobOrderSearchModel;
  SQLGuids: string[] = null;
  pagination = new Pagination();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Job Order No', field: 'bl_mrp_job_order_hdr.server_doc_1'},
    {headerName: 'Job Order Date', field: 'bl_mrp_job_order_hdr.date_txn'},
    {headerName: 'Item Code', field: 'bl_mrp_job_order_hdr.item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Customer Name', field: 'customerName', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Container Measure', field: 'bl_mrp_job_order_hdr.total_container_measure'},
    {headerName: 'Container Quantity', field: 'bl_mrp_job_order_hdr.container_qty'},
    {headerName: 'UOM', field: 'bl_mrp_job_order_hdr.uom'},
    {headerName: 'Process Status', field: 'bl_mrp_job_order_hdr.process_status'},
    // {headerName: 'QC Output Status', field: 'bl_mrp_job_order_hdr.batch_no.bin_code'},
    {headerName: 'Status', field: 'bl_mrp_job_order_hdr.status', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Completation Date', field: 'bl_mrp_job_order_hdr.completion_date', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Remarks', field: 'bl_mrp_job_order_hdr.remarks', cellStyle: () => ({'text-align': 'left'})},
  ];

  // columnsDefs$ = this.sessionStore.select(SessionSelectors.selectMasterSettings).pipe(
  //   map((a: any) => [
  //     ...this.columnsDefs,
  //     a.ENABLE_CUSTOM_STATUS_HDR_1 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_1 ? a.NAME_CUSTOM_STATUS_HDR_1 : 'client_doc_status_01',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_01',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //     a.ENABLE_CUSTOM_STATUS_HDR_2 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_2 ? a.NAME_CUSTOM_STATUS_HDR_2 : 'client_doc_status_02',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_02',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //     a.ENABLE_CUSTOM_STATUS_HDR_3 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_3 ? a.NAME_CUSTOM_STATUS_HDR_3 : 'client_doc_status_03',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_03',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //     a.ENABLE_CUSTOM_STATUS_HDR_4 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_4 ? a.NAME_CUSTOM_STATUS_HDR_4 : 'client_doc_status_04',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_04',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //     a.ENABLE_CUSTOM_STATUS_HDR_5 ? {
  //       headerName: a.NAME_CUSTOM_STATUS_HDR_5 ? a.NAME_CUSTOM_STATUS_HDR_5 : 'client_doc_status_05',
  //       field: 'bl_fi_generic_doc_hdr.client_doc_status_05',
  //       cellStyle: () => ({'text-align': 'left'})
  //     } : {hide: true},
  //   ])
  // );

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private mrpJobOrderHdrService: MrpJobOrderHdrService,
    private brnchService: BranchService,
    private sqlService: SubQueryService,
    private readonly store: Store<InternalJobOrderStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: (grid) => {
        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;
        this.pagination.conditionalCriteria = [
          { columnName: "calcTotalRecords", operator: "=", value: "true" },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
          // { columnName: "obj_access", operator: "=", value: "GUEST" },
          {
            columnName: "guids",
            operator: "=",
            value: this.SQLGuids
              ? this.SQLGuids.slice(
                grid.request.startRow,
                grid.request.endRow
              ).toString()
              : "",
          },
        ];
        this.subs.sink = this.mrpJobOrderHdrService
          .getByCriteria(this.pagination, apiVisa)
          .subscribe(
            (resolved) => {
              const data = sortOn(resolved.data).filter((entity) =>
                filter.by(entity)
              );
              const totalRecords = filter.isFiltering
                ? this.SQLGuids
                  ? this.SQLGuids.length
                  : resolved.totalRecords
                : data.length;
              console.log("Data",data);
              grid.success({
                rowData: data,
                rowCount: totalRecords,
              });
              this.gridApi.forEachNode((node) => {
                if (
                  node.data?.bl_mrp_job_order_hdr.guid === this.localState.selectedRowGuid
                )
                  node.setSelected(true);
              });
            },
            (err) => {
              grid.fail();
            }
          );
      },
    };
    this.gridApi.setServerSideDatasource(datasource);
   
    this.subs.sink = this.store
      .select(InternalJobOrderSelectors.selectAgGrid)
      .subscribe((resolved) => {
        if (resolved) {
          this.gridApi.refreshServerSideStore();
          this.store.dispatch(
            InternalJobOrderActions.resetAgGrid()
          );
        }
      });
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onAdd() {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false});
    this.viewColFacade.onNextAndReset(this.index, 2);
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe(
        {next: resolve => {
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }}
      );
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  onRowClicked(entity: MrpJobOrderHdrContainerModel) {
    if (entity) {
      this.store.dispatch(InternalJobOrderActions.selectEditMode({editMode: true}));
      this.store.dispatch(InternalJobOrderActions.selectEntity({entity: entity}));
      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance<LocalState>(this.index, {
          ...this.localState, deactivateAdd: false, deactivateList: true, selectedRowGuid: entity.bl_mrp_job_order_hdr.guid
        });
        this.viewColFacade.onNextAndReset(this.index, 1);
      }
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
