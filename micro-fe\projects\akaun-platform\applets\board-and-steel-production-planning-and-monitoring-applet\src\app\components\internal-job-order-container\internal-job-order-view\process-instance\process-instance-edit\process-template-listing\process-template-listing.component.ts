import { Component, ChangeDetectionStrategy, ViewChild } from "@angular/core";
import { Store } from "@ngrx/store";
import { ComponentStore } from "@ngrx/component-store";
import {
  CouponSerialNumberContainerModel,
  FinancialItemService,
  MrpProcessTemplateContainerModel,
  MrpProcessTemplateService,
  MrpProcessTypeService,
  MrpProdsysContainerModel,
  MrpProdsysService,
  Pagination,
  SubQueryService,
} from "blg-akaun-ts-lib";
import { forkJoin, iif, Observable, of, zip } from "rxjs";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { SubSink } from "subsink2";
import {
  pageFiltering,
  pageSorting,
} from "projects/shared-utilities/listing.utils";
import { catchError, concatMap, map, mergeMap } from "rxjs/operators";
import * as moment from "moment";
import { SearchQueryModel } from "projects/shared-utilities/models/query.model";
import { PaginationComponent } from "projects/shared-utilities/utilities/pagination/pagination.component";
import { ViewColumnFacade } from "projects/akaun-platform/applets/internal-job-order-applet/src/app/facades/view-column.facade";
import { InternalJobOrderStates } from "projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/states";
import { InternalJobOrderSelectors } from "projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/selectors";


interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: "app-process-template-listing",
  templateUrl: "./process-template-listing.component.html",
  styleUrls: ["./process-template-listing.component.scss"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore],
})
export class ProcessTemplateListingComponent extends ViewColumnComponent {
  protected compName = "Process Template Listing";
  protected readonly index = 0;
  private localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.deactivateAdd
  );
  readonly deactivateList$ = this.componentStore.select(
    (state) => state.deactivateList
  );

  toggleColumn$: Observable<boolean>;
  // searchModel = processTemplateSearchModel;

  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
  };

  gridApi;
  SQLGuids: string[] = null;
  pagination = new Pagination();
  process_template_hdr_guid;

  columnsDefs = [
    {
      headerName: "Process Template Name",
      field: "bl_mrp_process_template_hdr.code",
      comparator: (valueA, valueB) =>
        valueA.toLowerCase().localeCompare(valueB.toLowerCase()),
      width: 110,
      suppressSizeToFit: true,
      cellStyle: () => ({ "text-align": "left" }),
    },
    { 
      headerName: "Output Items",
      valueGetter: (params) => 
      { return params.data.outputItemNames.join('\n')},
      cellStyle: { "text-align": "left", "white-space": 'pre' },
      autoHeight: true,
      width: 110,
    },
    // {headerName: 'Machine Code', field: 'machine_code', suppressSizeToFit: false, cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Status', field: 'bl_mrp_process_template_hdr.status', suppressSizeToFit: true, cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Modified Date', field: 'bl_mrp_process_template_hdr.updated_date', type: 'rightAligned',
    // valueFormatter: params => moment(params.value).format('YYYY-MM-DD HH:MM:SS')},
    // {headerName: 'Created Date', field: 'bl_mrp_process_template_hdr.created_date', type: 'rightAligned',
    // valueFormatter: params => moment(params.value).format('YYYY-MM-DD HH:MM:SS')}
  ];

  rowData = [];

  private subs = new SubSink();

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    // private readonly draftStore: Store<DraftStates>,
    private viewColFacade: ViewColumnFacade,
    private processTemplateService: MrpProcessTemplateService,
    private prodsysService: MrpProdsysService,
    private processTypeService: MrpProcessTypeService,
    private financialItemService: FinancialItemService,
    private readonly componentStore: ComponentStore<LocalState>,
    private subQueryService: SubQueryService,
    private readonly store: Store<InternalJobOrderStates>,
    // private readonly processTemplateStore: Store<ProcessTemplateStates>
  ) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.store.select(InternalJobOrderSelectors.selectProcessInstance).subscribe(data => {
      console.log("Process instance data",data);
      this.process_template_hdr_guid = data.process_template_hdr_guid;
    })
  }

  onNext() {
    // this.draftStore.dispatch(ProcessTemplateActions.resetDraft());
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateList: false,
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();

    // this.subs.sink = this.processTemplateStore
    //   .select(ProcessTemplateSelectors.selectAgGrid)
    //   .subscribe((a) => {
    //     if (a) {
    //       this.gridApi.refreshServerSideStore();
    //       this.processTemplateStore.dispatch(
    //         ProcessTemplateActions.updateAgGridDone()
    //       );
    //     }
    //   });
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: (grid) => {
        // this.processTemplateStore.dispatch(
        //   ProcessTemplateActions.loadProcessTemplateInit({
        //     request: grid.request,
        //   })
        // );

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.conditionalCriteria = [
          { columnName: "orderBy", operator: "=", value: "updated_date" },
          { columnName: "order", operator: "=", value: "DESC" },
          { columnName: "guid", operator: "=", value: this.process_template_hdr_guid },
          // {
          //   columnName: "guids",
          //   operator: "=",
          //   value: this.SQLGuids
          //     ? this.SQLGuids.slice(
          //         grid.request.startRow,
          //         grid.request.endRow
          //       ).toString()
          //     : "",
          // },
        ];
        //Change to use from process_template_table
        this.subs.sink = this.processTemplateService
          .getByCriteria(this.pagination, apiVisa)
          .pipe(
            mergeMap((b) => {
              const source: Observable<MrpProcessTemplateContainerModel>[] = [];
              console.log(b);
              b.data.forEach((link) =>
                source.push(
                  zip(
                    this.prodsysService
                      .getByGuid(
                        link.bl_mrp_process_template_hdr.machine_guid?.toString(),
                        apiVisa
                      )
                      .pipe(catchError((err) => of(err))),
                    this.processTypeService
                      .getByGuid(
                        link.bl_mrp_process_template_hdr.process_guid?.toString(),
                        apiVisa
                      )
                      .pipe(catchError((err) => of(err))),
                  ).pipe(
                    map(([b_a, b_b]) => {
                      link = Object.assign(
                        {
                          machine_code: b_a.error
                            ? b_a.error.code
                            : b_a.data.bl_mrp_prodsys_hdr.code,
                          status: b_a.error
                            ? b_a.error.code
                            : b_a.data.bl_mrp_prodsys_hdr.status,
                          process_code: b_b.error
                            ? b_b.error.code
                            : b_b.data.bl_mrp_process_type_hdr.code,
                        },
                        link
                      );
                      return link;
                    })
                  )
                )
              );
              return iif(
                () => b.data.length > 0,
                forkJoin(source).pipe(
                  map((b_inner) => {
                    b.data = b_inner;
                    return b;
                  })
                ),
                of(b)
              );
            })
          ).pipe(
            concatMap((c) => {
              let output = []
              c.data.forEach( (data) => {
                data = Object.assign(
                  {
                    outputItemNames: data.bl_mrp_process_template_boms.filter(l => l.txn_type === 'OUTPUT' && l.status === 'ACTIVE').map(bom => bom.code)
                  },
                  data
                )
                output.push(data)
              })
              c.data = output
              return of(c);
            })
          )
          .subscribe(
            (resolved) => {
              console.log("process template resolved",resolved);
              // this.processTemplateStore.dispatch(
              //   ProcessTemplateActions.loadProcessTemplateSuccess({
              //     totalRecords: resolved.totalRecords,
              //   })
              // );
              let filteredProcessTemplate = [];
              resolved.data.forEach(data => {
                if(data.bl_mrp_process_template_hdr.guid === this.process_template_hdr_guid.toString()){
                  console.log("dataaaaa",data);
                  filteredProcessTemplate.push(data);
                }
              })
              const data = sortOn(filteredProcessTemplate).filter((entity) =>
                filter.by(entity)
              );
              const totalRecords = filter.isFiltering
                ? this.SQLGuids
                  ? this.SQLGuids.length
                  : filteredProcessTemplate.length
                : data.length;
              grid.success({
                rowData: data,
                rowCount: totalRecords,
              });
            },
            (err) => {
              grid.fail();
            }
          );
      },
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(entity: MrpProcessTemplateContainerModel) {
    // this.processTemplateStore.dispatch(
    //   ProcessTemplateActions.selectProcessTemplateForEdit({
    //     processTemplate: entity,
    //   })
    // );
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateList: false,
    });
    this.viewColFacade.onNextAndReset(this.index, 2);
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table,
      };
      this.subs.sink = this.subQueryService
        .post(sql, AppConfig.apiVisa)
        .subscribe({
          next: (resolve) => {
            this.SQLGuids = resolve.data;
            this.paginationComp.firstPage();
            this.gridApi.refreshServerSideStore();
          },
        });
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
