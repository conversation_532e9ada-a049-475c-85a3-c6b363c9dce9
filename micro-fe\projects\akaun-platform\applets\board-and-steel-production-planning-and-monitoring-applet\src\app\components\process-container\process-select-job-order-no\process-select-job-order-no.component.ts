import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import {
  GenericDocSingleLineContainer,
  Pagination,
  SubQueryService,
  bl_fi_generic_doc_single_line,
  GenericDocSingleLineService,
  LocationService,
  GenericDocLineContainerModel} from 'blg-akaun-ts-lib';
import { forkJoin, iif, Observable, of, zip } from 'rxjs';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../models/advanced-search-models';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { Store } from '@ngrx/store';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { ProcessStates } from '../../../state-controllers/process-controller/store/states';
import { ProcessActions } from '../../../state-controllers/process-controller/store/actions';

interface LocalState {
  deactivateReturn: boolean;
  selectedRowGuid: string;
}
@Component({
  selector: 'app-process-select-job-order-no',
  templateUrl: './process-select-job-order-no.component.html',
  styleUrls: ['./process-select-job-order-no.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessSelectJobOrderNoComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Select Job Order No';
  protected readonly index = 5;
  protected localState: LocalState;

  prevIndex: number;
  protected prevLocalState: any;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);

  searchModel = internalJobOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    onCellClicked: (params) => this.onCellClicked(params.data)
  };

  gridApi;

  columnsDefs = [
    // {
    //   headerName: '',
    //   field: 'checkBox',
    //   checkboxSelection: true,
    //   minWidth: 50,
    //   width: 50,
    //   flex: 0,
    //   onCellClicked: (params) => null
    // },
    {headerName: 'Location', field: 'location', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Job Order No', field: 'bl_fi_generic_doc_single_line.batch_no.bin_code'},
    {headerName: 'Item Code', field: 'bl_fi_generic_doc_single_line.item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Container Measure', field: 'bl_fi_generic_doc_single_line.batch_no.container_measure'},
    {headerName: 'Container Qty', field: 'bl_fi_generic_doc_single_line.batch_no.container_qty'},
    {headerName: 'Priority', field: 'bl_fi_generic_doc_single_line.batch_no.priority'},
    {headerName: 'Remarks', field: 'bl_fi_generic_doc_single_line.item_remarks', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'No of Bins', field: 'line_property_json.noOfBins'},
    // {headerName: 'Priority', field: 'line_property_json.priority'},
    // {headerName: 'Status', field: 'stockBalQty', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Customer Name', field: 'customerName', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Creation Date', field: 'bl_fi_generic_doc_hdr.created_date', type: 'rightAligned',
    // valueFormatter: params => moment(params.value).format('YYYY-MM-DD')},
    // {headerName: 'Type', field: 'salesAgent', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Amount Txn', field: 'bl_fi_generic_doc_hdr.amount_txn', type: 'numericColumn',
    // valueFormatter: params => formatMoneyInList(params.value)},
  ];

  columnsDefs$ = this.sessionStore.select(SessionSelectors.selectMasterSettings).pipe(
    map((a: any) => [
      ...this.columnsDefs,
      // a.ENABLE_CUSTOM_STATUS_HDR_1 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_1 ? a.NAME_CUSTOM_STATUS_HDR_1 : 'client_doc_status_01',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_01',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_2 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_2 ? a.NAME_CUSTOM_STATUS_HDR_2 : 'client_doc_status_02',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_02',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_3 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_3 ? a.NAME_CUSTOM_STATUS_HDR_3 : 'client_doc_status_03',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_03',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_4 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_4 ? a.NAME_CUSTOM_STATUS_HDR_4 : 'client_doc_status_04',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_04',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_5 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_5 ? a.NAME_CUSTOM_STATUS_HDR_5 : 'client_doc_status_05',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_05',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
    ])
  );

  SQLGuids: string[] = null;

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private singleLineService: GenericDocSingleLineService,
    private sqlService: SubQueryService,
    private lctnService: LocationService,
    private readonly store: Store<InternalJobOrderStates>,
    // private readonly draftstore: Store<DraftStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly processStore: Store<ProcessStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        const sortModel = grid.request.sortModel;
        const filterModel = grid.request.filterModel;
        const sortOn = pageSorting(sortModel);
        const filter = pageFiltering(filterModel);
        this.subs.sink = this.singleLineService.getByCriteria(new Pagination(
          this.SQLGuids ? 0 : grid.request.startRow,
          grid.request.endRow - grid.request.startRow,
          [
            {columnName: 'calcTotalRecords', operator: '=', value: 'true'},
            {columnName: 'orderBy', operator: '=', value: 'updated_date'},
            {columnName: 'order', operator: '=', value: 'DESC'},
            {
              columnName: 'guids',
              operator: '=',
              value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
            }
          ]
        ), apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<GenericDocLineContainerModel>[] = [];
            b.data.forEach((doc) =>
              source.push(
                zip(
                  this.lctnService.getByGuid(doc.bl_fi_generic_doc_line.guid_store.toString(), apiVisa).pipe(
                    catchError((err) => of(err))
                  )).pipe(
                    map(([b_a]) => {
                      return Object.assign({
                        location: b_a.error ? b_a.error.code : b_a.data.bl_inv_mst_location.code
                      }, doc);
                    })
                  )
              )
            );
            return iif(() => b.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                b.data = b_inner;
                return b;
              })),
              of(b)
            );
          })
        ).subscribe( resolved => {
          const data = sortOn(resolved.data).filter(entity => filter.by(entity));
          const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : resolved.totalRecords) : data.length;
          grid.success({
            rowData: data,
            rowCount: totalRecords
          });
        }, err => {
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe( a => {
      if (a) {
        this.gridApi.refreshServerSideStore();
        this.store.dispatch(InternalJobOrderActions.resetAgGrid());
      }
    });
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAddOutput: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAdd() {
    this.viewColFacade.addPlannedOutput(this.gridApi.getSelectedNodes().map(r => r.data), this.index);
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe(
        {next: resolve => {
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }}
      );
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  onCellClicked(jobOrderNo: GenericDocSingleLineContainer) {
    if (jobOrderNo) {
      jobOrderNo.bl_fi_generic_doc_single_line = Object.assign({
        location: (<any>jobOrderNo).location
      }, jobOrderNo.bl_fi_generic_doc_single_line);
      // this.processStore.dispatch(ProcessActions.selectJobOrderNo({jobOrderNo}));
    //   if (!this.localState.deactivateList) {
    //     this.viewColFacade.updateInstance<LocalState>(this.index, {
    //       ...this.localState, deactivateAdd: false, deactivateList: true, selectedRowGuid: entity.bl_fi_generic_doc_hdr.guid.toString()
    //     });
    //     this.viewColFacade.onNextAndReset(this.index, 1);
    //   }
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
