import { ActionReducerMap } from '@ngrx/store';
import { DraftStates } from '../states';
import * as fromAttachmentReducers from './attachment.reducers';
import * as fromPNSReducers from './pns.reducers';
import * as fromPNSEditReducers from './pns-edit.reducers';
import * as fromSettlementReducers from './settlement.reducers';
import * as fromSettlementEditReducers from './settlement-edit.reducers';
import * as fromHDRReducers from './hdr.reducers';
import * as fromHDREditReducers from './hdr-edit.reducers';
import * as fromLinkReducers from './link.reducers';

export const draftReducers: ActionReducerMap<DraftStates> = {
    attachment: fromAttachmentReducers.reducers,
    pns: fromPNSReducers.reducers,
    pnsEdit: fromPNSEditReducers.reducers,
    settlement: fromSettlementReducers.reducers,
    settlementEdit: fromSettlementEditReducers.reducers,
    hdr: fromHDRReducers.reducers,
    hdrEdit: fromHDREditReducers.reducers,
    link: fromLinkReducers.reducers,
}