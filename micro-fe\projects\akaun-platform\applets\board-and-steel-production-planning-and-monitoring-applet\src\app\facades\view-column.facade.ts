import { Injectable } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass, bl_fi_mst_entity_line_RowClass, EntityContainerModel } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { ViewColActions } from 'projects/shared-utilities/application-controller/store/actions';
import { ViewColSelectors } from 'projects/shared-utilities/application-controller/store/selectors';
import { AppStates } from 'projects/shared-utilities/application-controller/store/states';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { SnackBarConstants } from '../models/constants/snack-bar.constants';
import { BinActions, HDRActions, HDREditActions, JobOrderNoActions, PlannedInputActions, PlannedOutputActions, PNSEditActions } from '../state-controllers/draft-controller/store/actions';
import { DraftStates } from '../state-controllers/draft-controller/store/states';
import { InternalJobOrderActions } from '../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderStates } from '../state-controllers/internal-job-order-controller/store/states';
import { ProcessActions } from '../state-controllers/process-controller/store/actions';
import { ProcessStates } from '../state-controllers/process-controller/store/states';
import { ViewCacheActions } from '../state-controllers/view-cache-controller/store/actions';
import { ViewCacheSelectors } from '../state-controllers/view-cache-controller/store/selectors';
import { ViewCacheStates } from '../state-controllers/view-cache-controller/store/states';

@Injectable()
export class ViewColumnFacade {

  viewColState: ViewColumnState;
  internalSOCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectInternalSOCache);
  processMenu$ = this.viewCacheStore.select(ViewCacheSelectors.selectProcessMenu);
  salesOrderCache$ = this.viewCacheStore.select(ViewCacheSelectors.selecSalesOrderCache);

  firstCol$ = this.appStore.select(ViewColSelectors.selectFirstColComp);
  secondCol$ = this.appStore.select(ViewColSelectors.selectSecondColComp);
  breadCrumbs$ = this.appStore.select(ViewColSelectors.selectBreadCrumbs);
  leftDrawer$ = this.appStore.select(ViewColSelectors.selectLeftDrawer);
  rightDrawer$ = this.appStore.select(ViewColSelectors.selectRightDrawer);
  toggleColumn$ = this.appStore.select(ViewColSelectors.selectSingleColumn);
  prevIndex$ = this.appStore.select(ViewColSelectors.selectPrevIndex);
  prevLocalState$ = () => {
    ViewColSelectors.selectPrevLocalState.release();
    return this.appStore.select(ViewColSelectors.selectPrevLocalState);
  }

  constructor(
    private readonly appStore: Store<AppStates>,
    private readonly viewCacheStore: Store<ViewCacheStates>,
    private readonly soStore: Store<InternalJobOrderStates>,
    private readonly draftStore: Store<DraftStates>,
    private readonly processStore: Store<ProcessStates>,
    private router: Router,
    private snackBar: MatSnackBar,
    private toastr: ToastrService
  ) {
    this.appStore.select(ViewColSelectors.selectViewColState).subscribe( resolve => this.viewColState = resolve);
  }

  setViewColState(state: ViewColumnState) {
    this.appStore.dispatch(ViewColActions.setViewColState({state}));
  }

  onNext(index: number) {
    this.appStore.dispatch(ViewColActions.viewColNext({index}));
  }

  onNextAndReset(curIndex: number, nextIndex: number) {
    this.appStore.dispatch(ViewColActions.viewColNextAndReset({curIndex, nextIndex}));
  }

  onPrev(index: number) {
    this.appStore.dispatch(ViewColActions.viewColPrev({index}));
  }

  updateInstance<T>(index: number, localState: T) {
    this.appStore.dispatch(ViewColActions.viewColUpdateInstance({index, localState}));
  }

  goToIndex(index: number) {
    this.appStore.dispatch(ViewColActions.goToIndex({index}));
  }

  goBackIndex(index: number) {
    this.appStore.dispatch(ViewColActions.viewColRvIndex({index}));
  }

  goForwardIndex(index: number) {
    this.appStore.dispatch(ViewColActions.viewColFwIndex({index}));
  }

  resetIndex(index: number) {
    this.appStore.dispatch(ViewColActions.resetIndex({index}));
  }

  toggleColumn(toggle: boolean) {
    this.appStore.dispatch(ViewColActions.toggleColumn({toggle}));
  }

  selectLocalState(index: number) {
    return this.appStore.select(ViewColSelectors.selectLocalState, index);
  }

  selectProcessCache(process: string) {
    this.processStore.dispatch(ProcessActions.selectCurrentProcess({process}));
    return this.viewCacheStore.select(ViewCacheSelectors.selectProcessCache, process);
  }

  gotoFourOhFour() {
    this.router.navigate(['404']);
  }

  saveInternalSOState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cacheInternalSO({cache: this.viewColState}));
  }

  saveSalesOrderState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cacheSalesOrder({cache: this.viewColState}));
  }

  saveProcessState(process: string) {
    this.viewCacheStore.dispatch(ViewCacheActions.cacheProcess({process, cache: this.viewColState}));
  }

  showSuccessToast(message: string) {
    this.toastr.success(
      message,
      'Success',
      {
        tapToDismiss: true,
        progressBar: true,
        timeOut: 1300
      }
    );
  }

  showFailedToast(err) {
    this.toastr.error(
      err.message,
      'Error',
      {
        tapToDismiss: true,
        progressBar: true,
        timeOut: 1300
      }
    );
  }

  showSnackBar(message: string) {
    this.snackBar.open(message, 'Close');
  }

  // selectCustomer(entity: {entity: EntityContainerModel, contact: bl_fi_mst_entity_line_RowClass}, pageIndex: number) {
  //   // 1: edit, 2: create
  //   switch (pageIndex) {
  //     case 1:
  //       this.soStore.dispatch(InternalJobOrderActions.selectCustomerEdit({entity}));
  //     break;
  //     case 2:
  //       this.soStore.dispatch(InternalJobOrderActions.selectCustomer({entity}));
  //     break;
  //   }
  //   this.showSnackBar(SnackBarConstants.customerResetEffects);
  // }

  addPlannedOutput(line: bl_fi_generic_doc_line_RowClass, pageIndex: number) {
    // 1: edit, 3: create
    switch (pageIndex) {
      case 1:
        // this.draftStore.dispatch(PNSEditActions.addPNS({pns: line}));
      break;
      case 3:
        this.draftStore.dispatch(PlannedOutputActions.addPlannedOutput({line}));
        this.resetIndex(2);
      break;
    }
    this.showSnackBar(SnackBarConstants.addPlannedOutput);
  }

  addPlannedInput(line: bl_fi_generic_doc_line_RowClass, pageIndex: number) {
    // 1: edit, 4: create
    switch (pageIndex) {
      case 1:
        // this.draftStore.dispatch(PNSEditActions.addPNS({pns: line}));
      break;
      case 4:
        this.draftStore.dispatch(PlannedInputActions.addPlannedInput({line}));
        this.resetIndex(2);
      break;
    }
    this.showSnackBar(SnackBarConstants.addPlannedInput);
  }

  editLineItem(pns: bl_fi_generic_doc_line_RowClass, pageIndex: number) {
    // 1: edit, 2: create
    switch (pageIndex) {
      case 1:
        this.draftStore.dispatch(PNSEditActions.editPNS({pns}));
      break;
      case 2:
        this.draftStore.dispatch(PlannedOutputActions.editPlannedOutput({line: pns}));
        break;
    }
    this.resetIndex(pageIndex);
    this.showSnackBar(SnackBarConstants.editLineItem);
  }

  deleteLineItem(guid: string, pageIndex: number) {
    // 1: edit, 2: create
    switch (pageIndex) {
      case 1:
        this.draftStore.dispatch(PNSEditActions.deletePNS({guid}));
      break;
      case 2:
        this.draftStore.dispatch(PlannedOutputActions.deletePlannedOutput({guid}));
        break;
    }
    this.resetIndex(pageIndex);
    this.showSnackBar(SnackBarConstants.deleteLineItem);
  }

  resetDraft(pageIndex: number) {
    // 1: edit, 2: create
    switch (pageIndex) {
      case 1:
        this.draftStore.dispatch(HDREditActions.resetHDRInit());
        this.draftStore.dispatch(PNSEditActions.resetPNSInit());
        this.draftStore.dispatch(BinActions.resetBins());
        this.soStore.dispatch(InternalJobOrderActions.resetJobOrderEdit());
      break;
      case 2:
        this.draftStore.dispatch(JobOrderNoActions.reset());
        this.soStore.dispatch(InternalJobOrderActions.resetJobOrder());
      break;
    }
    this.showSnackBar(SnackBarConstants.resetJO);
  }
}
