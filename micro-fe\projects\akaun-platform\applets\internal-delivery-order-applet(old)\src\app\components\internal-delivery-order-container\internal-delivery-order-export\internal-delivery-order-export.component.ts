import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_hdr_RowClass, PrintableFormatContainerModel } from 'blg-akaun-ts-lib';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { AppletConstants } from '../../../models/constants/applet-constants';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';

@Component({
  selector: 'app-internal-delivery-order-export',
  templateUrl: './internal-delivery-order-export.component.html',
  styleUrls: ['./internal-delivery-order-export.component.css']
})
export class InternalDeliveryOrderExportComponent implements OnInit {


  protected subs = new SubSink();

  @Input() draft$: Observable<bl_fi_generic_doc_hdr_RowClass>;
  @Output() print = new EventEmitter();

  readonly appletSettings$ = this.sessionStore.select(SessionSelectors.selectMasterSettings);

  apiVisa = AppConfig.apiVisa;
  printableFormat = new FormControl();
  txn_type = AppletConstants.docType;

  constructor(private readonly store: Store<InternalDeliveryOrderStates>,
    private readonly sessionStore: Store<SessionStates>) { }

  ngOnInit() {
    this.subs.sink = this.appletSettings$.subscribe(settings => {
      this.printableFormat.setValue(settings?.PRINTABLE);
    })
  }

  onPrintableFormatChange(e: PrintableFormatContainerModel) {
    this.printableFormat.setValue(e.bl_prt_printable_format_hdr.guid.toString());
  };


  onPrint() {
    if (this.printableFormat.value) {
      this.store.dispatch(InternalDeliveryOrderActions.printJasperPdfInit({ guid: this.printableFormat.value.toString() }));
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
