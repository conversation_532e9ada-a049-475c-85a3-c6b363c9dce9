import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { InventoryItemService, MrpProcessInstanceLinesService, MrpProcessInstanceService, Pagination, TenantUserProfileService, bl_fi_generic_doc_hdr_RowClass, bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable, forkJoin, iif, of, zip } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ProcessStates } from '../../../../state-controllers/process-controller/store/states';
import { ProcessSelectors } from '../../../../state-controllers/process-controller/store/selectors';

@Component({
  selector: 'app-job-order-view-planned-input',
  templateUrl: './job-order-view-planned-input.component.html',
  styleUrls: ['./job-order-view-planned-input.component.css']
})
export class JobOrderViewPlannedInputComponent implements OnInit {

  @Input() localState: any;
  @Input() rowData: bl_fi_generic_doc_line_RowClass[] = [];

  @Output() next = new EventEmitter();
  @Output() lineItem = new EventEmitter<bl_fi_generic_doc_line_RowClass>();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    onCellClicked: (params) => this.onRowClicked(params)
  };

  gridApi;
  pagination = new Pagination();
  SQLGuids: string[] = null;
  private subs = new SubSink();
  processInstanceGuid;

  columnsDefs = [
    // {headerName: 'Job Order No', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Size of Wire', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Required Length', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Priority', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Description', field: 'description'},
    {headerName: 'Batch No', field: 'batchNo', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Planned Length', field: 'plannedLength'},
    {headerName: 'Planned Qty', field: 'plannedQty'},
    {headerName: 'Actual Length', field: 'actualLength', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Actual Quantity', field: 'actualQty', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'UOM', field: 'uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Remarks', field: 'remarks', cellStyle: () => ({'text-align': 'left'})},

  ];

  selectedRowIndex = null;

  constructor(    
    private mrpProcessInstanceService: MrpProcessInstanceService,
    private mrpProcessInstanceLinesService: MrpProcessInstanceLinesService,
    private inventoryItemService : InventoryItemService,
    private profileService: TenantUserProfileService,
    protected readonly processStore: Store<ProcessStates>,

    ) { }

  ngOnInit() {
    this.subs.sink = this.processStore.select(ProcessSelectors.selectProcess).subscribe(data => {
      console.log("Process Data",data);
      this.processInstanceGuid = data.guid;
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();  
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: "calcTotalRecords", operator: "=", value: "true" },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
          // { columnName: "bl_mrp_process_template_hdr_guid", operator: "=", value: this.process_template_hdr_guid },
          { columnName: 'guid', operator: '=', value: this.processInstanceGuid },
          { columnName: 'item_txn_type', operator: '=', value: 'INPUT' },

          {
            columnName: "guids",
            operator: "=",
            value: this.SQLGuids
              ? this.SQLGuids.slice(
                grid.request.startRow,
                grid.request.endRow
              ).toString()
              : "",
          },
        ];
        let totalrec = 0;
        this.mrpProcessInstanceLinesService.getLinesByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {
            console.log("Planned Input",resolved);
            totalrec = resolved.data.length;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                itemperm.bl_mrp_process_instance_line.inv_item_guid ?
                this.inventoryItemService.getByGuid(itemperm.bl_mrp_process_instance_line.inv_item_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),

                ).pipe(
                  map(([b_a]) => {
                    console.log('Ivn ->>>',b_a);
                    let obj = {"guid":'', "item_code": '', "description": '', "plannedLength": '',"plannedQty" : '',"actualLength" : '',"actualQty" : '',"uom" : '',"remarks" : '',"batchNo":''};
                    obj.guid = itemperm.bl_mrp_process_instance_line.guid ?  itemperm.bl_mrp_process_instance_line.guid : '';
                    obj.item_code = b_a.data.bl_inv_mst_item_hdr.code ? b_a.data.bl_inv_mst_item_hdr.code : '';
                    obj.description = b_a.data.bl_inv_mst_item_hdr.descr ? b_a.data.bl_inv_mst_item_hdr.descr : '';
                    obj.plannedLength = itemperm.bl_mrp_process_instance_line.qty_planned ? itemperm.bl_mrp_process_instance_line.qty_planned.toString() : '0'
                    obj.plannedQty = itemperm.bl_mrp_process_instance_line.qty_planned ? itemperm.bl_mrp_process_instance_line.qty_planned.toString() : '0'
                    obj.actualLength = itemperm.bl_mrp_process_instance_line.qty_actual ? itemperm.bl_mrp_process_instance_line.qty_actual.toString() : '0'
                    obj.actualQty = itemperm.bl_mrp_process_instance_line.qty_actual ? itemperm.bl_mrp_process_instance_line.qty_actual.toString() : '0'
                    obj.batchNo = itemperm.bl_mrp_process_instance_line.batch_no ? itemperm.bl_mrp_process_instance_line.batch_no.toString() : ''
                    // obj.code = itemperm.bl_mrp_process_template_bom.code.toString();
                    // obj.ratio = itemperm.bl_mrp_process_template_bom.ratio.toString();
                    obj.uom = itemperm.bl_mrp_process_instance_line.uom_json?.uom ?  itemperm.bl_mrp_process_instance_line.uom_json?.uom : '';
                    console.log("Objecr Input",obj);
                    return obj;
                  })
                )
            )
            );
            return iif(() => resolved.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              console.log("Objecr Input",data);

              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    // this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
    //   if (resolved) {
    //     this.gridApi.refreshServerSideStore({ purge: true });
    //   }
    // });
  }

  onNext() {
    this.next.emit();
  }

  onRowClicked(e) {
    // this.selectedRowIndex = this.localState.selectedLineItemRowIndex === null ? e.rowIndex : null;
    this.lineItem.emit(e.data);
  }

}
