h3 {
  font-size: 17px;
}

input {
  width: 100%;
  height: 100%;
}

label {
  color: white;
  width: 183px;
  height: 44px;
  border-radius: 10px;
  border: #1e88e5 1px solid;
  background-color: #1e88e5;
  padding: 8px 16px;
  cursor: pointer;
}

label:hover {
  color: #1e88e5;
  background-color: white;
}

.main-container {
  padding: 2rem;
}

.drop-zone {
  width: 100%;
  height: 200px;
  padding: 3rem;
  text-align: center;
  border: dashed 1px lightgrey;
  position: relative;
  -webkit-transition: 0.3s;
  -moz-transition: 0.3s;
  -ms-transition: 0.3s;
  -o-transition: 0.3s;
  transition: 0.3s;
}

.grid-container {
  display: flex;
  margin: 10px;
  overflow: auto;
}

.img-container {
  position: relative;
  box-shadow: 0 1px 1px -2px rgb(0 0 0 / 20%), 0 2px 2px 0 rgb(0 0 0 / 14%), 0 1px 5px 0 rgb(0 0 0 / 12%);
  width: 125px;
  height: 100px;
  margin: 10px;
}

.file-preview {
  width: 125px;
  height: 75px;
}

.file-name {
  margin: 0;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

