<div class="view-col-table" fxLayout="column">
  <div [ngStyle.gt-md]="{'flex-flow': 'row'}" [ngStyle.lt-lg]="{'flex-flow': 'row wrap'}" fxLayoutAlign="space-between end" fxLayoutGap="10px">
    <div fxFlex="55" fxFlex.lt-lg="100">
      <div [ngStyle.lt-lg]="{'margin-left': '10px'}" fxLayout="row">
        <app-advanced-search fxFlex [id]="'internal-so'" [advSearchModel]="searchModel"></app-advanced-search>
      </div>
    </div>
    <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
      <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
      <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
    </div>
  </div>
  <div style="height: 100%;">
    <ag-grid-angular #agGrid
    style="height: 100%;"
    class="ag-theme-balham"
    [getRowClass]="pagination.getRowClass"
    [columnDefs]="columnsDefs"
    [rowData]="[]"
    [paginationPageSize]="pagination.rowPerPage"
    [cacheBlockSize]="10"
    [pagination]="true"
    [animateRows]="true"
    [defaultColDef]="defaultColDef"
    [suppressRowClickSelection]="true"
    [sideBar]="true"
    [rowModelType]="'serverSide'"
    (rowClicked)="onRowClicked($event.data)"
    (gridReady)="onGridReady($event)">
    </ag-grid-angular>
  </div>
</div>
