import { Injectable } from '@angular/core';
import { InternalJobOrderCreateLineItemComponent } from '../components/internal-job-order-container/internal-job-order-create-line-item/internal-job-order-create-line-item.component';
import { InternalJobOrderCreateComponent } from '../components/internal-job-order-container/internal-job-order-create/internal-job-order-create.component';
import { InternalJobOrderListingComponent } from '../components/internal-job-order-container/internal-job-order-listing/internal-job-order-listing.component';
import { InternalJobOrderSelectCustomerComponent } from '../components/internal-job-order-container/internal-job-order-select-customer/internal-job-order-select-customer.component';
import { InternalJobOrderViewComponent } from '../components/internal-job-order-container/internal-job-order-view/internal-job-order-view.component';
import { InternalJobOrderAddLineItemComponent } from '../components/internal-job-order-container/internal-job-order-add-line-item/internal-job-order-add-line-item.component';
import { InternalJobOrderEditLineItemComponent } from '../components/internal-job-order-container/internal-job-order-edit-line-item/internal-job-order-edit-line-item.component';
import { InternalJobOrderAddAttachmentsComponent } from '../components/internal-job-order-container/internal-job-order-add-attachments/internal-job-order-add-attachments.component';
import { InternalJobOrderEditIssueComponent } from '../components/internal-job-order-container/internal-job-order-edit-issue/internal-job-order-edit-issue.component';
import { InternalJobOrderAddRelatedDocumentsComponent } from '../components/internal-job-order-container/internal-job-order-add-related-documents/internal-job-order-add-related-documents.component';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { ViewColumn } from 'projects/shared-utilities/view-column';
import { SalesOrderLinkAddComponent } from '../components/internal-job-order-container/internal-job-order-view/gendoc-link/gendoc-link-add/gendoc-link-add.component';
import { ProcessInstanceEditComponent } from '../components/internal-job-order-container/internal-job-order-view/process-instance/process-instance-edit/process-instance-edit.component';
import { ProcessInstanceCreateComponent } from '../components/internal-job-order-container/internal-job-order-view/process-instance/process-instance-create/process-instance-create.component';

@Injectable()
export class MasterListPagesService {

  // TODO: thinking of using a hashmap and need to get rid of adding index manually into pages component
  private initialState: ViewColumnState = {
    firstColumn: new ViewColumn(0, InternalJobOrderListingComponent, 'Job Order No Listing', {
      deactivateAdd: false,
      deactivateList: false,
      selectedRowIndex: null,
    }),
    secondColumn: null,
    viewCol: [
      new ViewColumn(0, InternalJobOrderListingComponent, 'Job Order No Listing', {
        deactivateAdd: false,
        deactivateList: false,
        selectedRowIndex: null,
      }),
      new ViewColumn(1, InternalJobOrderViewComponent, 'Internal Job Order View', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateCustomer: false,
        deactivateJobAgent: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateLineItem: false,
        deactivateSettlement: false,
        deactivateAddContra: false,
        deactivateViewContra: false,
        deactivateAddAttachments: false,
        selectedIndex: 0,
        childSelectedIndex: 0,
        selectedLineItemRowIndex: null,
        deleteConfirmation: false
      }),
      new ViewColumn(2, InternalJobOrderCreateComponent, 'Internal Job Order Create', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateCustomer: false,
        deactivateJobAgent: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateLineItem: false,
        deactivateSettlement: false,
        deactivateAddContra: false,
        deactivateAddAttachments: false,
        selectedIndex: 0,
        childSelectedIndex: 0,
        selectedLineItemRowIndex: null
      }),
      new ViewColumn(3, InternalJobOrderCreateLineItemComponent, 'Select Line Item', {
        deactivateReturn: false,
        deactivateFIList: false,
        // deactivateSearchItemList: false,
        // deactivateJobsheetItemList: false,
        // deactivateQuotationItemList: false,
        // deactivatePreviousSOList: false,
        selectedIndex: 0
      }),
      new ViewColumn(4, InternalJobOrderSelectCustomerComponent, 'Select Customer', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateList: false
      }),
      new ViewColumn(5, InternalJobOrderAddLineItemComponent, 'Add Line Item', {
        deactivateReturn: false,
        selectedIndex: 0
      }),
      new ViewColumn(6, InternalJobOrderEditLineItemComponent, 'Edit Line Item', {
        deactivateReturn: false,
        selectedIndex: 0,
        deactivateAddDoc: false,
        deactivateIssueLink: false,
        deliveryDetailsEdit: false,
        itemDetailsSelectedIndex: 0,
        serialNumberSelectedIndex: 0,
        deleteConfirmation: false
      }),
      new ViewColumn(7, InternalJobOrderAddAttachmentsComponent, 'Add Attachments', {
        deactivateReturn: false
      }),
      new ViewColumn(8, InternalJobOrderEditIssueComponent, 'Add Attachments', {
        deactivateReturn: false
      }),
      new ViewColumn(9, InternalJobOrderAddRelatedDocumentsComponent, 'Add Related Documents', {
        deactivateReturn: false,
        deactivateList: false
      }),
      new ViewColumn(10, SalesOrderLinkAddComponent, 'Sales Order Link Add', {
      }),
      new ViewColumn(11, ProcessInstanceEditComponent, 'Process Instance Edit', {
      }),
      new ViewColumn(12, ProcessInstanceCreateComponent, 'Process Instance Create', {
      }),
    ],
    breadCrumbs: [],
    leftDrawer: [],
    rightDrawer: [],
    singleColumn: false,
    prevIndex: null
  };

  get pages() {
    return this.initialState;
  }

  constructor() { }
}
