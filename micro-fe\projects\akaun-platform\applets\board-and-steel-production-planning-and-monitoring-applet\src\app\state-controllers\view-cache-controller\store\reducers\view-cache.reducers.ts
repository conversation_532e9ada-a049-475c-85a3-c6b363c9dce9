import { Action, createReducer, on } from '@ngrx/store';
import { ViewCacheActions } from '../actions';
import { initialState, ViewCacheState } from '../states/view-cache.states';

export const viewCacheFeatureKey = 'viewCache';

export const viewCacheReducer = createReducer(
  initialState,
  on(ViewCacheActions.cacheInternalSO, (state, action) => ({...state, internalSO: action.cache})),
  on(ViewCacheActions.cacheProcess, (state, action) => ({...state, [action.process]: {
    ...action.cache,
    firstColumn: {...action.cache.firstColumn, localState: JSON.parse(JSON.stringify(action.cache.firstColumn.localState))},
    secondColumn: action.cache.secondColumn ? {
      ...action.cache.secondColumn, localState: JSON.parse(JSON.stringify(action.cache.firstColumn.localState))} : null,
    viewCol: [...action.cache.viewCol.map(v => ({...v, localState: JSON.parse(JSON.stringify(v.localState))}))],
    breadCrumbs: [...action.cache.breadCrumbs.map(v => ({...v, localState: JSON.parse(JSON.stringify(v.localState))}))],
    leftDrawer: [...action.cache.leftDrawer.map(v => ({...v, localState: JSON.parse(JSON.stringify(v.localState))}))],
    rightDrawer: [...action.cache.rightDrawer.map(v => ({...v, localState: JSON.parse(JSON.stringify(v.localState))}))],
  }})),
  // TODO: Maybe no need to deep copy the viewColState as it might give better UX?
  on(ViewCacheActions.addProcess, (state, action) => ({...state, [action.process]: {
    ...action.cache,
    firstColumn: {...action.cache.firstColumn, localState: JSON.parse(JSON.stringify(action.cache.firstColumn.localState))},
    secondColumn: action.cache.secondColumn ? {
      ...action.cache.secondColumn, localState: JSON.parse(JSON.stringify(action.cache.firstColumn.localState))} : null,
    viewCol: [...action.cache.viewCol.map(v => ({...v, localState: JSON.parse(JSON.stringify(v.localState))}))],
    breadCrumbs: [...action.cache.breadCrumbs.map(v => ({...v, localState: JSON.parse(JSON.stringify(v.localState))}))],
    leftDrawer: [...action.cache.leftDrawer.map(v => ({...v, localState: JSON.parse(JSON.stringify(v.localState))}))],
    rightDrawer: [...action.cache.rightDrawer.map(v => ({...v, localState: JSON.parse(JSON.stringify(v.localState))}))],
  }})),
  on(ViewCacheActions.changeProcess, (state, action) => ({...state, processMenu: action.process})),
  on(ViewCacheActions.cacheSalesOrder, (state, action) => ({...state, salesOrder: action.cache})),

);

export function reducer(state: ViewCacheState | undefined, action: Action) {
  return viewCacheReducer(state, action);
}


