const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/process-maintenance-applet/runtime.js',
    './dist/process-maintenance-applet/polyfills-es5.js',
    './dist/process-maintenance-applet/scripts.js',
    './dist/process-maintenance-applet/main.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/process-maintenance-applet');
  await concat(files, './elements/akaun-platform/applets/process-maintenance-applet/process-maintenance-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
