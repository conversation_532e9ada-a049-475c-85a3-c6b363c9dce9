import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderCreateCustomFieldsComponent } from './internal-delivery-order-create-custom-fields.component';

describe('InternalDeliveryOrderCreateCustomFieldsComponent', () => {
  let component: InternalDeliveryOrderCreateCustomFieldsComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderCreateCustomFieldsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderCreateCustomFieldsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderCreateCustomFieldsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
