import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { AppletContainerModel, AppletService, bl_applet_ext_RowClass } from 'blg-akaun-ts-lib';
import { ToastrService } from 'ngx-toastr';
import { AppConfig } from 'projects/shared-utilities/visa';
import { exhaustMap, map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ToastConstants } from '../../../models/constants/toast.constants';

@Component({
  selector: 'app-field-configuration',
  templateUrl: './field-configuration.component.html',
  styleUrls: ['./field-configuration.component.css']
})
export class FieldConfigurationComponent implements OnInit, OnDestroy {

  private subs = new SubSink();

  apiVisa = AppConfig.apiVisa;

  sst = new FormControl();
  wht = new FormControl();
  segment = new FormControl();
  dimension = new FormControl();
  profitCenter = new FormControl();
  project = new FormControl();

  appletContainer: AppletContainerModel;

  constructor(
    private appletService: AppletService,
    private toastr: ToastrService
    ) { }

  ngOnInit() {
    this.subs.sink = this.appletService.getByGuid(sessionStorage.getItem('appletGuid'), this.apiVisa).subscribe({next: resolve => {
      if (resolve) {
        this.appletContainer = resolve.data;
        const sst = resolve.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.value_json?.INCLUDE_SST;
        const wht = resolve.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.value_json?.INCLUDE_WHT;
        const segment = resolve.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.value_json?.INCLUDE_SEGMENT;
        const dimension = resolve.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.value_json?.INCLUDE_DIMENSION;
        const profitCenter = resolve.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.value_json?.INCLUDE_PROFIT_CENTER;
        const project = resolve.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.value_json?.INCLUDE_PROJECT;
        this.sst.patchValue(sst);
        this.wht.patchValue(wht);
        this.segment.patchValue(segment);
        this.dimension.patchValue(dimension);
        this.profitCenter.patchValue(profitCenter);
        this.project.patchValue(project);
      }
    }});
    this.subs.sink = this.sst.valueChanges.subscribe({next: resolve => {
      const exist = this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS');
      if (exist) {
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json = {
          ...this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json,
          INCLUDE_SST: resolve
        };
      } else {
        const appletExt = new bl_applet_ext_RowClass();
        appletExt.param_code = 'APPLET_SETTINGS';
        appletExt.param_name = 'APPLET_SETTINGS';
        appletExt.param_type = 'JSON';
        appletExt.value_json = {
          INCLUDE_SST: resolve
        };
        this.appletContainer.bl_applet_exts.push(appletExt);
      }
    }});
    this.subs.sink = this.wht.valueChanges.subscribe({next: resolve => {
      const exist = this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS');
      if (exist) {
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json = {
          ...this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json,
          INCLUDE_WHT: resolve
        };
      } else {
        const appletExt = new bl_applet_ext_RowClass();
        appletExt.param_code = 'APPLET_SETTINGS';
        appletExt.param_name = 'APPLET_SETTINGS';
        appletExt.param_type = 'JSON';
        appletExt.value_json = {
          INCLUDE_WHT: resolve
        };
        this.appletContainer.bl_applet_exts.push(appletExt);
      }
    }});
    this.subs.sink = this.segment.valueChanges.subscribe({next: resolve => {
      const exist = this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS');
      if (exist) {
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json = {
          ...this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json,
          INCLUDE_SEGMENT: resolve
        };
      } else {
        const appletExt = new bl_applet_ext_RowClass();
        appletExt.param_code = 'APPLET_SETTINGS';
        appletExt.param_name = 'APPLET_SETTINGS';
        appletExt.param_type = 'JSON';
        appletExt.value_json = {
          INCLUDE_SEGMENT: resolve
        };
        this.appletContainer.bl_applet_exts.push(appletExt);
      }
    }});
    this.subs.sink = this.dimension.valueChanges.subscribe({next: resolve => {
      const exist = this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS');
      if (exist) {
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json = {
          ...this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json,
          INCLUDE_DIMENSION: resolve
        };
      } else {
        const appletExt = new bl_applet_ext_RowClass();
        appletExt.param_code = 'APPLET_SETTINGS';
        appletExt.param_name = 'APPLET_SETTINGS';
        appletExt.param_type = 'JSON';
        appletExt.value_json = {
          INCLUDE_DIMENSION: resolve
        };
        this.appletContainer.bl_applet_exts.push(appletExt);
      }
    }});
    this.subs.sink = this.profitCenter.valueChanges.subscribe({next: resolve => {
      const exist = this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS');
      if (exist) {
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json = {
          ...this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json,
          INCLUDE_PROFIT_CENTER: resolve
        };
      } else {
        const appletExt = new bl_applet_ext_RowClass();
        appletExt.param_code = 'APPLET_SETTINGS';
        appletExt.param_name = 'APPLET_SETTINGS';
        appletExt.param_type = 'JSON';
        appletExt.value_json = {
          INCLUDE_PROFIT_CENTER: resolve
        };
        this.appletContainer.bl_applet_exts.push(appletExt);
      }
    }});
    this.subs.sink = this.project.valueChanges.subscribe({next: resolve => {
      const exist = this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS');
      if (exist) {
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json = {
          ...this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').value_json,
          INCLUDE_PROJECT: resolve
        };
      } else {
        const appletExt = new bl_applet_ext_RowClass();
        appletExt.param_code = 'APPLET_SETTINGS';
        appletExt.param_name = 'APPLET_SETTINGS';
        appletExt.param_type = 'JSON';
        appletExt.value_json = {
          INCLUDE_PROJECT: resolve
        };
        this.appletContainer.bl_applet_exts.push(appletExt);
      }
    }});
  }

  onSave() {
    this.subs.sink = this.appletService.getByGuid(sessionStorage.getItem('appletGuid'), this.apiVisa).pipe(
      map(a => {
        const revision = a.data.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS')?.revision;
        this.appletContainer.bl_applet_exts.find(x => x.param_code === 'APPLET_SETTINGS').revision = revision ? revision : null;
        this.appletContainer = {
          bl_applet_hdr: {
            ...this.appletContainer.bl_applet_hdr,
            revision: a.data.bl_applet_hdr.revision
          },
          bl_applet_exts: [
            ...this.appletContainer.bl_applet_exts
          ]
        };
        return this.appletContainer;
      }),
      exhaustMap(b => this.appletService.put(b, this.apiVisa))
    ).subscribe({next: resolve => {
      this.toastr.success(
        ToastConstants.settingsSavedSuccess,
        'Success',
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      this.appletContainer = resolve.data;
    }, error: err => {
      this.toastr.error(
        err.message,
        'Error',
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
    }});
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
