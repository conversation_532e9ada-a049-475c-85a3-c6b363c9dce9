import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute } from '@angular/router';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { InventoryItemService, MrpProcessInstanceLinesService, MrpProcessInstanceService, Pagination, TenantUserProfileService, bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { Observable, combineLatest, forkJoin, iif, of, zip } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { ProcessSelectors } from '../../../state-controllers/process-controller/store/selectors';
import { ProcessStates } from '../../../state-controllers/process-controller/store/states';
import { InternalJobOrderEditLineItemDepartmentComponent } from '../../internal-job-order-container/internal-job-order-edit-line-item/edit-line-item-item-details/internal-job-order-edit-line-item-department/internal-job-order-edit-line-item-department.component';
import { AddPlannedOutputMainComponent } from './add-planned-output-main/add-planned-output-main.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';

interface LocalState {
  deactivateAddOutput: boolean;
  deactivateReturn: boolean;
  selectedIndex: number;
}

@Component({
  selector: 'app-process-add-planned-output',
  templateUrl: './process-add-planned-output.component.html',
  styleUrls: ['./process-add-planned-output.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessAddPlannedOutputComponent extends ViewColumnComponent {

  protected compName = 'Add Planned Output';
  protected index = 3;
  protected localState: LocalState;

  // readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  // readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  // readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);

  prevIndex: number;
  protected prevLocalState: any;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
  };

  gridApi;
  pagination = new Pagination();
  SQLGuids: string[] = null;
  private subs = new SubSink();
  processInstanceGuid;

  columnsDefs = [
    // {headerName: 'Job Order No', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Size of Wire', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Required Length', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Priority', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Description', field: 'description'},
    {headerName: 'Batch No', field: 'batchNo', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Planned Qty', field: 'plannedQty'},
    {headerName: 'New Item Code', field: 'newItemCode'},
    {headerName: 'New Description', field: 'newDesc'},
    {headerName: 'New Batch No', field: 'newBatchNo'},
    {headerName: 'New Planned Qty', field: 'newPlannedQty', cellStyle: () => ({'text-align': 'left'})},
    

  ];

  selectedRowIndex = null;

  constructor(    
    private mrpProcessInstanceService: MrpProcessInstanceService,
    protected viewColFacade: ViewColumnFacade,
    private mrpProcessInstanceLinesService: MrpProcessInstanceLinesService,
    private inventoryItemService : InventoryItemService,
    private profileService: TenantUserProfileService,
    protected readonly processStore: Store<ProcessStates>,

    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.processStore.select(ProcessSelectors.selectProcess).subscribe(data => {
      console.log("Process Data",data);
      this.processInstanceGuid = data.guid;
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();  
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: "calcTotalRecords", operator: "=", value: "true" },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
          // { columnName: "bl_mrp_process_template_hdr_guid", operator: "=", value: this.process_template_hdr_guid },
          { columnName: 'guid', operator: '=', value: this.processInstanceGuid },
          { columnName: 'item_txn_type', operator: '=', value: 'INPUT' },

          {
            columnName: "guids",
            operator: "=",
            value: this.SQLGuids
              ? this.SQLGuids.slice(
                grid.request.startRow,
                grid.request.endRow
              ).toString()
              : "",
          },
        ];
        let totalrec = 0;
        this.mrpProcessInstanceLinesService.getLinesByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {
            console.log("Planned Input",resolved);
            totalrec = resolved.data.length;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                itemperm.bl_mrp_process_instance_line.inv_item_guid ?
                this.inventoryItemService.getByGuid(itemperm.bl_mrp_process_instance_line.inv_item_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),

                ).pipe(
                  map(([b_a]) => {
                    console.log('Ivn ->>>',b_a);
                    let obj = {"guid":'', "item_code": '', "description": '', "plannedLength": '',"plannedQty" : '',"actualLength" : '',"actualQty" : '',"uom" : '',"remarks" : '',"batchNo":''};
                    obj.guid = itemperm.bl_mrp_process_instance_line.guid ?  itemperm.bl_mrp_process_instance_line.guid : '';
                    obj.item_code = b_a.data.bl_inv_mst_item_hdr.code ? b_a.data.bl_inv_mst_item_hdr.code : '';
                    obj.description = b_a.data.bl_inv_mst_item_hdr.descr ? b_a.data.bl_inv_mst_item_hdr.descr : '';
                    obj.plannedLength = itemperm.bl_mrp_process_instance_line.qty_planned ? itemperm.bl_mrp_process_instance_line.qty_planned.toString() : '0'
                    obj.plannedQty = itemperm.bl_mrp_process_instance_line.qty_planned ? itemperm.bl_mrp_process_instance_line.qty_planned.toString() : '0'
                    obj.actualLength = itemperm.bl_mrp_process_instance_line.qty_actual ? itemperm.bl_mrp_process_instance_line.qty_actual.toString() : '0'
                    obj.actualQty = itemperm.bl_mrp_process_instance_line.qty_actual ? itemperm.bl_mrp_process_instance_line.qty_actual.toString() : '0'
                    obj.batchNo = itemperm.bl_mrp_process_instance_line.batch_no ? itemperm.bl_mrp_process_instance_line.batch_no.toString() : ''
                    // obj.code = itemperm.bl_mrp_process_template_bom.code.toString();
                    // obj.ratio = itemperm.bl_mrp_process_template_bom.ratio.toString();
                    obj.uom = itemperm.bl_mrp_process_instance_line.uom_json?.uom ?  itemperm.bl_mrp_process_instance_line.uom_json?.uom : '';
                    console.log("Objecr Input",obj);
                    return obj;
                  })
                )
            )
            );
            return iif(() => resolved.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              console.log("Objecr Input",data);

              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    // this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
    //   if (resolved) {
    //     this.gridApi.refreshServerSideStore({ purge: true });
    //   }
    // });
  }

  onReturn() {
    this.viewColFacade.updateInstance(1, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(1);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
