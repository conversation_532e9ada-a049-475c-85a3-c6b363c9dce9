import { ChangeDetector<PERSON>ef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { BranchContainerModel, LocationContainerModel, GuidDataFieldInterface, MrpJobOrderHdrService, bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { withLatestFrom, filter, debounceTime, distinctUntilChanged, tap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../models/applet-settings.model';
import { Store } from '@ngrx/store';
import { InternalJobOrderStates } from '../../../../state-controllers/internal-job-order-controller/store/states';
import { InternalJobOrderSelectors } from '../../../../state-controllers/internal-job-order-controller/store/selectors';
@Component({
  selector: 'app-internal-job-order-create-main',
  templateUrl: './internal-job-order-create-main.component.html',
  styleUrls: ['./internal-job-order-create-main.component.css']
})
export class InternalJobOrderCreateMainComponent implements OnInit, OnDestroy {

  @Input() draft$: Observable<bl_mrp_job_order_hdr_RowClass>;
  @Input() appletSettings$: Observable<AppletSettings>;
  @Output() itemCode = new EventEmitter();
  @Output() updateMain = new EventEmitter();

  apiVisa = AppConfig.apiVisa;

  PROCESS_STATUS = ['PLANNED','IN_PROGRESS','COMPLETED','ON_HOLD','CANCELLED'];
  QC_OUTPUT_STATUS = ['PASSED','REJECTED'];

  private subs = new SubSink();

  form: FormGroup;
  uom;
  editMode;
  selectedBranch: GuidDataFieldInterface;
  selectedCompany: GuidDataFieldInterface;
  jobOrderNoRestriction = false;
  messageList: string[] = [];

  leftColControls = [
    {label: 'Job Order No', formControl: 'jobOrderNo', type: 'text', readonly: false},
    {label: 'Item Code', formControl: 'itemCode', type: 'itemCode', readonly: false},
    {label: 'UOM', formControl: 'uom', type: 'text', readonly: false},
    {label: 'Ad-hoc Quantity', formControl: 'adHocQty', type: 'number', readonly: false },
    {label: 'Process Status', formControl: 'process_status', type: 'process_status', readonly: false},
    {label: 'QC Output Status', formControl: 'qcOutputStatus', type: 'qcOutputStatus', readonly: false},
  ];

  rightColControls = [
    {label: 'Job Order Date', formControl: 'jobOrderDate', type: 'date', readonly: false},
    {label: 'Item Name', formControl: 'itemName', type: 'itemName', readonly: false},
    {label: 'Container Quantity', formControl: 'containerQty', type: 'number', readonly: false},
    {label: 'Total Container Measure', formControl: 'totalContainerMeasure', type: 'number', readonly: false},
    {label: 'Estimated Packing Date', formControl: 'estimatedPackingDate', type: 'date', readonly: false},
    {label: 'Completion Date', formControl: 'completionDate', type: 'date', readonly: false},
    {label: 'Remarks', formControl: 'remarks', type: 'text', readonly: false},
  ];

  constructor(
    private cdr: ChangeDetectorRef,
    private mrpJobOrderHdrService: MrpJobOrderHdrService,
    protected readonly store: Store<InternalJobOrderStates>,
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      jobOrderNo: new FormControl(''),
      jobOrderDate: new FormControl(''),
      itemCode: new FormControl('',Validators.required),
      itemName: new FormControl(''),
      itemGuid : new FormControl(''),
      baseQty: new FormControl(''),
      uom: new FormControl(''),
      containerQty: new FormControl(0.00),
      adHocQty: new FormControl(0.00),
      totalContainerMeasure: new FormControl(0.00),
      process_status: new FormControl(''),
      estimatedPackingDate: new FormControl(''),
      description: new FormControl(''),
      completionDate: new FormControl(''),
      qcOutputStatus: new FormControl(),
      remarks: new FormControl(),
      branch: new FormControl(null, Validators.required),
      location: new FormControl(null, Validators.required),
      company: new FormControl(null, Validators.required),
    });
    this.store.select(InternalJobOrderSelectors.selectEditMode).subscribe(data => {
      this.editMode = data;
    });

    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectItem).subscribe(data => {
      this.uom = data?.bl_fi_mst_item_hdr?.uom;
    })
    this.subs.sink = this.draft$.pipe(
      withLatestFrom(this.appletSettings$)
    ).subscribe(([fromDraft, fromAppletSettings]) => {
      this.jobOrderNoRestriction = fromAppletSettings.ENABLE_MANUAL_JOB_ORDER;
      this.form.patchValue({
        jobOrderNo: fromDraft.server_doc_1,
        // totalContainerMeasure: (<any>fromDraft.batch_no)?.container_measure,
        itemCode: fromDraft.item_code,
        itemName: fromDraft.item_name,
        itemGuid: fromDraft.item_guid,
        branch: fromDraft.guid_branch ?? fromAppletSettings.DEFAULT_BRANCH,
        location: fromDraft.guid_store,
        company: fromDraft.guid_comp ?? fromAppletSettings.DEFAULT_COMPANY,
        jobOrderDate: fromDraft.date_txn  ? fromDraft.date_txn : new Date(),
        uom: fromDraft.uom,
        containerQty: fromDraft.container_qty ? fromDraft.container_qty : 0.00,
        adHocQty: fromDraft.ad_hoc_qty ? fromDraft.ad_hoc_qty : 0.00,
        totalContainerMeasure: fromDraft.total_container_measure ? fromDraft.total_container_measure : 0.00,
        process_status: fromDraft.process_status ? fromDraft.process_status : 'PLANNED',
        estimatedPackingDate: fromDraft.estimated_pkg_date,
        completionDate: fromDraft.completion_date,
        remarks: fromDraft.remarks,
      });
    });

    this.subs.sink = this.form.get('jobOrderNo').valueChanges.pipe(
      filter(() => this.jobOrderNoRestriction && !this.editMode), 
      debounceTime(300), 
      distinctUntilChanged(), 
      tap(joNum => this.onCheck(joNum))
    ).subscribe();

    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectIsProcessGeneratedForJO)
      .subscribe(isProcessGenerated => {
        if (isProcessGenerated) {
          this.form.controls['itemCode'].disable();
          this.form.controls['itemName'].disable();
          this.form.controls['uom'].disable();
          this.form.controls['branch'].disable();
          this.form.controls['location'].disable();
        }
    })
  }

  onCalculateTotalContainerMeasure(){
    if(this.form){
      const containerQty = parseFloat(this.form.controls['containerQty'].value) ?? 0.0
      const adHocQty = parseFloat(this.form.controls['adHocQty'].value) ?? 0.0

      const totalContainerMeasure = Number(containerQty)+ Number(adHocQty);
      this.form.patchValue({
        totalContainerMeasure : totalContainerMeasure,
      })
      this.form.controls['totalContainerMeasure'].setValue(totalContainerMeasure);
      this.updateMain.emit(this.form.value);
    }
  }

  onBranchSelected(branch: BranchContainerModel) {
    this.selectedBranch = branch.bl_fi_mst_branch.guid;
    this.form.patchValue({ 
      company: branch.bl_fi_mst_branch.comp_guid,
    });
    this.selectedCompany = branch.bl_fi_mst_branch.comp_guid;
    if (!this.form.value.location && branch.bl_fi_mst_branch_ext.find(x => x.param_code === "MAIN_LOCATION")) {
      this.form.controls['location'].setValue(branch.bl_fi_mst_branch_ext.find(x => x.param_code === "MAIN_LOCATION")?.value_string);
    }
    this.updateMain.emit(this.form.value);
  }

  onLocationSelected(location: LocationContainerModel) {
    this.form.patchValue({ location: location.bl_inv_mst_location.guid });
    this.updateMain.emit(this.form.value);
  }

  onCheck(joNum: string) {
    const dto = { job_order_no: joNum };
    const messagesToCheck = [
      'JOB ORDER NUMBER NEEDS TO HAVE A VALUE TO VERIFY',
      'JOB ORDER NUMBER NEEDS TO CONTAIN AT LEAST ONE ALPHABET',
      'JOB ORDER NUMBER CANNOT CONTAIN ANY SPECIAL CHARACTERS',
      'JOB ORDER NUMBER ALREADY EXISTS'
    ];
    this.subs.sink = this.mrpJobOrderHdrService.verifyJobOrderNumber(dto, this.apiVisa).subscribe(response => {
      this.messageList = response.data;
      if(messagesToCheck.some(message => this.messageList.includes(message))){
        this.form.controls['jobOrderNo'].setErrors({ exist: true });
        this.form.invalid;
      } else {
        this.updateMain.emit(this.form.value);
      }
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
