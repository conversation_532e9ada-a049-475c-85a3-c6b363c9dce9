<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div> <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button" (click)="onReturn()"> <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px"> </button> <span> Login Create </span> </div> <button mat-raised-button
      type="button" (click)="onSave()" [disabled]="!form.valid || !doesEmailExist" color={{isClicked}}>{{addSuccess}}</button>
  </div>
</mat-card-title>
<form [formGroup]="form" #formDirectives="ngForm">
  <mat-tab-group [dynamicHeight]="true">
    <mat-tab label="Main">
      <div fxLayout="column" class="view-col-forms">

        <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
          <mat-form-field appearance="outline">
            <mat-label>User email</mat-label>
            <input maxlength="255" matInput formControlName="user_email" required
              pattern="[a-zA-Z0-9.-_]{1,}@[a-zA-Z.-]{2,}[.]{1}[a-zA-Z]{2,}">
            <mat-hint *ngIf="form.controls['user_email'].value?.length === 255" class="text-danger font-14">
              Please insert no more than 255
            </mat-hint>
            <mat-error *ngIf="form.controls['user_email'].invalid">
              Please enter a valid <strong>email address</strong>
            </mat-error>
            <mat-hint *ngIf="haveError" style="color: red;">{{message}}</mat-hint>
            <mat-hint *ngIf="!haveError" style="color: green;">{{message}}</mat-hint>
            <!-- <mat-error *ngIf="haveError">{{message}}</mat-error> -->
          </mat-form-field>
        </div>
        <button *ngIf="verifyBtnAppear" mat-raised-button color="primary" type="button" (click)="checkUserExist()" style="margin:5px;">Verify Email</button>
        <button *ngIf="sendBtnAppear" mat-raised-button color="primary" type="button" (click)="sendInvite()" style="margin:5px;">Send Invite</button>
        <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
          <mat-form-field appearance="outline">
            <mat-label>Rank</mat-label>
            <mat-select placeholder="Payment Type" [formControl]="form.controls['rank']" required>
              <mat-option *ngFor="let item of rank" [value]="item.value">{{item.viewValue}}
              </mat-option>
            </mat-select>
            <mat-hint *ngIf="form.controls['rank'].hasError('required') && form.controls['rank'].touched"
              class="text-danger font-14">Please select rank </mat-hint>
          </mat-form-field>
        </div>
        <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
          <mat-form-field appearance="outline">
            <mat-label>Status</mat-label>
            <mat-select placeholder="Status" [formControl]="form.controls['status']" required>
              <mat-option *ngFor="let item of status" [value]="item.value">{{item.viewValue}}
              </mat-option>
            </mat-select>
            <mat-hint *ngIf="form.controls['status'].hasError('required') && form.controls['status'].touched"
              class="text-danger font-14">Please select status</mat-hint>
          </mat-form-field>
        </div>
      </div>

    </mat-tab>
  </mat-tab-group>
</form>