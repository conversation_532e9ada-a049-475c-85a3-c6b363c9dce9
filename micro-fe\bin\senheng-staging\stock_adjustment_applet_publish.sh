#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=senheng-staging --project=stock-adjustment-applet --output-hashing none --aot
node elements-build-scripts/akaun/stock-adjustment-applet-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://senheng-applets/bigledger/wavelet-erp/stock-adjustment-applet/staging s3://senheng-applets/bigledger/wavelet-erp/stock-adjustment-applet/staging/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile senheng-staging --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/stock-adjustment-applet/ s3://senheng-applets/bigledger/wavelet-erp/stock-adjustment-applet/staging --profile senheng-staging --acl public-read --recursive
