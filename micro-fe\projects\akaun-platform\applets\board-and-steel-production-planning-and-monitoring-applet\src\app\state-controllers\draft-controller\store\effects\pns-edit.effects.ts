import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { map, withLatestFrom } from 'rxjs/operators';
import { InternalJobOrderSelectors } from '../../../internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from '../../../internal-job-order-controller/store/states';
import { PNSEditActions } from '../actions';

@Injectable()
export class PNSEditEffects {

    constructor(
        private actions$: Actions,
        private readonly store: Store<InternalJobOrderStates>
    ) { }
}
