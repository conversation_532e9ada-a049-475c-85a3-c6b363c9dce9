import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { StoreModule } from '@ngrx/store';
import { InternalJobOrderModule } from '../components/internal-job-order-container/internal-job-order.module';
import { ProcessModule } from '../components/process-container/process.module';
import { ViewColumnFacade } from '../facades/view-column.facade';
import { reducers } from '../state-controllers/view-cache-controller/store/reducers';
import { viewCacheFeatureKey } from '../state-controllers/view-cache-controller/store/reducers/view-cache.reducers';
import { SalesOrderModule } from '../components/sales-order-container/sales-order.module';
import { salesOrderFeatureKey } from '../state-controllers/sales-order-controller/store/reducers/sales-order.reducers';
import { salesOrderReducers } from '../state-controllers/sales-order-controller/store/reducers';

@NgModule({
  imports: [
    CommonModule,
    InternalJobOrderModule,
    ProcessModule,
    SalesOrderModule,
    StoreModule.forFeature(viewCacheFeatureKey, reducers.viewCache),
    StoreModule.forFeature(salesOrderFeatureKey, salesOrderReducers.salesOrder)

  ],
  providers: [ViewColumnFacade]
})
export class ViewCacheModule {}
