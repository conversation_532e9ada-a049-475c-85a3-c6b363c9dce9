import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { AppletService, bl_fi_generic_doc_line_RowClass, TaxCodeCfgService } from 'blg-akaun-ts-lib';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { BinActions } from '../../../state-controllers/draft-controller/store/actions';
import { BinSelectors, HDRSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { AddLineItemItemDetailsComponent } from './internal-job-order-item-details/add-line-item-item-details.component';

interface LocalState {
  deactivateReturn: boolean;
  deactivateIssueLink: boolean;
  selectedIndex: number;
  itemDetailsSelectedIndex: number;
  serialNumberSelectedIndex: number;
}

@Component({
  selector: 'app-internal-job-order-add-line-item',
  templateUrl: './internal-job-order-add-line-item.component.html',
  styleUrls: ['./internal-job-order-add-line-item.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalJobOrderAddLineItemComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Add Line Item';
  protected index = 7;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  // readonly deactivateBatchNo$ = this.componentStore.select(state => state.deactivateBatchNo);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly itemDetailsSelectedIndex$ = this.componentStore.select(state => state.itemDetailsSelectedIndex);
  readonly serialNumberSelectedIndex$ = this.componentStore.select(state => state.serialNumberSelectedIndex);

  prevIndex: number;
  protected prevLocalState: any;
  // batch$ = this.store.select(InternalJobOrderSelectors.selectBatch);
  item$ = this.store.select(InternalJobOrderSelectors.selectItem);
  tax$ = this.taxService.get(AppConfig.apiVisa).pipe(map(d => ({
    sst: d.data.filter(s => s.bl_fi_cfg_tax_code.tax_gst_type),
    wht: d.data.filter(s => s.bl_fi_cfg_tax_code.tax_wht_type)
  })));
  dept$ = combineLatest(
    [
      this.draftStore.select(HDRSelectors.selectDimension),
      this.draftStore.select(HDRSelectors.selectProfitCenter),
      this.draftStore.select(HDRSelectors.selectProject),
      this.draftStore.select(HDRSelectors.selectSegment)
  ]).pipe(
    map(([a, b, c, d]) => ({guid_dimension: a, guid_profit_center: b, guid_project: c, guid_segment: d}))
  );
  line$ = this.store.select(InternalJobOrderSelectors.selectLineItem);
  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)]).pipe(
      map(([a, b]) => ({...a, ...b}))
    );
  bins$ = this.draftStore.select(BinSelectors.selectAll);

  targetIndex: number;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(AddLineItemItemDetailsComponent) itemDetails: AddLineItemItemDetailsComponent;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private taxService: TaxCodeCfgService,
    private readonly store: Store<InternalJobOrderStates>,
    private readonly draftStore: Store<DraftStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly componentStore: ComponentStore<LocalState>
  ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    // TODO: Optimize this
    this.subs.sink = this.viewColFacade.breadCrumbs$.subscribe(a => this.targetIndex = a[1].index);
  }

  disableAdd() {
    return this.itemDetails?.main.form.invalid;
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAdd() {
    const line = new bl_fi_generic_doc_line_RowClass();
    line.item_guid = this.itemDetails.main.form.value.itemGuid;
    line.item_code = this.itemDetails.main.form.value.itemCode;
    line.item_name = this.itemDetails.main.form.value.itemName;
    line.amount_discount = this.itemDetails.main.form.value.discountAmount;
    line.amount_net = this.itemDetails.main.form.value.netAmount;
    line.amount_std = this.itemDetails.main.form.value.stdAmount;
    line.amount_tax_gst = this.itemDetails.main.form.value.taxAmount;
    line.amount_tax_wht = this.itemDetails.main.form.value.whtAmount;
    line.amount_txn = this.itemDetails.main.form.value.txnAmount;
    line.quantity_base = this.itemDetails.main.form.value.quantity;
    line.tax_gst_code = this.itemDetails.main.form.value.sstCode;
    line.tax_wht_code = this.itemDetails.main.form.value.whtCode;
    line.guid_dimension = this.itemDetails.dept.form.value.dimension;
    line.guid_profit_center = this.itemDetails.dept.form.value.profitCenter;
    line.guid_project = this.itemDetails.dept.form.value.project;
    line.guid_segment = this.itemDetails.dept.form.value.segment;
    line.item_remarks = this.itemDetails.main.form.value.remarks;
    line.item_property_json = {...this.itemDetails.main.form.value};
    line.txn_type = 'PNS';
    line.quantity_signum = 0;
    line.amount_signum = 0;
    line.server_doc_type = 'INTERNAL_SALES_ORDER';
    line.client_doc_type = 'INTERNAL_SALES_ORDER';
    line.qty_open = this.itemDetails.main.form.value.quantity;
    // this.viewColFacade.addLineItem(line, this.targetIndex);
  }

  onIssueLink(e) {
    if (!this.localState.deactivateIssueLink) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateIssueLink: true,
        deactivateReturn: true
      });
      this.viewColFacade.onNextAndReset(this.index, 13);
    }
  }

  // onBin(bin) {
    // this.store.dispatch(InternalJobOrderActions.selectBin({bin}));
    // if (!this.localState.deactivateStockAvailability) {
    //   this.viewColFacade.updateInstance<LocalState>(this.index, {
    //     ...this.localState,
    //     deactivateStockAvailability: true,
    //     deactivateReturn: true
    //   });
    //   this.viewColFacade.onNextAndReset(this.index, 14);
    // }
  // }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
        itemDetailsSelectedIndex: this.itemDetails.matTab.selectedIndex,
      });
    }
    this.subs.unsubscribe();
  }

}

