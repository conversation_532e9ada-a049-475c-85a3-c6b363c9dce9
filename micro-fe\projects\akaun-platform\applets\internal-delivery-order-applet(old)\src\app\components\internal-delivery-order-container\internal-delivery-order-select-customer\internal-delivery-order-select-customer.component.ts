import { Component, ChangeDetectionStrategy, ViewChild, Input } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { EntityContainerModel, Pagination, CustomerService, SubQueryService, SupplierService, MerchantService, EmployeeService, ConditionalCriterion, ApiResponseModel, CustomerHeaderService, EntityService, bl_fi_mst_entity_line_RowClass } from 'blg-akaun-ts-lib';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SubSink } from 'subsink2';
import { customerSearchModel } from '../../../models/advanced-search-models/customer.model';
import { Store } from '@ngrx/store';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import * as moment from 'moment';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { EntityActions } from '../../../state-controllers/customer-controller/store/actions';
import { EntityStates } from '../../../state-controllers/customer-controller/store/states';
import { EntitySelectors } from '../../../state-controllers/customer-controller/store/selectors';
import { Observable, of, Subject } from 'rxjs';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { filter, map, switchMap, tap } from 'rxjs/operators';
import { FormControl } from '@angular/forms';
// import { SupplierActions } from '../../../state-controllers/supplier-controller/actions';


export class EntityView {
  id;
  guid;
  name;
  code;
  type;
  glCode;
  creditTerms;
  creditLimit;
  currency;
  description;
  status;
  entity_id;
  tax;
  updated_date;
  modifiedBy;
  created_date;
  createdBy;
}
interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  deactivateReturn: boolean;
  rowIndexListing: number;
}

@Component({
  selector: 'app-internal-delivery-order-select-customer',
  templateUrl: './internal-delivery-order-select-customer.component.html',
  styleUrls: ['./internal-delivery-order-select-customer.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalDeliveryOrderSelectCustomerComponent extends ViewColumnComponent {

  selectEntity$;
  protected subs = new SubSink();

  protected compName = 'Select Customer';
  protected readonly index = 6;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly rowIndexListing$ = this.componentStore.select(state => state.rowIndexListing);
  readonly checkAgGrid$ = this.entityStore.select(EntitySelectors.selectAgGrid);
  prevIndex: number;
  protected prevLocalState: any;
  selectedRowIndex: number;
  snapshot: string = null;
  totalCount: number;
  totalRecords$: Subject<number> = new Subject<number>();
  criteriaList: ConditionalCriterion[];
  emptyGrid: boolean = false;

  searchModel = customerSearchModel;
  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;
  toggleMode: boolean = false;
  // columnsDefs = [
  //   {
  //     headerName: 'Entity Code', cellStyle: () => ({ 'text-align': 'left' }),
  //     valueGetter: (params) => {
  //       let entity_code = "";
  //       if(params.data.bl_fi_mst_entity_hdr.is_customer)
  //         entity_code = entity_code + "Customer: " + params.data.bl_fi_mst_entity_hdr.customer_code + " ";
  //       if(params.data.bl_fi_mst_entity_hdr.is_supplier)
  //         entity_code = entity_code + "Supplier: " + params.data.bl_fi_mst_entity_hdr.supplier_code + " ";
  //       if(params.data.bl_fi_mst_entity_hdr.is_merchant)
  //         entity_code = entity_code + "Merchant: " + params.data.bl_fi_mst_entity_hdr.merchant_code + " ";
  //       if(params.data.bl_fi_mst_entity_hdr.is_employee)
  //         entity_code = entity_code + "Employee: " + params.data.bl_fi_mst_entity_hdr.employee_code + " ";
  //       return entity_code;
  //     }
  //   },
  //   { headerName: 'Name', field: 'bl_fi_mst_entity_hdr.name', cellStyle: () => ({ 'text-align': 'left' }) },
  //   { headerName: 'Txn Type', field: 'bl_fi_mst_entity_hdr.txn_type', cellStyle: () => ({ 'text-align': 'left' }) },
  //   // {
  //   //   headerName: 'Currency', field: 'bl_fi_mst_entity_ext', valueFormatter: params =>
  //   //     params.value.find(x => x.param_code === 'CURRENCY')?.value_json?.currency
  //   // },
  //   {
  //     headerName: 'Creation Date', field: 'bl_fi_mst_entity_hdr.created_date',
  //     valueFormatter: (params) => params.value ? moment(params.value).format('YYYY-MM-DD') : '',
  //     cellStyle: () => ({ 'text-align': 'left' })
  //   },
  //   {
  //     headerName: 'Modified Date', field: 'bl_fi_mst_entity_hdr.updated_date',
  //     valueFormatter: (params) => params.value ? moment(params.value).format('YYYY-MM-DD') : '',
  //     cellStyle: () => ({ 'text-align': 'left' })
  //   },
  //   { headerName: 'Status', field: 'bl_fi_mst_entity_hdr.status', cellStyle: () => ({ 'text-align': 'left' }) },
  // ];

  columnsDefs = [
    {
      headerName: 'Code', cellStyle: () => ({ 'text-align': 'left' }), cellRenderer: 'agGroupCellRenderer',
      valueGetter: (params) => {
        let entity_code = "";
        if(params.data.bl_fi_mst_entity_hdr.is_customer)
          entity_code = entity_code + "Customer: " + params.data.bl_fi_mst_entity_hdr.customer_code + " ";
        if(params.data.bl_fi_mst_entity_hdr.is_supplier)
          entity_code = entity_code + "Supplier: " + params.data.bl_fi_mst_entity_hdr.supplier_code + " ";
        if(params.data.bl_fi_mst_entity_hdr.is_merchant)
          entity_code = entity_code + "Merchant: " + params.data.bl_fi_mst_entity_hdr.merchant_code + " ";
        if(params.data.bl_fi_mst_entity_hdr.is_employee)
          entity_code = entity_code + "Employee: " + params.data.bl_fi_mst_entity_hdr.employee_code + " ";
        return entity_code;
      }
    },
    { headerName: 'Type', field: 'bl_fi_mst_entity_hdr.txn_type', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Name', field: 'bl_fi_mst_entity_hdr.name', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Contact Number', field: 'bl_fi_mst_entity_hdr.phone', type: 'numericColumn' },
  ];

  detailCellRendererParams = {
    detailGridOptions: {
      columnDefs: [
        { headerName: 'Contact Name', field: 'contact.name' },
        { headerName: 'Position', field: 'contact.contact_json.position' },
        { headerName: 'Customer ID', field: 'contact.id_no' },
        { headerName: 'Mobile No', field: 'contact.contact_json.mobile_no' },
      ],
      defaultColDef: {
        filter: 'agTextColumnFilter',
        floatingFilter: true,
        floatingFilterComponentParams: { suppressFilterButton: true },
        flex: 1,
        sortable: true,
        resizable: true
      },
      rowData: [],
      onRowClicked: (params) => this.onRowClicked(params.data)
    },
    getDetailRowData: params => {
      // supply data to the detail grid
      const details = [];
      params.data.bl_fi_mst_entity_line.forEach(l => {
        details.push({ entity: { ...params.data }, contact: { ...l } });
      });
      params.successCallback(details);
    },
  };

  SQLGuids: string[] = null;
  pagination = new Pagination();

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;
  entity: string;
  // entity_code: string;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected entityService: EntityService,
    protected store: Store<InternalDeliveryOrderStates>,
    private entityStore: Store<EntityStates>,
    private sqlService: SubQueryService,
    protected readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
      if (a.rowIndexListing === null) {
        this.gridApi?.deselectAll();
      }
    });

    this.entityStore.select(EntitySelectors.selectToggleMode).subscribe((data) => {
           console.log("toggle er vejal",data);
            this.toggleMode = data;
    });
  }

  // onNext() {
  //   this.viewColFacade.gotoFourOhFour();
  //   // this.viewColFacade.updateInstance(this.index, {
  //   //   ...this.localState, deactivateAdd: true, deactivateList: false});
  //   // this.viewColFacade.onNextAndReset(this.index, 3);
  // }

  // onGridReady(params) {
  //   const apiVisa = AppConfig.apiVisa;
  //   this.gridApi = params.api;
  //   this.gridApi.closeToolPanel();
  //   const datasource = {
  //     getRows: grid => {
  //       // this.store.dispatch(InternalPackingOrderActions.loadPackingOrdersInit({request: grid.request}));
  //       let pagination = new Pagination(grid.request.startRow, grid.request.endRow, [
  //         { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
  //       ],);

  //       if (this.SQLGuids) {
  //         pagination.conditionalCriteria.push({
  //           columnName: 'hdr_guids',
  //           operator: '=',
  //           value: this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString()
  //         });
  //         pagination.snapshot = this.snapshot;
  //       }

  //       this.subs.sink = this.service.getByCriteria(pagination, apiVisa).subscribe(resolved => {
  //         if (resolved.data.length > 0) {
  //           this.snapshot = resolved.data[resolved.data.length - 1].bl_fi_mst_entity_hdr.guid.toString();
  //         }
  //         // resolved.data[0].bl_fi_mst_entity_hdr
  //         // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderSuccess({totalRecords: resolved.totalRecords}));
  //         grid.successCallback(resolved.data, resolved.totalRecords);
  //       }, err => {
  //         // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderFailed({error: err.message}));
  //         grid.failCallback();
  //       });
  //     }
  //   };
  //   this.gridApi.setServerSideDatasource(datasource);
  //   this.entityStore.select(EntitySelectors.selectAgGrid).subscribe( a => {
  //     if (a) {
  //       console.log("enter a?")
  //       this.gridApi.refreshServerSideStore();
  //       this.store.dispatch(EntityActions.resetAgGrid());
  //     }
  //   });
  // }

  refreshGrid() {
    this.emptyGrid ? this.clear() : this.retrieveData(this.criteriaList);
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.gridApi.sizeColumnsToFit();
    // this.getTotalRecordCount();
    this.retrieveData();
    this.checkAgGrid$.subscribe(status => {
      if (status) {
        console.log("AG Grid Status",status);
        // this.getTotalRecordCount();
        this.retrieveData();
        this.store.dispatch(EntityActions.updateAgGridDone({ status: false }));
      }
    });
  }

  retrieveData(criteria?, apiVisa = AppConfig.apiVisa) {
    criteria = criteria ? criteria : [];
    this.snapshot = null;
    this.emptyGrid = false;
    const datasource = {
      getRows: this.getRowsFactory(criteria, apiVisa)
    };
    this.gridApi.setServerSideDatasource(datasource);
    // this.paginationComponent.firstPage();

  }

  getRowsFactory(criteria, apiVisa) {
    return grid => {
      // const filter = this.pageFiltering(grid.request.filterModel);
      // const sortOn = this.pageSorting(grid.request.sortModel);
      const sortModel = grid.request.sortModel;
      const sortCriteria = [];
      if (sortModel.length > 0) {
        sortModel.forEach(element => {
          sortCriteria.push({ columnName: 'orderBy', value: element.colId })
        });
        sortCriteria.push({ columnName: 'order', value: sortModel[0].sort.toUpperCase() })
      }
      // this.store.dispatch(CustomerActions.loadCustomerExtsInit({ request: grid.request }));
      this.entityService.getByCriteriaSnapshot(
        new Pagination(
          0,
          grid.request.endRow - grid.request.startRow,
          criteria,
          sortCriteria,
          this.snapshot),
        apiVisa)
        .subscribe(a => {
          console.log("data structure returned from snapshot:", a)
          if (a.data.length > 0) {
            this.snapshot = a.data[a.data.length - 1].bl_fi_mst_entity_hdr.guid.toString();
          }
          // Calculate totalRecords if end reached.
          const start = grid.request.startRow;
          const end = grid.request.endRow;
          const totalRecords = a.data.length < (end - start) ? start + a.data.length : null;

          if (!this.totalCount && totalRecords) {
            this.totalCount = totalRecords;
            this.setTotalRecordCount(totalRecords);
          }

          // const data = sortOn(a.data.map(containerToViewModel).filter(o => filter.by(o)));
          // const totalRecords: number = filter.isFiltering ? a.totalRecords : data.length;
          if (this.paginationComp.currentPage > this.paginationComp.totalPage.value) { this.paginationComp.firstPage() }
          // const data = a.data.map(containerToViewModel);
          grid.successCallback(a.data, totalRecords);
          this.gridApi.forEachNode(node => {
            if (node.rowIndex == this.localState.rowIndexListing) {
              node.setSelected(true);
            }
          });
          // this.store.dispatch(CustomerActions.loadCustomerExtSuccess({ totalRecords: totalRecords }));
        }, err => {
          grid.failCallback();
          // this.store.dispatch(CustomerActions.loadCustomerExtFailed({ error: err.message }));
        });
    };
  }

  setTotalRecordCount(totalCount: number) {
    this.totalRecords$.next(totalCount);
  }

  clear() {
    const dataSource = {
      getRows(params: any) {
        params.successCallback([], 0);
      }
    };
    this.gridApi.setServerSideDatasource(dataSource);
  }

  searchQuery(query: string, table: string) {
    const query$ = this.sqlService
      .post({ 'subquery': query, 'table': 'bl_fi_mst_entity_hdr' }, AppConfig.apiVisa)
      .pipe(
        switchMap(resp => of(resp))
      );
    query$.pipe(
      filter((resp: ApiResponseModel<any>) => resp.data.length > 0),
      tap(resp => {
        this.setTotalRecordCount(resp.data.length);
      })
    )
      .subscribe(resp => {
        const criteria = [
          { columnName: 'hdr_guids', operator: '=', value: resp.data.join(',') }
        ];
        this.criteriaList = criteria;
        this.retrieveData(criteria);
        this.setTotalRecordCount(resp.data.length);
        this.emptyGrid = false;
      });
    query$.pipe(
      filter((resp: ApiResponseModel<any>) => resp.data.length === 0)
    )
      .subscribe(resp => {
        this.criteriaList = [];
        this.emptyGrid = true;
        this.setTotalRecordCount(0);
        this.clear();
      }
      );
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      // this.searchQuery(e.queryString);
      this.searchQuery(`${e.queryString} AND hdr.status ILIKE '%ACTIVE%' `, e.table);
    } else {
      this.criteriaList = [];
      this.emptyGrid = false;
      this.retrieveData();
      this.setTotalRecordCount(this.totalCount);
    }
  }


  // onSearch(e: SearchQueryModel) {
  //   if (!e.isEmpty) {
  //     const sql = {
  //       subquery: e.queryString,
  //       table: e.table
  //     };
  //     console.log("sql", sql);
  //     this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe(
  //       {
  //         next: resolve => {
  //           this.SQLGuids = resolve.data;
  //           this.paginationComp.firstPage();
  //           this.gridApi.refreshServerSideStore();
  //         }
  //       }
  //     );
  //   } else {
  //     this.SQLGuids = null;
  //     this.paginationComp.firstPage();
  //     this.gridApi.refreshServerSideStore();
  //   }
  // }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClickedNoContact(entity: EntityContainerModel) {
    console.log(entity);

    if (this.toggleMode === false) {

      if (entity) {

        let entity_code = "";
        if(entity.bl_fi_mst_entity_hdr.is_customer)
          entity_code = entity_code + "Customer: " + entity.bl_fi_mst_entity_hdr.customer_code + " ";
        if(entity.bl_fi_mst_entity_hdr.is_supplier)
          entity_code = entity_code + "Supplier: " + entity.bl_fi_mst_entity_hdr.supplier_code + " ";
        if(entity.bl_fi_mst_entity_hdr.is_merchant)
          entity_code = entity_code + "Merchant: " + entity.bl_fi_mst_entity_hdr.merchant_code + " ";
        if(entity.bl_fi_mst_entity_hdr.is_employee)
          entity_code = entity_code + "Employee: " + entity.bl_fi_mst_entity_hdr.employee_code + " ";

          // create mode
        if (this.prevIndex === 3) {
          this.store.dispatch(InternalDeliveryOrderActions.selectEntity({ entity:{entity,contact: null}}));
        }
        // edit mode
        else if (this.prevIndex === 1) {
          this.store.dispatch(InternalDeliveryOrderActions.selectEntityEdit({ entity:{entity,contact: null}}));
        }

        // this.store.dispatch(InternalPackingOrderActions.selectPackingOrderEntity({entity}));
        // if (!this.localState.deactivateList) {
        //   this.viewColFacade.updateInstance(this.index, {
        //     ...this.localState, deactivateAdd: false, deactivateList: true});
        //   this.viewColFacade.onNextAndReset(this.index, 1);
        // }

        this.onReturn();
      }

    } else {
      console.log("Send to the edit customer", this.entity);
      // if(this.entity === "customer"){

        this.store.dispatch(EntityActions.selectEntityExtGuid({ guid: entity.bl_fi_mst_entity_hdr.guid.toString() }));
        this.store.dispatch(EntityActions.resetAddress())
        this.store.dispatch(EntityActions.updateAgGridDone({ status: false }));
      // }
      // else if(this.entity === "supplier"){
      //   this.store.dispatch(SupplierActions.selectSupplierExtGuid({ guid: entity.bl_fi_mst_entity_hdr.guid.toString() }));
      //   this.store.dispatch(SupplierActions.resetAddress())
      // }
      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance(this.index, { ...this.localState, deactivateList: true, rowIndexListing: this.selectedRowIndex });
        this.viewColFacade.onNextAndReset(this.index, 16);
      }
    }

  }

  onRowClicked(entity: { entity: EntityContainerModel, contact: bl_fi_mst_entity_line_RowClass }) {
    if (entity) {
          // create mode
          if (this.prevIndex === 3) {
            this.store.dispatch(InternalDeliveryOrderActions.selectEntity({ entity }));
          }
          // edit mode
          else if (this.prevIndex === 1) {
            this.store.dispatch(InternalDeliveryOrderActions.selectEntityEdit({ entity }));
          }
       }
  }

  toggle(event: MatSlideToggleChange) {
    if (event.checked) {
      this.toggleMode = true;
      this.entityStore.dispatch(EntityActions.selectToggleMode({SelectedToggleMode: true}));
    } else {
      this.toggleMode = false;
    }
  }

  onNext() {
    this.viewColFacade.startDraft();
    this.viewColFacade.updateInstance(this.index, { ...this.localState, deactivateAdd: true });
    this.viewColFacade.onNextAndReset(this.index, 15);
  }



  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false,
      deactivateEntity: false,
      deactivateDeliveryAgent: false,
      deactivateShippingInfo: false,
      deactivateBillingInfo: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
