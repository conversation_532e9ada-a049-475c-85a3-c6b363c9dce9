import { ChangeDetectionStrategy, Component, Input, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { MatTabGroup } from '@angular/material/tabs';
import { MainDetailsComponent } from './main-details/main-details.component';
import { DeliveryInstructionsComponent } from './delivery-instructions/delivery-instructions.component';

@Component({
  selector: 'app-line-item-details',
  templateUrl: './item-details.component.html',
  styleUrls: ['./item-details.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ItemDetailsComponent extends ViewColumnComponent {

  @Input() selectedIndex$: any;
  @Input() editMode: boolean;
  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(MainDetailsComponent) main: MainDetailsComponent;
  @ViewChild(DeliveryInstructionsComponent) delivery: DeliveryInstructionsComponent

}
