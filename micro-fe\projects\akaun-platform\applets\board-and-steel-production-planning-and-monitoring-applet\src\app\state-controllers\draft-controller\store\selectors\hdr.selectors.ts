import { createSelector } from "@ngrx/store";
import { selectDraftState } from "../..";

export const selectHdr = createSelector(
  selectDraftState,
  (s1) => s1.hdr
)

export const selectSegment = (state) => state.draft.hdr.guid_segment
export const selectProfitCenter = (state) => state.draft.hdr.guid_profit_center
export const selectProject = (state) => state.draft.hdr.guid_project
export const selectDimension = (state) => state.draft.hdr.guid_dimension