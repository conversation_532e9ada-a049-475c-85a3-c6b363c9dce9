import { createAction, props } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass, bl_fi_mst_entity_line_RowClass, bl_inv_batch_hdr_RowClass, bl_inv_bin_hdr_RowClass, EntityContainerModel, FinancialItemContainerModel, GenericDocARAPContainerModel, GenericDocContainerModel, JobContainerModel, JsonDatatypeInterface, Pagination, PickPackQueueContainerModel, PickPackQueueSearchDto, PricingSchemeLinkContainerModel } from 'blg-akaun-ts-lib';



export const loadPickPackQueuesInit = createAction('[Pick Pack Queue] Load Init', props<{ pagination: Pagination }>());
export const loadPickPackQueueSuccess = createAction('[Pick Pack Queue] Load Success', props<{ PickPackQueues: any [] }>());
export const loadPickPackQueueFailed = createAction('[Pick Pack Queue] Load Failed', props<{ error: string }>());

export const searchPickPackQueuesInit = createAction('[Pick Pack Queue] Search Init', props<{ searchDto: PickPackQueueSearchDto }>());

export const createJobFromDeliveryOrderInit = createAction('[Pick Pack Queue] Create Job From Delivery Order Init', props<{ jobContainer:JobContainerModel[] }>());
export const createJobFromDeliveryOrderSuccess = createAction('[Pick Pack Queue] Create Job From Delivery Order Init');
export const createJobFromDeliveryOrderFailed = createAction('[Pick Pack Queue] Create Job From Delivery Order Init', props<{ error: string }>());    

export const selectEntityInit = createAction('[Pick Pack Queue] Select Entity Item Init', props<{ entity: GenericDocContainerModel }>());
export const selectEntityCustomerSuccess = createAction('[Pick Pack Queue] Select Entity Customer Success', props<{ entity: EntityContainerModel }>());
export const selectEntityCustomerFailed = createAction('[Pick Pack Queue] Select Entity Customer Failed', props<{ error: string }>());

export const selectCustomer = createAction('[Pick Pack Queue] Select Customer', props<{ entity: { entity: EntityContainerModel, contact: bl_fi_mst_entity_line_RowClass } }>());
export const selectShippingAddress = createAction('[Pick Pack Queue] Select Shipping Address', props<{ shipping_address: JsonDatatypeInterface }>());
export const selectBillingAddress = createAction('[Pick Pack Queue] Select Billing Address', props<{ billing_address: JsonDatatypeInterface }>());
export const selectContraDoc = createAction('[Pick Pack Queue] Select Contra Doc', props<{ entity: GenericDocContainerModel }>());
export const selectContraLink = createAction('[Pick Pack Queue] Select Contra Link', props<{ link: GenericDocARAPContainerModel }>());
export const selectItem = createAction('[Pick Pack Queue] Select Item Init', props<{ entity: FinancialItemContainerModel }>());


export const selectMember = createAction('[Pick Pack Queue] Select Member', props<{ guid: string, cardNo: string, name: string }>());
export const selectMemberEdit = createAction('[Pick Pack Queue] Select Member Edit', props<{ guid: string, cardNo: string, name: string }>());

export const selectLineItemInit = createAction('[Pick Pack Queue] Select Line Item Init', props<{ line: bl_fi_generic_doc_line_RowClass }>());
export const selectLineItemSuccess = createAction('[Pick Pack Queue] Select Line Item Success', props<{ entity: FinancialItemContainerModel }>());
export const selectLineItemFailed = createAction('[Pick Pack Queue] Select Line Item Failed', props<{ error: string }>());

export const createPickPackQueuesInit = createAction('[Pick Pack Queue] Create Init');
export const createPickPackQueueSuccess = createAction('[Pick Pack Queue] Create Success', props<{ hdrGuid: string }>());
export const createPickPackQueueFailed = createAction('[Pick Pack Queue] Create Failed', props<{ error: string }>());

export const deletePickPackQueuesInit = createAction('[Pick Pack Queue] Delete Init');
export const deletePickPackQueueSuccess = createAction('[Pick Pack Queue] Delete Success');
export const deletePickPackQueueFailed = createAction('[Pick Pack Queue] Delete Failed', props<{ error: string }>());

export const editPickPackQueuesInit = createAction('[Pick Pack Queue] Edit Init');
export const editPickPackQueueSuccess = createAction('[Pick Pack Queue] Edit Success', props<{ hdrGuid: string }>());
export const editPickPackQueueFailed = createAction('[Pick Pack Queue] Edit Failed', props<{ error: string }>());

export const selectCustomerEdit = createAction('[Pick Pack Queue] Select Customer Edit', props<{ entity: { entity: EntityContainerModel, contact: bl_fi_mst_entity_line_RowClass } }>());
export const selectShippingAddressEdit = createAction('[Pick Pack Queue] Select Shipping Address Edit', props<{ shipping_address: JsonDatatypeInterface }>());
export const selectBillingAddressEdit = createAction('[Pick Pack Queue] Select Billing Address Edit', props<{ billing_address: JsonDatatypeInterface }>());

export const resetAgGrid = createAction('[Pick Pack Queue] Reset Ag Grid Update');

export const printJasperPdfInit = createAction('[Pick Pack Queue] Print Jasper Pdf Init');
export const printJasperPdfSuccess = createAction('[Pick Pack Queue] Print Jasper Pdf Success');
export const printJasperPdfFailed = createAction('[Pick Pack Queue] Print Jasper Pdf Failed');

export const convertSalesSOInit = createAction('[Pick Pack Queue] Convert Init');
export const convertSalesSOSuccess = createAction('[Pick Pack Queue] Convert Success');
export const convertSalesSOFailed = createAction('[Pick Pack Queue] Convert Failed');

export const resetPickPackQueue = createAction('[Pick Pack Queue] Reset');
export const resetPickPackQueueEdit = createAction('[Pick Pack Queue] Reset Edit');

export const addContraInit = createAction('[Pick Pack Queue] Add Contra Init', props<{ contra: GenericDocARAPContainerModel }>());
export const addContraSuccess = createAction('[Pick Pack Queue] Add Contra Success');
export const addContraFailed = createAction('[Pick Pack Queue] Add Contra Failed', props<{ error: string }>());

export const deleteContraInit = createAction('[Pick Pack Queue] Delete Contra Init');
export const deleteContraSuccess = createAction('[Pick Pack Queue] Delete Contra Success');
export const deleteContraFailed = createAction('[Pick Pack Queue] Delete Contra Failed', props<{ error: string }>());

export const selectDetailsInit = createAction('[Pick Pack Queue] Select Details Init', props<{ entity: any }>());
export const selectDetailsReset = createAction('[Pick Pack Queue] Select Details Reset');

export const selectPricingSchemeLinkWhenAdd = createAction('[Pick Pack Queue] Select Pricing Scheme Link When Adding New Item', props<{ item: any }>());
export const selectPricingSchemeLink = createAction('[Pick Pack Queue] Select Pricing Scheme Link', props<{ item: any }>());
export const selectPricingSchemeLinkSuccess = createAction('[Pick Pack Queue] Select Pricing Link Scheme Success', props<{ pricing: PricingSchemeLinkContainerModel[] }>());
export const selectPricingSchemeLinkFailed = createAction('[Pick Pack Queue] Select Pricing Link Scheme Failed', props<{ error: string }>());

export const selectPricingScheme = createAction('[Pick Pack Queue] Select Pricing Scheme', props<{ pricingScheme: any }>());
export const addPricingSchemeLinkInit = createAction('[Pick Pack Queue] Add Pricing Scheme Link Init', props<{ link: PricingSchemeLinkContainerModel }>());
export const addPricingSchemeLinkSuccess = createAction('[Pick Pack Queue] Add Pricing Scheme Link Success');
export const addPricingSchemeLinkFailed = createAction('[Pick Pack Queue] Add Pricing Scheme Link Failed', props<{ error: string }>());
export const editPricingSchemeLinkInit = createAction('[Pick Pack Queue] Edit Pricing Scheme Link Init', props<{ link: PricingSchemeLinkContainerModel }>());
export const editPricingSchemeLinkSuccess = createAction('[Pick Pack Queue] Edit Pricing Scheme Link Success');
export const editPricingSchemeLinkFailed = createAction('[Pick Pack Queue] Edit Pricing Scheme Link Failed', props<{ error: string }>());

// For Serial, Bin, batch
export const getInvItem = createAction('[Pick Pack Queue] Get Inventory Item', props<{ entity: FinancialItemContainerModel }>());
export const selectInvItem = createAction('[Pick Pack Queue] Select Inventory Item', props<{ invItem }>());
export const noInvItemFound = createAction('[Pick Pack Queue] No Inventory Item Found');
export const selectSerial = createAction('[Pick Pack Queue] Select Serial Number', props<{ serial }>());
export const selectBatch = createAction('[Pick Pack Queue] Select Batch Number', props<{ batch: bl_inv_batch_hdr_RowClass }>());
export const selectBin = createAction('[Pick Pack Queue] Select Bin Number', props<{ bin: bl_inv_bin_hdr_RowClass }>());
export const setPickPackQueueInLineItem = createAction('[Pick Pack Queue] Set Pick Pack Queue', props<{ entity: GenericDocContainerModel }>())

export const printGetDocumentInit = createAction('[Pick Pack Queue] Print Get Document Init', props<{ branchGuid: string, documentType: string, orderItemIds: string }>());
export const printGetDocumentSuccess = createAction('[Pick Pack Queue] Print Get Document Success');
export const printGetDocumentFailed = createAction('[Pick Pack Queue] Print Get Document Failed');

export const printGetDocumentShopeeInit = createAction('[Pick Pack Queue] Print Get Document Shopee Init', props<{ branchGuid: string, order_sn: string , package_number: string, docType: string }>());
export const printGetDocumentShopeeSuccess = createAction('[Pick Pack Queue] Print Get Document Shopee Success');
export const printGetDocumentShopeeFailed = createAction('[Pick Pack Queue] Print Get Document Shopee Failed');

export const updatePostingStatus = createAction('[Pick Pack Queue] Update Posting Status', props<{ status: any, doc: GenericDocContainerModel }>());
export const updatePostingStatusSuccess = createAction('[Pick Pack Queue] Update Posting Status Success', props<{ doc: GenericDocContainerModel }>());
export const updatePostingStatusFailed = createAction('[Pick Pack Queue] Update Posting Status Failed', props<{ error: string }>());

export const setEditMode = createAction('[Pick Pack Queue] Set Edit Mode', props<{ editMode: boolean }>());

export const updateKnockoffListingConfig = createAction('[Internal Pick Pack Queue] Update Knockoff Listing Config', props<{ settings: any }>());

export const bulkUpdateToPackedLZDInit = createAction('[Pick Pack Queue] Bulk Update ToPacked LZD Init', props<{ pagination: Pagination, branchGuid: string, orderid: string }>());
export const bulkUpdateToPackedLZDSuccess = createAction('[Pick Pack Queue] Bulk Update ToPacked LZD Success');
export const bulkUpdateToPackedLZDFailed = createAction('[Pick Pack Queue] Bulk Update ToPacked LZD Failed', props<{ error: string }>());

export const bulkUpdateToShipLZDInit = createAction('[Pick Pack Queue] Bulk Update ToShip LZD Init', props<{ pagination: Pagination, branchGuid: string, orderid: string }>());
export const bulkUpdateToShipLZDSuccess = createAction('[Pick Pack Queue] Bulk Update ToShip LZD Success');
export const bulkUpdateToShipLZDFailed = createAction('[Pick Pack Queue] Bulk Update ToShip LZD Failed', props<{ error: string }>());

export const bulkUpdateToShipSHPInit = createAction('[Pick Pack Queue] Bulk Update ToShip SHP Init', props<{ pagination: Pagination, branchGuid: string, orderid: string }>());

export const editGenLineItemInit = createAction('[Line Item] Edit Generic Doc Line Item Init', props<{ genDoc: GenericDocContainerModel }>());
export const editGenLineItemSuccess = createAction('[Line Item] Edit Generic Doc Line Item Success');
export const editGenLineItemFailed = createAction('[Line Item] Edit Generic Doc Line Item Failed', props<{ error: string }>());

export const selectLineItem = createAction('[Pick Pack Queue] Select Line Item', props<{ lineItem: bl_fi_generic_doc_line_RowClass }>());
export const selectOrder = createAction('[Pick Pack Queue] Select Order Line Item Success', props<{ genDoc: GenericDocContainerModel }>());

export const selectShopeeGetShipParam = createAction('[Pick Pack Queue] Select Shopee Get Ship Param', props<{ shipParam: any }>());
export const createPickPackQueueGenDocLinkSuccess = createAction('[Pick Pack Queue] Create Gen Doc Link Success');
export const createPickPackQueueGenDocLinkFailed = createAction('[Pick Pack Queue] Create Gen Doc Link Failed', props<{error: string}>());
export const editPickPackQueueGenDocLinkSuccess = createAction('[Pick Pack Queue] Edit Gen Doc Link Success');
export const editPickPackQueueGenDocLinkFailed = createAction('[Pick Pack Queue] Edit Gen Doc Link Failed', props<{error: string}>());
export const selectCompanyGuid = createAction('[Delivery Order] Select Company Guid', props<{ compGuid: string }>());

export const updateToPackedLZDInit = createAction('[Pick Pack Queue] Update ToPacked LZD Init', props<{ pagination: Pagination, branchGuid: string, orderid: string }>());
export const updateToPackedLZDSuccess = createAction('[Pick Pack Queue] Update ToPacked LZD Success');
export const updateToPackedLZDFailed = createAction('[Pick Pack Queue] Update ToPacked LZD Failed', props<{ error: string }>());

export const uploadLZSPStatus = createAction('[Pick Pack Queue] Upload Lazada/Shopee Status Success');

export const selectGUID = createAction('[Pick Pack Queue] Select GUID', props<{ guid: string }>());
export const storeMembershipGuid = createAction('[Pick Pack Queue] Store Membership Guid', props<{ guid: string }>());
export const selectPricingSchemeGUID = createAction('[Pick Pack Queue] Select Pricing Scheme GUID', props<{ guid: string }>());

