import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const processTemplateSearchModel: SearchModel = {
  label: {
    processType: 'Process Type',
    machineCode: 'Machine Code',
    itemCode: 'Item Code',
    status: 'Status'
  },
  dataType: {
    processType: 'string',
    machineCode: 'string',
    itemCode: 'string',
    status: ['select',['ACTIVE','CLOSED']]
  },
  form: new FormGroup({
    processType: new FormControl(),
    machineCode: new FormControl(),
    itemCode: new FormControl(),
    status: new FormControl()
  }),

  joins: [
    // { type: 'INNER JOIN', table: 'bl_mrp_prodsys_hdr', alias: 'process', onCondition: 'hdr.process_guid = process.guid', joinOnBasic: true },
    { type: 'INNER JOIN', table: 'bl_mrp_prodsys_hdr', alias: 'machine', onCondition: 'hdr.machine_guid = machine.guid', joinOnBasic: true },
    { type: 'INNER JOIN', table: 'bl_inv_mst_item_hdr', alias: 'item', onCondition: 'hdr.output_inv_item_hdr_guid = item.guid', joinOnBasic: true }
  ],

  query: (query) => 
    `(hdr.code ILIKE '%${query}%' OR machine.code ILIKE '%${query}%' OR item.code ILIKE '%${query}%') 
     AND hdr.status = 'ACTIVE'`,

  table: 'bl_mrp_process_template_hdr',

    queryCallbacks: {
      processType: processType => processType ? `UPPER(process.name) ILIKE UPPER('%${processType}%')` : '',
      machineCode: machineCode => machineCode ? `UPPER(machine.code) ILIKE UPPER('%${machineCode}%')` : '',
      itemCode: itemCode => itemCode ? `UPPER(item.code) ILIKE UPPER('%${itemCode}%')` : '',
      status: status => status ? `hdr.status='${status}'` : '',
        // creationDate: creationDate => {
        //     if (creationDate.from || creationDate.to) {
        //         // assign modifiedDate.from to itself or modifiedDate.to if null
        //         const from = creationDate.from ? creationDate.from : creationDate.to;
        //         // assign creationDate.to to itself or creationDate.from if null
        //         const to = creationDate.to ? creationDate.to : creationDate.from;
        //         return `hdr.created_date >= '${from.format('YYYY-MM-DD')}' AND hdr.created_date <= '${to.format('YYYY-MM-DD')}'`
        //     }
        //     return ''
        // },
    }
};

