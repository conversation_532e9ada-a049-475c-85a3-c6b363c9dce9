import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators, FormBuilder } from '@angular/forms';
import { ApiVisa, AssetsService, bl_fi_mst_entity_line_RowClass, CurrencyService, EntityContainerModel, GlcodeService } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { switchMap, toArray } from 'rxjs/operators';

@Component({
  selector: 'app-customer-create-main',
  templateUrl: './customer-create-main.component.html',
  styleUrls: ['./customer-create-main.component.css']
})
export class CustomerCreateMainComponent implements OnInit {

  @Input() draft$: Observable<EntityContainerModel>;

  @Output() updateDraft = new EventEmitter<any>();
  @Output() validDraft = new EventEmitter<any>();

  form: FormGroup;

  apiVisa = AppConfig.apiVisa;


  type = [
    { value: 'CORPORATE', viewValue: 'CORPORATE' },
    { value: 'INDIVIDUAL', viewValue: 'INDIVIDUAL' }
  ];
  status = [
    { value: 'ACTIVE', viewValue: 'ACTIVE' },
    { value: 'INACTIVE', viewValue: 'INACTIVE' }
  ];
  idType = [
    { value: 'PASSPORT', viewValue: 'PASSPORT' },
    { value: 'IDENTITY_CARD', viewValue: 'IDENTITY CARD (IC)' }
  ];
  gender = [
    { value: 'MALE', viewValue: 'MALE' },
    { value: 'FEMALE', viewValue: 'FEMALE' },
    { value: 'TRANS', viewValue: 'TRANSGENDER' },
    { value: 'RNS', viewValue: 'RATHER NOT SAY' },
  ]


  id_placeholder = 'ID Number';
  taxID_placeholder = 'Tax Registration Number'
  newGlCode: any = [];
  glCodeArr: any = [];

  glcodeGuid: any;
  glCodeArr1: any = [];
  glcodeName: any;
  newGlCode1: any = [];
  currency: any;
  currencyCode: any;
  currencyName: any;
  currencyArr: any = [];
  newCurrencyArr: any = [];
  countryArr: any = [];
  newCountryArr: any = [];
  types: any = [];
  newCurrency: any;
  constructor(
    private fb: FormBuilder,
    private glcodeService: GlcodeService,
    private currencyService: CurrencyService,
  ) { }

  ngOnInit() {
    this.getCurrency();
    this.form = this.fb.group({
      code: ['', Validators.compose([Validators.required])],
      name: ['', Validators.compose([Validators.required])],
      type: ['', Validators.compose([Validators.required])],
      gender: [],
      id_number: [],
      currency: ['', Validators.compose([Validators.required])],
      currentCurrency: [''],
      status: [''],
      // creditTerms: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      // creditLimit: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      description: [],
      glCode: ['', Validators.compose([Validators.required])],
      currentGlCode: [],
      taxID: [''],
      id_type: ['']
    });
    this.form.patchValue({
      status: 'ACTIVE',
      id_type: 'IDENTITY_CARD',
    });


    // getting GL code
    this.glcodeService.get(this.apiVisa).pipe(
      switchMap((x: any) => {
        return x.data;
      }),
      toArray()
    ).subscribe((x: any) => {
      this.glCodeArr = x;
      for (const key in this.glCodeArr) {
        if (this.glCodeArr[key]) {
          this.glcodeGuid = this.glCodeArr[key].bl_fi_mst_glcode.guid;
          this.glcodeName = this.glCodeArr[key].bl_fi_mst_glcode.name;
          this.glCodeArr1.push({
            glcode_guid: this.glcodeGuid,
            glcode_name: this.glcodeName,
          });
        }

      }
      this.newGlCode1 = this.glCodeArr1;
      console.log('new gl code', this.newGlCode1);
    }
    );
    this.glcodeService.get(this.apiVisa)
      .subscribe(
        (x: any) => {
          x.data.forEach(data => {
            data.bl_fi_mst_glcode;
            this.glCodeArr.push(data.bl_fi_mst_glcode);
          });
          this.newGlCode = this.glCodeArr;
        }
      );

    // this.draft$.subscribe(draft => {
    // });
    this.monitorChanges();
  }
  getCurrency() {
    this.currencyService.get(this.apiVisa).subscribe((x: any) => {
      for (const key in x.data) {
        if (x.data[key]) {
          if (x.data[key].bl_fi_mst_ccy.code === 'MYR') {
            this.currencyArr.unshift(
              x.data[key].bl_fi_mst_ccy
            );
          }
          this.currencyArr.push(x.data[key].bl_fi_mst_ccy);
        }
      }
      this.newCurrency = this.currencyArr;
    });
  }
  monitorChanges() {
    this.form.valueChanges.subscribe(a => {
      this.updateDraft.emit(a);
      if (this.form.valid) {
        this.validDraft.emit('true');
      }
    }
    );
  }

  applyGLCodeFilter(filterValue: string) {
    console.log('this is filtervalue', filterValue);
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // MatTableDataSource defaults to lowercase matches
    this.newGlCode1 = this.glCodeArr1.filter((option) => option.glcode_name.toLowerCase().includes(filterValue));
  }
  applyCurrencyFilter(filterValue: string) {
    filterValue = filterValue.trim(); // Remove whitespace
    filterValue = filterValue.toLowerCase(); // MatTableDataSource defaults to lowercase matches
    this.newCurrency = this.currencyArr.filter((option) => option.display_main.toLowerCase().includes(filterValue) ||
      option.display_short.toLowerCase().includes(filterValue));
  }
  onTypeChange(type) {

    if (type.value === 'INDIVIDUAL') {
      if (this.form.value.id_type === null) {
        this.form.patchValue({
          id_type: 'IDENTITY_CARD',
        });
      }
      this.id_placeholder = 'ID Number';
      this.taxID_placeholder = 'Tax Registration Number'
    } else if (type.value === 'CORPORATE') {
      this.id_placeholder = 'Company Registration Number';
      this.taxID_placeholder = 'Company Tax Registration Number'
    }
  }
  onSubmit() {

  }
}
