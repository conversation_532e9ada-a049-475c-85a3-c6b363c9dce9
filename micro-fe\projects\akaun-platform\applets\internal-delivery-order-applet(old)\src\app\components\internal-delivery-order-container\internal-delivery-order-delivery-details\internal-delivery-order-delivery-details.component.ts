import { ChangeDetectionStrategy, Component, Input, OnInit, ViewEncapsulation } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { CellValueChangedEvent } from 'ag-grid-community';
import { bl_fi_generic_doc_hdr_RowClass, PickPackQueueService, InternalSalesOrderService, CompanyService, BranchService, LocationService, GenericDocContainerModel, PickPackQueueLineDtoModel, bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { PNSActions, HDRActions } from '../../../state-controllers/draft-controller/store/actions';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { DateCellRendererComponent } from '../../utilities/date-cell-renderer/date-cell-renderer.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-internal-delivery-order-delivery-details',
  templateUrl: './internal-delivery-order-delivery-details.component.html',
  styleUrls: ['./internal-delivery-order-delivery-details.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore],
  encapsulation: ViewEncapsulation.None
})
export class InternalDeliveryOrderDeliveryDetailsComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = "Delivery Details";
  protected readonly index = 0;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.deactivateAdd
  );
  readonly deactivateList$ = this.componentStore.select(
    (state) => state.deactivateList
  );

  toggleColumn$: Observable<boolean>;
  // searchModel = internalSalesOrderSearchModel;
  @Input() rowData: any[] = [];
  @Input() draft$: Observable<bl_fi_generic_doc_hdr_RowClass>;

  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
  };

  gridApi;

  form: FormGroup;

  columnsDefs = [
    {
      headerName: "Item Code",
      field: "item_code",
      cellStyle: () => ({ "text-align": "left" }),
      checkboxSelection: true,
      headerCheckboxSelection: true,
    },
    {
      headerName: "Item Name",
      field: "item_name",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "UOM",
      field: "uom",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Volumetric Weight",
      field: "",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Weight",
      field: "",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Requested Delivery Date",
      field: "track_delivery_date_requested",
      cellRenderer: "dateCellRenderer",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Require Delivery",
      field: "delivery_required",
      cellEditor: "agRichSelectCellEditor",
      cellEditorParams: {
        values: ["YES", "NO"],
      },
      cellClass: "drop-down-style",
      editable: true,
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Delivery Type",
      field: "track_delivery_logic",
      cellEditor: "agRichSelectCellEditor",
      cellEditorParams: {
        values: ["INTERNAL_DELIVERY", "EXTERNAL_DELIVERY", "PICKUP"],
      },
      cellClass: "drop-down-style",
      editable: true,
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Base Quantity",
      field: "quantity_base",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Qty To Deliver",
      type: "numericColumn",
      maxWidth: 150,
      valueGetter: (params) => {
        return params.data.qtyArranged;
      },
      valueSetter: (params) => {
        let newKOvalue = parseInt(params.newValue);
        let valueChanged;
        if (params.data?.qtyPending) {
          valueChanged =
            newKOvalue <= params.data?.qtyPending && newKOvalue >= 0;
        } else {
          valueChanged =
            newKOvalue <= params.data.quantity_base && newKOvalue >= 0;
        }

        if (valueChanged) {
          params.data.qtyArranged = newKOvalue;
          console.log("newKOvalue", newKOvalue);
        }
        return valueChanged;
      },
      cellStyle: () => ({ "text-align": "left", backgroundColor: "orange" }),
      hide: true,
    },
    {
      headerName: "Qty Pending To Deliver",
      field: "qtyPending",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Pick Pack Status",
      field: "ppStatus",
      cellStyle: () => ({ "text-align": "left" }),
      editable: false,
    },
    {
      headerName: "Pick Pack Queue",
      field: "pick_pack_queue",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Tracking ID",
      field: "tracking_id",
      cellStyle: () => ({ "text-align": "left" }),
      editable: true,
    },
    {
      headerName: "Shipping From Branch",
      field: "",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Shipping From Location",
      field: "",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Delivery Status",
      field: "",
      cellStyle: () => ({ "text-align": "left" }),
      editable: true,
    },
    {
      headerName: "Delivery Region",
      field: "del_region_hdr_state",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Delivery Remarks",
      field: "track_delivery_remarks",
      cellStyle: () => ({ "text-align": "left" }),
      editable: true,
    },
  ];
  genDocHdrGuid: any;
  disableSendtoQ: boolean;

  constructor(
    private pickPackQService: PickPackQueueService,
    protected viewColFacade: ViewColumnFacade,
    protected soService: InternalSalesOrderService,
    protected compService: CompanyService,
    protected brchService: BranchService,
    private readonly draftStore: Store<DraftStates>,
    protected lctnService: LocationService,
    private readonly store: Store<InternalDeliveryOrderStates>,
    protected readonly componentStore: ComponentStore<LocalState>
  ) {
    super();
  }

  frameworkComponents = {
    dateCellRenderer: DateCellRendererComponent
  };

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.form = new FormGroup({
      trackingID: new FormControl(),
      deliveryType: new FormControl(),
    });

    this.draft$.subscribe((data) => {
      this.genDocHdrGuid = data.guid;
      console.log("Draft",data)
    });

    for (var i = 0; i < this.rowData.length; i++) {
      // merge objects into one with multiple props
      this.rowData[i] = Object.assign(this.rowData[i], {
        qtyArranged: this.rowData[i].quantity_base,
        qtyPending: null,
      });

      // if (this.rowData[i].pick_pack_queue === "YES") {
      //   this.disableSendtoQ = true;
      // }
    }

    this.subs.sink = this.pickPackQService
      .getPickPackQueueByQuery(
        {
          guids: [this.genDocHdrGuid],
        },
        AppConfig.apiVisa
      )
      .subscribe((response) => {
        response.data.forEach((record) => {
          console.log("PP", record.bl_fi_pick_pack_queues);
          if (record.bl_fi_pick_pack_queues) {
            record.bl_fi_pick_pack_queues.forEach((record) => {
              for (var i = 0; i < this.rowData.length; i++) {
                // merge objects into one with multiple props
                if (this.rowData[i].guid === record.guid_doc_1_line) {
                  this.rowData[i] = Object.assign(this.rowData[i], {
                    qtyPending: record.qty_balance,
                    qtyArranged:
                      +this.rowData[i].quantity_base - record.qty_balance,
                    ppStatus: record.status,
                  });
                } 
              }
            });
          }
        });
      });

    setTimeout(() => {
      for (var i = 0; i < this.rowData.length; i++) {
        // merge objects into one with multiple props
        if (this.rowData[i].pick_pack_queue === true) {
          if (this.rowData[i].qtyPending === null) {
            this.rowData[i].qtyPending = 0;
          }
        }
      }
      this.gridApi.setRowData(this.rowData);
      console.log("ABC", this.rowData);
    }, 3000);

    this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.selectDeliveryDetailListingStatus).subscribe(a => {
      if (a) {
        this.refreshPickPackQueue();
        this.store.dispatch(InternalDeliveryOrderActions.resetDeliveryDetailsListing());
      }
    });
  }

  onNext() {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateList: false,
    });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(entity: GenericDocContainerModel) {
    if (entity) {
    }
  }

  onSendToQueue() {
    let lineItems = this.getAllRows();
    console.log(lineItems);
    lineItems = lineItems.filter(
      (a) =>
        a.delivery_required === "YES" &&
        a.delivery_required !== null &&
        a.pick_pack_queue !== true
    );
    const pickPackQueueLineDtoModel: PickPackQueueLineDtoModel[] = [];
    lineItems.forEach((record) => {
      pickPackQueueLineDtoModel.push({
        generic_doc_line_guid: record.guid.toString(),
        quantity: record.qtyArranged,
      });
    });
    for (var i = 0; i < this.rowData.length; i++) {
      if (this.rowData[i].delivery_required === "YES") {
        this.rowData[i].pick_pack_queue = true;
      }
    }
    console.log(lineItems);
    this.store.dispatch(
      InternalDeliveryOrderActions.pickPackQueueAllocationInit({
        pickPackQueueAllocation: pickPackQueueLineDtoModel,
      })
    );
    this.gridApi.setRowData(this.rowData);
    this.disableSendToQueue();
    this.refreshPickPackQueue();
  }

  refreshPickPackQueue() {
    this.subs.sink = this.pickPackQService
      .getPickPackQueueByQuery(
        {
          guids: [this.genDocHdrGuid],
        },
        AppConfig.apiVisa
      )
      .subscribe((response) => {
        response.data.forEach((record) => {
          console.log("PP", record.bl_fi_pick_pack_queues);
          if (record.bl_fi_pick_pack_queues) {
            record.bl_fi_pick_pack_queues.forEach((record) => {
              for (var i = 0; i < this.rowData.length; i++) {
                // merge objects into one with multiple props
                if (this.rowData[i].guid === record.guid_doc_1_line) {
                  this.rowData[i] = Object.assign(this.rowData[i], {
                    qtyPending: record.qty_balance,
                    qtyArranged:
                      +this.rowData[i].quantity_base - record.qty_balance,
                    ppStatus: record.status,
                  });
                } else {
                  this.rowData[i] = Object.assign(this.rowData[i], {
                    qtyPending: null,
                  });
                }
              }
            });
          }
        });
      });

    setTimeout(() => {
      for (var i = 0; i < this.rowData.length; i++) {
        // merge objects into one with multiple props
        if (this.rowData[i].pick_pack_queue === true) {
          if (this.rowData[i].qtyPending === null) {
            this.rowData[i].qtyPending = 0;
          }
        }
      }
      this.gridApi.setRowData(this.rowData);
      console.log("ABC", this.rowData);
    }, 3000);
  }

  onCellValueChanged(event: CellValueChangedEvent) {
    let data: bl_fi_generic_doc_line_RowClass = event.data;
    this.draftStore.dispatch(PNSActions.editPNS({ pns: data }));
    console.log(data);
  }

  getAllRows() {
    let rowData = [];
    this.gridApi.forEachNode((node) => rowData.push(node.data));
    return rowData;
  }

  // addTrackingIDToAllLines() {
  //   // console.log(this.form.value.trackingID);
  //   const trackingID = this.form.value.trackingID;
  //   for (var i = 0; i < this.rowData.length; i++) {
  //     // merge objects into one with multiple props
  //     this.rowData[i].tracking_id = trackingID;
  //   }
  //   this.gridApi.setRowData(this.rowData);
  //   this.draftStore.dispatch(
  //     PNSActions.updateTrackingID({ trackingID: trackingID })
  //   );
  // }

  addTrackingIDToSelectedLines() {
    const trackingID = this.form.value.trackingID;
    let selectedRows = this.gridApi.getSelectedRows();
    for (var i = 0; i < this.rowData.length; i++) {
      selectedRows.forEach((record) => {
        console.log(record);
        if (record.guid === this.rowData[i].guid) {
          // merge objects into one with multiple props
          this.rowData[i].tracking_id = trackingID;
        }
      });
    }
    this.gridApi.setRowData(this.rowData);
    this.rowData.forEach((record) => {
      this.draftStore.dispatch(PNSActions.editPNS({ pns: record }));
    });
  }

  addDeliveryTypeToSelectedLines() {
    const deliveryType = this.form.value.deliveryType;
    let selectedRows = this.gridApi.getSelectedRows();
    for (var i = 0; i < this.rowData.length; i++) {
      selectedRows.forEach((record) => {
        console.log(record);
        if (record.guid === this.rowData[i].guid) {
          // merge objects into one with multiple props
          this.rowData[i].track_delivery_logic = deliveryType;
        }
      });
    }
    this.gridApi.setRowData(this.rowData);
    this.draftStore.dispatch(HDRActions.updateDeliveryType({deliveryType}));
    this.rowData.forEach((record) => {
      this.draftStore.dispatch(PNSActions.editPNS({ pns: record }));
    });
  }

  disableSendToQueue() {
    for (var i = 0; i < this.rowData.length; i++) {
      if (this.rowData[i].pick_pack_queue === true) {
        this.disableSendtoQ = true;
      }
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }


}
