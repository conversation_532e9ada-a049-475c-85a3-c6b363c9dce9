import { createAction, props } from '@ngrx/store';
import { GenericDocSingleLineContainer, bl_inv_bin_line_RowClass } from 'blg-akaun-ts-lib';

export const mapProcess = createAction('[Process] Map Process', props<{guid: string, process: string}>());

export const selectProcess = createAction('[Process] Select Process', props<{process: any}>());
export const selectBinLine = createAction('[Process] Select Bin Line', props<{binLine: bl_inv_bin_line_RowClass}>());
export const selectCurrentProcess = createAction('[Process] Select Current Process', props<{process: string}>());
export const updateProcessForm = createAction('[Process] Update Process Form', props<{form: any}>());
export const updateProcess = createAction('[Process] Update Process');
export const updateProcessSuccess = createAction('[Process] Process Update Success');
