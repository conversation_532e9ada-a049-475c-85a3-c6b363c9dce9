<div class="view-col-table" fxLayout="column">
  <mat-card-title class="column-title">
    <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
      <div fxFlex="3 0 0">
        <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
          <!-- <app-advanced-search fxFlex fxFlex.lt-sm="100" [id]="'internal-so-line-item'" [advSearchModel]="searchModel"></app-advanced-search> -->
        </div>
      </div>
      <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
        <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
  </mat-card-title>
  <div style="height: 100%;">
    <ag-grid-angular #agGrid
    id="grid"
    style="height: 100%;"
    class="ag-theme-balham"
    [getRowClass]="pagination.getRowClass"
    [columnDefs]="columnsDefs"
    [rowData]="[]"
    [paginationPageSize]="pagination.rowPerPage"
    [cacheBlockSize]="pagination.rowPerPage"
    [pagination]="true"
    [animateRows]="true"
    [defaultColDef]="defaultColDef"
    [suppressRowClickSelection]="true"
    [sideBar]="true"
    rowModelType="serverSide"
    [serverSideStoreType]="'partial'"
    (gridReady)="onGridReady($event)">
    </ag-grid-angular>
  </div>
</div>
