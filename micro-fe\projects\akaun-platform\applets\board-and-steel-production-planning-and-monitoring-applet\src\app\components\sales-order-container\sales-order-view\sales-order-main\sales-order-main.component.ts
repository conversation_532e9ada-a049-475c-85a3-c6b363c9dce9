import { After<PERSON><PERSON>w<PERSON>hecked, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { BranchContainerModel, GuidDataFieldInterface, LocationContainerModel, bl_fi_generic_doc_single_line, bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../models/applet-settings.model';
import { Store } from '@ngrx/store';
import { InternalJobOrderStates } from '../../../../state-controllers/internal-job-order-controller/store/states';
import { JobOrderNoSelectors } from '../../../../state-controllers/draft-controller/store/selectors';
import { InternalJobOrderSelectors } from '../../../../state-controllers/internal-job-order-controller/store/selectors';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { SalesOrderStates } from '../../../../state-controllers/sales-order-controller/store/states';
import { SalesOrderSelectors } from '../../../../state-controllers/sales-order-controller/store/selectors';
import { AttrAst } from '@angular/compiler';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';
@Component({
  selector: 'app-sales-order-main',
  templateUrl: './sales-order-main.component.html',
  styleUrls: ['./sales-order-main.component.css']
})
export class SalesOrderMainComponent {

  @Input() draft$: Observable<bl_mrp_job_order_hdr_RowClass>;
  @Input() appletSettings$: Observable<AppletSettings>;

  @Output() itemCode = new EventEmitter();
  @Output() updateForm = new EventEmitter();

  apiVisa = AppConfig.apiVisa;

  PROCESS_STATUS = ['PLANNED', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD', 'CANCELLED'];
  QC_OUTPUT_STATUS = ['PASSED', 'REJECTED'];
  Scenario_Option = ['PRODUCTION_DONE', 'PRODUCTION_IN_PROGRESS', 'PRODUCTION_NOT_STARTED'];
  STATUS = ['OPEN', 'PROCESSED', 'COMPLETED'];
  private subs = new SubSink();

  form: FormGroup;
  uom;
  editMode;
  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );
  branchGuids: any[];
  locationGuids: any[];
  selectedBranch: GuidDataFieldInterface;
  selectedLocation: GuidDataFieldInterface;
  selectedCompany: GuidDataFieldInterface;

  constructor(
    protected readonly store: Store<SalesOrderStates>,
    protected readonly permissionStore: Store<PermissionStates>,

  ) {

  }

  ngOnInit() {


    this.form = new FormGroup({
      avl_stock_bal: new FormControl(''),
      branch: new FormControl(),
      location: new FormControl(),
      customerName: new FormControl(''),
      date_txn: new FormControl(''),
      expected_delivery : new FormControl(''),
      guid: new FormControl(''),
      item_code: new FormControl(),
      item_name: new FormControl(),
      job_order_no: new FormControl(),
      process_status: new FormControl(''),
      qty: new FormControl(0.00),
      scenario_option: new FormControl(''),
      so_no: new FormControl(''),
      status: new FormControl(),
      uom: new FormControl(),
      entityId: new FormControl()
    });

    this.form.controls['branch'].disable();
    this.form.controls['location'].disable();
    this.form.controls['customerName'].disable();
    this.form.controls['date_txn'].disable();    
    this.form.controls['expected_delivery'].disable();
    this.form.controls['job_order_no'].disable();    
    this.form.controls['process_status'].disable();
    this.form.controls['so_no'].disable();
    this.form.controls['entityId'].disable();

    this.store.select(SalesOrderSelectors.selectSalesOrder).subscribe(data => {
      this.selectedBranch = data.branch_guid;
      this.form.patchValue({
        avl_stock_bal: data.avl_stock_bal,
        branch: data.branch_guid,
        location: data.location_guid,
        customerName: data.customerName,
        entityId : data.entityId,
        date_txn: data.date_txn,
        expected_delivery : data.expected_delivery,
        guid: data.guid,
        item_code: data.item_code,
        item_name: data.item_name,
        job_order_no: data.job_order_no,
        process_status: data.process_status,
        qty: data.qty,
        scenario_option: data.scenario_option,
        so_no: data.so_no,
        status: data.job_doc_state,
        uom: data.uom,
      })
    })
  
    this.subs.sink = this.userPermissionTarget$.subscribe((targets) => {
      console.log("targets", targets);
      let target = targets.filter(
        (target) =>
          target.permDfn === "TNT_API_DOC_INTERNAL_SALES_ORDER_READ_TGT_GUID"
      );
      this.branchGuids = (target[0]?.target!==null && Object.keys(target[0]?.target || {}).length !== 0) ? target[0]?.target["bl_fi_mst_branch"] : [];
    });
  }



  async onBranchSelected(e: BranchContainerModel) {
    this.selectedBranch = e.bl_fi_mst_branch.guid;

    this.form.controls['codeBranch'].setValue(e.bl_fi_mst_branch.code);
    console.log("test::", this.form.value)
    if (e.bl_fi_mst_branch.comp_guid) {
      this.form.controls['company'].setValue(e.bl_fi_mst_branch.comp_guid);
      this.selectedCompany = e.bl_fi_mst_branch.comp_guid;
    }
    // console.log("test::",this.form.value)
    // this.updateMain.emit(this.form.value);
  }

  onLocationSelected(e: LocationContainerModel) {
    this.form.controls['codeLocation'].setValue(e.bl_inv_mst_location.code);
    // this.updateMain.emit(this.form.value);
  }


  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
