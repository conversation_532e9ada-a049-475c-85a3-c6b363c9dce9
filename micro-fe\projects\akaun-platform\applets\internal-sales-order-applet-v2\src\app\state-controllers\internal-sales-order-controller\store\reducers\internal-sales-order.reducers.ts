import { Action, createReducer, on } from '@ngrx/store';
import { InternalSalesOrderActions } from '../actions';
import { initState, InternalSalesOrderState } from '../states/internal-sales-order.states';
import { HDREditActions } from '../../../draft-controller/store/actions';

export const internalSalesOrderFeatureKey = 'salesOrder';

export const internalSalesOrderReducer = createReducer(
  initState,
  on(InternalSalesOrderActions.selectPricingSchemeHdr, (state, action) => ({
    ...state, selectedPricingSchemeHdr: action.pricingSchemeHdr
  })),
  on(InternalSalesOrderActions.updateChildItem, (state, action) => ({
    ...state,
    childItems: state.childItems.map(obj =>
      obj.guid === action.child.guid ? { ...obj, ...action.child } : obj
    )
  })),
  on(InternalSalesOrderActions.selectChildItem, (state, action) => ({
    ...state, childItems: action.child
  })),
  on(InternalSalesOrderActions.getCompanyDetailsSuccess, (state, action) => ({
    ...state, selectedCOA: action.company?.bl_fi_mst_comp?.chart_of_acc_guid || null
  })),
  on(InternalSalesOrderActions.selectSegmentLine, InternalSalesOrderActions.loadSegmentLineSuccess, (state, action) => ({
    ...state, selectedSegmentLine:  action.segment
  })),
  on(InternalSalesOrderActions.selectProjectLine, InternalSalesOrderActions.loadProjectLineSuccess, (state, action) => ({
    ...state, selectedProjectLine:  action.project
  })),
  on(InternalSalesOrderActions.selectProfitCenterLine, InternalSalesOrderActions.loadProfitCenterLineSuccess, (state, action) => ({
    ...state, selectedProfitCenterLine:  action.profitCenter
  })),
  on(InternalSalesOrderActions.selectDimensionLine, InternalSalesOrderActions.loadDimensionLineSuccess, (state, action) => ({
    ...state, selectedDimensionLine:  action.dimension
  })),
  on(InternalSalesOrderActions.selectDepartmentMode, (state, action) => ({
    ...state, departmentMode:  action.mode
  })),
  on(InternalSalesOrderActions.selectCOA, (state, action) => ({
    ...state, selectedCOA:  action.coa
  })),
  on(InternalSalesOrderActions.selectSegment, InternalSalesOrderActions.loadSegmentSuccess, (state, action) => ({
    ...state, selectedSegment:  action.segment
  })),
  on(InternalSalesOrderActions.selectProject, InternalSalesOrderActions.loadProjectSuccess, (state, action) => ({
    ...state, selectedProject:  action.project
  })),
  on(InternalSalesOrderActions.selectProfitCenter, InternalSalesOrderActions.loadProfitCenterSuccess, (state, action) => ({
    ...state, selectedProfitCenter:  action.profitCenter
  })),
  on(InternalSalesOrderActions.selectDimension, InternalSalesOrderActions.loadDimensionSuccess, (state, action) => ({
    ...state, selectedDimension:  action.dimension
  })),
  on(InternalSalesOrderActions.selectEInvoiceEnabled, (state, action) => ({
    ...state, eInvoiceEnabled:  action.val
  })),
  on(InternalSalesOrderActions.draftComplete, (state, action) => ({ ...state, refreshGenDocListing: action.successCount > 0 ? true : false })),
  on(InternalSalesOrderActions.selectMTOSectionSuccess, (state, action) =>
    ({...state, mtoSectionList: action.container})),
  on(InternalSalesOrderActions.selectMTOComponentSuccess, (state, action) =>
    ({...state, mtoComponentList: action.container})),
  on(InternalSalesOrderActions.deleteMTOchildItem, (state, { guid }) => {
    const updatedRows = state.mtoMasterList.map(row => {
      return {
        ...row,
        component: row.component.map(comp => {
          return {
            ...comp,
            doclines: comp.doclines.filter(doc => doc.guid !== guid) // Remove the specific docline using its guid
          };
        })
      };
    });

    return {
      ...state,
      mtoMasterList: updatedRows
    };
  }),
  on(InternalSalesOrderActions.editMTOchildItem, (state, { guid,updatedInfo }) => {
    const updatedRows = state.mtoMasterList.map(row => {
      return {
        ...row,
        component: row.component.map(comp => {
          return {
            ...comp,
            doclines: comp.doclines.map(doc => {
              if (doc.guid === guid) {
                return {
                  ...doc,
                  ...updatedInfo,
                };
              }
              return doc;
            }),
          };
        }),
      };
    });

    return {
      ...state,
      mtoMasterList: updatedRows,
    };
  }),
  on(InternalSalesOrderActions.resetMTO, (state, action) =>
    ({...state,
      mtoStatus:null,
      selectSection:  null,
      selectComponent:[],
      mtoMasterList:[],
    })),
  on(InternalSalesOrderActions.loadMTOMasterInit, (state, action) => ({
    ...state, mtoMasterList: action.json.mto
  })),
  on(InternalSalesOrderActions.setMTOstatus, (state, action) =>
    ({...state, mtoStatus:action.status})),
  on(InternalSalesOrderActions.updateMTOoptionLine, (state, { line, section, componentGuid }) => {
    const updatedRows = state.mtoMasterList.map(row => {
      if (row.section === section) {
        return {
          ...row,
          component: row.component.map(comp => {
            if (comp.guid === componentGuid) {
              if (!comp.doclines.some(docline => docline.guid === line.guid)) {
                const updatedDoclines = [...comp.doclines, line];
                updatedDoclines.sort((a, b) => a.position_id - b.position_id);
                return {
                  ...comp,
                  doclines: updatedDoclines
                };
              }
            }
            return comp;
          })
        };
      }
      return row;
    });

    return {
      ...state,
      mtoMasterList: updatedRows
    };
  }),
  on(InternalSalesOrderActions.selectComponent, (state, action) =>
    ({...state, selectComponent: action.component})),
  on(InternalSalesOrderActions.selectSection, (state, action) =>
  ({...state, selectSection: action.section})),
  on(InternalSalesOrderActions.selectMTOMasterSuccess, (state, action) => ({
    ...state, mtoMasterList: action.list
  })),
  on(InternalSalesOrderActions.resetCustomerCategory, (state, action) => ({
    ...state, customerCategoryLink:  []
  })),
  on(InternalSalesOrderActions.selectCustomerCategorySuccess, (state, action) => ({
    ...state, customerCategoryLink:  action.category
  })),
  on(InternalSalesOrderActions.getPricebookSuccess, (state, action) =>
  ({ ...state, selectedPricebook: action.pb })),
  on(InternalSalesOrderActions.addGroupDiscountSuccess, (state, action) =>
  ({ ...state, groupDiscountPercentage: action.discPercentage })),
  on(InternalSalesOrderActions.resetRoundingGroupDiscountItem, (state, action) =>
  ({ ...state, groupDiscountItem: null,roundingItem: null,roundingFiveCent: null })),
  on(InternalSalesOrderActions.loadGroupDiscountItemSuccess, (state, action) =>
  ({ ...state, groupDiscountItem: action.item})),
  on(InternalSalesOrderActions.loadRoundingItemSuccess, (state, action) =>
  ({ ...state, roundingItem: action.item})),
  on(InternalSalesOrderActions.loadSalesOrderSuccess, (state, action) => ({
    ...state, loadedGenDocs: action.salesOrders, genDocListingSnapshotGuid: action.snapshotGuid
  })),
  on(InternalSalesOrderActions.selectEntityInit, (state, action) => ({
    ...state, selectedEntity: action.entity
  })),

  on(InternalSalesOrderActions.selectCustomer, (state, action) => ({
    ...state, selectedCustomer: action.entity.entity
  })),
  on(InternalSalesOrderActions.selectDeliveryEntity, (state, action) =>
    ({ ...state, selectedDeliveryEntity: action.entity.entity })),

  on(InternalSalesOrderActions.selectCustomerEdit, (state, action) => ({
    ...state, selectedCustomer: action.entity.entity
  })),

  on(InternalSalesOrderActions.selectShippingAddress, (state, action) => ({
    ...state, selectedShippingAddress: action.shipping_address
  })),

  on(InternalSalesOrderActions.copyBillingAddressToShippingAddress, (state, action) => ({
    ...state,
    billingAddressCopy: {
      billing_address: action.billing_address,
      billing_info: action.billing_info
    }
  })),

  on(InternalSalesOrderActions.refreshShipToAddress, (state, action) => ({
    ...state,
    refreshShipToAddress: action.refresh
  })),

  on(InternalSalesOrderActions.selectShippingAddressEdit, (state, action) => ({
    ...state, selectedShippingAddress: action.shipping_address
  })),

  on(InternalSalesOrderActions.selectBillingAddress, (state, action) => ({
    ...state, selectedBillingAddress: action.billing_address
  })),

  on(InternalSalesOrderActions.selectBillingAddressEdit, (state, action) => ({
    ...state, selectedBillingAddress: action.billing_address
  })),
  on(InternalSalesOrderActions.selectItem, InternalSalesOrderActions.loadFiItemFromReservationSuccess, (state, action) => ({
    ...state,
    selectedItem: action.entity,
  })),
  on(InternalSalesOrderActions.selectLineItemInit, (state, action) => ({
    ...state,
    selectedLineItem: action.line,
    selectedReservation: null,
    selectedBinReservations: [],
  })),
  on(InternalSalesOrderActions.selectLineItemToBeUpdated, (state, action) => ({
    ...state,
    selectedLineItemToBeUpdated: action.lineItem
  })),

  on(InternalSalesOrderActions.createSalesOrderSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, koAttachments:[]
  })),

  on(InternalSalesOrderActions.deleteSalesOrderSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, docLock: false
  })),
  on(InternalSalesOrderActions.updateAndFinalSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.cloneDocumentFromSearchSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.editSalesOrderSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, docLock: false
  })),
  on(InternalSalesOrderActions.updatePostingStatusSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, docLock: false
  })),
  on(InternalSalesOrderActions.convertSalesSOSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.refreshSOAfterRVIsCreated, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),

  on(InternalSalesOrderActions.resetAgGrid, (state, action) => ({
    ...state, refreshGenDocListing: false, firstLoadListing: true
  })),

  on(InternalSalesOrderActions.voidSalesOrderSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),

  on(InternalSalesOrderActions.selectEntityCustomerSuccess, (state, action) => ({
    ...state, selectedCustomer: action.entity
  })),

  on(InternalSalesOrderActions.selectLineItemSuccess, (state, action) => ({
    ...state, selectedItem: action.entity
  })),

  on(InternalSalesOrderActions.resetSalesOrder, (state, action) => ({
    ...state,
    selectedCustomer: null,
    selectedBillingAddress: null,
    selectedShippingAddress: null,
    selectedLineItem: null
  })),

  on(InternalSalesOrderActions.resetSalesOrderEdit, (state, action) => ({
    ...state,
    selectedCustomerEdit: null,
    selectedBillingAddressEdit: null,
    selectedShippingAddressEdit: null,
  })),

  on(InternalSalesOrderActions.selectContraDoc, (state, action) => ({
    ...state,
    selectedContraDoc: action.entity
  })),

  on(InternalSalesOrderActions.selectContraLink, (state, action) => ({
    ...state,
    selectedContraLink: action.link
  })),

  on(InternalSalesOrderActions.selectDetailsInit, (state, action) => ({
    ...state, selectedDetails: action.entity
  })),

  on(InternalSalesOrderActions.selectShopeeGetShipParam, (state, action) => ({
    ...state, shopeeGetShipParam: action.shipParam
  })),

  on(InternalSalesOrderActions.selectDetailsReset, (state, action) => ({
    ...state, selectedDetails: null
  })),

  // For Serial, Bin, batch
  on(InternalSalesOrderActions.getInvItem, (state, action) => ({
    ...state, selectedItem: action.entity
  })),

  on(InternalSalesOrderActions.selectInvItem, (state, action) => ({
    ...state,
    selectedInvItem: action.invItem
  })),

  on(InternalSalesOrderActions.selectSerial, (state, action) => ({
    ...state,
    selectedSerial: action.serial
  })),

  on(InternalSalesOrderActions.selectBatch, (state, action) => ({
    ...state,
    selectedBatch: action.batch
  })),

  on(InternalSalesOrderActions.selectBin, (state, action) => ({
    ...state,
    selectedBin: action.bin
  })),

  on(InternalSalesOrderActions.setSalesOrderInLineItem, (state, action) => ({
    ...state,
    selectedEntity: action.entity
  })),

  on(InternalSalesOrderActions.selectPricingSchemeLinkSuccess, (state, action) =>
  ({
    ...state, pricingSchemeLink: action.pricing
  })),

  on(InternalSalesOrderActions.selectPricingScheme, (state, action) => ({
    ...state,
    selectedPricingScheme: action.pricingScheme
  })),
  on(InternalSalesOrderActions.updatePostingStatusSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),

  on(InternalSalesOrderActions.setEditMode, (state, action) => ({
    ...state, editMode: action.editMode
  })),

  on(InternalSalesOrderActions.selectEntityOnEdit, (state, action) =>
  ({ ...state, selectedOrderEntity: action.entity.entity })),

  on(InternalSalesOrderActions.selectOrderForEdit, (state, action) =>
  ({ ...state, selectedOrder: action.genDoc, cloneGenericDocHdrGuids: [], cloneSourceGenDocHdr: null, docArapBalance: action.genDoc?.bl_fi_generic_doc_hdr.arap_bal, docOpenAmount: action.genDoc?.bl_fi_generic_doc_hdr.arap_doc_open })),

  on(InternalSalesOrderActions.updateKnockoffListingConfig, (state, action) => ({
    ...state,
    knockoffListingConfig: action.settings
  })),
  on(InternalSalesOrderActions.bulkUpdateToPackedLZDSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.bulkUpdateToShipLZDSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.editGenLineItemSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.selectOrder, (state, action) =>
  ({ ...state, selectedOrder: action.genDoc, cloneGenericDocHdrGuids: [], cloneSourceGenDocHdr: null, })),

  on(InternalSalesOrderActions.selectCompanyGuid, (state, action) => ({
    ...state,
    selectedCompGuid: action.compGuid
  })),
  on(InternalSalesOrderActions.selectGUID, (state, action) => ({
    ...state,
    selectedGuid: action.guid
  })),
  on(InternalSalesOrderActions.storeMembershipGuid, (state, action) => ({
    ...state,
    selectedMembershipGuid: action.guid
  })),
  on(InternalSalesOrderActions.selectPricingSchemeGUID, (state, action) => ({
    ...state,
    selectedPricingSchemeGUID: action.guid
  })),
  on(InternalSalesOrderActions.loadExternalJobDocsSuccess, (state, action) => ({
    ...state, loadedExternalJobDocs: action.jobDocs, jobListingStatus: false
  })),
  on(InternalSalesOrderActions.loadInternalJobDocsSuccess, (state, action) => ({
    ...state, loadedInternalJobDocs: action.jobDocs, jobListingStatus: false
  })),
  on(InternalSalesOrderActions.loadPickupJobDocsSuccess, (state, action) => ({
    ...state, loadedPickupJobDocs: action.jobDocs, jobListingStatus: false
  })),
  on(InternalSalesOrderActions.cancelJobDocSuccess, (state, action) => ({
    ...state, jobListingStatus: true
  })),
  on(InternalSalesOrderActions.resetJobListing, (state, action) => ({
    ...state, jobListingStatus: false
  })),
  on(InternalSalesOrderActions.cancelJobDocSuccess, (state, action) => ({
    ...state, deliveryDetailListingStatus: true
  })),
  on(InternalSalesOrderActions.resetDeliveryDetailsListing, (state, action) => ({
    ...state, deliveryDetailListingStatus: false
  })),
  on(InternalSalesOrderActions.pickPackQueueAllocationSuccess, (state, action) => ({
    ...state, deliveryDetailListingStatus: true
  })),
  on(InternalSalesOrderActions.setPreviousSnapshotGuid, (state, action) => ({
    ...state, previousGenDocListingSnapshotGuid: [...state.previousGenDocListingSnapshotGuid, action.snapshotGuid]
  })),
  on(InternalSalesOrderActions.removePreviousSnapshotGuid, (state, action) => ({
    ...state, previousGenDocListingSnapshotGuid: [...state.previousGenDocListingSnapshotGuid.filter(a=>a!==action.snapshotGuid)]
  })),
  // on(InternalSalesOrderActions.setSnapshotGuid, (state, action) => ({
  //   ...state, genDocListingSnapshotGuid: action.snapshotGuid
  // })),
  on(InternalSalesOrderActions.resetReportAgGrid, (state, action) => ({
    ...state, updateAgGrid: false
  })),
  on(InternalSalesOrderActions.loadIntercompanyPurchaseOrderSuccess, (state, action) => ({
    ...state, intercompanyPurchaseOrders: action.purchaseOrders
  })),
  on(InternalSalesOrderActions.createIntercompanyTransactionsSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.shopeeLazadaMarketplaceUpdateFailure, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.discardComplete, (state, action) => ({ ...state, refreshGenDocListing: action.successCount > 0 ? true : false })),
  on(InternalSalesOrderActions.selectShopeePickUpAddresses, (state, action) => ({
    ...state, shopeePickUpAddresses: action.addresses
  })),
  on(InternalSalesOrderActions.selectShopeePickUpAddress, (state, action) => ({
    ...state, selectedShopeePickUpAddress: action.address
  })),
  on(InternalSalesOrderActions.selectShopeePickUpTime, (state, action) => ({
    ...state, selectedShopeePickUpTime: action.time
  })),
  on(InternalSalesOrderActions.selectTotalRevenue, (state, action) => ({...state, totalRevenue: action.totalRevenue})),
  on(InternalSalesOrderActions.selectTotalExpense, (state, action) => ({...state, totalExpense: action.totalExpense})),
  on(InternalSalesOrderActions.selectTotalSettlement, (state, action) => ({...state, totalSettlement: action.totalSettlement})),

  on(InternalSalesOrderActions.selectTotalContra, (state, action) => ({...state, totalContra: action.totalContra})),
  on(InternalSalesOrderActions.selectDocOpenAmount, (state, action) => ({...state, docOpenAmount: action.docOpenAmount})),
  on(InternalSalesOrderActions.selectDocArapBalance, (state, action) => ({...state, docArapBalance: action.docArapBalance})),
  on(InternalSalesOrderActions.refreshArapListing, (state, action) => ({...state, refreshArapListing: action.refreshArapListing})),
  on(
    InternalSalesOrderActions.loadArapListingSuccess,
    (state, action) => ({ ...state, loadedArap: action.arapListing })
  ),
  on(InternalSalesOrderActions.fetchDeliveryChargesItemsSuccess, (state, action) => ({ ...state, deliveryCharges: action.payload })),
  on(InternalSalesOrderActions.resetContra, (state, action) => ({
    ...state,
    totalContraRecords: 0,
    totalContra : 0,
    docOpenAmount : 0,
    docArapBalance : 0,
    loadedArap : null
  })),
  on(InternalSalesOrderActions.disableCreate, (state, action) => ({
    ...state, disableCreate: action.disable
  })),
  on(InternalSalesOrderActions.editedOrder, (state, action) => ({
    ...state, editedOrder: action.edited
  })),
  on(InternalSalesOrderActions.incrementItemCounter, (state) => ({ ...state, itemCounter: state.itemCounter + 1 })),
  on(InternalSalesOrderActions.decrementItemCounter, (state) => ({ ...state, itemCounter: state.itemCounter - 1 })),
  on(InternalSalesOrderActions.resetSalesOrder, (state) => ({ ...state, itemCounter: 0 })),
  on(InternalSalesOrderActions.setItemCounter, (state, action) => ({ ...state, itemCounter: action.count })),
  on(InternalSalesOrderActions.setKOAttachments, (state, action) => ({
    ...state, koAttachments: [...state.koAttachments, action.attachments],
  })),
  on(InternalSalesOrderActions.getTotalRecordsSuccess, (state, action) => ({ ...state, totalRecords: action.totalRecords })),

  on(InternalSalesOrderActions.selectSettlement, (state, action) =>
    ({ ...state, selectedSettlement: action.settlement })),
  on(InternalSalesOrderActions.setDelimeter, (state, action) => ({
    ...state, delimeter: action.delimeter,
  })),
  on(InternalSalesOrderActions.createTempSalesOrderSuccess, (state, action) =>
  ({ ...state, createdTempDoc: action.response, koAttachments:[] })),
  on(HDREditActions.updateMainOnKOImport, (state, action) => {
    // TODO: get confirmation on data storage
    state.selectedOrder.bl_fi_generic_doc_hdr.guid_comp = action.genDocHdr.bl_fi_generic_doc_hdr.guid_comp;
    state.selectedOrder.bl_fi_generic_doc_hdr.guid_branch = action.genDocHdr.bl_fi_generic_doc_hdr.guid_branch;
    state.selectedOrder.bl_fi_generic_doc_hdr.guid_store = action.genDocHdr.bl_fi_generic_doc_hdr.guid_store;
    state.selectedOrder.bl_fi_generic_doc_hdr.code_branch = action.genDocHdr.bl_fi_generic_doc_hdr.code_branch;
    state.selectedOrder.bl_fi_generic_doc_hdr.code_company = action.genDocHdr.bl_fi_generic_doc_hdr.code_company;
    state.selectedOrder.bl_fi_generic_doc_hdr.code_location = action.genDocHdr.bl_fi_generic_doc_hdr.code_location;
    state.selectedOrder.bl_fi_generic_doc_hdr.doc_reference = action.genDocHdr.bl_fi_generic_doc_hdr.doc_reference;
    // state.date_txn = action.genDocHdr.bl_fi_generic_doc_hdr.date_txn;
    state.selectedOrder.bl_fi_generic_doc_hdr.doc_ccy = action.genDocHdr.bl_fi_generic_doc_hdr.doc_ccy;
    // state.amount_discount = action.form.groupDiscountAmount;
    state.selectedOrder.bl_fi_generic_doc_hdr.doc_remarks = action.genDocHdr.bl_fi_generic_doc_hdr.doc_remarks;
    state.selectedOrder.bl_fi_generic_doc_hdr.doc_internal_remarks = action.genDocHdr.bl_fi_generic_doc_hdr.doc_internal_remarks;
    state.selectedOrder.bl_fi_generic_doc_hdr.doc_external_remarks = action.genDocHdr.bl_fi_generic_doc_hdr.doc_external_remarks;
    state.selectedOrder.bl_fi_generic_doc_hdr.custom_status = action.genDocHdr.bl_fi_generic_doc_hdr.custom_status;
    state.selectedOrder.bl_fi_generic_doc_hdr.marketplace_status = action.genDocHdr.bl_fi_generic_doc_hdr.marketplace_status;
    state.selectedOrder.bl_fi_generic_doc_hdr.contact_key_guid = action.genDocHdr.bl_fi_generic_doc_hdr.contact_key_guid;
    // state.member_guid = action.form.memberCard;
    state.selectedOrder.bl_fi_generic_doc_hdr.doc_entity_hdr_guid = action.genDocHdr.bl_fi_generic_doc_hdr.doc_entity_hdr_guid;
    state.selectedOrder.bl_fi_generic_doc_hdr.doc_entity_hdr_json = action.genDocHdr.bl_fi_generic_doc_hdr.doc_entity_hdr_json;
    state.selectedOrder.bl_fi_generic_doc_hdr.sales_entity_hdr_guid = action.genDocHdr.bl_fi_generic_doc_hdr.sales_entity_hdr_guid;
    state.selectedOrder.bl_fi_generic_doc_hdr.billing_json = action.genDocHdr.bl_fi_generic_doc_hdr.billing_json;
    state.selectedOrder.bl_fi_generic_doc_hdr.delivery_entity_json = action.genDocHdr.bl_fi_generic_doc_hdr.delivery_entity_json;
    state.selectedOrder.bl_fi_generic_doc_hdr.tracking_id = action.genDocHdr.bl_fi_generic_doc_hdr.tracking_id;
    state.selectedOrder.bl_fi_generic_doc_hdr.property_json = action.genDocHdr.bl_fi_generic_doc_hdr.property_json;
    state.selectedOrder.bl_fi_generic_doc_hdr.due_date = action.genDocHdr.bl_fi_generic_doc_hdr.due_date ? action.genDocHdr.bl_fi_generic_doc_hdr.due_date : state.selectedOrder.bl_fi_generic_doc_hdr.due_date;
    state.selectedOrder.bl_fi_generic_doc_hdr.delivery_branch_guid = action.genDocHdr.bl_fi_generic_doc_hdr.delivery_branch_guid;
    state.selectedOrder.bl_fi_generic_doc_hdr.delivery_location_guid = action.genDocHdr.bl_fi_generic_doc_hdr.delivery_location_guid;
    state.selectedOrder.bl_fi_generic_doc_hdr.delivery_branch_code = action.genDocHdr.bl_fi_generic_doc_hdr.delivery_branch_code;
    state.selectedOrder.bl_fi_generic_doc_hdr.delivery_location_code = action.genDocHdr.bl_fi_generic_doc_hdr.delivery_location_code;
    return state;
  }),
  on(InternalSalesOrderActions.addContra, (state, action) => ({
    ...state, addedContraDoc: action.contraDoc
  })),

  on(InternalSalesOrderActions.selectDocLink, (state, action) => ({
    ...state, docLink: action.docLink
  })),
  on(InternalSalesOrderActions.getCurrencySuccess, (state, action) =>
    ({ ...state, selectedCurrency: action.ccy })),
  on(InternalSalesOrderActions.selectPnsLevel2, (state, action) => ({
    ...state, pnsLevel2: action.pnsLevel2
  })),
  on(InternalSalesOrderActions.closeKoComplete, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesOrderActions.selectLinkedSalesOrder, (state, action) => ({
    ...state, linkedJobOrder: action.linkedJobOrder
  })),
  on(InternalSalesOrderActions.selectJobOrderLinkGuid, (state, action) => ({
    ...state, jobOrderLinkGuid: action.guid
  })),
  on(InternalSalesOrderActions.resetSettingItemFilter, (state, action) =>
    ({...state, selectedItemCategoryFilter:[]})),
    on(InternalSalesOrderActions.selectSettingItemFilterSuccess, (state, action) =>
    ({...state, selectedItemCategoryFilter:action.setting})),
    on(InternalSalesOrderActions.loadSettingItemFilterSuccess, (state, action) =>
    ({...state, itemCategoryFilterList:action.setting})),
    on(InternalSalesOrderActions.selectEMPCategoryGroupSuccess, (state, action) =>
    ({...state, categoryGroupList:action.categoryGroup})),
    on(InternalSalesOrderActions.selectDefaultBranchPricingSchemeSucccess, (state, action) => ({
      ...state, defaultBranchPricingScheme:  action.pricingScheme
    })),
    on(InternalSalesOrderActions.selectBranchCategoryGroupSucess, (state, action) =>
    ({...state, branchCategoryGroupGuid:action.guid})),
    on(InternalSalesOrderActions.selectEntityBranch, (state, action) => ({
      ...state,
      selectedEntityBranch: action.entityBranch
    })),
    on(InternalSalesOrderActions.cloneDocumentInit, (state, action) => ({ ...state, disableCloneBtn: true })),
    on(InternalSalesOrderActions.cloneDocumentSuccess, (state, action) => ({ ...state, cloneGenericDocHdrGuids: [...state.cloneGenericDocHdrGuids, action.dto?.cloneGenericDocHdrGuid?.toString() ]})),
    on(InternalSalesOrderActions.pollClonedDocumentSuccess, (state, action) => ({ ...state, refreshGenDocListing: true, disableCloneBtn: false })),
    on(InternalSalesOrderActions.pollClonedDocumentFail, (state, action) => ({ ...state, disableCloneBtn: false })),
    on(InternalSalesOrderActions.loadCloneSourceGenDocHdrSuccess, (state, action) => ({ ...state, cloneSourceGenDocHdr: action.hdr })),

    on(InternalSalesOrderActions.selectCopyDepartmentFromHdr, (state, action) => ({
        ...state, copyDepartmentFromHdr:  action.value
      })),

    on(InternalSalesOrderActions.selectRowData, (state, action) =>
        ({...state, rowData: action.rowData})),
    on(InternalSalesOrderActions.selectTotalRecords, (state, action) =>
        ({...state, totalRecords: action.totalRecords})),
    on(InternalSalesOrderActions.selectSearchItemRowData, (state, action) =>
        ({...state, searchItemRowData: action.rowData})),
    on(InternalSalesOrderActions.selectSearchItemTotalRecords, (state, action) =>
        ({...state, searchItemTotalRecords: action.totalRecords})),
    on(InternalSalesOrderActions.selectFirstLoadListing, (state, action) =>
      ({...state, firstLoadListing: action.firstLoadListing})),
    on(InternalSalesOrderActions.resetExpansionPanel, (state, action) => ({
      ...state, resetExpansionPanel: action.resetIndex
    })),
    on(InternalSalesOrderActions.selectEntityBranch, (state, action) => ({
      ...state,
      selectedEntityBranch: action.entityBranch
    })),
    on(InternalSalesOrderActions.cloneDocumentInit, (state, action) => ({ ...state, disableCloneBtn: true })),
    on(InternalSalesOrderActions.cloneDocumentSuccess, (state, action) => ({ ...state, cloneGenericDocHdrGuids: [...state.cloneGenericDocHdrGuids, action.dto?.cloneGenericDocHdrGuid?.toString() ]})),
    on(InternalSalesOrderActions.pollClonedDocumentSuccess, (state, action) => ({ ...state, refreshGenDocListing: true, disableCloneBtn: false })),
    on(InternalSalesOrderActions.pollClonedDocumentFail, (state, action) => ({ ...state, disableCloneBtn: false })),
    on(InternalSalesOrderActions.loadCloneSourceGenDocHdrSuccess, (state, action) => ({ ...state, cloneSourceGenDocHdr: action.hdr })),

    on(InternalSalesOrderActions.selectCopyDepartmentFromHdr, (state, action) => ({
        ...state, copyDepartmentFromHdr:  action.value
      })),

    on(InternalSalesOrderActions.selectRowData, (state, action) =>
        ({...state, rowData: action.rowData})),
    on(InternalSalesOrderActions.selectTotalRecords, (state, action) =>
        ({...state, totalRecords: action.totalRecords})),
    on(InternalSalesOrderActions.selectSearchItemRowData, (state, action) =>
        ({...state, searchItemRowData: action.rowData})),
    on(InternalSalesOrderActions.selectSearchItemTotalRecords, (state, action) =>
        ({...state, searchItemTotalRecords: action.totalRecords})),
    on(InternalSalesOrderActions.selectFirstLoadListing, (state, action) =>
      ({...state, firstLoadListing: action.firstLoadListing})),
    on(InternalSalesOrderActions.resetExpansionPanel, (state, action) => ({
      ...state, resetExpansionPanel: action.resetIndex
    })),
    on(InternalSalesOrderActions.selectEntityBranch, (state, action) => ({
      ...state,
      selectedEntityBranch: action.entityBranch
    })),
    on(InternalSalesOrderActions.cloneDocumentInit, (state, action) => ({ ...state, disableCloneBtn: true })),
    on(InternalSalesOrderActions.cloneDocumentSuccess, (state, action) => ({ ...state, cloneGenericDocHdrGuids: [...state.cloneGenericDocHdrGuids, action.dto?.cloneGenericDocHdrGuid?.toString() ]})),
    on(InternalSalesOrderActions.pollClonedDocumentSuccess, (state, action) => ({ ...state, refreshGenDocListing: true, disableCloneBtn: false })),
    on(InternalSalesOrderActions.pollClonedDocumentFail, (state, action) => ({ ...state, disableCloneBtn: false })),
    on(InternalSalesOrderActions.loadCloneSourceGenDocHdrSuccess, (state, action) => ({ ...state, cloneSourceGenDocHdr: action.hdr })),

    on(InternalSalesOrderActions.selectCopyDepartmentFromHdr, (state, action) => ({
        ...state, copyDepartmentFromHdr:  action.value
      })),

    on(InternalSalesOrderActions.selectRowData, (state, action) =>
        ({...state, rowData: action.rowData})),
    on(InternalSalesOrderActions.selectTotalRecords, (state, action) =>
        ({...state, totalRecords: action.totalRecords})),
    on(InternalSalesOrderActions.selectSearchItemRowData, (state, action) =>
        ({...state, searchItemRowData: action.rowData})),
    on(InternalSalesOrderActions.selectSearchItemTotalRecords, (state, action) =>
        ({...state, searchItemTotalRecords: action.totalRecords})),
    on(InternalSalesOrderActions.selectFirstLoadListing, (state, action) =>
      ({...state, firstLoadListing: action.firstLoadListing})),
    on(InternalSalesOrderActions.lockDocument, (state, action) => ({
      ...state, docLock: true
    })),
    on(InternalSalesOrderActions.selectDocumentForKO, (state, action) => ({
      ...state, selectedDocForKO: action.docGuid
    })),
    on(InternalSalesOrderActions.selectApprovalStatus, (state, action) => ({
      ...state, approvalStatus: action.status
    })),
    on(InternalSalesOrderActions.saveClientDoc1, (state, { clientDoc1 }) => ({
      ...state,
      clientDoc1,
    })),
    on(InternalSalesOrderActions.selectAttachment, (state, action) => ({...state, attchment: action.attchment})),
    on(InternalSalesOrderActions.selectStockReservation, (state, action) => ({ ...state,
      selectedReservation: action.reservation, selectedBinReservations: [] })),
    on(InternalSalesOrderActions.loadBinReservationsSuccess, (state, action) => ({ ...state, selectedBinReservations: action.binReservations })),
    on(InternalSalesOrderActions.resetSelectedReservation, (state, _) => ({
      ...state, selectedReservation: null, selectedBinReservations: [] })),
    on(InternalSalesOrderActions.setTotalSalesOrderLineAmount, (state, action) => ({
      ...state, totalSalesOrderLineAmount: action.totalSalesOrderLineAmount
    })),
    on(InternalSalesOrderActions.selectSearchChildItemRowData, (state, action) =>
    ({...state, searchChildItemRowData: action.rowData})),
    on(InternalSalesOrderActions.selectSearchChildItemTotalRecords, (state, action) =>
    ({...state, searchChildItemTotalRecords: action.totalRecords})),

);

export function reducer(state: InternalSalesOrderState | undefined, action: Action) {
  return internalSalesOrderReducer(state, action);
}
