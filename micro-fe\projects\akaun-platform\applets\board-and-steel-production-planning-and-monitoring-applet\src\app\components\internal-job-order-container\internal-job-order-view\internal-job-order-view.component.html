<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        View Job Order No
      </span>
    </div>
    <div fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="5px">
      <button mat-raised-button color="primary" type="button"
      (click)="onReset()">RESET</button>
      <button mat-raised-button color="primary" type="button"
      [disabled]="disableButton()"
      (click)="onSave()">SAVE</button>
    </div>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Main">
    <div class="with-delete">
      <app-internal-job-order-create-main [draft$]="draft$" [appletSettings$]="appletSettings$" (updateMain)="onUpdateMain($event)"></app-internal-job-order-create-main>
    </div>
  </mat-tab>

  <mat-tab label="Sales Order Link">
      <app-gendoc-link-listing></app-gendoc-link-listing>
  </mat-tab>
  <!-- <mat-tab label="Department Hdr">
    <div class="with-delete"> -->
      <!-- <app-internal-job-order-create-department [appletSettings$]="appletSettings$" [draft$]="draft$" (updateDepartment)="onUpdateDepartment($event)"></app-internal-job-order-create-department> -->
    <!-- </div>
  </mat-tab> -->
  <!-- <mat-tab label="Attachments">
    <div class="with-delete">
      <app-internal-job-order-create-attachments [rowData]="attachments$ | async" [localState]="localState$ | async" (next)="onAddAttachments()"></app-internal-job-order-create-attachments>
    </div>
  </mat-tab> -->
  <mat-tab label="Process Instance">
    <app-process-instance-listing (updateGroupGuid)="onGoupNameUpdated($event)"></app-process-instance-listing>
  </mat-tab>
</mat-tab-group>
<div style="padding: 5px;">
  <button mat-raised-button color="warn" type="button" (click)="onDelete()">
    <span>{{ (deleteConfirmation$ | async) ? 'CLICK AGAIN TO CONFIRM' : 'DELETE'}}</span>
  </button>
</div>
