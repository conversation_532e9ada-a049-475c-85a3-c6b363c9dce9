import { Injectable } from '@angular/core';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { ViewColumn } from 'projects/shared-utilities/view-column';
import { ProcessingListingComponent } from '../components/processing-container/processing-listing/processing-listing.component';
import { ProcessingViewComponent } from '../components/processing-container/processing-view/processing-view.component';


@Injectable()
export class ProcessingPagesService {

  private initialState: ViewColumnState = {
    firstColumn:  new ViewColumn(0, ProcessingListingComponent, 'Processing Listing', {
      deactivateAdd: false,
      deactivateList: false
    }),
    secondColumn: null,
    viewCol: [
      new ViewColumn(0, ProcessingListingComponent, 'Processing Listing', {
        deactivateAdd: false,
        deactivateList: false
      }),
      new ViewColumn(1, ProcessingViewComponent, 'Processing View', {
        deactivateReturn: false,
        selectedIndex: 0
      })
    ],
    breadCrumbs: [],
    leftDrawer: [],
    rightDrawer: [],
    singleColumn: false,
    prevIndex: null
  };

  get pages() {
    return this.initialState;
  }

  constructor() { }
}
