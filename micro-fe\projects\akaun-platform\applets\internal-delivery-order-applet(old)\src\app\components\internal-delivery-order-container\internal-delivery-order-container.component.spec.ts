import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderContainerComponent } from './internal-delivery-order-container.component';

describe('InternalDeliveryOrderContainerComponent', () => {
  let component: InternalDeliveryOrderContainerComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderContainerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderContainerComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderContainerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
