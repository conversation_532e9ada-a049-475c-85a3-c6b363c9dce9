import { ChangeDetectionStrategy, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Que<PERSON><PERSON><PERSON>, ViewChildren } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SubSink } from 'subsink2';
import { AppletSettings, CustomStatusParams } from '../../../models/applet-settings.model';
import { CustomStatusSettingsComponent } from './custom-status-settings/custom-status-settings.component';

@Component({
  selector: 'app-custom-status',
  templateUrl: './custom-status.component.html',
  styleUrls: ['./custom-status.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CustomStatusComponent implements On<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> {

  private subs = new SubSink();

  form = new FormGroup({});

  masterSettings$ = this.store.select(SessionSelectors.selectMasterSettings);

  @ViewChildren(CustomStatusSettingsComponent) customStatusSettings: QueryList<CustomStatusSettingsComponent>;

  constructor(
    private readonly store: Store<SessionStates>
    ) { }

  ngOnInit() {
    for (let i = 1; i < 6; i++) {
      this.form.addControl(`ENABLE_CUSTOM_STATUS_${i}`, new FormControl());
      this.form.addControl(`ENABLE_CUSTOM_STATUS_HDR_${i}`, new FormControl());
      this.form.addControl(`ENABLE_CUSTOM_STATUS_LINE_${i}`, new FormControl());
      this.subs.sink = this.form.controls[`ENABLE_CUSTOM_STATUS_${i}`].valueChanges.subscribe({next: resolve => {
        this.onChange(i);
      }});
    }
    this.subs.sink = this.masterSettings$.subscribe({next: (resolve: AppletSettings) => {
      this.form.patchValue({
        ENABLE_CUSTOM_STATUS_1: resolve?.ENABLE_CUSTOM_STATUS_1,
        ENABLE_CUSTOM_STATUS_2: resolve?.ENABLE_CUSTOM_STATUS_2,
        ENABLE_CUSTOM_STATUS_3: resolve?.ENABLE_CUSTOM_STATUS_3,
        ENABLE_CUSTOM_STATUS_4: resolve?.ENABLE_CUSTOM_STATUS_4,
        ENABLE_CUSTOM_STATUS_5: resolve?.ENABLE_CUSTOM_STATUS_5,
        ENABLE_CUSTOM_STATUS_HDR_1: resolve?.ENABLE_CUSTOM_STATUS_HDR_1,
        ENABLE_CUSTOM_STATUS_HDR_2: resolve?.ENABLE_CUSTOM_STATUS_HDR_2,
        ENABLE_CUSTOM_STATUS_HDR_3: resolve?.ENABLE_CUSTOM_STATUS_HDR_3,
        ENABLE_CUSTOM_STATUS_HDR_4: resolve?.ENABLE_CUSTOM_STATUS_HDR_4,
        ENABLE_CUSTOM_STATUS_HDR_5: resolve?.ENABLE_CUSTOM_STATUS_HDR_5,
        ENABLE_CUSTOM_STATUS_LINE_1: resolve?.ENABLE_CUSTOM_STATUS_LINE_1,
        ENABLE_CUSTOM_STATUS_LINE_2: resolve?.ENABLE_CUSTOM_STATUS_LINE_2,
        ENABLE_CUSTOM_STATUS_LINE_3: resolve?.ENABLE_CUSTOM_STATUS_LINE_3,
        ENABLE_CUSTOM_STATUS_LINE_4: resolve?.ENABLE_CUSTOM_STATUS_LINE_4,
        ENABLE_CUSTOM_STATUS_LINE_5: resolve?.ENABLE_CUSTOM_STATUS_LINE_5,
      });
    }});
  }

  onChange(index: number) {
    switch (this.form.controls[`ENABLE_CUSTOM_STATUS_${index}`].value) {
      case true:
        this.form.controls[`ENABLE_CUSTOM_STATUS_HDR_${index}`].enable();
        this.form.controls[`ENABLE_CUSTOM_STATUS_LINE_${index}`].enable();
      break;
      default:
        this.form.controls[`ENABLE_CUSTOM_STATUS_HDR_${index}`].patchValue(false);
        this.form.controls[`ENABLE_CUSTOM_STATUS_LINE_${index}`].patchValue(false);
        this.form.controls[`ENABLE_CUSTOM_STATUS_HDR_${index}`].disable();
        this.form.controls[`ENABLE_CUSTOM_STATUS_LINE_${index}`].disable();
      break;
    }
  }

  onSave() {
    let settings = {...this.form.getRawValue()};
    this.customStatusSettings.forEach(i => {
      i.gridApi.stopEditing();
      settings = {...settings, ...i.getCustomStatus()};
    });
    this.store.dispatch(SessionActions.saveMasterSettingsInit({settings}));
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
