import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { BranchService, GenericDocContainerModel, InternalOutboundDeliveryOrderService, LocationService, Pagination, SubQueryService } from 'blg-akaun-ts-lib';
import { forkJoin, iif, Observable, of, zip } from 'rxjs';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { Store } from '@ngrx/store';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { internalDeliveryOrderSearchModel } from '../../../models/advanced-search-models/internal-delivery-order.model';
import { ProcesssingStates } from '../../../state-controllers/processing-controller/store/states';
import { AppConfig } from 'projects/shared-utilities/visa';
import { ProcessingSelectors } from '../../../state-controllers/processing-controller/store/selectors';
import { ProcessingActions } from '../../../state-controllers/processing-controller/store/actions';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-processing-listing',
  templateUrl: './processing-listing.component.html',
  styleUrls: ['./processing-listing.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class ProcessingListingComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Processing Listing';
  protected readonly index = 0;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  searchModel = internalDeliveryOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Doc No', field: 'bl_fi_generic_doc_hdr.server_doc_1', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Branch', field: 'bl_fi_generic_doc_hdr.code_branch', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Location', field: 'bl_fi_generic_doc_hdr.code_location', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Customer Name', field: 'bl_fi_generic_doc_hdr.doc_entity_hdr_json', cellStyle: () => ({'text-align': 'left'}),
    valueFormatter: params => params.value?.entityName},
    {headerName: 'Status', field: 'bl_fi_generic_doc_hdr.status', cellStyle: () => ({'text-align': 'left'})},
  ];

  SQLGuids: string[] = null;
  pagination = new Pagination();

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private doService: InternalOutboundDeliveryOrderService,
    private brchService: BranchService,
    private lctnService: LocationService,
    private sqlService: SubQueryService,
    private readonly store: Store<ProcesssingStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false});
    this.viewColFacade.onNextAndReset(this.index, 3);
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        const sortModel = grid.request.sortModel;
        const filterModel = grid.request.filterModel;
        const sortOn = pageSorting(sortModel);
        const filter = pageFiltering(filterModel);
        this.subs.sink = this.doService.getByCriteria(new Pagination(
          this.SQLGuids ? 0 : grid.request.startRow,
          grid.request.endRow - grid.request.startRow, [
          {columnName: 'calcTotalRecords', operator: '=', value: 'true'},
          {columnName: 'orderBy', operator: '=', value: 'updated_date'},
          {columnName: 'order', operator: '=', value: 'DESC'},
          {
            columnName: 'guids',
            operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          }
        ]), apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<GenericDocContainerModel>[] = [];
            b.data.forEach( doc => source.push(
              zip(
                // this.compService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_comp.toString(), apiVisa).pipe(
                //   catchError((err) => of(err))
                // ),
                this.brchService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_branch.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                this.lctnService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_store.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                )).pipe(
                  map(([b_b, b_c]) => {
                    // doc.bl_fi_generic_doc_hdr.guid_comp = b_a.error ? b_a.error.code : b_a.data.bl_fi_mst_comp.name;
                    doc.bl_fi_generic_doc_hdr.code_branch = b_b.error ? b_b.error.code : b_b.data.bl_fi_mst_branch.name;
                    doc.bl_fi_generic_doc_hdr.code_location = b_c.error ? b_c.error.code : b_c.data.bl_inv_mst_location.name;
                    return doc;
                  })
                )
            ));
            return iif(() => b.data.length > 0,
              forkJoin(source).pipe(map(() => b)),
              of(b)
            );
          })
        ).subscribe( resolved => {
          const data = sortOn(resolved.data).filter(entity => filter.by(entity));
          const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : resolved.totalRecords) : data.length;
          grid.success({
            rowData: data,
            rowCount: totalRecords
          });
        }, err => {
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(ProcessingSelectors.selectAgGrid).subscribe( a => {
      if (a) {
        this.gridApi.refreshServerSideStore();
        this.store.dispatch(ProcessingActions.resetAgGrid());
      }
    });
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe(
        {next: resolve => {
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }}
      );
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(entity: GenericDocContainerModel) {
    if (entity) {
      this.store.dispatch(ProcessingActions.selectEntity({entity}));
      if (!this.localState.deactivateList) {
        this.viewColFacade.updateInstance(this.index, {
          ...this.localState, deactivateAdd: false, deactivateList: true});
        this.viewColFacade.onNextAndReset(this.index, 1);
      }
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
