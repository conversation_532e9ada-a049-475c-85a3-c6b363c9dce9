#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=dev --project=internal-sales-order-applet-v2 --output-hashing none
node elements-build-scripts/akaun/internal-sales-order-applet-v2-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://development-akaun-applets/bigledger/tonn-cable/internal-sales-order-applet-v2/staging s3://development-akaun-applets/bigledger/tonn-cable/internal-sales-order-applet-v2/staging/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile development-bigledger --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/internal-sales-order-applet-v2/ s3://development-akaun-applets/bigledger/tonn-cable/internal-sales-order-applet-v2/dev --profile development-bigledger --acl public-read --recursive
