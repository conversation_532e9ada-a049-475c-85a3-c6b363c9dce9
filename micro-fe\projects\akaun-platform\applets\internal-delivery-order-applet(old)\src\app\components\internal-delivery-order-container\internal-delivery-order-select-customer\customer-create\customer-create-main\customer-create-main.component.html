<form [formGroup]="form" (ngSubmit)="onSubmit()">
  <!-- <mat-card style="height: 100%; overflow: hidden; display: contents"> <mat-card-content> -->
  <div fxLayout="column" class="view-col-forms">
    <div fxLayout="row wrap" fxFlexAlign="center" class="row">
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Entity Name </mat-label> <input matInput placeholder="Entity Name"
            [formControl]="form.controls['name']" required maxlength="255">

          <mat-hint *ngIf="form.controls['name'].hasError('required') && form.controls['name'].touched"
            class="text-danger font-14">Please insert Entity Name </mat-hint>
        </mat-form-field>
      </div>
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Entity Code </mat-label> <input matInput placeholder="Entity Code"
            [formControl]="form.controls['code']" type="text" maxlength="255" required>
          <mat-hint *ngIf="form.controls['code'].hasError('required') && form.controls['code'].touched "
            class="text-danger font-14">Please insert Entity Code </mat-hint>
        </mat-form-field>
      </div>
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Status </mat-label>
          <mat-select placeholder="Status" formControlName="status" required>
            <mat-option *ngFor="let s of status" [value]="s.value"> {{s.viewValue}} </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label>Entity Type </mat-label>
          <mat-select placeholder="Type" formControlName="type" (selectionChange)="onTypeChange($event)" required>
            <mat-option *ngFor="let type of type" [value]="type.value"> {{type.viewValue}} </mat-option>
          </mat-select>
          <mat-hint *ngIf="form.controls['type'].hasError('required') && form.controls['type'].touched"
            class="text-danger font-14">Please insert Entity Type </mat-hint>
        </mat-form-field>
      </div>
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100"
        *ngIf="form.get('type').value === 'INDIVIDUAL'">
        <mat-form-field appearance="outline">
          <mat-label> Identity Type </mat-label>
          <mat-select placeholder="Identity Type" formControlName="id_type">
            <mat-option *ngFor="let type of idType" [value]="type.value"> {{type.viewValue}} </mat-option>
          </mat-select>
          <mat-hint *ngIf="form.controls['id_type'].value?.length === 255" class="text-danger font-14">Please
            insert no more than 255
          </mat-hint>
        </mat-form-field>
      </div>
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> {{id_placeholder}} </mat-label> <input matInput placeholder={{id_placeholder}}
            formControlName="id_number" type="text" maxlength="255">
        </mat-form-field>
      </div>
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100"
      *ngIf="form.get('type').value === 'INDIVIDUAL'">
        <mat-form-field appearance="outline">
        <mat-label>Gender</mat-label>
          <mat-select placeholder="Gender" formControlName="gender">
            <mat-option *ngFor="let gender of gender" [value]="gender.value"> {{gender.viewValue}} </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> {{taxID_placeholder}} </mat-label> <input matInput placeholder={{taxID_placeholder}}
            formControlName="taxID" type="text" maxlength="255">
            <mat-hint *ngIf="form.controls['taxID'].value?.length === 255" class="text-danger font-14">Please
              insert
              no more than 255
            </mat-hint>
          </mat-form-field>
      </div>
      <!-- <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> {{taxID_placeholder}} </mat-label> <input matInput placeholder={{taxID_placeholder}}
            formControlName="taxID" type="text" maxlength="255" required>
            <mat-hint *ngIf="form.controls['taxID'].value?.length === 255" class="text-danger font-14">Please
              insert
              no more than 255
            </mat-hint>
          </mat-form-field>
      </div> -->
      <!-- <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" *ngIf="form.get('type').value === 'CORPORATE'">
        <mat-form-field appearance="outline">
          <mat-label> Company Tax Registration ID </mat-label> <input matInput placeholder="Company Tax Registration ID"
            formControlName="taxID" type="text" maxlength="255">
          <mat-hint *ngIf="form.controls['taxID'].value?.length === 255" class="text-danger font-14">Please
            insert
            no more than 255
          </mat-hint>
        </mat-form-field>
      </div> -->
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Currency </mat-label>
          <mat-select placeholder="Currency" [formControl]="form.controls['currency']" required>
            <mat-option>
              <ngx-mat-select-search (keyup)="applyCurrencyFilter($event.target.value)" [placeholderLabel]="'Currency'"
                [noEntriesFoundLabel]="'No matching records found'" formControlName="currentCurrency" ngDefaultControl>
              </ngx-mat-select-search>
            </mat-option>

            <mat-option *ngFor="let item of newCurrency" [value]="item.display_short"> {{item.display_short}} - {{
              item.display_main }} </mat-option>
          </mat-select>
          <mat-hint *ngIf="form.controls['currency'].hasError('required') && form.controls['currency'].touched"
            class="text-danger font-14">Please choose currency </mat-hint>
        </mat-form-field>
      </div>
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Description </mat-label> <input matInput placeholder="Description" formControlName="description"
            type="text" maxlength="255">
        </mat-form-field>
      </div>
      <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
        <mat-form-field appearance="outline">
          <mat-label> GL Code </mat-label>
          <mat-select placeholder="GL Code" formControlName="glCode" required>
            <mat-option>
              <ngx-mat-select-search (keyup)="applyGLCodeFilter($event.target.value)" [placeholderLabel]="'GL Code'"
                [noEntriesFoundLabel]="'No matching records found'" formControlName="currentGlCode" ngDefaultControl>
              </ngx-mat-select-search>
            </mat-option>

            <mat-option *ngFor="let item of newGlCode1" [value]="item.glcode_guid">{{ item.glcode_name }} </mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>
  </div> <!-- </mat-card-content> </mat-card> -->
</form>