<div class="view-col-table" fxLayout="column">
    <div fxLayout="row wrap" fxLayoutAlign="end">
      <div fxLayout="row wrap" fxLayoutGap="10px" [style.padding]="'5px'">
        <button
          mat-raised-button
          color="primary"
          type="button"
          (click)="onCancelJobs()"
          style="height: 50px"
        >
          Cancel Job
        </button>
      </div>
      <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
        <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
    <div style="height: 100%;">
        <ag-grid-angular #agGrid
        style="height: 100%;"
        class="ag-theme-balham"
        rowModelType="clientSide"
        serverSideStoreType="partial"
        [getRowClass]="pagination.getRowClass"
        [columnDefs]="columnsDefs"
        [rowData]="jobDocs$ | async"
        [paginationPageSize]="pagination.rowPerPage"
        [cacheBlockSize]="pagination.rowPerPage"
        [pagination]="true"
        [animateRows]="true"
        [defaultColDef]="defaultColDef"
        [suppressRowClickSelection]="true"
        [sideBar]="true"
        [groupSelectsChildren]="true"
        [groupDefaultExpanded]="-1"
        [rowHeight]="50"
        [groupDisplayType]="groupDisplayType"
        [groupRowRendererParams]="groupRowRendererParams"
        [getRowStyle]="getRowStyle"
        (gridReady)="onGridReady($event)"
        [rowSelection]="rowSelection"
        [suppressAggFuncInHeader]="true"
        (firstDataRendered)="headerHeightSetter()"
        (columnResized)="headerHeightSetter()"
        >
      </ag-grid-angular>
    </div>
  </div>
  