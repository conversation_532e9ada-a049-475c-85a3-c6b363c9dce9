import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import {
  BinModel,
  BinService,
  bl_fi_generic_doc_line_RowClass, bl_inv_bin_line_RowClass, BranchService,
  GenericDocContainerModel,
  InternalJobOrderService,
  LocationService, Pagination,
  SubQueryService
} from 'blg-akaun-ts-lib';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { forkJoin, iif, Observable, of, zip } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { internalJobOrderSearchModel } from '../../../models/advanced-search-models';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { ProcessActions } from '../../../state-controllers/process-controller/store/actions';
import { ProcessStates } from '../../../state-controllers/process-controller/store/states';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  deactivateReturn: boolean;
  selectedRowGuid: string;
}
@Component({
  selector: 'app-process-select-bin',
  templateUrl: './process-select-bin.component.html',
  styleUrls: ['./process-select-bin.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessSelectBinComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Select Bin';
  protected readonly index = 6;
  protected localState: LocalState;

  prevIndex: number;
  protected prevLocalState: any;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);

  searchModel = internalJobOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    onCellClicked: (params) => this.onCellClicked(params.data)
  };

  gridApi;

  columnsDefs = [
    // {
    //   headerName: '',
    //   field: 'checkBox',
    //   checkboxSelection: true,
    //   minWidth: 50,
    //   width: 50,
    //   flex: 0,
    //   onCellClicked: (params) => null
    // },
    {headerName: 'Location', field: 'location', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Job Order No', field: 'code'},
    {headerName: 'Qty Measure', field: 'container_measure'},
    {headerName: 'Qty Container', field: 'container_qty'},
    // {headerName: 'Remarks', field: 'item_remarks', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'No of Bins', field: 'line_property_json.noOfBins'},
    // {headerName: 'Priority', field: 'line_property_json.priority'},
    // {headerName: 'Status', field: 'stockBalQty', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Customer Name', field: 'customerName', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Creation Date', field: 'bl_fi_generic_doc_hdr.created_date', type: 'rightAligned',
    // valueFormatter: params => moment(params.value).format('YYYY-MM-DD')},
    // {headerName: 'Type', field: 'salesAgent', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Amount Txn', field: 'bl_fi_generic_doc_hdr.amount_txn', type: 'numericColumn',
    // valueFormatter: params => formatMoneyInList(params.value)},
  ];

  columnsDefs$ = this.sessionStore.select(SessionSelectors.selectMasterSettings).pipe(
    map((a: any) => [
      ...this.columnsDefs,
      // a.ENABLE_CUSTOM_STATUS_HDR_1 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_1 ? a.NAME_CUSTOM_STATUS_HDR_1 : 'client_doc_status_01',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_01',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_2 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_2 ? a.NAME_CUSTOM_STATUS_HDR_2 : 'client_doc_status_02',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_02',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_3 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_3 ? a.NAME_CUSTOM_STATUS_HDR_3 : 'client_doc_status_03',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_03',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_4 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_4 ? a.NAME_CUSTOM_STATUS_HDR_4 : 'client_doc_status_04',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_04',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
      // a.ENABLE_CUSTOM_STATUS_HDR_5 ? {
      //   headerName: a.NAME_CUSTOM_STATUS_HDR_5 ? a.NAME_CUSTOM_STATUS_HDR_5 : 'client_doc_status_05',
      //   field: 'bl_fi_generic_doc_hdr.client_doc_status_05',
      //   cellStyle: () => ({'text-align': 'left'})
      // } : {minWidth: 0},
    ])
  );

  SQLGuids: string[] = null;

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private soService: InternalJobOrderService,
    private lctnService: LocationService,
    private sqlService: SubQueryService,
    private binService: BinService,
    private readonly store: Store<InternalJobOrderStates>,
    private readonly processStore: Store<ProcessStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        const sortModel = grid.request.sortModel;
        const filterModel = grid.request.filterModel;
        const sortOn = pageSorting(sortModel);
        const filter = pageFiltering(filterModel);
        this.subs.sink = this.binService.getByCriteria(new Pagination(
          this.SQLGuids ? 0 : grid.request.startRow,
          grid.request.endRow - grid.request.startRow,
          [
            {columnName: 'calcTotalRecords', operator: '=', value: 'true'},
            {columnName: 'orderBy', operator: '=', value: 'updated_date'},
            {columnName: 'order', operator: '=', value: 'DESC'},
            {
              columnName: 'guids',
              operator: '=',
              value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
            }
          ]
        ), apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<BinModel>[] = [];
            b.data.forEach( doc => source.push(
              zip(
                this.lctnService.getByGuid(doc.bl_inv_bin_hdr.guid_location.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                )).pipe(
                  map(([b_a]) => Object.assign({
                    location: b_a.error ? b_a.error.code : b_a.data.bl_inv_mst_location.code
                  }, doc))
                )
            ));
            return iif(() => b.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                b.data = b_inner;
                return b;
              })),
              of(b)
            );
          })
        ).subscribe( resolved => {
          const data = sortOn(resolved.data).filter(entity => filter.by(entity));
          const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : resolved.totalRecords) : data.length;
          const fakeObj = [
            {
              "bl_inv_bin_hdr": 
                {
                  "guid" : "6aff0718-93f2-4f86-ad64-52ef8e4724e8",
                  "guid_branch" : "0a9827d5-d1dd-404c-a899-81bd6c8b262d",
                  "guid_location" : "1a59bba0-32d4-c84a-63f3-8263050aba9a",
                  "bin_type" : null,
                  "code" : "MY_FIRST_BIN",
                  "name" : "MY_FIRST_BIN",
                  "description" : "MY_FIRST_BIN",
                  "status" : "ACTIVE",
                  "revision" : "D5099F94-518E-4C20-8D51-A498A6584D86",
                  "vrsn" : null,
                  "qty_balance" : 32000.0000000000000000000000,
                  "created_by_subject_guid" : "cf11cba1-a491-4432-bb87-2e9f46410612",
                  "updated_by_subject_guid" : "c6bc3fd7-7e80-4e99-a8fa-35872f870a60",
                  "namespace" : null,
                  "module_guid" : null,
                  "applet_guid" : null,
                  "created_date" : "2021-06-30T15:26:06.456Z",
                  "updated_date" : "2021-06-30T15:26:06.456Z",
                  "item_guid" : "d1be92b8-c504-9dde-8821-79fc3c43d5f9"
                }
              ,
              "bl_inv_bin_line": [
                {
                  "guid" : "8a9d05d3-95bc-406d-9d41-3af85ef68f8e",
                  "hdr_guid" : "6aff0718-93f2-4f86-ad64-52ef8e4724e8",
                  "qty" : 11000.0000000000000000000000,
                  "container_qty" : 1.0000000000000000000000,
                  "container_measure" : 10000.0000000000000000000000,
                  "namespace" : null,
                  "module_guid" : null,
                  "applet_guid" : null,
                  "acl_policy" : "{}",
                  "acl_config" : "{}",
                  "created_date" : "2021-06-30T15:26:06.000Z",
                  "updated_date" : "2021-06-30T15:26:06.000Z",
                  "created_by_subject_guid" : null,
                  "updated_by_subject_guid" : null,
                  "status" : "ACTIVE",
                  "revision" : "18f026f6-fb40-4c3f-b29f-c1c41ea8625c",
                  "vrsn" : null,
                  "code" : "211021-1"
                },
                {
                  "guid" : "174a3235-557d-437e-8307-ded61d89af41",
                  "hdr_guid" : "6aff0718-93f2-4f86-ad64-52ef8e4724e8",
                  "qty" : 10600.0000000000000000000000,
                  "container_qty" : 1.0000000000000000000000,
                  "container_measure" : 10000.0000000000000000000000,
                  "namespace" : null,
                  "module_guid" : null,
                  "applet_guid" : null,
                  "acl_policy" : "{}",
                  "acl_config" : "{}",
                  "created_date" : "2021-06-30T15:26:06.000Z",
                  "updated_date" : "2021-06-30T15:26:06.000Z",
                  "created_by_subject_guid" : null,
                  "updated_by_subject_guid" : null,
                  "status" : "ACTIVE",
                  "revision" : "1ea218aa-4285-4bf3-9805-0fb8ec00f5be",
                  "vrsn" : null,
                  "code" : "211021-2"
                },
                {
                  "guid" : "093bec05-e35a-4a8e-a71e-bf8377ea4077",
                  "hdr_guid" : "6aff0718-93f2-4f86-ad64-52ef8e4724e8",
                  "qty" : 10400.0000000000000000000000,
                  "container_qty" : 1.0000000000000000000000,
                  "container_measure" : 10000.0000000000000000000000,
                  "namespace" : null,
                  "module_guid" : null,
                  "applet_guid" : null,
                  "acl_policy" : "{}",
                  "acl_config" : "{}",
                  "created_date" : "2021-06-30T15:26:06.000Z",
                  "updated_date" : "2021-06-30T15:26:06.000Z",
                  "created_by_subject_guid" : null,
                  "updated_by_subject_guid" : null,
                  "status" : "ACTIVE",
                  "revision" : "0ee7e0c5-da75-41ef-a089-f28eeb8a1575",
                  "vrsn" : null,
                  "code" : "211021-3"
                }
              ]}
          ];
          grid.success({
            // rowData: data,
            // rowCount: totalRecords
            rowData: fakeObj.map(f => f.bl_inv_bin_line.map(l => Object.assign({
              guid_location: f.bl_inv_bin_hdr.guid_location,
              guid_branch: f.bl_inv_bin_hdr.guid_branch,
              item_guid: f.bl_inv_bin_hdr.item_guid,
              item_code: 'UAT TEST 1',
              location: 'L-TONN CABLE BRANCH'
            }, l)))[0],
            rowCount: fakeObj.map(f => f.bl_inv_bin_line)[0].length
          });
          // this.gridApi.forEachNode(a => {
          //   if (a.data.bl_fi_generic_doc_hdr.guid === this.localState.selectedRowGuid) {
          //     a.setSelected(true);
          //   }
          // });
        }, err => {
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe( a => {
      if (a) {
        this.gridApi.refreshServerSideStore();
        this.store.dispatch(InternalJobOrderActions.resetAgGrid());
      }
    });
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAddInput: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAdd() {
    console.log(this.gridApi.getSelectedNodes().map(r => r.data));
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe(
        {next: resolve => {
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }}
      );
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  onCellClicked(binLine: bl_inv_bin_line_RowClass) {
    if (binLine) {
      this.processStore.dispatch(ProcessActions.selectBinLine({binLine}));
    //   if (!this.localState.deactivateList) {
    //     this.viewColFacade.updateInstance<LocalState>(this.index, {
    //       ...this.localState, deactivateAdd: false, deactivateList: true, selectedRowGuid: entity.bl_fi_generic_doc_hdr.guid.toString()
    //     });
    //     this.viewColFacade.onNextAndReset(this.index, 1);
    //   }
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
