import { ProcessStates } from '../states';

export const selectCurrentProcess = (state: ProcessStates) => state.process.currentProcess;
export const selectProcessMap = (state: ProcessStates) => state.process.processMap;
export const selectMachineMap = (state: ProcessStates) => state.process.machineMap;
export const selectProcess = (state: ProcessStates) => state.process.selectedProcess;
export const selectBinLine = (state: ProcessStates) => state.process.selectedBinLine;
export const selectProcessForm = (state: ProcessStates) => state.process.updatedProcessForm;
