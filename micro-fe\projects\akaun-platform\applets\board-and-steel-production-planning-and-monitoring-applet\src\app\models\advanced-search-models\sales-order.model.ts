import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const internalSalesOrderSearchModel: SearchModel = {
  label: {
    txnDate: 'Transaction Date',
    expectedDeliveryDate: 'Expected Delivery Date',
    salesOrderNo: 'Sales Order No.',
    customerName: 'Customer Name',
    itemName: 'Item Name',
    itemCode: 'Item Code',
    scenarioOption: 'Scenario Option',
    jobDocStatus: 'Job Doc Status',
    jobOrderNo: 'Job Order No.',
  },
  dataType: {
    txnDate: 'date',
    expectedDeliveryDate: 'date',
    salesOrderNo: 'string',
    customerName: 'string',
    itemCode: 'string',
    itemName: 'string',
    // machineCode: 'string',
    scenarioOption: ['select', ['PRODUCTION_DONE', 'PRODUCTION_IN_PROGRESS', 'PRODUCTION_NOT_STARTED']],
    jobDocStatus: ['select', ['OPEN', 'PROCESSED', 'COMPLETED']],
    jobOrderNo: 'string',
  },
  form: new FormGroup({
    txnDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    expectedDeliveryDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    salesOrderNo: new FormControl(),
    customerName: new FormControl(),
    itemCode: new FormControl(),
    itemName: new FormControl(),
    scenarioOption: new FormControl(),
    jobDocStatus: new FormControl(),
    jobOrderNo: new FormControl(),
  }),

  joins: [
    {
      type: 'LEFT JOIN',
      table: 'bl_fi_generic_doc_line',
      alias: 'genLine',
      onCondition: `hdr.generic_doc_line_guid = genLine.guid`,
      joinOnBasic: true
    },
    {
      type: 'LEFT JOIN',
      table: 'bl_fi_generic_doc_hdr',
      alias: 'genHdr',
      onCondition: `hdr.generic_doc_hdr_guid = genHdr.guid`,
      joinOnBasic: false
    },
    {
      type: 'LEFT JOIN',
      table: 'bl_mrp_job_order_hdr',
      alias: 'jobOrderHdr',
      onCondition: `hdr.job_order_guid = jobOrderHdr.guid`,
      joinOnBasic: false
    },
  ],

  query: (query) => 
  `(genHdr.server_doc_1 ILIKE '%${query}%' OR genHdr.sales_entity_hdr_name ILIKE '%${query}%') OR (genLine.item_code ILIKE '%${query}%' OR genLine.item_name ILIKE '%${query}%') OR (jobOrderHdr.server_doc_1 ILIKE '%${query}%') 
   AND hdr.status = 'ACTIVE'`,

  table: 'bl_mrp_job_order_generic_doc_link',


  queryCallbacks: {
    // docNo: query => query ? `hdr.status='${query}'` : '',
    txnDate: txnDate => {
      if (txnDate.from || txnDate.to) {
          const from = txnDate.from ? `genHdr.date_txn >= '${txnDate.from.format('YYYY-MM-DD')}'` : '';
          const to = txnDate.to ? `genHdr.date_txn <= '${txnDate.to.format('YYYY-MM-DD')}'` : '';
          return `${from} ${(from && to) ? 'AND' : ''} ${to}`;
      }
      return '';
  },
  expectedDeliveryDate: expectedDeliveryDate => {
      if (expectedDeliveryDate.from || expectedDeliveryDate.to) {
          // assign expectedDeliveryDate.from to itself or expectedDeliveryDate.to if null
          const from = expectedDeliveryDate.from ? expectedDeliveryDate.from : expectedDeliveryDate.to;
          // assign creationDate.to to itself or creationDate.from if null
          const to = expectedDeliveryDate.to ? expectedDeliveryDate.to : expectedDeliveryDate.from;
          return `genLine.track_delivery_time_estimated >= '${from.format('YYYY-MM-DD')}' AND genLine.track_delivery_time_estimated <= '${to.format('YYYY-MM-DD')}'`;
      }
      return '';
  },
  salesOrderNo: salesOrderNo => salesOrderNo ? `UPPER(genHdr.server_doc_1) ILIKE UPPER('%${salesOrderNo}%')` : '',
  customerName: customerName => customerName ? `UPPER(genHdr.sales_entity_hdr_name) ILIKE UPPER('%${customerName}%')` : '',
  scenarioOption: query => query ? `hdr.scenario_option='${query}'` : '',
  }
};

