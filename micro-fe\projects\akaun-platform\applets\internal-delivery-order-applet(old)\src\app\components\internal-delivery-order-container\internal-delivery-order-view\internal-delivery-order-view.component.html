<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        View Internal Delivery Order
      </span>
    </div>
    <div fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="5px">
      <button mat-raised-button color="primary" type="button"
      (click)="onReset()">RESET</button>
      <button mat-raised-button color="primary" type="button"
      (click)="onPostToFinal()">FINAL</button>
      <button mat-raised-button color="primary" type="button"
      [disabled]="disableButton()"
      (click)="onSave()">SAVE</button>
    </div>
  </div>
</mat-card-title>
<mat-tab-group [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Main">
    <app-internal-delivery-order-create-main [draft$]="draft$" (updateMain)="onUpdateMain($event)"></app-internal-delivery-order-create-main>
  </mat-tab>
  <mat-tab label="Account">
    <app-internal-delivery-order-create-account [toggleColumn$]="toggleColumn$" [hdrDraft$]="hdrDraft$" [childSelectedIndex$]="childSelectedIndex$" [draft$]="draft$"
    (entity)="onEntity()" (shippingInfo)="onShippingInfo()" (billingInfo)="onBillingInfo()" (contactPerson)="onContactPerson()" (selectShipping)="goToSelectShipping()"
    (updateShipTo)="onUpdateShippingInfo($event)" (updateShippingAddress)="onUpdateShippingAddress($event)"></app-internal-delivery-order-create-account>
  </mat-tab>
  <mat-tab label="Custom Fields">
    <app-internal-delivery-order-create-custom-fields [customFields]="customFields$ | async" [draft$]="draft$"
    (updateCustomFields)="onUpdateCustomFields($event)" (changeCustomFields)="onChangeCustomFields($event)"></app-internal-delivery-order-create-custom-fields>
  </mat-tab>
  <mat-tab label="Line Items">
    <app-internal-delivery-order-create-line-items [localState]="localState$ | async" [rowData]="updatedRowData" (next)="onNextAdd()" (lineItem)="onLineItem($event)"></app-internal-delivery-order-create-line-items>
  </mat-tab>
  <mat-tab label="Delivery Details">
    <div class="with-delete">
      <app-internal-delivery-order-delivery-details [draft$]="hdr$" [rowData]="pns$ | async"></app-internal-delivery-order-delivery-details>
    </div>
  </mat-tab>
  <mat-tab label="Department Hdr">
    <app-internal-delivery-order-create-department [draft$]="draft$" (updateDepartment)="onUpdateDepartment($event)"></app-internal-delivery-order-create-department>
  </mat-tab>
  <mat-tab label="Export">
    <app-internal-delivery-order-export [draft$]="draft$"></app-internal-delivery-order-export>
  </mat-tab>
  <mat-tab label="Status">
    <app-internal-delivery-order-custom-status [draft$]="draft$" [appletSettings$]="appletSettings$"
      (updateStatus)="onUpdateStatus($event)"></app-internal-delivery-order-custom-status>
  </mat-tab>
</mat-tab-group>
<div style="padding: 5px;">
  <button mat-raised-button color="warn" type="button" (click)="onDelete()">
    <span>{{ (deleteConfirmation$ | async) ? 'CLICK AGAIN TO CONFIRM' : 'DELETE'}}</span>
  </button>
</div>
