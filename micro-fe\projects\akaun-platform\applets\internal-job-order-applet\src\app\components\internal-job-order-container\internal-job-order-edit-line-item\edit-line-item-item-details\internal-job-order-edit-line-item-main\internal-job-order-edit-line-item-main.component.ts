import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { bl_fi_generic_doc_line_RowClass, FinancialItemContainerModel, TaxCodeContainerModel } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { combineLatest, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-internal-job-order-edit-line-item-main',
  templateUrl: './internal-job-order-edit-line-item-main.component.html',
  styleUrls: ['./internal-job-order-edit-line-item-main.component.css']
})
export class InternalJobOrderEditLineItemMainComponent implements OnInit, OnDestroy {

  @Input() line$: Observable<bl_fi_generic_doc_line_RowClass>;
  @Input() item$: Observable<FinancialItemContainerModel>;
  @Input() tax$: Observable<{
    sst: TaxCodeContainerModel[],
    wht: TaxCodeContainerModel[],
  }>;

  @Output() customer = new EventEmitter();
  @Output() shippingInfo = new EventEmitter();
  @Output() billingInfo = new EventEmitter();
  apiVisa = AppConfig.apiVisa;

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Item Code*', formControl: 'itemCode', type: 'text', readonly: true, hint: ''},
    {label: 'Item Name*', formControl: 'itemName', type: 'text', readonly: true, hint: ''},
    {label: 'Quantity*', formControl: 'quantity', type: 'quantity', readonly: false, hint: ''},
    {label: 'Unit Price*', formControl: 'unitPrice', type: 'unitPrice', readonly: false, hint: 'Unit Discount x Quantity'},
    {label: 'Unit Discount', formControl: 'unitDiscount', type: 'unitDiscount', readonly: false, hint: ''},
    {label: 'Discount Amount', formControl: 'discountAmount', type: 'discountAmount', readonly: false, hint: ''},
    {label: 'STD Amount*', formControl: 'stdAmount', type: 'money', readonly: true, hint: 'Unit Price x Quantity'},
    {label: 'Net Amount*', formControl: 'netAmount', type: 'netAmount', readonly: false, hint: 'STD Amount - Discount Amount'},
    {label: 'SST/GST/VAT Code', formControl: 'sstCode', type: 'sstCode', readonly: false, hint: ''},
    {label: 'SST/GST/VAT', formControl: 'sst', type: 'money', readonly: true, hint: ''},
    {label: 'Tax Amount', formControl: 'taxAmount', type: 'money', readonly: true, hint: 'SST/GST/VAT x Net Amount'},
    {label: 'WHT Code', formControl: 'whtCode', type: 'whtCode', readonly: false, hint: ''},
    {label: 'WHT', formControl: 'wht', type: 'money', readonly: true, hint: ''},
    {label: 'WHT Amount', formControl: 'whtAmount', type: 'money', readonly: true, hint: 'WHT x Net Amount'},
    {label: 'Txn Amount*', formControl: 'txnAmount', type: 'txnAmount', readonly: false, hint: 'Net Amount + Tax Amount - WHT Amount'},
  ];

  filteredUnitPrice: Observable<string[]>;
  filteredSST: Observable<TaxCodeContainerModel[]>;
  filteredWHT: Observable<TaxCodeContainerModel[]>;

  taxList: TaxCodeContainerModel[] = [];
  whtList: TaxCodeContainerModel[] = [];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      itemGuid: new FormControl(),
      itemCode: new FormControl('', Validators.compose([Validators.required])),
      itemName: new FormControl('', Validators.compose([Validators.required])),
      quantity: new FormControl(0, Validators.compose([Validators.required, Validators.min(1)])),
      unitPrice: new FormControl('0.00', Validators.compose([Validators.required, Validators.min(0.01)])),
      unitDiscount: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      stdAmount: new FormControl('0.00', Validators.compose([Validators.required, Validators.min(0)])),
      discountAmount: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      netAmount: new FormControl('0.00', Validators.compose([Validators.required, Validators.min(0)])),
      sstCode: new FormControl(''),
      sst: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      whtCode: new FormControl(''),
      wht: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      taxAmount: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      whtAmount: new FormControl('0.00', Validators.compose([Validators.min(0)])),
      txnAmount: new FormControl('0.00', Validators.compose([Validators.required, Validators.min(0.01)])),
      remarks: new FormControl(),
      uom: new FormControl(),
    });
    // TODO: Optimize this
    this.subs.sink = this.tax$.subscribe({next: resolve => {
      this.taxList = resolve?.sst;
      this.whtList = resolve?.wht;
    }})
    this.subs.sink = combineLatest([this.item$, this.tax$, this.line$]).pipe(
    ).subscribe({next: ([item, tax, line]) => {
      this.form.patchValue({
        itemGuid: item?.bl_fi_mst_item_hdr.guid,
        itemCode: item?.bl_fi_mst_item_hdr.code,
        itemName: item?.bl_fi_mst_item_hdr.name,
        quantity: line.quantity_base,
        unitPrice: ((parseFloat(line.amount_net.toString()) + parseFloat(line.amount_discount.toString())) / parseFloat(line.quantity_base.toString())).toFixed(2),
        stdAmount: parseFloat(line.amount_std.toString()).toFixed(2),
        discountAmount: parseFloat(line.amount_discount.toString()).toFixed(2),
        netAmount: parseFloat(line.amount_net.toString()).toFixed(2),
        sstCode: line.tax_gst_code,
        sst: !line.tax_gst_code ? '0.00' : (parseFloat(tax.sst.find(w =>
          w.bl_fi_cfg_tax_code.tax_code === line.tax_gst_code)?.bl_fi_cfg_tax_code.tax_rate_txn.toString()) / 100).toFixed(2),
        whtCode: line.tax_wht_code,
        wht: !line.tax_wht_code ? '0.00' : (parseFloat(tax.wht.find(w =>
          w.bl_fi_cfg_tax_code.tax_code === line.tax_wht_code)?.bl_fi_cfg_tax_code.tax_rate_txn.toString()) / 100).toFixed(2),
        taxAmount: parseFloat(line.amount_tax_gst.toString()).toFixed(2),
        whtAmount: parseFloat(line.amount_tax_wht.toString()).toFixed(2),
        txnAmount: line.amount_txn,
        remarks: line.item_remarks,
        uom: item?.bl_fi_mst_item_hdr.uom
      });
      this.recalculate();
    }});
    this.filteredUnitPrice = combineLatest([this.form.controls['unitPrice'].valueChanges, this.item$]).pipe(
      map(([a, b]) => {
        if (a) {
          return (<any>b.bl_fi_mst_item_hdr?.price_json)?.item_base_uom_pricing.filter(option => option.price_amount.includes(a.toString()))
        } else {
          return (<any>b.bl_fi_mst_item_hdr?.price_json)?.item_base_uom_pricing;
        }
      })
    )
    this.filteredSST = combineLatest([this.form.controls['sstCode'].valueChanges, this.tax$]).pipe(
      map(([a, b]) => {
        if (a) {
          return b.sst.filter(s => s.bl_fi_cfg_tax_code.tax_code.includes(a.toString()))
        } else {
          return b.sst
        }
      })
    )
    this.filteredWHT = combineLatest([this.form.controls['whtCode'].valueChanges, this.tax$]).pipe(
      map(([a, b]) => {
        if (a) {
          return b.wht.filter(s => s.bl_fi_cfg_tax_code.tax_code.includes(a.toString()))
        } else {
          return b.wht
        }
      })
    )
    this.onCalculate();
  }

  onCalculateFromUnitDisc() {
    const quantity = parseInt(this.form.controls['quantity'].value);
    const sst = parseFloat(this.form.controls['sst'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const unitDiscount = parseFloat(this.form.controls['unitDiscount'].value);
    // const netAmount = parseFloat(this.form.controls['netAmount'].value);
    // const taxAmount = parseFloat(this.form.controls['taxAmount'].value);
    // const whtAmount = parseFloat(this.form.controls['whtAmount'].value);
    // const discountAmount = parseFloat(this.form.controls['discountAmount'].value);
    // const stdAmount = parseFloat(this.form.controls['stdAmount'].value);
    this.form.controls['discountAmount'].setValue((quantity * unitDiscount).toFixed(2));
    this.form.controls['netAmount'].setValue(((unitPrice - unitDiscount) * quantity).toFixed(2));
    this.form.controls['txnAmount'].setValue(((unitPrice - unitDiscount) * quantity * (1 + sst - wht)).toFixed(2));
    // this.form.controls['whtAmount'].setValue((netAmount * wht).toFixed(2));
    // this.form.controls['stdAmount'].setValue((quantity * unitPrice).toFixed(2));
  }

  onCalculateFromAmountDisc() {
    const quantity = parseInt(this.form.controls['quantity'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const taxAmount = parseFloat(this.form.controls['taxAmount'].value);
    const whtAmount = parseFloat(this.form.controls['whtAmount'].value);
    const discountAmount = parseFloat(this.form.controls['discountAmount'].value);
    this.form.controls['unitDiscount'].setValue((discountAmount / quantity).toFixed(2));
    this.form.controls['netAmount'].setValue((unitPrice * quantity - discountAmount).toFixed(2));
    this.form.controls['txnAmount'].setValue(((unitPrice * quantity) - discountAmount + taxAmount - whtAmount).toFixed(2));
  }

  onCalculateFromAmountNet() {
    const quantity = parseInt(this.form.controls['quantity'].value);
    const sst = parseFloat(this.form.controls['sst'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const netAmount = parseFloat(this.form.controls['netAmount'].value);
    const stdAmount = parseFloat(this.form.controls['stdAmount'].value);
    this.form.controls['unitDiscount'].setValue((unitPrice - (netAmount / quantity)).toFixed(2));
    this.form.controls['discountAmount'].setValue((stdAmount - netAmount).toFixed(2));
    this.form.controls['txnAmount'].setValue((netAmount * (1 + sst - wht)).toFixed(2));
  }

  onCalculateFromAmountTxn() {
    const quantity = parseInt(this.form.controls['quantity'].value);
    const sst = parseFloat(this.form.controls['sst'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const txnAmount = parseFloat(this.form.controls['txnAmount'].value);
    const taxAmount = parseFloat(this.form.controls['taxAmount'].value);
    const whtAmount = parseFloat(this.form.controls['whtAmount'].value);
    const stdAmount = parseFloat(this.form.controls['stdAmount'].value);
    this.form.controls['unitDiscount'].setValue((unitPrice - (txnAmount - taxAmount + whtAmount) / quantity).toFixed(2));
    this.form.controls['discountAmount'].setValue((stdAmount - (txnAmount - taxAmount + whtAmount)).toFixed(2));
    this.form.controls['netAmount'].setValue((txnAmount / (1 + sst - wht)).toFixed(2));
  }

  onCalculate() {
    const quantity = parseInt(this.form.controls['quantity'].value);
    const sst = parseFloat(this.form.controls['sst'].value);
    const unitPrice = parseFloat(this.form.controls['unitPrice'].value);
    const wht = parseFloat(this.form.controls['wht'].value);
    const unitDiscount = parseFloat(this.form.controls['unitDiscount'].value);
    const discountAmount = parseFloat(this.form.controls['discountAmount'].value);
    this.form.controls['netAmount'].setValue((quantity * unitPrice - discountAmount).toFixed(2));
    this.form.controls['taxAmount'].setValue(((quantity * unitPrice - discountAmount) * sst).toFixed(2));
    this.form.controls['whtAmount'].setValue(((quantity * unitPrice - discountAmount) * wht).toFixed(2));
    this.form.controls['stdAmount'].setValue((quantity * unitPrice).toFixed(2));
    this.form.controls['discountAmount'].setValue((quantity * unitDiscount).toFixed(2));
    this.form.controls['txnAmount'].setValue(((1 + sst - wht) * ((quantity * unitPrice) - discountAmount)).toFixed(2));
  }

  recalculate() {
    const quantity = parseInt(this.form.controls['quantity'].value);
    const netAmount = parseFloat(this.form.controls['netAmount'].value);
    const discountAmount = parseFloat(this.form.controls['discountAmount'].value);
    this.form.controls['unitDiscount'].setValue((discountAmount / quantity).toFixed(2));
    this.form.controls['unitPrice'].setValue(((netAmount + discountAmount) / quantity).toFixed(2));
  }

  nullHandler(x: string, value: string) {
    if (x === 'quantity') {
      value ? this.form.controls[x].setValue(value) : this.form.controls[x].setValue(0);
    } else {
      value ? this.form.controls[x].setValue(parseFloat(value).toFixed(2)) : this.form.controls[x].setValue('0.00')
    }
  }

  onParseDelay(x: FormControl, e: string) {
    setTimeout(() => {
      if (x.value === null) {
        x.setValue('0.00');
      }
      if (typeof(x.value) === 'number' && x.value.toFixed(2) === parseFloat(e).toFixed(2)) {
        e ? x.setValue(parseFloat(e).toFixed(2)) : x.setValue('0.00');
      }
      this.onCalculate();
    }, 100)
  }

  onSST(e: string) {
    this.form.controls['sst'].setValue(
      (parseFloat(this.taxList.find(s => s.bl_fi_cfg_tax_code.tax_code === e).bl_fi_cfg_tax_code.tax_rate_txn.toString()) / 100).toFixed(2)
    )
    this.onCalculate();
  }

  onWHT(e: string) {
    this.form.controls['wht'].setValue(
      (parseFloat(this.whtList.find(s => s.bl_fi_cfg_tax_code.tax_code === e).bl_fi_cfg_tax_code.tax_rate_txn.toString()) / 100).toFixed(2)
    )
    this.onCalculate();
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
