import { Action, createReducer, on } from '@ngrx/store';
import { AttachmentActions } from '../../../draft-controller/store/actions';
import { PickPackQueueActions } from '../actions';
import { initState, PickPackQueueState } from '../states/pick-pack-queue-states';

export const pickPackQueueFeatureKey = 'PickPackQueue';

export const PickPackQueueReducer = createReducer(
  initState,
  on(PickPackQueueActions.loadPickPackQueueSuccess, (state, action) => ({
    ...state, loadedGenDocs: action.PickPackQueues.map(pickPackQueue => ({
      ...pickPackQueue,
      bl_fi_generic_doc_lines: pickPackQueue.bl_fi_generic_doc_lines[0],
      bl_fi_pick_pack_queues: pickPackQueue.bl_fi_pick_pack_queues[0],
      bl_inv_current_location_stock_balances: pickPackQueue.bl_inv_current_location_stock_balances?.[0]?.[0],
      bl_inv_current_company_stock_balances: pickPackQueue.bl_inv_current_company_stock_balances?.[0]?.[0],
    }))
  })),

 

  on(PickPackQueueActions.selectEntityInit, (state, action) => ({
    ...state, selectedEntity: action.entity
  })),

  on(PickPackQueueActions.selectCustomer, (state, action) => ({
    ...state, selectedCustomer: action.entity.entity
  })),

  on(PickPackQueueActions.selectCustomerEdit, (state, action) => ({
    ...state, selectedCustomer: action.entity.entity
  })),

  on(PickPackQueueActions.selectShippingAddress, (state, action) => ({
    ...state, selectedShippingAddress: action.shipping_address
  })),

  on(PickPackQueueActions.selectShippingAddressEdit, (state, action) => ({
    ...state, selectedShippingAddress: action.shipping_address
  })),

  on(PickPackQueueActions.selectBillingAddress, (state, action) => ({
    ...state, selectedBillingAddress: action.billing_address
  })),

  on(PickPackQueueActions.selectBillingAddressEdit, (state, action) => ({
    ...state, selectedBillingAddress: action.billing_address
  })),

  on(PickPackQueueActions.selectItem, (state, action) => ({
    ...state,
    selectedItem: action.entity
  })),

  on(PickPackQueueActions.selectLineItemInit, (state, action) => ({
    ...state,
    selectedLineItem: action.line
  })),

  on(PickPackQueueActions.createJobFromDeliveryOrderSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),


  on(PickPackQueueActions.createPickPackQueueSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),

  on(PickPackQueueActions.deletePickPackQueueSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),

  on(PickPackQueueActions.editPickPackQueueSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),

  on(PickPackQueueActions.convertSalesSOSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),

  on(PickPackQueueActions.resetAgGrid, (state, action) => ({
    ...state, refreshGenDocListing: false
  })),

  on(PickPackQueueActions.selectEntityCustomerSuccess, (state, action) => ({
    ...state, selectedCustomer: action.entity
  })),

  on(PickPackQueueActions.selectLineItemSuccess, (state, action) => ({
    ...state, selectedItem: action.entity
  })),

  on(PickPackQueueActions.resetPickPackQueue, (state, action) => ({
    ...state,
    selectedCustomer: null,
    selectedBillingAddress: null,
    selectedShippingAddress: null,
  })),

  on(PickPackQueueActions.resetPickPackQueueEdit, (state, action) => ({
    ...state,
    selectedCustomerEdit: null,
    selectedBillingAddressEdit: null,
    selectedShippingAddressEdit: null,
  })),

  on(AttachmentActions.uploadAttachmentsSuccess, (state, action) => ({
    ...state,
    selectedEntity: {
      ...state.selectedEntity,
      bl_fi_generic_doc_ext: [...state.selectedEntity.bl_fi_generic_doc_ext, ...action.ext]
    }
  })),

  on(PickPackQueueActions.selectContraDoc, (state, action) => ({
    ...state,
    selectedContraDoc: action.entity
  })),

  on(PickPackQueueActions.selectContraLink, (state, action) => ({
    ...state,
    selectedContraLink: action.link
  })),

  on(PickPackQueueActions.selectDetailsInit, (state, action) => ({
    ...state, selectedDetails: action.entity
  })),

  on(PickPackQueueActions.selectShopeeGetShipParam, (state, action) => ({
    ...state, shopeeGetShipParam: action.shipParam
  })),

  on(PickPackQueueActions.selectDetailsReset, (state, action) => ({
    ...state, selectedDetails: null
  })),

  // For Serial, Bin, batch
  on(PickPackQueueActions.getInvItem, (state, action) => ({
    ...state, selectedItem: action.entity
  })),

  on(PickPackQueueActions.selectInvItem, (state, action) => ({
    ...state,
    selectedInvItem: action.invItem
  })),

  on(PickPackQueueActions.selectSerial, (state, action) => ({
    ...state,
    selectedSerial: action.serial
  })),

  on(PickPackQueueActions.selectBatch, (state, action) => ({
    ...state,
    selectedBatch: action.batch
  })),

  on(PickPackQueueActions.selectBin, (state, action) => ({
    ...state,
    selectedBin: action.bin
  })),

  on(PickPackQueueActions.setPickPackQueueInLineItem, (state, action) => ({
    ...state,
    selectedEntity: action.entity
  })),

  on(PickPackQueueActions.selectPricingSchemeLinkSuccess, (state, action) =>
  ({
    ...state, pricingSchemeLink: action.pricing
  })),

  on(PickPackQueueActions.selectPricingScheme, (state, action) => ({
    ...state,
    selectedPricingScheme: action.pricingScheme
  })),

  on(PickPackQueueActions.updatePostingStatusSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),

  on(PickPackQueueActions.setEditMode, (state, action) => ({
    ...state, editMode: action.editMode
  })),
  
  on(PickPackQueueActions.updateKnockoffListingConfig, (state, action) => ({
    ...state, 
    knockoffListingConfig: action.settings
  })), 
  on(PickPackQueueActions.bulkUpdateToPackedLZDSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(PickPackQueueActions.bulkUpdateToShipLZDSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(PickPackQueueActions.editGenLineItemSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(PickPackQueueActions.selectOrder, (state, action) =>
  ({ ...state, selectedOrder: action.genDoc })),
  
  on(PickPackQueueActions.selectCompanyGuid, (state, action) => ({
    ...state,
    selectedCompGuid: action.compGuid
  })),
  on(PickPackQueueActions.selectGUID, (state, action) => ({
    ...state,
    selectedGuid: action.guid
  })),
  on(PickPackQueueActions.storeMembershipGuid, (state, action) => ({
    ...state,
    selectedMembershipGuid: action.guid
  })),
  on(PickPackQueueActions.selectPricingSchemeGUID, (state, action) => ({
    ...state,
    selectedPricingSchemeGUID: action.guid
  })),
);

export function reducer(state: PickPackQueueState | undefined, action: Action) {
  return PickPackQueueReducer(state, action);
}
