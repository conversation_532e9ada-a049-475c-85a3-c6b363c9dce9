const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/operator-applet/runtime-es2015.js',
    './dist/operator-applet/polyfills-es2015.js',
    './dist/operator-applet/scripts.js',
    './dist/operator-applet/main-es2015.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/operator-applet');
  await concat(files, './elements/akaun-platform/applets/operator-applet/operator-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
