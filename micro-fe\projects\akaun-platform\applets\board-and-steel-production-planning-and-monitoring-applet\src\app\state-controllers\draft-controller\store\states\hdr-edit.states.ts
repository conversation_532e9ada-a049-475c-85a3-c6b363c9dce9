import { bl_fi_generic_doc_hdr_RowClass, bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';

export const initState: bl_mrp_job_order_hdr_RowClass = {
    guid: '',
    namespace: '',
    module_guid: '',
    applet_guid: '',
    server_doc_type: '',
    client_doc_type: '',
    guid_parent: '',
    guid_comp: '',
    guid_branch: '',
    guid_store: '',
    guid_profit_center: '',
    guid_segment: '',
    guid_project: '',
    guid_dimension: '',
    guid_glcode: '',
    label_json: null,
    foreign_ccy: '',
    base_doc_line_guid: '',
    base_doc_line_ccy: false,
    base_doc_line_xrate: 0,
    doc_ccy: '',
    amount_cogs: 0,
    amount_std: 0,
    amount_discount: 0,
    amount_net: 0,
    tax_gst_code: '',
    tax_gst_type: '',
    tax_gst_rate: 0,
    amount_tax_gst: 0,
    tax_wht_type: '',
    tax_wht_code: '',
    tax_wht_rate: 0,
    amount_tax_wht: 0,
    amount_txn: 0,
    amount_json: null,
    amount_open_balance: 0,
    amount_signum: 0,
    item_guid: '',
    item_code: '',
    item_machine_code: '',
    item_name: '',
    item_desc: '',
    item_remarks: '',
    item_reference_no: '',
    item_custom_template_json: null,
    item_custom_data_json: null,
    item_property_json: null,
    item_child_json: null,
    item_to_item_link_json: null,
    sort_code: '',
    line_option_json: null,
    txn_type: '',
    txn_code: '',
    reference_no: '',
    quantity_signum: 0,
    quantity_base: 0,
    unit_price_by_uom: 0,
    qty_by_uom: 0,
    unit_disc_by_uom: 0,
    client_key: '',
    client_source: '',
    client_hashed_value: '',
    uom_json: null,
    line_property_json: null,
    position_id: '',
    date_txn: '',
    log_json: null,
    created_date: '',
    updated_date: '',
    created_by_subject_guid: '',
    updated_by_subject_guid: '',
    status: '',
    revision: '',
    vrsn: '',
    acl_config: null,
    acl_policy: null,
    qty_open: 0,
    serial_no: null,
    batch_no: null,
    t2t_doc_hdr_guid: '',
    t2t_doc_line_guid: '',
    client_data_source: '',
    client_item_guid: '',
    client_item_code: '',
    entity_line_guid: '',
    point_type: '',
    point_amount: 0,
    point_currency: '',
    entity_fi_item_link_guid: '',
    entity_inv_item_link_guid: '',
    t2t_tenant_guid: '',
    coupon_hdr_guid: '',
    coupon_line_guid: '',
    item_txn_type: '',
    item_sub_type: '',
    client_value: '',
    client_doc_status_01: '',
    client_doc_status_02: '',
    client_doc_status_03: '',
    client_doc_status_04: '',
    client_doc_status_05: '',
    guid_store_2: '',
    arap_pns_amount: 0,
    arap_stlm_amount: 0,
    tracking_id: '',
    mrp_prodsys_hdr_guid: '',
    uom: '',
    container_qty: 0,
    ad_hoc_qty: 0,
    total_container_measure: 0,
    process_status: '',
    estimated_pkg_date: '',
    completion_date: '',
    remarks: '',
    qc_output_status: '',
    server_doc_1: '',
    template_group_guid: ''
};
