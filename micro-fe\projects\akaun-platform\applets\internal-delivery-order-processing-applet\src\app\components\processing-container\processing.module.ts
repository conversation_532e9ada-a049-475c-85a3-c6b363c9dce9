import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { FirstColumnDirective } from './first-column.directive';
import { SecondColumnDirective } from './second-column.directive';
import { BlgAkaunNgLibModule } from 'blg-akaun-ng-lib';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { processingFeatureKey } from '../../state-controllers/processing-controller/store/reducers/processing.reducers';
import { reducers } from '../../state-controllers/processing-controller/store/reducers';
import { ProcessingEffects } from '../../state-controllers/processing-controller/store/effects/processing.effects';
import { AgGridModule } from 'ag-grid-angular';
import { ProcessingListingComponent } from './processing-listing/processing-listing.component';
import { ProcessingContainerComponent } from './processing-container.component';
import { ProcessingViewComponent } from './processing-view/processing-view.component';
import { ProcessingViewMainComponent } from './processing-view/processing-view-main/processing-view-main.component';
import { ProcessingViewLineItemsComponent } from './processing-view/processing-view-line-items/processing-view-line-items.component';

@NgModule({
  declarations: [
    FirstColumnDirective,
    SecondColumnDirective,
    ProcessingContainerComponent,
    ProcessingListingComponent,
    ProcessingViewComponent,
    ProcessingViewMainComponent,
    ProcessingViewLineItemsComponent
  ],
  imports: [
    CommonModule,
    UtilitiesModule,
    AgGridModule,
    BlgAkaunNgLibModule,
    StoreModule.forFeature(processingFeatureKey, reducers.processing),
    EffectsModule.forFeature([ProcessingEffects])
  ]
})
export class ProcessingModule { }
