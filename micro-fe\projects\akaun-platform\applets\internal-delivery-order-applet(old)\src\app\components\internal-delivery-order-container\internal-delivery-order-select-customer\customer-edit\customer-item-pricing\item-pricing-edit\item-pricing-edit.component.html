<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div> <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()"> <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px"> </button> <span> {{bread}} </span> </div>
    <button mat-raised-button type="button" (click)="onSave()" [disabled]="disableButton()"
      color={{isClicked}}>{{addSuccess}}</button>
  </div>
</mat-card-title>
<form [formGroup]="form" #formDirectives="ngForm">
  <mat-tab-group [dynamicHeight]="true">
    <mat-tab label="Main">
      <div fxLayout="column" class="view-col-forms">
        <div fxLayout="raw wrap" fxFlexAlign="center" class="row">
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Item Code</mat-label>
              <input matInput placeholder="Category Code" [formControl]="form.controls['itemCode']">
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Item Name</mat-label>
              <input matInput placeholder="Category Name" [formControl]="form.controls['itemName']">
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Customer Item Code</mat-label>
              <input matInput placeholder="Customer Code" [formControl]="form.controls['customerCode']">
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Customer Item Name</mat-label>
              <input matInput placeholder="Customer Name" [formControl]="form.controls['customerName']">
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <app-currency [(currency)]="form.controls['currency']"></app-currency>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Purchase Price*</mat-label>
              <input matInput placeholder="Purchase Price" name="myDecimal" placeholder="Decimal" ng-model="myDecimal | number : 2" [formControl]="form.controls['purchasePrice']" type="number">
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Sales Price*</mat-label>
              <input matInput placeholder="Sales Price" [formControl]="form.controls['salesPrice']" type="number">
            </mat-form-field>
          </div>
        </div>
        <!-- <div class=" center" style="margin-top: 10px; width: 50px;">
          <button mat-raised-button color="warn" type="button" (click)="onRemove()" disabled="true">Remove </button>
        </div> -->
      </div>
    </mat-tab>
  </mat-tab-group>
</form>