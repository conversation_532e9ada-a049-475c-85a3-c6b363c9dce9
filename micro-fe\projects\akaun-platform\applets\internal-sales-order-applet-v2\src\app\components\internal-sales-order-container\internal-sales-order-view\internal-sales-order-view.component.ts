import { Component, ChangeDetectionStrategy, ViewChild, ViewChildren, ElementRef, AfterViewChecked } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass, BranchService, CompanyEntitySalesAvailableCreditLimitService, GenericDocARAPContainerModel, GenericDocContainerModel, InternalSalesOrderService, LazadaGetShipmentProviderService, MoneyDatatypeInterface, bl_fi_generic_doc_hdr_RowInterface } from 'blg-akaun-ts-lib';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { combineLatest, EMPTY, iif, Observable, of } from 'rxjs';
import { delay, map, mergeMap, tap, withLatestFrom, take  } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { BillingInfo, InternalSOStatus, InternalSODepartment, InternalSOMain, ShippingInfo, InternalSODelivery, BillingAddress, ShippingAddress } from '../../../models/internal-sales-order.model';
import { HDREditActions, MarketplaceStatusActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDREditSelectors, MarketplaceStatusSelectors, PNSEditSelectors, SettlementEditSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalSalesOrderActions } from '../../../state-controllers/internal-sales-order-controller/store/actions';
import { InternalSalesOrderSelectors } from '../../../state-controllers/internal-sales-order-controller/store/selectors';
import { InternalSalesOrderStates } from '../../../state-controllers/internal-sales-order-controller/store/states';
import { InternalSalesOrderCreateAccountComponent } from '../internal-sales-order-create/internal-sales-order-create-account/internal-sales-order-create-account.component';
import { InternalSalesOrderCreateLineItemsComponent } from '../internal-sales-order-create/internal-sales-order-create-line-items/internal-sales-order-create-line-items.component';
import { InternalSalesOrderCreateMainComponent } from '../internal-sales-order-create/internal-sales-order-create-main/internal-sales-order-create-main.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { ClientSidePermissionsSelectors } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors';
import { AppletSettings } from '../../../models/applet-settings.model';
import { SalesOrderReturnPopUpComponent } from '../internal-sales-order-create/return-popup/popup.component';
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import * as moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { AppletServiceMVVM } from '../../../services/applet-service.component';
import { CloseConfirmDialogComponent } from 'projects/shared-utilities/utilities/close-confirmation-popup/close-confirm-dialog.component';
import { DeliveryDetailsComponent } from '../internal-sales-order-delivery-details/delivery-details.component';
import { AkaunDiscardDialogComponent } from 'projects/shared-utilities/dialogues/akaun-discard-dialog/akaun-discard-dialog.component';
import { AkaunVoidDialogComponent } from "projects/shared-utilities/dialogues/akaun-void-dialog/akaun-void-dialog.component";
import { SearchDocumentsComponent } from './search-documents/search-documents.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateReturn: boolean;
  deactivateCustomer: boolean;
  deactivateSalesAgent: boolean;
  deactivateShippingInfo: boolean;
  deactivateBillingInfo: boolean;
  deactivateLineItem: boolean;
  deactivateSettlement: boolean;
  deactivateAddContra: boolean;
  deactivateViewContra: boolean;
  deactivateAddAttachments: boolean;
  selectedIndex: number;
  childSelectedIndex: number;
  selectedLineItemRowIndex: number;
  deleteConfirmation: boolean;
  deliveryDetailSelectedIndex: number;
  expandedPanel: number;
  selectedSearchIndex: number;
}

interface CreditLimit {
  entity_hdr_guid?: string;
  available_credit_limt?: number;
  new_available_credit_limt?: number;
  customer_code?: string;
  generic_doc_guids?: string[];
  generic_doc_hdr_server_docs?: string[];
}

interface creditLimitInputDto {
  entity_guids: string[];
  entity_credit_limit_guid: string;
  company_guid: string;
}

@Component({
  selector: 'app-internal-sales-order-view',
  templateUrl: './internal-sales-order-view.component.html',
  styleUrls: ['./internal-sales-order-view.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class InternalSalesOrderViewComponent extends ViewColumnComponent implements AfterViewChecked {

  protected subs = new SubSink();

  protected compName = 'Internal Sales Order View';
  protected index = 1;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deactivateCustomer$ = this.componentStore.select(state => state.deactivateCustomer);
  readonly deactivateSalesAgent$ = this.componentStore.select(state => state.deactivateSalesAgent);
  readonly deactivateShippingInfo$ = this.componentStore.select(state => state.deactivateShippingInfo);
  readonly deactivateBillingInfo$ = this.componentStore.select(state => state.deactivateBillingInfo);
  readonly deactivateLineItem$ = this.componentStore.select(state => state.deactivateLineItem);
  readonly deactivateSettlement$ = this.componentStore.select(state => state.deactivateSettlement);
  readonly deactivateAddContra$ = this.componentStore.select(state => state.deactivateAddContra);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly childSelectedIndex$ = this.componentStore.select(state => state.childSelectedIndex);
  readonly deleteConfirmation$ = this.componentStore.select(state => state.deleteConfirmation);
  readonly selectedSearchIndex$ = this.componentStore.select(state => state.selectedSearchIndex);
  readonly userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );
  readonly deliveryDetailSelectedIndex$ = this.componentStore.select(state => state.deliveryDetailSelectedIndex);
  readonly expandedPanel$ = this.componentStore.select(state => state.expandedPanel);
  prevIndex: number;
  protected prevLocalState: any;
  doc_type;
  hdrGuid;
  hdr;
  selectedHdr;
  appletSettings: AppletSettings;
  clientSidePermissionSettings: any;
  SHOW_FINAL_BUTTON: boolean;
  SHOW_DISCARD_BUTTON: boolean;
  SHOW_DRAFT_BUTTON: boolean;
  SHOW_VOID_BUTTON: boolean;
  SHOW_GROSS_PROFIT: boolean;
  entityCreditLimitCheck: CreditLimit [] = [];
  ENABLE_AUTO_POPUP: boolean;
  HIDE_DELETE_BUTTON_IN_DRAFT_MODE: boolean;
  HIDE_CLONE_BUTTON: boolean;
  genDocLock$ = this.store.select(InternalSalesOrderSelectors.selectGenDocLock);
  creditLimitInputDto: creditLimitInputDto[] = [];
  akaunVoidDialogComponent: MatDialogRef<AkaunVoidDialogComponent>;

  mainTabInvalid: boolean = false;
  accountTabInvalid: boolean = false;
  lineTabInvalid: boolean = false;
  genDocLock: boolean;
  title: Observable<any> = of('Create');

  hdr$ = this.draftStore.select(HDREditSelectors.selectHdr);
  draft$ = this.store.select(InternalSalesOrderSelectors.selectOrder);
  pns$ = this.draftStore.select(PNSEditSelectors.selectAll).pipe(
    map(a => a.filter(l => l.status !== 'DELETED')),
    // Sorting data based on position id to match the same order as in add line items
    map(sortedData => this.customSortByPositionId(sortedData))
  );
  order$ = this.store.select(InternalSalesOrderSelectors.selectEntity);
  disableCloneBtn$ = this.store.select(InternalSalesOrderSelectors.selectDisableCloneBtn);

  pnsForLineItem$ = this.draftStore.select(PNSEditSelectors.selectAll).pipe(
    withLatestFrom(this.draftStore.select(HDREditSelectors.selectHdr)),
    map(([a, b]) => {
      // TODO: To Revert Back in case any error. Remove the code after testing and no issues.
      // if(b.client_doc_type === "LAZADA_SALES_ORDER"){
      //   let lineItemList = a.filter(l => l.status !== 'DELETED' && l.txn_type === 'PNS');
      //   let itemGuid = [];
      //   let lineItemModel = []
      //   const counts = {};
      //   lineItemList.forEach(lineData => {
      //     itemGuid.push(lineData.item_guid);
      //   })
      //   itemGuid.forEach(function (x) { counts[x] = (counts[x] || 0) + 1; });
      //   console.log("counts",counts);
      //   for(var index in counts){
      //     lineItemModel = []
      //     lineItemList.forEach(lineData => {
      //       if(lineData.item_code !== "LZD_SHIPPING_FEE"){
      //         // if(lineData.item_guid === index){
      //         //   lineData.quantity_base = counts[index];
      //         // }
      //         let container = { ...lineData }
      //         const stdAmount = Number(lineData.unit_price_std) * Number(lineData.quantity_base)
      //         const netAmount = stdAmount - Number(lineData.amount_discount)
      //         container.unit_price_std = parseFloat(lineData.amount_txn.toString());
      //         container.unit_price_txn = parseFloat(lineData.amount_txn.toString());
      //         container.amount_std = lineData.amount_txn;
      //         container.amount_net = lineData.amount_txn;
      //         container.amount_txn = <MoneyDatatypeInterface> netAmount;
      //         lineItemModel.push(container)
      //         console.log("Item Container",container);
      //       } else {
      //         let container = { ...lineData }
      //         const stdAmount = Number(lineData.unit_price_std) * Number(lineData.quantity_base)
      //         const netAmount = stdAmount - Number(lineData.amount_discount)
      //         container.unit_price_std = parseFloat(lineData.amount_net.toString());
      //         container.amount_txn = <MoneyDatatypeInterface> netAmount;
      //         lineItemModel.push(container)
      //       }
      //     })
      //   }
      //   return lineItemModel;
      // }
      // else if(b.client_doc_type === "SHOPEE_SALES_ORDER"){
      //   let lineItemList = a.filter(l => l.status !== 'DELETED' && l.txn_type === 'PNS');
      //   let itemGuid = [];
      //   let lineItemModel = []
      //   const counts = {};
      //   lineItemList.forEach(lineData => {
      //     itemGuid.push(lineData.item_guid);
      //   })
      //   itemGuid.forEach(function (x) { counts[x] = (counts[x] || 0) + 1; });
      //   for(var index in counts){
      //     lineItemModel = []
      //     lineItemList.forEach(lineData => {
      //       if(lineData.item_code !== "SHOPEE_SHIPPING_FEE"){
      //         let container = { ...lineData }
      //         container.unit_price_std = parseFloat(lineData.amount_txn.toString());
      //         container.unit_price_txn = parseFloat(lineData.amount_txn.toString());
      //         const stdAmount = Number(container.unit_price_std) * Number(container.quantity_base)
      //         const netAmount = stdAmount - Number(container.amount_discount)
      //         container.amount_txn = <MoneyDatatypeInterface> netAmount;
      //         lineItemModel.push(container)
      //       } else {
      //         let container = { ...lineData }
      //         container.unit_price_std = parseFloat(lineData.amount_net.toString());
      //         const stdAmount = Number(container.unit_price_std) * Number(container.quantity_base)
      //         const netAmount = stdAmount - Number(lineData.amount_discount)
      //         container.amount_txn = <MoneyDatatypeInterface> netAmount;
      //         lineItemModel.push(container)
      //       }
      //     })
      //   }
      //   return lineItemModel;
      // }
      // else {
      //   return a.filter(l => l.status !== 'DELETED')
      // }
      // return a.filter(l => l.status !== 'DELETED')

      // Filter out elements with status 'DELETED'

      let filteredData = a.filter(item => item.status !== 'DELETED');

      if (this.appletSettings.HIDE_UNWRAPPED_ITEMS) {
        // Identify 'BUNDLE' items and collect their GUIDs
        const bundleGuids = filteredData.filter(item => item.item_txn_type === 'BUNDLE' || item.item_txn_type === 'MADE_TO_ORDER').map(item => item.guid);

        // Further filter out the child items of 'BUNDLE' items
        filteredData = filteredData.filter(item => !bundleGuids.includes(item.guid_parent));
      }

      // Sort the filtered data by position_id
      const sortedData = filteredData.sort((itemA, itemB) => {
        return +itemA.position_id - +itemB.position_id;
      });

      return sortedData;
    })
  );

  pnsLineItem$ = this.store.select(InternalSalesOrderSelectors.selectEntity).pipe(
    map(a => {
      if(a.bl_fi_generic_doc_hdr.client_doc_type === "LAZADA_SALES_ORDER" || a.bl_fi_generic_doc_hdr.client_doc_type === "SHOPEE_SALES_ORDER"){
        let lineItemList = a.bl_fi_generic_doc_line.filter(l => l.status !== 'DELETED' && l.txn_type === 'PNS');
        let itemGuidNo = 0;
        let itemGuid = [];
        const counts = {};
        lineItemList.forEach(lineData => {
          // if(lineData.txn_type === "PNS"){
            // if(this.itemGuid.length<1){
              itemGuid.push(lineData.item_guid);
            // }
            itemGuidNo += 1;
          // }
        })
        // console.log("itemGuidNo::",itemGuid);
        itemGuid.forEach(function (x) { counts[x] = (counts[x] || 0) + 1; });
        // console.log("count::",counts);
        for(var index in counts){
          lineItemList.forEach(lineData => {
            if(lineData.item_code !== "LZD_SHIPPING_FEE"){
              if(lineData.item_guid === index){
                lineData.quantity_base = counts[index];
              }
              lineData.unit_price_std = parseFloat(lineData.amount_txn.toString());
              lineData.unit_price_txn = parseFloat(lineData.amount_txn.toString());
              lineData.amount_std = lineData.amount_txn;
              lineData.amount_net = lineData.amount_txn;
            }
            else {
              lineData.unit_price_std = parseFloat(lineData.amount_net.toString());
              lineData.amount_txn = lineData.amount_net;
            }
          })
        }
        return lineItemList;
      }
      else{
        return a.bl_fi_generic_doc_line.filter(l => l.status !== 'DELETED')
      }
    })
  )
  settlement$ = this.draftStore.select(SettlementEditSelectors.selectAll);
  attachments$ = this.store.select(InternalSalesOrderSelectors.selectEntity).pipe(
    map(a => a.bl_fi_generic_doc_ext.filter(x => x.param_code === 'GEN_DOC_FILE'))
  );

  deleteConfirmation = false;
  statusTabs = [];
  postingStatus: any;
  status: any;
  editedOrder: boolean;

  // Shopee Pickup Disabled Save
  disableShopeePickUp$ = combineLatest([
    this.store.select(MarketplaceStatusSelectors.selectMarketplaceStatusState),
    this.store.select(InternalSalesOrderSelectors.selectShopeeGetShipParam),
    this.store.select(InternalSalesOrderSelectors.selectShopeePickUpAddress),
    this.store.select(InternalSalesOrderSelectors.selectShopeePickUpTime)
  ]).pipe(
    map(([mpStatus, shipShopee, shopeeAddress, shopeeTime]) => {
      const status = (mpStatus && mpStatus?.mpStatusShopee && mpStatus.mpStatusShopee == "processed")
      const ship = (shipShopee && shipShopee == 'pickup')
      return (status && ship && (!shopeeAddress || !shopeeTime)) ? true : false
    })
  )

  masterSettings$ = this.sessionStore.select(
    SessionSelectors.selectMasterSettings
  );

  clientSidePermissions$ = this.permissionStore.select(
    ClientSidePermissionsSelectors.selectAll
  );

  FINAL_STATUS_GUID: string;
  CURRENT_STATUS_GUID: string;
  panels = [
    { title: 'Search Document', content: 'search-documents', condition: { status: 'TEMP' } },
    { title: 'Main', content: 'main', expandSetting: 'EXPAND_MAIN_DETAILS', invalid: false },
    { title: 'Account', content: 'account', expandSetting: 'EXPAND_ACCOUNT', invalid: false },
    { title: 'Lines', content: 'line-items', expandSetting: 'EXPAND_LINE_ITEMS', invalid: false },
    { title: 'ARAP', content: 'arap', hide: 'HIDE_MAIN_ARAP_TAB', expandSetting: 'EXPAND_MAIN_ARAP'},
    { title: 'Delivery Details', content: 'delivery-details', hide: 'HIDE_DELIVERY_DETAILS_TAB', expandSetting: 'EXPAND_DELIVERY_DETAILS' },
    { title: 'Delivery Trips', content: 'delivery-trips', hide: 'HIDE_DELIVERY_TRIPS_TAB', expandSetting: 'EXPAND_DELIVERY_TRIPS'},
    { title: 'Delivery Plans', content: 'delivery-plans',hide:'HIDE_DELIVERY_PLANS_TAB'},
    { title: 'Settlement', content: 'settlement', hide: 'HIDE_SETTLEMENT_TAB', expandSetting: 'EXPAND_SETTLEMENT' },
    { title: 'KO For', content: 'ko-for', condition: { status: 'TEMP' }, expandSetting: 'EXPAND_KO_FOR'},
    { title: 'Receipt Voucher', content: 'receipt-voucher', hide:'HIDE_RECEIPT_VOUCHER_TAB'},
    { title: 'Department Hdr', content: 'department-hdr', hide: 'HIDE_DEPARTMENT_HDR_TAB', expandSetting: 'EXPAND_DEPARTMENT_HDR' },
    { title: 'TraceDocument', content: 'trace-document', hide: 'HIDE_MAIN_CONTRA_TAB', expandSetting: 'EXPAND_TRACE_DOCUMENT'},
    { title: 'Contra', content: 'contra', hide: 'HIDE_MAIN_CONTRA_TAB', expandSetting: 'EXPAND_MAIN_CONTRA' },
    { title: 'Doc Link', content: 'doc-link', hide: 'HIDE_DOC_LINK_TAB', expandSetting: 'EXPAND_DOC_LINK'},
    { title: 'Attachments', content: 'attachments', hide: 'HIDE_ATTACHMENT_TAB', expandSetting: 'EXPAND_ATTACHMENT' },
    { title: 'Export', content: 'export', hide: 'HIDE_EXPORT_TAB', expandSetting: 'EXPAND_EXPORT' },
    { title: 'Ecomsync', content: 'ecomsync', hide: 'HIDE_ECOMSYNC_TAB'},
    { title: 'Status', content: 'status', hide: 'HIDE_STATUS_TAB' },
    { title: 'Events', content: 'events"', hide: 'HIDE_EVENTS_TAB'}
  ];
  expandedPanelIndex: number = 0;
  orientation: boolean;
  isPanelExpanded: boolean = false;
  viewCheckedSub: boolean = false;
  resetExpansion: boolean = false;
  @ViewChildren('panel', { read: ElementRef }) panelScroll;
  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(SearchDocumentsComponent) search: SearchDocumentsComponent;
  @ViewChild(InternalSalesOrderCreateAccountComponent) account: InternalSalesOrderCreateAccountComponent;
  @ViewChild(InternalSalesOrderCreateMainComponent) main: InternalSalesOrderCreateMainComponent;
  @ViewChild(InternalSalesOrderCreateLineItemsComponent) lines: InternalSalesOrderCreateLineItemsComponent;
  @ViewChild(DeliveryDetailsComponent) deliveryDetails: DeliveryDetailsComponent;
  isShowDraft = false;
  hasUpdatePermission: boolean;
  akaunDiscardDialogComponent: MatDialogRef<AkaunDiscardDialogComponent>;
  docNo: any;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    private readonly sessionStore: Store<SessionStates>,
    protected readonly store: Store<InternalSalesOrderStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly permissionStore: Store<PermissionStates>,
    protected readonly componentStore: ComponentStore<LocalState>,
    private getShipmentProviderService : LazadaGetShipmentProviderService,
    private brnchService: BranchService,
    private dialogRef: MatDialog,
    protected soService: InternalSalesOrderService,
    private entitySalesAvailableCreditLimitService: CompanyEntitySalesAvailableCreditLimitService,
    private toastr: ToastrService,
    private appletServiceMVVM: AppletServiceMVVM,
  ) {
    super();
  }
  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({...a, ...b})));
  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.subs.sink = this.appletSettings$.subscribe({
      next: (resolve: AppletSettings) => {
        this.FINAL_STATUS_GUID = resolve.FINAL_STATUS_GUID ? resolve.FINAL_STATUS_GUID.toString() : null ;
        this.appletSettings = resolve;
      },
    });

    this.subs.sink = this.appletSettings$.subscribe((resolve: AppletSettings) => {
      this.HIDE_CLONE_BUTTON = resolve?.HIDE_CLONE_BUTTON;
    });

    this.subs.sink = this.clientSidePermissions$.subscribe({
      next: (resolve) => {
        this.clientSidePermissionSettings = resolve;
        resolve.forEach(permission => {
          if (permission.perm_code === "SHOW_GENDOC_FINAL_BUTTON") {
            this.SHOW_FINAL_BUTTON = true;
          }

          if (permission.perm_code === "SHOW_GENDOC_DISCARD_BUTTON") {
            this.SHOW_DISCARD_BUTTON = true;
          }
          if (permission.perm_code === "SHOW_DRAFT_BUTTON") {
            this.SHOW_DRAFT_BUTTON = true;
          }
          if (permission.perm_code === "SHOW_GROSS_PROFIT") {
            this.SHOW_GROSS_PROFIT = true;
          }
        })
      }
    });

    this.subs.sink = this.hdr$.subscribe((hdrData) => {
      console.log("hdr", hdrData);
      this.hdr = hdrData;
      this.doc_type = hdrData.client_doc_type;
      this.docNo = hdrData.server_doc_1
      this.CURRENT_STATUS_GUID = hdrData.wf_process_status_guid ? hdrData.wf_process_status_guid.toString() : '';
    })
    // TODO: Optimize this
    this.subs.sink = this.deleteConfirmation$.pipe(
      mergeMap(a => {
        return iif(() => a, of(a).pipe(delay(3000)), of(EMPTY));
      })
    ).subscribe(resolve => {
      if (resolve === true) {
        this.componentStore.patchState({ deleteConfirmation: false });
        this.deleteConfirmation = false;
      }
    });
    this.subs.sink = this.appletSettings$.subscribe(resolve => {
      this.isShowDraft =  resolve?.SHOW_DRAFT_BUTTON;
      this.HIDE_DELETE_BUTTON_IN_DRAFT_MODE = resolve.HIDE_DELETE_BUTTON_IN_DRAFT_MODE ?? false;
      // console.log("resolve", resolve);
      if (resolve.ENABLE_CUSTOM_STATUS_HDR_1) {
        this.statusTabs.push({
          statusNum: 1,
          label: resolve.NAME_CUSTOM_STATUS_HDR_1
        });
      }
      if (resolve.ENABLE_CUSTOM_STATUS_HDR_2) {
        this.statusTabs.push({
          statusNum: 2,
          label: resolve.NAME_CUSTOM_STATUS_HDR_2
        });
      }
      if (resolve.ENABLE_CUSTOM_STATUS_HDR_3) {
        this.statusTabs.push({
          statusNum: 3,
          label: resolve.NAME_CUSTOM_STATUS_HDR_3
        });
      }
      if (resolve.ENABLE_CUSTOM_STATUS_HDR_4) {
        this.statusTabs.push({
          statusNum: 4,
          label: resolve.NAME_CUSTOM_STATUS_HDR_4
        });
      }
      if (resolve.ENABLE_CUSTOM_STATUS_HDR_5) {
        this.statusTabs.push({
          statusNum: 5,
          label: resolve.NAME_CUSTOM_STATUS_HDR_5
        });
      }
    });
    this.subs.sink = this.order$.subscribe(resolve => {
      this.hdrGuid = resolve.bl_fi_generic_doc_hdr.guid;
      this.status = resolve.bl_fi_generic_doc_hdr.status;
      this.postingStatus = resolve.bl_fi_generic_doc_hdr.posting_status;
      resolve.bl_fi_generic_doc_hdr.status === 'TEMP'? this.title = of('Create') : this.title = of('Edit');
    });
    this.subs.sink = this.userPermissionTarget$.subscribe((targets) => {
      console.log("targets", targets);
      let updatePermissionTarget = targets.filter(
        (target) =>
          target.permDfn === "TNT_API_DOC_INTERNAL_SALES_ORDER_UPDATE_TGT_GUID"
      );
      let adminUpdatePermissionTarget = targets.filter(
        (target) =>
          target.permDfn === "TNT_TENANT_ADMIN"
      );
      let ownerUpdatePermissionTarget = targets.filter(
        (target) =>
          target.permDfn === "TNT_TENANT_OWNER"
      );
      if(updatePermissionTarget[0]?.hasPermission || adminUpdatePermissionTarget[0]?.hasPermission || ownerUpdatePermissionTarget[0]?.hasPermission){
        this.hasUpdatePermission = true
      }else{
        this.hasUpdatePermission = false
      }
    });

    this.draftStore.select(HDREditSelectors.selectHdr).subscribe((value) => {
      this.selectedHdr = value;
    });

    this.store.select(InternalSalesOrderSelectors.selectEditedOrder).subscribe(resp => {
      this.editedOrder = resp;
    });

    this.subs.sink = this.pns$.subscribe(pns =>{
      if(pns.length > 0) {
        this.highlightLineTab(false)
      }else{
        this.highlightLineTab(true)
      }
    });

    this.store.select(InternalSalesOrderSelectors.selectResetExpansionPanel).subscribe(x => {
      this.resetExpansion = x;
      if (!this.resetExpansion){
        this.subs.sink = this.expandedPanel$.subscribe(expandedPanel =>{
          if(expandedPanel !== undefined) {
            this.expandedPanelIndex = expandedPanel;
        }});
       } else {
          this.expandedPanelIndex = 0;
      }
      this.initializeExpandedPanels();
    });

  }

  ngAfterViewChecked() {
    if (this.isPanelExpanded) {
      if(this.resetExpansion){
        this.expandedPanelIndex = 0;
        this.store.dispatch(InternalSalesOrderActions.resetExpansionPanel({ resetIndex: null }));
      } else {
        if(!this.viewCheckedSub) {
          this.expandedPanel$.subscribe(expandedPanel => {
            if (expandedPanel !== undefined) { this.expandedPanelIndex = expandedPanel }
          });
          this.viewCheckedSub = true;
        }
      }
      setTimeout(() => this.panelScroll.toArray()[this.expandedPanelIndex].nativeElement.scrollIntoView({ behavior: 'smooth' }),500);;
      this.isPanelExpanded = false;  // Reset the flag after scrolling
    }
  }


  onReset() {
    this.viewColFacade.resetDraft(this.index);
  }

  onSave() {
    console.log("onSave")
    if (!this.validateInput()){
      return;
    }

    if(this.status === 'TEMP'){
      this.store.dispatch(InternalSalesOrderActions.createSalesOrdersInit());
    }else{
      console.log("SAVE Doc")
      this.store.dispatch(InternalSalesOrderActions.editedOrder({edited: false}));
      this.store.dispatch(InternalSalesOrderActions.editSalesOrdersInit());
    }
    // });
  }

  validateInput(): boolean {
    // Mandatory logic check for sales agent
    if (!this.main.validateSalesAgent()) {
      this.toastr.error(
        "Please select a Sales Agent to proceed.",
        "Sales Agent Required",
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 3000,
        }
      );
      return false; // Halt the save process
    } else {
      return true;
    }
  }

  onDelete() {
    if (this.deleteConfirmation) {
      this.store.dispatch(InternalSalesOrderActions.deleteSalesOrdersInit());
      this.deleteConfirmation = false;
      this.componentStore.patchState({ deleteConfirmation: false });
    } else {
      this.deleteConfirmation = true;
      this.componentStore.patchState({ deleteConfirmation: true });
    }
  }

  disableButton() {
    return (this.main?.form.invalid || this.account?.entityDetails?.form.invalid || this.lineTabInvalid) && this.status !== 'ACTIVE';
  }

  disableReset() {
    return (this.postingStatus === "FINAL" || this.postingStatus === "VOID" || this.postingStatus === "DISCARDED") || (this.status !== "ACTIVE" || this.status !== "TEMP");
  }

  showFinal(){
    if(this.genDocLock){
      return false;
    }
    // return (!this.appletSettings.HIDE_GENDOC_FINAL_BUTTON || this.SHOW_FINAL_BUTTON) && (!this.postingStatus || this.postingStatus==="DRAFT")  && this.status === "ACTIVE";
    return (
      (!this.appletSettings?.HIDE_GENDOC_FINAL_BUTTON || this.SHOW_FINAL_BUTTON) &&
      this.status === 'ACTIVE' &&
      (!this.postingStatus || this.postingStatus === "DRAFT") &&
      ((this.FINAL_STATUS_GUID === this.CURRENT_STATUS_GUID) ||
      (this.FINAL_STATUS_GUID === null)) &&
      (this.postingStatus!=="VOID")
      // && (this.FINAL_STATUS_GUID === this.CURRENT_STATUS_GUID)
    );
  }

  showDraft(){
    return (this.postingStatus==="FINAL" && this.isShowDraft && this.SHOW_DRAFT_BUTTON);
  }

  onUpdateMain(form: InternalSOMain) {
    this.draftStore.dispatch(HDREditActions.updateMain({ form }));
  }

  onUpdateMainDelivery(form: InternalSODelivery) {
    console.log("form:: ", form);
    this.draftStore.dispatch(HDREditActions.updateMainDelivery({ form }));
  }

  onUpdateDepartment(form: InternalSODepartment) {
    this.draftStore.dispatch(HDREditActions.updateDepartment({ form }));
  }

  onUpdateBillingInfo(form: BillingInfo) {
    this.draftStore.dispatch(HDREditActions.updateBillingInfo({ form }));
  }

  onUpdateBillingAddress(form: BillingAddress) {
    this.draftStore.dispatch(HDREditActions.updateBillingAddress({ form }));
  }

  onUpdateShippingInfo(form: ShippingInfo) {
    this.draftStore.dispatch(HDREditActions.updateShippingInfo({ form }));
  }

  onUpdateShippingAddress(form: ShippingAddress) {
    this.draftStore.dispatch(HDREditActions.updateShippingAddress({ form }));
  }

  onConvert() {
    this.store.dispatch(InternalSalesOrderActions.convertSalesSOInit());
  }

  // onPrint() {
  //   this.store.dispatch(InternalSalesOrderActions.printJasperPdfInit());
  // }

  onUpdateStatus(form: InternalSOStatus) {
    this.store.dispatch(HDREditActions.updateStatus({ form }));
  }

  onVoid() {
    this.akaunVoidDialogComponent = this.dialogRef.open(AkaunVoidDialogComponent, { width: '400px' });
    this.akaunVoidDialogComponent.componentInstance.confirmMessage = 'Are you sure you want to void document ';
    this.akaunVoidDialogComponent.componentInstance.docNo = this.docNo;
    this.akaunVoidDialogComponent.afterClosed().subscribe((result) => {
      if(result === true) {
        this.subs.sink = this.draft$.subscribe(resolve => {
          console.log(resolve);
          const json = {
            posting_status: 'VOID'
          }
          let temp: GenericDocContainerModel = {
            bl_fi_generic_doc_hdr: resolve.bl_fi_generic_doc_hdr,
            bl_fi_generic_doc_event: null,
            bl_fi_generic_doc_ext: null,
            bl_fi_generic_doc_link: null,
            bl_fi_generic_doc_line: null
          };
          if (resolve.bl_fi_generic_doc_hdr.posting_status === 'FINAL' || (resolve.bl_fi_generic_doc_hdr.status !== 'ACTIVE' && resolve.bl_fi_generic_doc_hdr.status !== null)) {
            this.store.dispatch(InternalSalesOrderActions.voidSalesOrderInit({ status: json, doc: temp }));
          } else {
            this.viewColFacade.showFailedToast({ message: 'This document has not been finalized yet' });
          }
        });
      }
    });
  }

  showVoid(): boolean {
    if (this.genDocLock) {
      return false;
    }

    return (!this.appletSettings?.HIDE_GENDOC_VOID_BUTTON || this.SHOW_VOID_BUTTON)
      && this.postingStatus === 'FINAL';
  }

  onReturn() {
    this.store.dispatch(InternalSalesOrderActions.selectCOA(null)); // Set COA to null when discarding purchase order
    if(this.postingStatus !== 'DISCARDED' && this.editedOrder){
      this.dialogRef.open(SalesOrderReturnPopUpComponent);
    }
    else{
    this.store.dispatch(InternalSalesOrderActions.refreshArapListing({refreshArapListing : true}));
    this.store.dispatch(InternalSalesOrderActions.setEditMode({ editMode: false }));
    this.store.dispatch(
      MarketplaceStatusActions.updatempStatusShopee({
        mpStatusShopee: null
      })
    );
    this.store.dispatch(InternalSalesOrderActions.selectShopeeGetShipParam({shipParam: null}));
    this.store.dispatch(InternalSalesOrderActions.selectShopeePickUpAddress({ address: null }))
    this.store.dispatch(InternalSalesOrderActions.selectShopeePickUpTime({ time: null }))
    if (this.orientation) {
      this.savePanelState();
      this.store.dispatch(InternalSalesOrderActions.resetExpansionPanel({ resetIndex: null }));
    }
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false,
      selectedRowGuid: null
    });
    this.viewColFacade.onPrev(this.prevIndex);
    }
  }

  onCustomer() {
    if (!this.localState.deactivateCustomer) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateCustomer: true,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateLineItem: false,
        deactivateAddContra: false,
        deactivateViewContra: false,
        deactivateAddAttachments: false
      });
      this.viewColFacade.onNextAndReset(this.index, 5);
    }
  }

  goToSelectDeliveryEntity() {
    if (this.orientation) {
      this.savePanelState();
    }
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateAdd: false,
      deactivateReturn: true,
      deactivateCustomer: true,
      deactivateShippingInfo: false,
      deactivateBillingInfo: false,
      deactivateSettlement: false,
      deactivateLineItem: false,
      deactivateAddContra: false,
      deactivateViewContra: false,
      deactivateAddAttachments: false
    });
    this.viewColFacade.onNextAndReset(this.index, 61);

  }

  onMember() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 18);
  }

  onShippingAddress() {
    if (!this.localState.deactivateShippingInfo) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateCustomer: false,
        deactivateShippingInfo: true,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateLineItem: false,
        deactivateAddContra: false,
        deactivateViewContra: false,
        deactivateAddAttachments: false
      });
      this.viewColFacade.onNextAndReset(this.index, 4);
    }
  }

  onBillingAddress() {
    if (!this.localState.deactivateBillingInfo) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateCustomer: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: true,
        deactivateSettlement: false,
        deactivateLineItem: false,
        deactivateAddContra: false,
        deactivateViewContra: false,
        deactivateAddAttachments: false
      });
      this.viewColFacade.onNextAndReset(this.index, 6);
    }
  }

  onNextAdd() {
    if (!this.localState.deactivateAdd) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: true,
        deactivateReturn: true,
        deactivateCustomer: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateLineItem: false,
        deactivateAddContra: false,
        deactivateViewContra: false,
        deactivateAddAttachments: false
      });
      this.viewColFacade.onNextAndReset(this.index, 3);
    }
  }

  async onLineItem(line: bl_fi_generic_doc_line_RowClass) {
    console.log("line::",line)
    this.store.dispatch(InternalSalesOrderActions.selectLineItemInit({ line }));
    if (!this.localState.deactivateLineItem) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateCustomer: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateLineItem: true,
        deactivateAddContra: false,
        deactivateViewContra: false,
        deactivateAddAttachments: false
      });
      this.viewColFacade.onNextAndReset(this.index, 9);
    }
    let test$ = this.store.select(InternalSalesOrderSelectors.selectEntity);
    this.subs.sink = test$.subscribe( async (hdrData) => {
      if(hdrData.bl_fi_generic_doc_hdr.client_doc_type === "LAZADA_SALES_ORDER"){
        let branchData = await this.brnchService.getByGuidPromise(line.guid_branch.toString(), AppConfig.apiVisa);
        let shipping = [];
        let ship;
        branchData.data.bl_fi_mst_branch_ext.forEach(branchExt => {
          if(branchExt.param_code === "SHIPMENT_PROVIDER"){
            shipping.push({value: branchExt.value_string.toString(), viewValue: branchExt.value_string.toString()})
          }
        })
        if(shipping[0].value === "J&T MY"){
          ship = shipping[1].value;
        }
        else {
          ship = shipping[0].value;
        }
        let trying = {
          branch_guid: line.guid_branch,
          order_item_ids: hdrData.bl_fi_generic_doc_ext.find(x => x.param_code === "LAZADA_ORDER_ITEM_ID" && x.guid_doc_line === line.guid)?.value_string ?? null,
          delivery_type: "Dropship",
          shipment_provider: ship ?? null,
          shipping_provider: ship ?? null,
          tracking_number: line.tracking_id || null,
          // tracking_number: hdrData.bl_fi_generic_doc_ext.find(x => x.param_code === "LAZADA_ORDER_ITEM_TRACKING_NUMBER" && x.guid_doc_line === line.guid)?.value_string || null,
          mkt_status: null
        };
        this.store.dispatch(InternalSalesOrderActions.selectDetailsInit({ entity: trying }))
      }
      else if(hdrData.bl_fi_generic_doc_hdr.client_doc_type === "SHOPEE_SALES_ORDER"){
        let trying = {
          branch_guid: line.guid_branch,
          shopee_ordersn: hdrData.bl_fi_generic_doc_ext.find(x => x.param_code === "SHOPEE_ORDERSN").value_string || null,
          tracking_number: line.tracking_id || null,
          mkt_status: null
        };
        console.log("trying::",trying)
        this.store.dispatch(InternalSalesOrderActions.selectDetailsInit({ entity: trying }))
      }
      else {
        let trying = {
          branch_guid: null,
          order_item_ids: null,
          delivery_type: null,
          shipment_provider: null,
          shipping_provider: null,
          tracking_number: null,
          mkt_status: null
        };
        this.store.dispatch(InternalSalesOrderActions.selectDetailsInit({ entity: trying }))
      }
    })
  }

  onAddSettlement() {
    if (!this.localState.deactivateSettlement) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateCustomer: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: true,
        deactivateLineItem: false,
        deactivateAddContra: false,
        deactivateViewContra: false,
        deactivateAddAttachments: false
      });
      this.viewColFacade.onNextAndReset(this.index, 8);
    }
  }

  goToSettlementEdit(settlement: bl_fi_generic_doc_line_RowClass) {
    if (settlement && !this.localState.deactivateSettlement) {
      this.store.dispatch(InternalSalesOrderActions.selectSettlement({ settlement }))
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: true,
        deactivateReturn: true,
        deactivateList: false,
        selectedLine: settlement.guid
      });
      if (this.orientation) {
        this.savePanelState();
      }
      this.viewColFacade.onNextAndReset(this.index, 55);
    }
  }

  onAddContra() {
    this.store.dispatch(InternalSalesOrderActions.refreshArapListing({refreshArapListing : false}));
    if (!this.localState.deactivateAddContra) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateCustomer: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateLineItem: false,
        deactivateAddContra: true,
        deactivateViewContra: false,
        deactivateAddAttachments: false
      });
      this.viewColFacade.onNextAndReset(this.index, 11);
    }
  }

  onViewContra(e: GenericDocARAPContainerModel) {
    this.store.dispatch(InternalSalesOrderActions.selectContraLink({ link: e }));
    if (!this.localState.deactivateViewContra) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateCustomer: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateLineItem: false,
        deactivateAddContra: false,
        deactivateViewContra: true,
        deactivateAddAttachments: false
      });
      this.viewColFacade.onNextAndReset(this.index, 15);
    }
  }

  onAddAttachments() {
    if (!this.localState.deactivateAddAttachments) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateCustomer: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateLineItem: false,
        deactivateAddContra: false,
        deactivateAddAttachments: true
      });
      this.viewColFacade.onNextAndReset(this.index, 12);
    }
  }

  deleteCondition() {
    if (this.postingStatus === "DRAFT" && this.HIDE_DELETE_BUTTON_IN_DRAFT_MODE) {
      return false;
    }
    else if ((this.postingStatus === "FINAL") || (this.status !== "ACTIVE" && this.status !== "TEMP" && this.status !== null) || (this.doc_type === "LAZADA_SALES_ORDER" || this.doc_type === "SHOPEE_SALES_ORDER")) {
      return false;
    }
    else {
      return true;
    }
  }

  async onFinal() {
    console.log("onFinal")
    if (!this.validateInput()){
      return;
    }

    this.subs.sink = this.hdr$.subscribe(async (resolve) => {
      const json = {
        posting_status: "FINAL",
      };
      let entityGuids: string[] = [];
      let docsToFinal: GenericDocContainerModel[] = [];
      let temp: GenericDocContainerModel = {
        bl_fi_generic_doc_hdr: resolve,
        bl_fi_generic_doc_event: null,
        bl_fi_generic_doc_ext: null,
        bl_fi_generic_doc_link: null,
        bl_fi_generic_doc_line: null,
      };
      entityGuids.push(
        temp.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString()
      );
      this.creditLimitInputDto.push({
        company_guid: temp.bl_fi_generic_doc_hdr.guid_comp.toString(),
        entity_credit_limit_guid: (<any>temp.bl_fi_generic_doc_hdr)
          .doc_entity_hdr_json?.creditLimitGuid,
        entity_guids: [
          temp.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString(),
        ],
      });
      const transformedPayload = {
        creditLimitDto: this.creditLimitInputDto.map((item) => ({
          entity_guid: item.entity_guids[0], // Assuming there is only one entity_guid in the array
          company_guid: item.company_guid,
          credit_limit_guid: item.entity_credit_limit_guid,
        })),
      };

      docsToFinal.push(temp);
      if (resolve.posting_status !== "FINAL" && resolve.status === "ACTIVE") {
        if (this.appletSettings.ENABLE_CREDIT_LIMIT_FILTER === true) {
          this.entityCreditLimitCheck = [];
          let creditLimitDTO = transformedPayload;
          this.subs.sink = await this.entitySalesAvailableCreditLimitService
            .post(creditLimitDTO, AppConfig.apiVisa)
            .subscribe((response) => {
              response.data.forEach((record) => {
                this.entityCreditLimitCheck.push({
                  available_credit_limt: record.available_credit_limit,
                  new_available_credit_limt: record.available_credit_limit,
                  entity_hdr_guid: record.entity_hdr_guid,
                  customer_code: record.customer_code,
                  generic_doc_guids: [],
                  generic_doc_hdr_server_docs: [],
                });
              });

              docsToFinal.forEach((doc) => {
                this.entityCreditLimitCheck.forEach((record) => {
                  if (
                    record.entity_hdr_guid ===
                    doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid
                  ) {
                    console.log("amount_txn", doc.bl_fi_generic_doc_hdr.amount_txn);
                    const amountTxnAsNumber = parseFloat(
                      doc.bl_fi_generic_doc_hdr.amount_txn.toString()
                    );
                    if (!isNaN(amountTxnAsNumber)) {
                      record.new_available_credit_limt -= amountTxnAsNumber;
                    }
                    record.generic_doc_guids.push(
                      doc.bl_fi_generic_doc_hdr.guid.toString()
                    );
                    record.generic_doc_hdr_server_docs.push(
                      doc.bl_fi_generic_doc_hdr.server_doc_1.toString()+" "
                    );
                  }
                });
              });

              docsToFinal.forEach((doc) => {
                this.entityCreditLimitCheck.forEach((record) => {
                  if (record.new_available_credit_limt > 0) {
                    if (
                      doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid ===
                      record.entity_hdr_guid
                    ) {
                      console.log("entityCreditLimitCheck", this.entityCreditLimitCheck)
                      this.store.dispatch(InternalSalesOrderActions.updateAndFinalInit());
                    }
                  } else {
                    this.toastr.error(
                      "Customer " +
                        record.customer_code +
                        " - Available Credit Limit: " +
                        record.available_credit_limt +
                        " - Selected: " +
                        record.generic_doc_hdr_server_docs.toString(),
                      "Error",
                      {
                        tapToDismiss: true,
                        progressBar: true,
                        timeOut: 5000,
                      }
                    );
                  }
                });
              });
            });
        }
        else {
          this.store.dispatch(InternalSalesOrderActions.updateAndFinalInit());
        }
      } else {
        this.viewColFacade.showFailedToast({
          message: "This document has been posted",
        });
      }
    });
  }

  onFormStatusChange(status: string) {
    if (status === 'INVALID') {
      this.highlightMainTab(true);
    }else{
      this.highlightMainTab(false);
    }
  }

  highlightMainTab(boolean:boolean) {
    this.mainTabInvalid = boolean;
    const panelIndex = this.panels.findIndex(panel => panel.title === 'Main');
    this.panels[panelIndex].invalid = boolean;
  }

  onAccountStatusChange(status: string) {
    if (status === 'INVALID') {
      this.highlightAccountTab(true);
    }else{
      this.highlightAccountTab(false);
    }
  }

  highlightAccountTab(boolean:boolean) {
    this.accountTabInvalid = boolean;
    const panelIndex = this.panels.findIndex(panel => panel.title === 'Account');
    this.panels[panelIndex].invalid = boolean;
  }

  highlightLineTab(boolean:boolean) {
    this.lineTabInvalid = boolean;
    console.log("lineTabInvalid",this.lineTabInvalid);
    const panelIndex = this.panels.findIndex(panel => panel.title === 'Lines');
    this.panels[panelIndex].invalid = boolean;
  }

  initializeExpandedPanels(): void {
    // Initialize the expanded panels based on appletSettings
      const expandedIndex = this.panels.findIndex(panel =>
        panel.expandSetting && this.appletSettings[panel.expandSetting]
      );

      if (expandedIndex !== -1) {
        this.expandedPanelIndex = expandedIndex;
      }

  }

  onPanelOpened(index: number): void {
    if (this.orientation) {
      this.expandedPanelIndex = index;
      this.isPanelExpanded = true;
    }
  }

  savePanelState(): void {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      expandedPanel: this.expandedPanelIndex
    });
  }

  getFilteredPanels(draft: any) {
    return this.panels.filter(panel => {
      const hidePanels = panel.hide && this.appletSettings[panel.hide];
      const conditionPanels = !panel.condition || panel.condition.status === draft?.status;
      return !hidePanels && conditionPanels;
    });
  }

  showPanels(): boolean {
    if(this.appletSettings?.VERTICAL_ORIENTATION){
      if(this.appletSettings?.DEFAULT_ORIENTATION === 'HORIZONTAL'){
        this.orientation = false;
      } else {
        this.orientation = true;
      }
    } else {
      if(this.appletSettings?.DEFAULT_ORIENTATION === 'VERTICAL'){
        this.orientation = true;
      } else {
        this.orientation = false;
      }
    }
    return this.orientation;
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.appletSettings$.subscribe(appletSettings=>{
        if(appletSettings?.AUTO_UI && (this.appletServiceMVVM.getNextField()==='sales_agent' || this.appletServiceMVVM.getNextField()==='credit_term' || this.appletServiceMVVM.getNextField()==='credit_limit')){
          this.viewColFacade.updateInstance<LocalState>(this.index, {
            ...this.localState,
            selectedIndex: 0,
            childSelectedIndex: this.account.matTab?.selectedIndex,
            selectedLineItemRowIndex: this.lines.selectedRowIndex,
            deliveryDetailSelectedIndex: this.deliveryDetails?.matTab?.selectedIndex,
            selectedSearchIndex: this.search?.matTab?.selectedIndex
          });
        }else if(appletSettings?.AUTO_UI && this.appletServiceMVVM.getNextField()==='line_item'){
          this.viewColFacade.updateInstance<LocalState>(this.index, {
            ...this.localState,
            selectedIndex: 2,
            childSelectedIndex: this.account.matTab?.selectedIndex,
            selectedLineItemRowIndex: this.lines.selectedRowIndex,
            deliveryDetailSelectedIndex: this.deliveryDetails?.matTab?.selectedIndex,
            selectedSearchIndex: this.search?.matTab?.selectedIndex
          });
        }else if(appletSettings?.AUTO_UI && this.appletServiceMVVM.getNextField()==='billing'){
          this.viewColFacade.updateInstance<LocalState>(this.index, {
            ...this.localState,
            selectedIndex: 1,
            childSelectedIndex: 1,
            selectedLineItemRowIndex: this.lines.selectedRowIndex,
            deliveryDetailSelectedIndex: this.deliveryDetails?.matTab?.selectedIndex,
            selectedSearchIndex: this.search?.matTab?.selectedIndex
          });
        }else if(appletSettings?.AUTO_UI && this.appletServiceMVVM.getNextField()==='shipping'){
          this.viewColFacade.updateInstance<LocalState>(this.index, {
            ...this.localState,
            selectedIndex: 1,
            childSelectedIndex: 2,
            selectedLineItemRowIndex: this.lines.selectedRowIndex,
            deliveryDetailSelectedIndex: this.deliveryDetails?.matTab?.selectedIndex,
            selectedSearchIndex: this.search?.matTab?.selectedIndex
          });
        }
        else{
          this.viewColFacade.updateInstance<LocalState>(this.index, {
            ...this.localState,
            selectedIndex: this.matTab?.selectedIndex,
            childSelectedIndex: this.account.matTab?.selectedIndex,
            selectedLineItemRowIndex: this.lines.selectedRowIndex,
            deliveryDetailSelectedIndex: this.deliveryDetails?.matTab?.selectedIndex,
            selectedSearchIndex: this.search?.matTab?.selectedIndex
          });
        }})
    }
    if (this.orientation) {
      this.savePanelState();
      this.store.dispatch(InternalSalesOrderActions.resetExpansionPanel({ resetIndex: null }));
    }
    this.subs.unsubscribe();
  }

  showDiscard(): boolean {
    if(this.genDocLock){
      return false;
    }

    return (!this.appletSettings?.HIDE_GENDOC_DISCARD_BUTTON || this.SHOW_DISCARD_BUTTON) && (!this.postingStatus || this.postingStatus==="DRAFT") && this.status == 'ACTIVE';
  }


  onDiscard() {
    this.akaunDiscardDialogComponent = this.dialogRef.open(AkaunDiscardDialogComponent, { width: '400px' });
    this.akaunDiscardDialogComponent.componentInstance.confirmMessage = 'Are you sure you want to discard document ';
    this.akaunDiscardDialogComponent.componentInstance.docNo = this.docNo;
    this.akaunDiscardDialogComponent.afterClosed().subscribe((result) => {
      if(result === true){
        this.store.dispatch(InternalSalesOrderActions.discardInit({ guids: [this.hdrGuid.toString()], fromEdit: true }));
      }
    });
  }

  onDraft() {
    this.store.dispatch(InternalSalesOrderActions.draftInit({ guids: [this.hdrGuid.toString()], fromEdit: true }));
  }

  showClone() {
    return !this.HIDE_CLONE_BUTTON;
  }

  customSortByPositionId(rows) {
    return rows.sort((a, b) => {
      const positionA = a.position_id?.split('-').map(Number)??[];
      const positionB = b.position_id?.split('-').map(Number)??[];

      for (let i = 0; i < Math.max(positionA.length, positionB.length); i++) {
        const numA = positionA[i] || 0;
        const numB = positionB[i] || 0;

        if (numA !== numB) {
          return numA - numB;
        }
      }

      return 0; // If all segments are equal, return 0 (equal)
    });
  }

  showClose(): boolean {
    return (!this.appletSettings.HIDE_CLOSE_BUTTON)
    && this.status === 'ACTIVE' && (!this.postingStatus || this.postingStatus==="FINAL") ;
  }
  onClose() {
    const dialogRef = this.dialogRef.open(CloseConfirmDialogComponent);
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Logic to close the item
        this.store.dispatch(InternalSalesOrderActions.closeKoInit({ guid: this.hdrGuid.toString() }));
        // console.log('Item closed.');
        this.onReturn();
      } else {
        console.log('Action cancelled.');
      }
    });
  }
  clone() {
    this.store.dispatch(InternalSalesOrderActions.cloneDocumentInit({}));
  }

  isNotShadow() {
    return !(this.hdr.forex_doc_hdr_guid);
   }

  showGP(): boolean {
    return (!this.appletSettings.HIDE_GROSS_PROFIT_TAB && this.SHOW_GROSS_PROFIT);
  }

}
