import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { map, withLatestFrom } from 'rxjs/operators';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { SnackBarConstants } from '../../../../models/constants/snack-bar.constants';
import { SettlementActions } from '../actions';
import { HDRSelectors } from '../selectors';
import { DraftStates } from '../states';

@Injectable()
export class SettlementEffects {

    addSettlement$ = createEffect(() => this.actions$.pipe(
        ofType(SettlementActions.addSettlementInit),
        withLatestFrom(this.store.select(HDRSelectors.selectHdr)),
        map(([a, b]) => {
            if (parseFloat(<any>a.settlement.amount_txn) <= parseFloat(<any>b.amount_open_balance)) {
                this.viewColFacade.showSnackBar(SnackBarConstants.addSettlementSuccess);
                this.viewColFacade.resetIndex(2);
                return SettlementActions.addSettlementSuccess({settlement: a.settlement})
            } else {
                this.viewColFacade.showSnackBar(SnackBarConstants.addSettlementFailed);
                return SettlementActions.addSettlementFailed();
            }
        })
    ));

    constructor(
        private actions$: Actions,
        private viewColFacade: ViewColumnFacade,
        private readonly store: Store<DraftStates>
    ) { }
}
