import { createAction, props } from '@ngrx/store';
import { JobOrderMain, JobOrderNoDepartment } from '../../../../models/internal-job-order.model';

export const updateMain = createAction('[Draft: Job Order No Edit] Update Main', props<{ form: JobOrderMain }>());
export const updateDepartment = createAction('[Draft: Job Order No Edit] Update Department', props<{ form: JobOrderNoDepartment }>());
export const resetHDR = createAction('[Draft: Job Order No Edit] Reset');
