<section>
    <fieldset>
        <legend class="field-header">Client Doc Status {{index}}</legend>
        <mat-form-field appearance="outline">
            <mat-label>Name</mat-label>
            <input matInput type="text" [formControl]="form.controls['NAME_CUSTOM_STATUS_' + fieldSet + '_' + index]">
        </mat-form-field>
        <div fxLayout="row wrap" fxLayoutAlign="space-between center">
            <button class="blg-button-icon" mat-button matTooltip="Add" type="button" (click)="onAdd()">
                <img src="assets/images/add.png" alt="add" width="40px" height="40px">
            </button>
            <div class="blg-accent">
                <app-pagination #pagination [agGridReference]="agGrid"></app-pagination>
            </div>
        </div>
        <ag-grid-angular
          #agGrid
          class="ag-theme-balham"
          domLayout="autoHeight"
          singleClickEdit="true"
          pagination="true"
          [rowData]="[]"
          [animateRows]="true"
          [paginationPageSize]="pagination.rowPerPage"
          [defaultColDef]="defaultColDef"
          [suppressRowClickSelection]="false"
          [frameworkComponents]="frameworkComponents"
          [columnDefs]="columnDefs"
          (gridReady)="onGridReady($event)">
        </ag-grid-angular>
    </fieldset>
</section>