<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div> <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()"> <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px"> </button> <span> Login Edit </span> </div> <button mat-raised-button
      color="primary" type="button" (click)="onSave()" [disabled]="!form.valid">Update</button>
  </div>
</mat-card-title>
<form [formGroup]="form" #formDirectives="ngForm">
  <mat-tab-group [dynamicHeight]="true">
    <mat-tab label="Main">
      <div fxLayout="column" class="view-col-forms">

        <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
          <mat-form-field appearance="outline">
            <mat-label>User email</mat-label> <input maxlength="255" matInput formControlName="user_email" readonly
              required>
            <mat-hint *ngIf="form.controls['subject_guid'].value?.length === 255" class="text-danger font-14">
              Please insert no more than 255
            </mat-hint>
            <mat-hint
              *ngIf="form.controls['subject_guid'].hasError('required') && form.controls['subject_guid'].touched"
              class="text-danger font-14">Please insert user email </mat-hint>
          </mat-form-field>
        </div>
        <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
          <mat-form-field appearance="outline">
            <mat-label>Rank</mat-label>
            <mat-select placeholder="Payment Type" [formControl]="form.controls['rank']" required>
              <mat-option *ngFor="let item of rank" [value]="item.value">{{item.viewValue}}
              </mat-option>
            </mat-select>
            <mat-hint *ngIf="form.controls['rank'].hasError('required') && form.controls['rank'].touched"
              class="text-danger font-14">Please select rank </mat-hint>
          </mat-form-field>
        </div>
        <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
          <mat-form-field appearance="outline">
            <mat-label>Status</mat-label>
            <mat-select placeholder="Status" [formControl]="form.controls['status']">
              <mat-option *ngFor="let item of status" [value]="item.value">{{item.viewValue}}
              </mat-option>
            </mat-select>
            <mat-hint *ngIf="form.controls['status'].hasError('required') && form.controls['status'].touched"
              class="text-danger font-14">Please select status</mat-hint>
          </mat-form-field>
        </div>

        <div class=" center" style="margin-top: 10px; width: 50px;">
          <button mat-raised-button color="warn" type="button" (click)="onRemove()">Remove </button>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</form>
