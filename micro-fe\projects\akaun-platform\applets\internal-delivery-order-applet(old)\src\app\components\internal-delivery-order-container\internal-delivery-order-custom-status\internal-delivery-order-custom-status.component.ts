import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { Observable, zip } from 'rxjs';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-internal-delivery-order-custom-status',
  templateUrl: './internal-delivery-order-custom-status.component.html',
  styleUrls: ['./internal-delivery-order-custom-status.component.css']
})
export class InternalDeliveryOrderCustomStatusComponent implements OnInit {

  @Input() draft$: Observable<GenericDocContainerModel>;
  @Input() appletSettings$: Observable<any>;

  @Output() updateStatus = new EventEmitter();

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [];

  statusList = [];

  constructor() { }

  ngOnInit(): void {
      this.form = new FormGroup({
          status1: new FormControl(),
          status2: new FormControl(),
          status3: new FormControl(),
          status4: new FormControl(),
          status5: new FormControl(),
      });
      this.subs.sink = zip(this.draft$, this.appletSettings$).subscribe(
          ([draft, appletSettings]) => {
              if (appletSettings.ENABLE_CUSTOM_STATUS_HDR_1) {
                  this.leftColControls.push({label: appletSettings.NAME_CUSTOM_STATUS_HDR_1, formControl: 'status1', type: 'dropdown', readonly: false});
                  this.statusList.push(appletSettings.LIST_CUSTOM_STATUS_HDR_1);
                  this.form.patchValue({
                      status1: draft.bl_fi_generic_doc_hdr.client_doc_status_01
                  });
              }
              if (appletSettings.ENABLE_CUSTOM_STATUS_HDR_2) {
                  this.leftColControls.push({label: appletSettings.NAME_CUSTOM_STATUS_HDR_2, formControl: 'status2', type: 'dropdown', readonly: false});
                  this.statusList.push(appletSettings.LIST_CUSTOM_STATUS_HDR_2);
                  this.form.patchValue({
                      status2: draft.bl_fi_generic_doc_hdr.client_doc_status_02
                  });
              }
              if (appletSettings.ENABLE_CUSTOM_STATUS_HDR_3) {
                  this.leftColControls.push({label: appletSettings.NAME_CUSTOM_STATUS_HDR_3, formControl: 'status3', type: 'dropdown', readonly: false});
                  this.statusList.push(appletSettings.LIST_CUSTOM_STATUS_HDR_3);
                  this.form.patchValue({
                      status3: draft.bl_fi_generic_doc_hdr.client_doc_status_03
                  });
              }
              if (appletSettings.ENABLE_CUSTOM_STATUS_HDR_4) {
                  this.leftColControls.push({label: appletSettings.NAME_CUSTOM_STATUS_HDR_4, formControl: 'status4', type: 'dropdown', readonly: false});
                  this.statusList.push(appletSettings.LIST_CUSTOM_STATUS_HDR_4);
                  this.form.patchValue({
                      status4: draft.bl_fi_generic_doc_hdr.client_doc_status_04
                  });
              }
              if (appletSettings.ENABLE_CUSTOM_STATUS_HDR_5) {
                  this.leftColControls.push({label: appletSettings.NAME_CUSTOM_STATUS_HDR_5, formControl: 'status5', type: 'dropdown', readonly: false});
                  this.statusList.push(appletSettings.LIST_CUSTOM_STATUS_HDR_5);
                  this.form.patchValue({
                      status5: draft.bl_fi_generic_doc_hdr.client_doc_status_05
                  });
              }
          }
      );
  }


  ngOnDestroy() {
      this.subs.unsubscribe();
  }

}
