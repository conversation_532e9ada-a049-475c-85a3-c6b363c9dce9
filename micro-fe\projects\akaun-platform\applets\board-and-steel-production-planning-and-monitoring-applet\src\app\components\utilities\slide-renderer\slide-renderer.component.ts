import { Component } from '@angular/core';
import { MatSlideToggleChange } from '@angular/material/slide-toggle';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
  selector: 'app-slide-renderer',
  templateUrl: './slide-renderer.component.html',
  styleUrls: ['./slide-renderer.component.css']
})
export class SlideRendererComponent implements ICellRendererAngularComp {

  params;

  isChecked : boolean;

  agInit(params: ICellRendererParams) {
    this.params = params;
    this.isChecked = params.value;
  }

  onChange(event: MatSlideToggleChange) {
    this.isChecked = event.checked;
    this.params.node.data.is_active = this.isChecked ? true : false;
    if (this.params.eGridCell) {
        this.params.eGridCell.focus();
    }
}

  refresh(params: ICellRendererParams): boolean {
    return true;
  }
}
