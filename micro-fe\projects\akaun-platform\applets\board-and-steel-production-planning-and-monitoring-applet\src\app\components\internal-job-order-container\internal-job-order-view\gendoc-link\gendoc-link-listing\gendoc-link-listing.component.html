<div class="view-col-table" fxLayout="column">
  <div fxLayout="row wrap" fxLayoutAlign="end">
    <div
      class="blg-accent"
      fxFlex="1 0 25"
      fxLayout="row"
      fxLayoutAlign="space-between center"
    >
      <button
        ngClass.xs="blg-button-mobile"
        #navBtn
        class="blg-button-icon"
        mat-button
        matTooltip="Add Child Item"
        type="button"
        [disabled]="deactivateAdd$ | async"
        (click)="onNext()"
      >
        <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null"
          src="assets/images/add.png"
          alt="add"
          width="40px"
          height="40px"
        />
      </button>
      <mat-form-field class="example-full-width" appearance="outline" class="search-box">
        <mat-label>Search</mat-label>
        <button mat-icon-button matSuffix (click)="onSearch()">
          <mat-icon>search</mat-icon>
        </button >
        <input matInput placeholder="Search" [formControl]="search" type="text">
      </mat-form-field>
     
      <!-- <app-advanced-search class="mobile" fxFlex [id]="'internal-po'" [advSearchModel]="searchModel"
        (search)="onSearch($event)"> </app-advanced-search> -->
      <app-pagination
        fxFlex
        #pagination
        [agGridReference]="agGrid"
      ></app-pagination>
      <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
     
    </div>
   
  </div>
  <div class="delete-btn">
    <button mat-raised-button color="warn" type="button" (click)="onDelete()">
      <span>Delete</span>
    </button>
  </div>
  <div style="height: 100%">
    <ag-grid-angular
      #agGrid
      id="grid"
      style="height: 100%"
      class="ag-theme-balham"
      [getRowClass]="pagination.getRowClass"
      [columnDefs]="columnsDefs"
      [rowData]="[]"
      [cacheBlockSize]="pagination.rowPerPage"
      [suppressRowClickSelection]="false"
      [rowSelection]="'multiple'"
      [rowModelType]="'serverSide'"
      [serverSideStoreType]="'partial'"
      [animateRows]="true"
      [sideBar]="true"
      (rowClicked)="onRowClicked($event.data)"
      [stopEditingWhenGridLosesFocus]="true"
      [defaultColDef]="defaultColDef"
      [paginationPageSize]="pagination.rowPerPage"
      [pagination]="true"
      [suppressPaginationPanel]="true"
      [suppressScrollOnNewData]="true"
      (gridReady)="onGridReady($event)"
    >
    </ag-grid-angular>
  </div>
</div>
