import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../models/applet-settings.model';

@Component({
  selector: 'app-default-settings',
  templateUrl: './default-settings.component.html',
  styleUrls: ['./default-settings.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DefaultSettingsComponent implements OnInit, OnDestroy {

  private subs = new SubSink();

  form: FormGroup;

  apiVisa = AppConfig.apiVisa;

  constructor(
    private readonly store: Store<SessionStates>,
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      DEFAULT_BRANCH: new FormControl(),
      DEFAULT_LOCATION: new FormControl()
    });
    this.subs.sink = this.store.select(SessionSelectors.selectMasterSettings).subscribe({next: (resolve: AppletSettings) => {
      this.form.patchValue({
        DEFAULT_BRANCH: resolve?.DEFAULT_BRANCH,
        DEFAULT_LOCATION: resolve?.DEFAULT_LOCATION
      });
    }});
  }

  onSave() {
    this.store.dispatch(SessionActions.saveMasterSettingsInit({settings: this.form.value}));
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
