import { ChangeDetectionStrategy, Component, Input, OnInit, ViewChild } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { Store } from '@ngrx/store';
import { RowGroupingDisplayType } from 'ag-grid-community';
import { bl_fi_generic_doc_hdr_RowClass, Pagination, ApiVisa, TripHdrService, SubQueryService, TripDeliveryHeaderContainerModel, ApiResponseModel } from 'blg-akaun-ts-lib';
import * as moment from 'moment';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable, forkJoin, of } from 'rxjs';
import { map, mergeMap, switchMap, filter } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { AppletSettings } from '../../../../models/applet-settings.model';
import { InternalDeliveryOrderActions } from '../../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderSelectors } from '../../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { InternalDeliveryOrderStates } from '../../../../state-controllers/internal-delivery-order-controller/store/states';
import { GroupRowInnerRenderer } from '../../../pick-pack-queue-container/pick-pack-queue-listing/group-row-inner-renderer.component';

@Component({
  selector: 'app-internal-delivery',
  templateUrl: './internal-delivery.component.html',
  styleUrls: ['./internal-delivery.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class InternalDeliveryComponent implements OnInit {

  SERVER_DOC_TYPE = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
  @Input() draft$: Observable<bl_fi_generic_doc_hdr_RowClass>;

  private subSink = new SubSink();
  itemGuid;
  paging = new Pagination();

  apiVisa: ApiVisa = AppConfig.apiVisa;
  public form: FormGroup;

  gridApi: any;
  gridColumnApi;

  toggleColumn$: Observable<boolean>;
  // searchModel = tripSearchModel;
  current_date: Date;
  appletSettings: AppletSettings;
  jobDocs$: Observable<any[]>;

  items = [
    { value: "a", viewValue: "Printable Format A" },
    { value: "b", viewValue: "Printable Format B" },
  ];
  searchItems = this.items;
  trips = [];
  searchTrips;

  tabs = ["Add to Trip", "Printing", "Cancel Jobs"];
  selectedTab = "Add to Trip";

  @ViewChild(PaginationComponent, { static: false })
  private paginationComponent: PaginationComponent;

  public groupDisplayType: RowGroupingDisplayType = "groupRows";
  public groupRowRendererParams = {
    innerRenderer: GroupRowInnerRenderer,
    // puts a checkbox onto each group row
    checkbox: true,
    suppressCount: true,
    // puts a row dragger onto each group row
    //rowDrag: true
  };

  defaultColDef = {
    flex: 1,
    minWidth: 150,
    resizable: true,
    wrapText: true,
    autoHeight: true,
    sortable: true,
    headerComponentParams: {
      template:
        '<div class="ag-cell-label-container" role="presentation">' +
        '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
        '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
        '    <span ref="eSortOrder" class="ag-header-icon ag-sort-order"></span>' +
        '    <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon"></span>' +
        '    <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon"></span>' +
        '    <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon"></span>' +
        '    <span ref="eText" class="ag-header-cell-text" role="columnheader" style="white-space: normal;"></span>' +
        '    <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>' +
        "  </div>" +
        "</div>",
    },
  };

  columnsDefs = [
    {
      headerName: "Delivery Job ID",
      field: "bl_del_job_hdr.running_number_01",
      rowGroup: true,
      hide: true,
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Sales Order No",
      field: "bl_fi_generic_doc_hdr.server_doc_1",
      cellStyle: () => ({ "text-align": "left" }) /*, checkboxSelection: true*/,
    },
    {
      headerName: "Requested Delivery Date",
      field: "bl_del_job_hdr.delivery_date_requested",
      width: 150,
      valueFormatter: (params) => {
        return params.value && params.value !== "None"
          ? moment(params.value).format("YYYY-MM-DD HH:mm:ss")
          : null;
      },
      sort: "desc",
      type: "rightAligned",
    },
    {
      headerName: "Recipient Address (City, State)",
      valueGetter: (params) => {
        return (
          params.data.bl_del_job_hdr.recipient_city +
          ", " +
          params.data.bl_del_job_hdr.recipient_state
        );
      },
      cellStyle: { "text-align": "left" },
      width: 110,
    },
    {
      headerName: "Delivery Region",
      field: "bl_fi_generic_doc_lines.del_region_hdr_state",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Shipping Location",
      field: "bl_del_job_hdr.to_location_code",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Tracking ID",
      field: "bl_del_job_hdr.tracking_id",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Item Code",
      field: "bl_del_job_docline_links.item_code",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Item Name",
      field: "bl_del_job_docline_links.item_name",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Delivery Type",
      field: "bl_del_job_hdr.delivery_logic",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "UOM",
      field: "bl_fi_generic_doc_lines.uom",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Delivery Quantity",
      field: "bl_del_job_docline_links.qty_to_deliver",
      type: "numericColumn",
    },
    {
      headerName: "Delivery Status",
      field: "bl_del_job_docline_links.delivery_status",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Remarks",
      field: "bl_fi_generic_doc_lines.item_remarks",
      cellStyle: () => ({ "text-align": "left" }),
    },
  ];

  public rowSelection: "single" | "multiple" = "multiple";

  columnsDefs$ = this.sessionStore
    .select(SessionSelectors.selectMasterSettings)
    .pipe(
      map((a: any) => [
        ...this.columnsDefs,
        a.ENABLE_CUSTOM_STATUS_LINE_1
          ? {
              headerName: a.NAME_CUSTOM_STATUS_LINE_1
                ? a.NAME_CUSTOM_STATUS_LINE_1
                : "client_doc_status_01",
              field: "client_doc_status_01",
              cellStyle: () => ({ "text-align": "left" }),
            }
          : { minWidth: 0 },
        a.ENABLE_CUSTOM_STATUS_LINE_2
          ? {
              headerName: a.NAME_CUSTOM_STATUS_LINE_2
                ? a.NAME_CUSTOM_STATUS_LINE_2
                : "client_doc_status_02",
              field: "client_doc_status_02",
              cellStyle: () => ({ "text-align": "left" }),
            }
          : { minWidth: 0 },
        a.ENABLE_CUSTOM_STATUS_LINE_3
          ? {
              headerName: a.NAME_CUSTOM_STATUS_LINE_3
                ? a.NAME_CUSTOM_STATUS_LINE_3
                : "client_doc_status_03",
              field: "client_doc_status_03",
              cellStyle: () => ({ "text-align": "left" }),
            }
          : { minWidth: 0 },
        a.ENABLE_CUSTOM_STATUS_LINE_4
          ? {
              headerName: a.NAME_CUSTOM_STATUS_LINE_4
                ? a.NAME_CUSTOM_STATUS_LINE_4
                : "client_doc_status_04",
              field: "client_doc_status_04",
              cellStyle: () => ({ "text-align": "left" }),
            }
          : { minWidth: 0 },
        a.ENABLE_CUSTOM_STATUS_LINE_5
          ? {
              headerName: a.NAME_CUSTOM_STATUS_LINE_5
                ? a.NAME_CUSTOM_STATUS_LINE_5
                : "client_doc_status_05",
              field: "client_doc_status_05",
              cellStyle: () => ({ "text-align": "left" }),
            }
          : { minWidth: 0 },
      ])
    );
  query: string;
  hdrGuid: string;
  jobHdrGuid: any;

  constructor(
    private readonly store: Store<InternalDeliveryOrderStates>,
    private viewColFacade: ViewColumnFacade,
    private tripHdrService: TripHdrService,
    private subQueryService: SubQueryService,
    protected readonly sessionStore: Store<SessionStates>
  ) {}

  masterSettings$ = this.sessionStore.select(
    SessionSelectors.selectMasterSettings
  );

  ngOnInit() {
    this.form = new FormGroup({
      tripGuid: new FormControl(),
      printableFormat: new FormControl(),
    });

    this.draft$.subscribe(data=>{
      this.hdrGuid=data.guid?.toString();
    })

    this.query = 'SELECT a.guid as requiredGuid FROM bl_del_job_hdr a INNER JOIN bl_del_job_dochdr_link b ON a.guid=b.hdr_guid WHERE b.guid_doc_hdr=\'' + this.hdrGuid + '\'';

    this.subSink.sink = this.subQueryService.post({'subquery': this.query, 'table': 'bl_del_job_hdr'}, this.apiVisa).subscribe(async (x: any)=>{
      if(x){
        console.log(x);
        this.jobHdrGuid = x.data

        this.store.dispatch(
          InternalDeliveryOrderActions.loadInternalJobDocsInit({
            pagination: new Pagination(0, 50),
            dto: {
              server_doc_type: "INTERNAL_OUTBOUND_DELIVERY_ORDER",
              delivery_logics: ["INTERNAL_DELIVERY"],
              guids:this.jobHdrGuid.length>0?this.jobHdrGuid:[" "]
            },
          })
        );
      }
    })

    this.subSink.sink = this.store.select(InternalDeliveryOrderSelectors.selectJobListingStatus).subscribe(a => {
      if (a) {
        this.store.dispatch(
          InternalDeliveryOrderActions.loadInternalJobDocsInit({
            pagination: new Pagination(0, 50),
            dto: {
              server_doc_type: "INTERNAL_OUTBOUND_DELIVERY_ORDER",
              delivery_logics: ["INTERNAL_DELIVERY"],
              guids:this.jobHdrGuid.length>0?this.jobHdrGuid:[" "]
            },
          })
        );
        this.store.dispatch(InternalDeliveryOrderActions.resetJobListing());
      }
    });

    //   this.subSink.sink= this.masterSettings$.subscribe({ next: (resolve: AppletSettings) => { this.appletSettings = resolve } });
    //   this.appletSettings.HIDE_JOB_ID ? this.columnsDefs[1]['hide'] = true: this.columnsDefs[1]['hide'] = false;

    //   this.appletSettings.HIDE_JOB_TYPE ? this.columnsDefs[2]['hide'] = true: this.columnsDefs[2]['hide'] = false;
    //   this.appletSettings.HIDE_START_DATE ? this.columnsDefs[3]['hide'] = true: this.columnsDefs[3]['hide'] = false;
    //   this.appletSettings.HIDE_END_DATE ? this.columnsDefs[4]['hide'] = true: this.columnsDefs[4]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_FROM_LOCATION ? this.columnsDefs[5]['hide'] = true: this.columnsDefs[5]['hide'] = false;
    //   this.appletSettings.HIDE_TRIP ? this.columnsDefs[6]['hide'] = true: this.columnsDefs[6]['hide'] = false;
    //   this.appletSettings.HIDE_VEHICLE ? this.columnsDefs[7]['hide'] = true: this.columnsDefs[7]['hide'] = false;
    //   this.appletSettings.HIDE_DRIVER ? this.columnsDefs[8]['hide'] = true: this.columnsDefs[8]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_SENDER ? this.columnsDefs[9]['hide'] = true: this.columnsDefs[9]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_SENDER_ADDRESS ? this.columnsDefs[10]['hide'] = true: this.columnsDefs[10]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_LOGISTICS_REF_NO ? this.columnsDefs[11]['hide'] = true: this.columnsDefs[11]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_TRACKING_ID ? this.columnsDefs[12]['hide'] = true: this.columnsDefs[12]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_RECIPIENT ? this.columnsDefs[13]['hide'] = true: this.columnsDefs[13]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_RECIPIENT_ADDRESS ? this.columnsDefs[14]['hide'] = true: this.columnsDefs[14]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_DELIVERY_QTY ? this.columnsDefs[15]['hide'] = true: this.columnsDefs[15]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_AVE_UNIT_CBM ? this.columnsDefs[16]['hide'] = true: this.columnsDefs[16]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_CBM ? this.columnsDefs[17]['hide'] = true: this.columnsDefs[17]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_PROCESS_STATUS ? this.columnsDefs[18]['hide'] = true: this.columnsDefs[18]['hide'] = false;
    //   // this.appletSettings.HIDE_JOB_SUB_PROCESS_STATUS? this.columnsDefs[19]['hide'] = true: this.columnsDefs[19]['hide'] = false;
    //   this.appletSettings.HIDE_JOB_PROCESS_RESOLUTION ? this.columnsDefs[20]['hide'] = true: this.columnsDefs[20]['hide'] = false;
  }

  filterTrip(search: string) {
    this.subQueryService
      .post(
        {
          // 'subquery': `select guid from bl_fi_mst_branch bfmb where (name ilike '%${search}%' or code ilike '%${search}%') and status ilike 'ACTIVE' limit 5`,
          subquery: `select guid as requiredGuid from bl_del_trip_hdr where (name ilike '%${search}%' or code ilike '%${search}%') and status = 'ACTIVE' limit 5`,
          table: "bl_del_trip_hdr",
        },
        this.apiVisa
      )
      .pipe(
        mergeMap((guidList) => {
          const source: Observable<TripDeliveryHeaderContainerModel>[] = [];

          guidList.data.forEach((g) =>
            source.push(
              this.tripHdrService
                .getByGuid(g, this.apiVisa)
                .pipe(map((g_inner) => g_inner.data))
            )
          );
          return forkJoin(source).pipe();
        }),
        map((b) => {
          this.trips = b;
          console.log(this.trips);
          // this.filteredOptions$.next(this.trips);
          return this.trips;
        })
      )
      .subscribe((x) => {});
  }

  getJobGuids() {
    return this.gridApi
      .getSelectedRows()
      .map((selection) => selection.bl_del_job_hdr.guid);
  }

  onCancelJobs(){
    const jobGuids = this.getJobGuids();
    console.log(jobGuids);

    this.store.dispatch(InternalDeliveryOrderActions.cancelJobDocInit({ jobGuids }))
  }

  getAllRows() {
    let rowData = [];
    this.gridApi.forEachNode(node => rowData.push(node.data));
    return rowData;
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();
    this.jobDocs$ = this.store.select(
      InternalDeliveryOrderSelectors.selectInternalJobDocs
    );
  }

  headerHeightSetter() {
    var padding = 20;
    var height = this.headerHeightGetter() + padding;
    this.gridApi.setHeaderHeight(height);
    // this.gridApi.resetRowHeights();
  }

  headerHeightGetter() {
    var columnHeaderTexts = document.querySelectorAll(".ag-header-cell-text");

    var columnHeaderTextsArray = [];

    columnHeaderTexts.forEach((node) => columnHeaderTextsArray.push(node));

    var clientHeights = columnHeaderTextsArray.map(
      (headerText) => headerText.clientHeight
    );
    var tallestHeaderTextHeight = Math.max(...clientHeights);
    return tallestHeaderTextHeight;
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  clear() {
    let dataSource = {
      getRows(params: any) {
        params.successCallback([], 0);
      },
    };
    this.gridApi.setServerSideDatasource(dataSource);
  }

  pageFiltering(filterModel) {
    var noFilters = Object.keys(filterModel).length <= 0;

    if (noFilters)
      return {
        by: (viewModel) => true,
        isFiltering: noFilters,
      };

    return {
      by: (viewModel) =>
        Object.keys(filterModel)
          .map((col) => {
            let newCol = col.split("."),
              tableColVal = null;

            if (newCol.length === 2)
              tableColVal = viewModel.bl_del_trip_hdr[`${newCol[1]}`];
            else if (newCol.length === 3)
              tableColVal =
                viewModel.bl_del_trip_hdr[`${newCol[1]}`][`${newCol[2]}`];

            if (typeof tableColVal == "number")
              return tableColVal === Number(filterModel[`${col}`].filter);
            else
              return tableColVal
                .toLowerCase()
                .includes(filterModel[`${col}`].filter.toLowerCase());
          })
          .reduce((p, c) => p && c),
      isFiltering: noFilters,
    };
  }

  pageSorting(sortModel) {
    return (data) => {
      if (sortModel.length <= 0) return data;

      let newData = data;
      sortModel.forEach((model) => {
        let col = model.colId.split(".");
        newData =
          model.sort === "asc"
            ? newData.sort(
                (p, c) => 0 - (p[col[0]][col[1]] > c[col[0]][col[1]] ? -1 : 1)
              )
            : newData.sort(
                (p, c) => 0 - (p[col[0]][col[1]] > c[col[0]][col[1]] ? 1 : -1)
              );
      });
      return newData;
    };
  }

  searchQuery(query: string, table: string) {
    var query$ = this.subQueryService
      .post({ subquery: query, table: table }, this.apiVisa)
      .pipe(switchMap((res) => of(res)));

    query$
      .pipe(filter((res: ApiResponseModel<any>) => res.data.length > 0))
      .subscribe((res) => {
        this.retrieveData([
          this.setCriteria("guids", res.data),
          this.setCriteria("calcTotalRecords", "true"),
        ]);
      });
    query$
      .pipe(filter((res: ApiResponseModel<any>) => res.data.length === 0))
      .subscribe((res) => {
        this.clear();
      });
  }

  onSearch(e: SearchQueryModel) {
    !e.isEmpty
      ? this.searchQuery(e.queryString, e.table)
      : this.retrieveData([this.setCriteria("calcTotalRecords", "true")]);
  }

  retrieveData(criteria) {
    if (criteria) {
      const datasource = {
        getRows: this.getRowsFactory(criteria),
      };
      this.gridApi.setServerSideDatasource(datasource);
    }
  }

  getRowsFactory(criteria) {
    let offset = 0;
    let limit = this.paginationComponent.rowPerPage;

    return (grid) => {
      var filter = this.pageFiltering(grid.request.filterModel);
      var sortOn = this.pageSorting(grid.request.sortModel);

      if (!Object.keys(grid.request.filterModel).length) {
        offset = grid.request.startRow;
        limit = grid.request.endRow - offset;
      }
    };
  }

  setCriteria(columnName, value) {
    return { columnName, operator: "=", value };
  }

  DateConvert(date) {
    if (date != null && "" !== date) {
      const moments = moment(date).format("YYYY-MM-DD HH:mm:ss");
      return moments;
    } else {
      const notEdited = " ";
      return notEdited;
    }
  }

  getRowStyle = (params) => {
    if (params.node.footer || params.node.group) {
      return { fontWeight: "bold" };
    }
  };

  onSortChanged(e) {
    e.api.refreshCells();
  }

  onFilterChanged(e) {
    e.api.refreshCells();
  }

  ngOnDestroy() {
    this.subSink.unsubscribe();
  }



}
