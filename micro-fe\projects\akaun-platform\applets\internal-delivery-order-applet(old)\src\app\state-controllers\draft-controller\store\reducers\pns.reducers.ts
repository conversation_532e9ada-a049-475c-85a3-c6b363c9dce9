import { Action, createReducer, on } from '@ngrx/store';
import { InternalDeliveryOrderActions } from '../../../internal-delivery-order-controller/store/actions';
import { PNSActions } from '../actions';
import { initState, pnsAdapter, PNSState } from '../states/pns.states';

export const pnsReducers = createReducer(
    initState,
    on(PNSActions.addPNS, (state, action) => pnsAdapter.addOne({
        guid: state.ids.length,
        ...action.pns
    }, state)),
    on(PNSActions.deletePNS, (state, action) => pnsAdapter.removeOne(action.guid, state)),
    on(PNSActions.editPNS, (state, action) => pnsAdapter.upsertOne(action.pns, state)),
    on(PNSActions.resetPNS, (state, action) => pnsAdapter.removeAll(state)),
    on(PNSActions.addBin, (state, action) => ({...state, binData: {
        ...state.binData,
        bin_hdr_guid: action.bin.bin_hdr_guid,
        bin_hdr_code: action.bin.bin_hdr_code,
        bin_line_guid: action.bin.bin_line_guid,
        qty: action.bin.qty,
        container_measure: action.bin.container_measure,
        container_qty: action.bin.container_qty
    }})),
    on(InternalDeliveryOrderActions.resetDeliveryOrder, (state, action) => pnsAdapter.removeAll(state)),
    on(InternalDeliveryOrderActions.selectEntityInit, (state, action) => pnsAdapter.setAll(action.entity.bl_fi_generic_doc_line, state)),
);

export function reducers(state: PNSState | undefined, action: Action) {
    return pnsReducers(state, action);
}
