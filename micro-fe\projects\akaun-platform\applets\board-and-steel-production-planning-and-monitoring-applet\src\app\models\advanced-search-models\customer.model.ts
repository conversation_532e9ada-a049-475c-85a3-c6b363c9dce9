import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const customerSearchModel: SearchModel = {
  label: {
    type: 'Entity Type',
    modifiedDate: 'Modified Date',
    status: 'Status',
  },
  dataType: {
    type: ['select', ['CORPORATE', 'INDIVIDUAL']],
    modifiedDate: 'date',
    status: ['select', ['ACTIVE', 'INACTIVE']],
  },
  form: new FormGroup({
    type: new FormControl(),
    modifiedDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    status: new FormControl()
  }),

  query: (query) => `hdr.status = 'ACTIVE' AND hdr.is_customer IN (true) AND  hdr.name ILIKE '%${query}%'`,
  table: `bl_fi_mst_entity_hdr`,
  queryCallbacks: {
    type: type => type ? ` hdr.txn_type = '${type}'` : '',
    status: status => status ? ` hdr.status = '${status}'` : '',
    modifiedDate: modifiedDate => {
      if (modifiedDate.from || modifiedDate.to) {
        // const from = modifiedDate.from ? modifiedDate.from : '';
        // const to = modifiedDate.to ? modifiedDate.to : '';
        const from = modifiedDate.from ? `hdr.updated_date >= '${modifiedDate.from.format('YYYY-MM-DD')}'` : '';
        const to = modifiedDate.to ? `hdr.updated_date <= '${modifiedDate.to.format('YYYY-MM-DD')}'` : '';
        return `${from} ${(from && to) ? 'AND' : ''} ${to}`;
      }
      return '';
    },
  },
};

