import { AfterViewChecked, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { bl_fi_generic_doc_single_line, bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../models/applet-settings.model';
import { Store } from '@ngrx/store';
import { InternalJobOrderStates } from '../../../../state-controllers/internal-job-order-controller/store/states';
import { JobOrderNoSelectors } from '../../../../state-controllers/draft-controller/store/selectors';
import { InternalJobOrderSelectors } from '../../../../state-controllers/internal-job-order-controller/store/selectors';
@Component({
  selector: 'app-internal-job-order-create-main',
  templateUrl: './internal-job-order-create-main.component.html',
  styleUrls: ['./internal-job-order-create-main.component.css']
})
export class InternalJobOrderCreateMainComponent implements OnInit, OnDestroy, AfterViewChecked {

  @Input() draft$: Observable<bl_mrp_job_order_hdr_RowClass>;
  @Input() appletSettings$: Observable<AppletSettings>;

  @Output() itemCode = new EventEmitter();
  @Output() updateMain = new EventEmitter();

  apiVisa = AppConfig.apiVisa;

  PROCESS_STATUS = ['PLANNED','IN_PROGRESS','COMPLETED','ON_HOLD','CANCELLED'];
  QC_OUTPUT_STATUS = ['PASSED','REJECTED'];

  private subs = new SubSink();

  form: FormGroup;
  uom;
  editMode;

  leftColControls = [
    {label: 'Job Order No', formControl: 'jobOrderNo', type: 'text', readonly: false},
    {label: 'Item Code', formControl: 'itemCode', type: 'itemCode', readonly: false},
    {label: 'UOM', formControl: 'uom', type: 'text', readonly: false},
    {label: 'Ad-hoc Quantity', formControl: 'adHocQty', type: 'number', readonly: false },
    {label: 'Process Status', formControl: 'process_status', type: 'process_status', readonly: false},
    {label: 'Description', formControl: 'description', type: 'text', readonly: false},
    {label: 'QC Output Status', formControl: 'qcOutputStatus', type: 'qcOutputStatus', readonly: false},
  ];

  rightColControls = [
    {label: 'Job Order Date', formControl: 'jobOrderDate', type: 'date', readonly: false},
    {label: 'Item Name', formControl: 'itemName', type: 'itemName', readonly: false},
    {label: 'Container Quantity', formControl: 'containerQty', type: 'number', readonly: false},
    {label: 'Total Container Measure', formControl: 'totalContainerMeasure', type: 'number', readonly: false},
    {label: 'Estimated Packing Date', formControl: 'estimatedPackingDate', type: 'date', readonly: false},
    {label: 'Completion Date', formControl: 'completionDate', type: 'date', readonly: false},
    {label: 'Remarks', formControl: 'remarks', type: 'text', readonly: false},
  ];

  constructor(
    protected readonly store: Store<InternalJobOrderStates>,
  ) { }

  ngOnInit() {

   
    this.form = new FormGroup({
      jobOrderNo: new FormControl(''),
      jobOrderDate: new FormControl(''),
      itemCode: new FormControl(''),
      itemName: new FormControl(''),
      itemGuid : new FormControl(''),
      baseQty: new FormControl(''),
      uom: new FormControl(''),
      containerQty: new FormControl(0.00),
      adHocQty: new FormControl(0.00),
      totalContainerMeasure: new FormControl(0.00),
      process_status: new FormControl(''),
      estimatedPackingDate: new FormControl(''),
      description: new FormControl(''),
      completionDate: new FormControl(''),
      qcOutputStatus: new FormControl(),
      remarks: new FormControl(),
    });
    this.store.select(InternalJobOrderSelectors.selectEditMode).subscribe(data => {
      this.editMode = data;
  })

    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectItem).subscribe(data => {
      console.log("qty to fetch",data);
      this.uom = data.bl_fi_mst_item_hdr.uom;
    })
    this.subs.sink = this.draft$.pipe(
      withLatestFrom(this.appletSettings$)
    ).subscribe(([a, b]) => {
      console.log("Abcd",a);
      this.form.patchValue({
        jobOrderNo: a.server_doc_1,

        // totalContainerMeasure: (<any>a.batch_no)?.container_measure,
        itemCode: a.item_code,
        itemName: a.item_name,
        itemGuid: a.item_guid,

        jobOrderDate: a.date_txn,
        uom: a.uom ? a.uom : this.uom,
        containerQty: a.container_qty ? a.container_qty : 0.00,
        adHocQty: a.ad_hoc_qty ? a.ad_hoc_qty : 0.00,
        totalContainerMeasure: a.total_container_measure ? a.total_container_measure : 0.00,
        process_status: a.process_status,
        estimatedPackingDate: a.estimated_pkg_date,
        // description: a.item_desc,
        completionDate: a.completion_date,
        qcOutputStatus: a.qc_output_status,
        remarks: a.remarks,
        // transactionDate: a.date_txn ?? new Date().toISOString().split('T')[0],
        // remarks: a.item_remarks
      });

    });
  }

  ngAfterViewChecked() {
    this.store.select(InternalJobOrderSelectors.selectTotalContainerQty).subscribe(data => {
      this.form.patchValue({
        containerQty : data
      })
      this.onCalculateTotalContainerMeasure();
      this.updateMain.emit(this.form.value)

    })
  }

  onCalculateTotalContainerMeasure(){
    if(this.form){
        const containerQty = parseFloat(this.form.controls['containerQty'].value) ?? 0.0
        const adHocQty = parseFloat(this.form.controls['adHocQty'].value) ?? 0.0

        const totalContainerMeasure = Number(containerQty)+ Number(adHocQty);
        this.form.patchValue({
          totalContainerMeasure : totalContainerMeasure,
        })
        this.form.controls['totalContainerMeasure'].setValue(totalContainerMeasure);
    }
  

  }



  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
