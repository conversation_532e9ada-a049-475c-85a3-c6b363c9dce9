import { NgxMatDateAdapter, NgxMatDateFormats, NgxMatDatetimePickerModule, NgxMatNativeDateModule, NGX_MAT_DATE_FORMATS } from '@angular-material-components/datetime-picker';
import { NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS } from '@angular-material-components/moment-adapter';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { MAT_DATE_LOCALE } from '@angular/material/core';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { AgGridModule } from 'ag-grid-angular';
import { DropDownModule } from 'blg-akaun-ng-lib';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { AppletUtilitiesModule } from '../../feature-modules/applet-utilities.module';
import { CustomNgxDatetimeAdapter } from '../../models/custom-ngx-date-time-adapter';
import { InternalJobOrderEffects } from '../../state-controllers/internal-job-order-controller/store/effects';
import { SlideRendererComponent } from '../utilities/slide-renderer/slide-renderer.component';
import { SalesOrderListingComponent } from './sales-order-listing/sales-order-listing.component';
import { salesOrderFeatureKey } from '../../state-controllers/sales-order-controller/store/reducers/sales-order.reducers';
import { salesOrderReducers } from '../../state-controllers/sales-order-controller/store/reducers';
import { SalesOrderContainerComponent } from './sales-order-container.component';
import { SalesOrderViewComponent } from './sales-order-view/sales-order-view.component';
import { SalesOrderMainComponent } from './sales-order-view/sales-order-main/sales-order-main.component';
import { ItemDetailsComponent } from './sales-order-view/item-details/item-details.component';
import { StockBalanceSummaryComponent } from './sales-order-view/item-details/stock-balance-summary/stock-balance-summary.component';
import { SalesOrderEffects } from '../../state-controllers/sales-order-controller/store/effects';
import { DetailedReservationStockComponent } from './sales-order-view/item-details/stock-balance-summary/detailed-reservation-stock/detailed-reservation-stock.component';
import { DetailedJobOrderStockComponent } from './sales-order-view/item-details/stock-balance-summary/detailed-job-order-stock/detailed-job-order-stock.component';

const CUSTOM_DATE_FORMATS: NgxMatDateFormats  = {
  parse: {
    dateInput: 'l, LTS'
  },
  display: {
    dateInput: 'YYYY-MM-DD HH:mm:ss',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  }
};

@NgModule({
  declarations: [
    SalesOrderContainerComponent,
    SalesOrderListingComponent,
    SalesOrderViewComponent,
    SalesOrderMainComponent,
    ItemDetailsComponent,
    StockBalanceSummaryComponent,
    DetailedReservationStockComponent,
    DetailedJobOrderStockComponent
  ],
  imports: [
    CommonModule,
    UtilitiesModule,
    DropDownModule,
    AgGridModule.withComponents([SlideRendererComponent]),
    StoreModule.forFeature(salesOrderFeatureKey, salesOrderReducers.salesOrder),
    EffectsModule.forFeature([SalesOrderEffects]),
    NgxMatDatetimePickerModule,
    NgxMatNativeDateModule,
    AppletUtilitiesModule
  ],
  providers: [
    {
      provide: NgxMatDateAdapter,
      useClass: CustomNgxDatetimeAdapter,
      deps: [MAT_DATE_LOCALE, NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS]
    },
    { provide: NGX_MAT_DATE_FORMATS, useValue: CUSTOM_DATE_FORMATS },
  ],
  exports: [
    
  ]
})
export class SalesOrderModule { }
