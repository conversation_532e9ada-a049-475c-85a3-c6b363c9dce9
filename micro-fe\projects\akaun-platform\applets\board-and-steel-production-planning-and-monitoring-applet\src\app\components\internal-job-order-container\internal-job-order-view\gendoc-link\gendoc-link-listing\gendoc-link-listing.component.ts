import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { Store } from "@ngrx/store";
import { bl_fi_mst_entity_line_RowClass, FinancialItemService, GenericDocLineService, InternalJobsheetService, InternalSalesOrderService, ItemHostPermissionService, ItemPermissionContainerModel, MrpJobOrderGenDocLinkService, Pagination, SalesInvoiceService, ServiceNoteIssueHdrGenericDocLinkService, SubQueryService } from "blg-akaun-ts-lib";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { forkJoin, iif, Observable, of, zip } from "rxjs";
import { SubSink } from "subsink2";

import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { catchError, map } from "rxjs/operators";
import { FormControl } from "@angular/forms";
import { InternalJobOrderStates } from "../../../../../state-controllers/internal-job-order-controller/store/states";
import { ViewColumnFacade } from "../../../../../facades/view-column.facade";
import { InternalJobOrderSelectors } from "../../../../../state-controllers/internal-job-order-controller/store/selectors";
import { InternalJobOrderActions } from "../../../../../state-controllers/internal-job-order-controller/store/actions";
import { JobOrderNoActions } from "../../../../../state-controllers/draft-controller/store/actions";

@Component({
  selector: "app-gendoc-link-listing",
  templateUrl: "./gendoc-link-listing.component.html",
  styleUrls: ["./gendoc-link-listing.component.scss"],
})
export class InvoiceServiceNoteLinkListingComponent
  extends ViewColumnComponent
  implements OnInit, OnDestroy {
  // @Input() itemCategory$: Observable<any>;
  deactivateAdd$;
  @Input() localState: any;

  @Output() lineItem = new EventEmitter<bl_fi_mst_entity_line_RowClass>();

  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    cellStyle: { textAlign: "left" },
  };

  gridApi;

  protected readonly index = 1;
  columnsDefs;
  rowData: any[] = [];
  apiVisa = AppConfig.apiVisa;
  labelGuids: any = [];
  tempCat$: Observable<boolean>;
  tryError$: Observable<any[]>;
  tempCatRow$: Observable<any>;
  protected subs = new SubSink();
  pinnedBottomRowData: any;
  packageListing: any = [];
  searchValue;
  pagination = new Pagination();
  SQLGuids: string[] = null;
  jobOrderHdrGuid;
  totalContainerQty = 0.00;

  public search = new FormControl();

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;
  constructor(
    private itemHostPermissionService: ItemHostPermissionService,
    private serviceNoteIssueHdrGenericDocLinkService : ServiceNoteIssueHdrGenericDocLinkService,
    private mrpJobOrderGenDocLinkService : MrpJobOrderGenDocLinkService,
    private genericDocLineService: GenericDocLineService,
    private subQueryService: SubQueryService,
    private itemService: FinancialItemService,
    private soService: InternalSalesOrderService,
    private readonly store: Store<InternalJobOrderStates>,
    private viewColFacade: ViewColumnFacade
  ) {
    super();
    const customComparator = (valueA, valueB) => {
      if (valueA != null && "" !== valueA && valueB != null && "" !== valueB) {
        return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
      }
    };
    this.columnsDefs = [
      {
        headerName: "Sales Invoice No",
        field: "doc_no",
        width: 250,
        checkboxSelection: true,
        comparator: customComparator,
      },
      {
        headerName: "Item Code",
        field: "item_code",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Item Name",
        field: "item_name",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "Quantity",
        field: "qty",
        width: 250,
        comparator: customComparator,
      },
      {
        headerName: "UOM",
        field: "uom",
        width: 250,
        comparator: customComparator,
      },
     
    ];
  }

  ngOnInit() {
    this.subs.sink = this.store
      .select(InternalJobOrderSelectors.selectEntity)
      .subscribe((joborderdata) => {
        console.log("The joborderdata itselft", joborderdata);
        this.jobOrderHdrGuid = joborderdata.bl_mrp_job_order_hdr.guid;
      });

    console.log("Guest tennt guid", this.jobOrderHdrGuid);
  }


  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {

        this.store.dispatch(InternalJobOrderActions.loadGenDocLinkInit({ request: grid.request }));


        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
          {
            columnName: "job_order_guid",
            operator: "=",
            value: this.jobOrderHdrGuid,
          },
          // { columnName: 'doc_type', operator: '=', value: 'INTERNAL_SALES_INVOICE' },
          {
            columnName: 'guids', operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
        ];
        let totalrec = 0;
        this.mrpJobOrderGenDocLinkService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {

            totalrec = resolved.totalRecords;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                this.soService.getByGuid(itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_hdr_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid ?
                this.genericDocLineService.getByGuid(itemperm.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),
                ).pipe(
                  map(([b_a,b_b]) => {
                    console.log("B_a",b_a);
                    console.log("B_b",b_b);
                    let obj = {"guid":'', "doc_no": '', "item_name": '', "item_code": '', "qty": '', 'uom':'' };
                    obj.guid = itemperm.bl_mrp_job_order_generic_doc_link.guid.toString();
                    obj.doc_no = b_a.data.bl_fi_generic_doc_hdr.server_doc_1;
                    obj.item_name = b_b ? b_b.data.bl_fi_generic_doc_line.item_name : '';
                    obj.item_code =b_b ? b_b.data.bl_fi_generic_doc_line.item_code : '';
                    obj.qty =b_b ? b_b.data.bl_fi_generic_doc_line.quantity_base : '';
                    obj.uom = b_b ? b_b.data.bl_fi_generic_doc_line.uom : '';

                    // obj.guid = itemperm.bl_t2t_fi_item_to_tenant_link.guid.toString();
                    // obj.basic_type = b_a.data.bl_fi_mst_item_hdr.txn_type;
                    // obj.sub_item_type = b_a.data.bl_fi_mst_item_hdr.sub_item_type;
                    this.totalContainerQty = this.totalContainerQty + (b_b ? b_b.data.bl_fi_generic_doc_line.quantity_base : 0.00);
                    console.log("Total Container Quantity", this.totalContainerQty);
                    this.store.dispatch(InternalJobOrderActions.selectTotalContainerQty({totalContainerQty : this.totalContainerQty}))
                    return obj;
                  })
                )
            )
            );
            return iif(() => resolved.totalRecords > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              this.store.dispatch(InternalJobOrderActions.loadGenDocLinkSuccess({ totalRecords: totalrec }));
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
            this.store.dispatch(InternalJobOrderActions.loadGenDocLinkFailed({ error: err.message }));
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
      if (resolved) {
        this.gridApi.refreshServerSideStore({ purge: true });
        this.store.dispatch(InternalJobOrderActions.resetAgGrid());
      }
    });
  }

  quickSearch() {
    this.gridApi.setQuickFilter(this.searchValue);
  }

  onSearch() {
    let searchValue = this.search.value;
    let query = `SELECT link.guid as requiredGuid FROM bl_svc_issue_gendoc_link AS link INNER JOIN bl_fi_generic_doc_line AS line ON link.generic_doc_line_guid = line.guid INNER JOIN bl_fi_generic_doc_hdr AS hdr ON link.generic_doc_hdr_guid = hdr.guid WHERE ((line.item_name = '${searchValue}') OR (line.item_code = '${searchValue}') OR (hdr.server_doc_1 = '${searchValue}')) and link.hdr_guid = '${this.jobOrderHdrGuid}' and link.doc_type = 'INTERNAL_SALES_INVOICE' and line.server_doc_type ='INTERNAL_SALES_INVOICE'`;
    if (query) {
      const sql = {
        subquery: query,
        table: 'bl_svc_issue_gendoc_link'
      };
      this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({
        next: resolve => {
          console.log("Searchhh",resolve);
          this.SQLGuids = resolve.data;
          this.paginationComp.firstPage();
          this.gridApi.refreshServerSideStore();
        }
      });
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }
  onRowClicked(entity) {
    // this.store.dispatch(GuestTenantActions.onRowClick({ rowData: entity }));

    if (!this.localState.deactivateList) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateList: true,
      });
      this.viewColFacade.onNextAndReset(this.index, 12);
    }
  }
  onDeleteCall(guid){
    this.store.dispatch(InternalJobOrderActions.selectGenericDocLinkGuid({guid :guid}));
    this.store.dispatch(InternalJobOrderActions.deleteGenericDocLinkInit());
  }
  onDelete(){
    this.gridApi.getSelectedRows().forEach(row => {
      console.log("Row guid",row.guid);
    
      setTimeout(() => {
        this.onDeleteCall(row.guid);
      }, 1000);
    })
  }
  onNext() {
    // this.viewColFacade.startDraft();
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
    });
    this.viewColFacade.onNextAndReset(this.index, 10);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
  callRefreshAfterMillis(params, millis, gridApi) {
    setTimeout(function () {
      gridApi.refreshCells(params);
    }, millis);
  }
}
