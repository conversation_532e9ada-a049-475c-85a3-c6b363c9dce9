import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProcessingViewLineItemsComponent } from './processing-view-line-items.component';

describe('ProcessingViewLineItemsComponent', () => {
  let component: ProcessingViewLineItemsComponent;
  let fixture: ComponentFixture<ProcessingViewLineItemsComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ProcessingViewLineItemsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProcessingViewLineItemsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
