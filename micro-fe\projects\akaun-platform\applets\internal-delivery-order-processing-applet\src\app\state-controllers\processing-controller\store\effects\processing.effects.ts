import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { of } from 'rxjs';
import { map, catchError, exhaustMap, withLatestFrom } from 'rxjs/operators';
import { bl_fi_generic_doc_ext_RowClass, InternalOutboundDeliveryOrderService } from 'blg-akaun-ts-lib';
import { ProcessingActions } from '../actions';
import { ToastrService } from 'ngx-toastr';
import { ProcesssingStates } from '../states';
import { Store } from '@ngrx/store';
import { ProcessingSelectors } from '../selectors';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { AppConfig } from 'projects/shared-utilities/visa';

@Injectable()
export class ProcessingEffects {

  apiVisa = AppConfig.apiVisa;

  editDeliveryOrder$ = createEffect(() => this.actions$.pipe(
    ofType(ProcessingActions.editDeliveryOrderInit),
    withLatestFrom(this.store.select(ProcessingSelectors.selectDraftEdit)),
    exhaustMap(([a, b]) => this.doService.getByGuid(b.bl_fi_generic_doc_hdr.guid.toString(), this.apiVisa).pipe(
      map((b_inner) => {
        let processingStatus = b_inner.data.bl_fi_generic_doc_ext.find(ext => ext.param_code === 'PROCESSING_STATUS');
        if (!processingStatus) {
          processingStatus = new bl_fi_generic_doc_ext_RowClass;
          processingStatus.param_code = 'PROCESSING_STATUS';
          processingStatus.param_name = 'PROCESSING_STATUS';
          processingStatus.param_type = 'STRING';
          b_inner.data.bl_fi_generic_doc_ext.push(processingStatus);
        }
        processingStatus.value_string = a.status;
        return b_inner.data;
      }),
      exhaustMap(c_inner => this.doService.put(c_inner, this.apiVisa).pipe(
        map(() => {
          this.toastr.success(
            'Process updated successfully',
            'Success',
            {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 1300
            }
          );
          this.viewColFacade.updateInstance(0, {
            deactivateAdd: false,
            deactivateList: false
          });
          this.viewColFacade.resetIndex(0);
          return ProcessingActions.editDeliveryOrderSuccess();
        }),
        catchError(err => {
          this.toastr.error(
            err.message,
            'Error',
            {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 1300
            }
          );
          return of(ProcessingActions.editDeliveryOrderFailed({error: err.message}));
        })
      )),
      catchError(err => {
        this.toastr.error(
          err.message,
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return of(ProcessingActions.editDeliveryOrderFailed({error: err.message}));
      })
    ))
  ));


  constructor(
    private actions$: Actions,
    private doService: InternalOutboundDeliveryOrderService,
    private toastr: ToastrService,
    private store: Store<ProcesssingStates>,
    private viewColFacade: ViewColumnFacade
  ) {}
}
