import { ProcessingActions } from '../actions';
import { Action, createReducer, on } from '@ngrx/store';
import { initState } from '../states/processing.states';
import { ProcessingState } from '../states/processing.states';
import { bl_fi_generic_doc_ext_RowClass, GenericDocContainerModel } from 'blg-akaun-ts-lib';

export const processingFeatureKey = 'processing';

export const processingReducer = createReducer(
  initState,
  on(ProcessingActions.loadDeliveryOrderSuccess, (state, action) =>
    ({...state, totalRecords: action.totalRecords})),
  on(ProcessingActions.loadDeliveryOrderFailed, (state, action) =>
    ({...state, errorLog: [...state.errorLog, {timeStamp: new Date(), log: action.error}]})),
  on(ProcessingActions.editDeliveryOrderSuccess, (state, action) => ({
    ...state, updateAgGrid: true
  })),
  on(ProcessingActions.resetAgGrid, (state, action) => ({
    ...state, updateAgGrid: false
  })),
  on(ProcessingActions.selectEntity, (state, action) => ({
    ...state, selectedEntity: action.entity, draftEdit: action.entity
  }))
);

export function reducer(state: ProcessingState | undefined, action: Action) {
  return processingReducer(state, action);
}
