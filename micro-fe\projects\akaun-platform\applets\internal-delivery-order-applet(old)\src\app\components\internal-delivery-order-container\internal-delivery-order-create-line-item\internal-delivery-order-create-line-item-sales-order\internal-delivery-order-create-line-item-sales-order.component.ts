import { Component, OnInit, ChangeDetectionStrategy, Input, EventEmitter, Output, OnDestroy } from '@angular/core';
import { Store } from '@ngrx/store';
import {
  BranchService,
  CompanyService,
  CustomerService,
  GenericDocContainerModel,
  LocationService,
  Pagination,
  InternalSalesOrderService } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable, zip, forkJoin, EMPTY, of, iif } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { internalDeliveryOrderSearchModel } from '../../../../models/advanced-search-models/internal-delivery-order.model';
import { InternalDeliveryOrderActions } from '../../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderStates } from '../../../../state-controllers/internal-delivery-order-controller/store/states';

@Component({
  selector: 'app-internal-delivery-order-create-line-item-sales-order',
  templateUrl: './internal-delivery-order-create-line-item-sales-order.component.html',
  styleUrls: ['./internal-delivery-order-create-line-item-sales-order.component.css']
})
export class InternalDeliveryOrderCreateLineItemSalesOrderComponent implements OnInit, OnDestroy {

  @Input() localState: any;

  @Output() next = new EventEmitter<any>();

  private subs = new SubSink();

  searchModel = internalDeliveryOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Doc No', field: 'bl_fi_generic_doc_hdr.server_doc_1', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Branch', field: 'bl_fi_generic_doc_hdr.code_branch', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Location', field: 'bl_fi_generic_doc_hdr.code_location', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Customer Name', field: 'bl_fi_generic_doc_hdr.doc_entity_hdr_json', cellStyle: () => ({'text-align': 'left'}),
    valueFormatter: params => params.value.entityName},
    {headerName: 'Status', field: 'bl_fi_generic_doc_hdr.status', cellStyle: () => ({'text-align': 'left'})},
  ];

  constructor(
    private readonly store: Store<InternalDeliveryOrderStates>,
    private soService: InternalSalesOrderService,
    // private compService: CompanyService,
    private brnchService: BranchService,
    private lctnService: LocationService,
    // private cstmrService: CustomerService
    ) { }

  ngOnInit() {
    console.log("Hello sales order")
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        // this.store.dispatch(InternalPackingOrderActions.loadPackingOrdersInit({request: grid.request}));
        this.subs.sink = this.soService.getByCriteria(new Pagination(grid.request.startRow, grid.request.endRow, [
          {columnName: 'calcTotalRecords', operator: '=', value: 'true'}
        ]), apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<GenericDocContainerModel>[] = [];
            b.data.forEach( doc => source.push(
              zip(
                // this.cstmrService.getByGuid(doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString(), apiVisa).pipe(
                //   catchError((err) => of(err))
                // ),
                this.brnchService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_branch.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                this.lctnService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_store.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                )).pipe(
                  map(([b_b, b_c]) => {
                    // doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid = b_a.error ? b_a.error.code : b_a.data.bl_fi_mst_entity_hdr.name;
                    // doc.bl_fi_generic_doc_hdr.guid_branch = b_b.error ? b_b.error.code : b_b.data.bl_fi_mst_branch.name;
                    // doc.bl_fi_generic_doc_hdr.guid_store = b_c.error ? b_c.error.code : b_c.data.bl_inv_mst_location.name;
                    doc.bl_fi_generic_doc_hdr.code_branch = b_b.error ? b_b.error.code : b_b.data.bl_fi_mst_branch.code;
                    doc.bl_fi_generic_doc_hdr.code_location = b_c.error ? b_c.error.code : b_c.data.bl_inv_mst_location.code;
                    return doc;
                  })
                )
            ));
            return iif(() => b.data.length > 0,
              forkJoin(source).pipe(map(() => b)),
              of(b)
            );
          })
        ).subscribe( resolved => {
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderSuccess({totalRecords: resolved.totalRecords}));
          grid.successCallback(resolved.data, resolved.totalRecords);
        }, err => {
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderFailed({error: err.message}));
          grid.failCallback();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    // this.subs.sink = this.store.select(InternalSalesOrderSelectors.selectAgGrid).subscribe( a => {
    //   if (a) {
    //     this.gridApi.purgeServerSideCache();
    //     this.store.dispatch(InternalSalesOrderActions.resetAgGrid());
    //   }
    // });
  }

  onRowClicked(entity: GenericDocContainerModel) {
    if (entity) {
      this.store.dispatch(InternalDeliveryOrderActions.selectDoc({entity}));
      this.next.emit();
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
