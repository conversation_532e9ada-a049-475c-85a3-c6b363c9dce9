import { Injectable } from '@angular/core';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { ViewColumn } from 'projects/shared-utilities/view-column';
import { InternalDeliveryOrderAddLineItemComponent } from '../components/internal-delivery-order-container/internal-delivery-order-create-line-item/internal-delivery-order-add-line-item/internal-delivery-order-add-line-item.component';
import { InternalDeliveryOrderCreateLineItemComponent } from '../components/internal-delivery-order-container/internal-delivery-order-create-line-item/internal-delivery-order-create-line-item.component';
import { InternalDeliveryOrderCreateComponent } from '../components/internal-delivery-order-container/internal-delivery-order-create/internal-delivery-order-create.component';
import { BatchNumberListingComponent } from '../components/internal-delivery-order-container/internal-delivery-order-edit-line-item/batch-number/batch-number-listing/batch-number-listing.component';
import { BinNumberListingComponent } from '../components/internal-delivery-order-container/internal-delivery-order-edit-line-item/bin-number/bin-number-listing/bin-number-listing.component';
import { InternalDeliveryOrderEditLineItemComponent } from '../components/internal-delivery-order-container/internal-delivery-order-edit-line-item/internal-delivery-order-edit-line-item.component';
import { KnockoffAddComponent } from '../components/internal-delivery-order-container/internal-delivery-order-edit-line-item/knockoff-add/knockoff-add.component';
import { KnockoffEditComponent } from '../components/internal-delivery-order-container/internal-delivery-order-edit-line-item/knockoff-edit/knockoff-edit.component';
import { InternalDeliveryOrderListingComponent } from '../components/internal-delivery-order-container/internal-delivery-order-listing/internal-delivery-order-listing.component';
import { InternalDeliveryOrderSelectBillingAddressComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-billing-address/internal-delivery-order-select-billing-address.component';
import { InternalDeliveryOrderSelectContactPersonComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-contact-person/internal-delivery-order-select-contact-person.component';
import { CustomerCreateComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-create/customer-create.component';
import { CreditLimitsEditComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/credit-limits-main/credit-limits-edit/credit-limits-edit.component';
import { CreditLimitsComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/credit-limits-main/credit-limits/credit-limits.component';
import { CreditTermsEditComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/credit-terms-main/credit-terms-edit/credit-terms-edit.component';
import { CreditTermsComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/credit-terms-main/credit-terms/credit-terms.component';
import { CreateAddressComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-address/address-create/customer-address-create.component';
import { EditAddressComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-address/address-edit/customer-address-edit.component';
import { CreateBranchComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-branch/branch-create/branch-create.component';
import { EditBranchComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-branch/branch-edit/branch-edit.component';
import { AddCategoryComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-category/category-add/category-add.component';
import { EditCategoryComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-category/category-edit/category-edit.component';
import { CreateContactComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-contact/contact-create/customer-contact-create.component';
import { EditContactComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-contact/contact-edit/customer-contact-edit.component';
import { CustomerEditComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-edit.component';
import { EditItemPricingComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-item-pricing/item-pricing-edit/item-pricing-edit.component';
import { CreateLoginComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-login/login-create/login-create.component';
import { EditLoginComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-login/login-edit/login-edit.component';
import { CreatePaymentConfigComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-payment-config/payment-config-create/payment-config-create.component';
import { EditPaymentConfigComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-payment-config/payment-config-edit/payment-config-edit.component';
import { CreateTaxComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-tax/tax-create/customer-tax-create.component';
import { EditTaxComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/customer-edit/customer-tax/tax-edit/customer-tax-edit.component';
import { InternalDeliveryOrderSelectCustomerComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-customer/internal-delivery-order-select-customer.component';
import { InternalDeliveryOrderSelectShippingAddressComponent } from '../components/internal-delivery-order-container/internal-delivery-order-select-shipping-address/internal-delivery-order-select-shipping-address.component';
import { InternalDeliveryOrderTransferSalesOrderComponent } from '../components/internal-delivery-order-container/internal-delivery-order-transfer-sales-order/internal-delivery-order-transfer-sales-order.component';
import { InternalDeliveryOrderViewLineItemComponent } from '../components/internal-delivery-order-container/internal-delivery-order-view-line-item/internal-delivery-order-view-line-item.component';
import { InternalDeliveryOrderViewComponent } from '../components/internal-delivery-order-container/internal-delivery-order-view/internal-delivery-order-view.component';

@Injectable()
export class InternalDeliveryOrderPagesService {

  private initialState: ViewColumnState = {
    firstColumn: new ViewColumn(0, InternalDeliveryOrderListingComponent, 'Internal Delivery Order Listing', {
      deactivateAdd: false,
      deactivateList: false
    }),
    secondColumn: null,
    viewCol: [
      new ViewColumn(0, InternalDeliveryOrderListingComponent, 'Internal Delivery Order Listing', {
        deactivateAdd: false,
        deactivateList: false
      }),
      new ViewColumn(1, InternalDeliveryOrderViewComponent, 'Internal Delivery Order View', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateEntity: false,
        deactivateSalesAgent: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateList: false,
        selectedIndex: 0,
        customFields: [
          { label: 'Transport Driver', formControl: 'transportDriver', type: 'text', readonly: false },
          { label: 'Transport Company', formControl: 'transportCompany', type: 'text', readonly: false },
          { label: 'Lorry No', formControl: 'lorryNo', type: 'text', readonly: false },
          { label: 'Total Weight (kg)', formControl: 'totalWeight', type: 'number', readonly: false },
          { label: 'Total Quantity', formControl: 'totalQuantity', type: 'number', readonly: false },
        ]
      }),
      // TODO: add another view column to edit item just like in sales invoice
      // new ViewColumn(2, InternalDeliveryOrderViewLineItemComponent, 'Internal Delivery Order View Line Item', {
      //   deactivateChange: false,
      //   deactivateReturn: false
      // }),
      new ViewColumn(2, InternalDeliveryOrderEditLineItemComponent, 'Internal Sales Order Edit Line', {
        deactivateReturn: false,
        selectedIndex: 0,
        itemSelectedIndex: 0,
        serialSelectedIndex: 0,
        deleteConfirmation: false
      }),
      new ViewColumn(3, InternalDeliveryOrderCreateComponent, 'Internal Delivery Order Create', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateEntity: false,
        deactivateSalesAgent: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateList: false,
        selectedIndex: 0,
        customFields: [
          // {label: 'PO Number', formControl: 'poNumber', type: 'text', readonly: true},
          { label: 'Transport Driver', formControl: 'transportDriver', type: 'text', readonly: false },
          { label: 'Transport Company', formControl: 'transportCompany', type: 'text', readonly: false },
          { label: 'Lorry No', formControl: 'lorryNo', type: 'text', readonly: false },
          { label: 'Total Weight (kg)', formControl: 'totalWeight', type: 'number', readonly: false },
          { label: 'Total Quantity', formControl: 'totalQuantity', type: 'number', readonly: false },
        ]
      }),
      new ViewColumn(4, InternalDeliveryOrderCreateLineItemComponent, 'Internal Delivery Order Create Line Item', {
        deactivateReturn: false,
        deactivateSOList: false,
        deactivateQuotationList: false,
        deactivateInvoiceList: false,
        selectedIndex: 0,
      }),
      new ViewColumn(5, InternalDeliveryOrderTransferSalesOrderComponent, 'Transfer From Sales Order', {
        deactivateReturn: false,
        selectedIndex: 0
      }),
      new ViewColumn(6, InternalDeliveryOrderSelectCustomerComponent, 'Select Address', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateList: false,
        rowIndexListing: null
      }),
      new ViewColumn(7, InternalDeliveryOrderSelectShippingAddressComponent, 'Select Customer', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateList: false
      }),
      new ViewColumn(8, InternalDeliveryOrderSelectBillingAddressComponent, 'Select Customer', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateList: false
      }),
      new ViewColumn(9, InternalDeliveryOrderSelectContactPersonComponent, 'Select Customer', {
        deactivateAdd: false,
        deactivateReturn: false,
        deactivateList: false
      }),
      new ViewColumn(10, BinNumberListingComponent, 'Bin Number Listing', {
        deactivateReturn: false,
      }),
      new ViewColumn(11, BatchNumberListingComponent, 'Batch Number Listing', {
        deactivateReturn: false,
      }),
      new ViewColumn(12, InternalDeliveryOrderAddLineItemComponent, 'Add Line Item', {
        deactivateReturn: false,
        selectedIndex: 0,
        itemSelectedIndex: 0,
        // serialSelectedIndex: 0,
      }),
      new ViewColumn(13, KnockoffAddComponent, 'Add Knockoff', {
        deactivateReturn: false,
      }),
      new ViewColumn(14, KnockoffEditComponent, 'Edit Knockoff', {
        deactivateReturn: false,
      }),
      new ViewColumn(15, CustomerCreateComponent, 'Customer Create', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(16, CustomerEditComponent, 'Customer Edit', {
        deactivateReturn: false,
        deactivateList: false,
        selectedIndex: 0,
        LimitIndex: 0,
      }),
      new ViewColumn(17, AddCategoryComponent, 'Entity Category Add', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(18, EditCategoryComponent, 'Entity Category Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(19, CreateLoginComponent, 'Customer Login Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(20, EditLoginComponent, 'Customer Login Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(21, CreatePaymentConfigComponent, 'Customer Payment Create', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(22, EditPaymentConfigComponent, 'Customer Payment Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(23, CreateTaxComponent, 'Customer Tax Create', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(24, EditTaxComponent, 'Customer Tax Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(25, CreateAddressComponent, 'Customer Address Create', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(26, EditAddressComponent, 'Customer Address Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(27, CreateContactComponent, 'Customer Contact Create', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(28, EditContactComponent, 'Customer Contact Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(29, CreateBranchComponent, 'Customer Branch Create', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(30, EditBranchComponent, 'Customer Branch Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(31, EditItemPricingComponent, 'Entity Item Pricing Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
        rowIndexList: null,
      }),
      new ViewColumn(32, CreditTermsComponent, 'Customer Credit Term Create', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(33, CreditTermsEditComponent, 'Customer Credit Term Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(34, CreditLimitsComponent, 'Customer Credit Limit Create', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(35, CreditLimitsEditComponent, 'Customer Credit Limit Edit', {
        deactivateReturn: false,
        selectedIndex: 0,
      }),
    ],
    breadCrumbs: [],
    leftDrawer: [],
    rightDrawer: [],
    singleColumn: false,
    prevIndex: null
  };

  get pages() {
    return this.initialState;
  }

  constructor() { }
}
