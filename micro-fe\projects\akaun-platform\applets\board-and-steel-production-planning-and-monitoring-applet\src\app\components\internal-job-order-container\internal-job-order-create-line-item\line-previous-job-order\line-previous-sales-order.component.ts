import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { 
  bl_fi_generic_doc_line_RowClass,
  EntityContainerModel,
  FinancialItemContainerModel,
  FinancialItemService,
  Pagination,
  InternalSalesOrderService, 
  GenericDocContainerModel} from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../../models/advanced-search-models/internal-job-order.model';

@Component({
  selector: 'app-line-previous-sales-order',
  templateUrl: './line-previous-sales-order.component.html',
  styleUrls: ['./line-previous-sales-order.component.css']
})
export class LinePreviousSalesOrderComponent implements OnInit, OnDestroy {

  @Input() customer: EntityContainerModel;

  @Output() item = new EventEmitter<FinancialItemContainerModel>();

  protected subs = new SubSink();

  prevIndex: number;
  protected prevLocalState: any;

  searchModel = internalJobOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Sales Order No', field: 'bl_fi_generic_doc_hdr.server_doc_1', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Sales Order Qty', field: 'quantity_base', type: 'numericColumn'},
    {headerName: 'Open Qty', field: 'qty_open', type: 'numericColumn'},
    {headerName: 'Delivered Qty', field: 'deliveredQuantity', type: 'numericColumn'},
    {headerName: 'UOM', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Unit Price', field: 'unitPrice', type: 'numericColumn'}
    // {headerName: 'Status', field: 'stockBalQty'},
  ];

  constructor(
    private soService: InternalSalesOrderService,
    private fiService: FinancialItemService
  ) {}

  ngOnInit() {}

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        this.subs.sink = this.soService.getLinesByCriteria(new Pagination(grid.request.startRow, grid.request.endRow, [
          {columnName: 'calcTotalRecords', operator: '=', value: 'true'},
          {columnName: 'doc_entity_hdr_guid', operator: '=', value: this.customer.bl_fi_mst_entity_hdr.guid.toString()},
          {columnName: 'line_txn_type', operator: '=', value: 'PNS'},
          {columnName: 'date_txn_to', operator: '=', value: new Date().toISOString()}
        ]), AppConfig.apiVisa).pipe(
        ).subscribe( resolved => {
          resolved.data = resolved.data.map(l =>
            Object.assign({
              item_code: l.bl_fi_generic_doc_line[0].item_code,
              item_name: l.bl_fi_generic_doc_line[0].item_name,
              quantity_base: l.bl_fi_generic_doc_line[0].quantity_base,
              qty_open: l.bl_fi_generic_doc_line[0].qty_open,
              item_property_json: l.bl_fi_generic_doc_line[0].item_property_json,
              deliveredQuantity: (<any>l.bl_fi_generic_doc_line[0]).quantity_base - (<any>l.bl_fi_generic_doc_line[0]).qty_open,
              unitPrice: ((
                parseFloat(l.bl_fi_generic_doc_line[0].amount_net.toString()) +
                parseFloat(l.bl_fi_generic_doc_line[0].amount_discount.toString())) /
                parseFloat(l.bl_fi_generic_doc_line[0].quantity_base.toString())).toFixed(2)
            }, l));
          grid.success({
            rowData: resolved.data,
            rowCount: resolved.totalRecords
          });
        }, err => {
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onRowClicked(entity: GenericDocContainerModel) {
    if (entity) {
      this.subs.sink = this.fiService.getByGuid(
        entity.bl_fi_generic_doc_line[0].item_guid.toString(), AppConfig.apiVisa).subscribe({next: resolve =>
          this.item.emit(resolve.data)
      });
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
