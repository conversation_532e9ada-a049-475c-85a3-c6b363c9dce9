import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { MatSlideToggleChange } from "@angular/material/slide-toggle";
import { ComponentStore } from "@ngrx/component-store";
import { Store } from "@ngrx/store";
import { UUID } from "angular2-uuid";
import {
  ApiResponseModel,
  bl_fi_generic_doc_hdr_RowClass,
  bl_fi_generic_doc_line_RowClass,
  bl_fi_generic_doc_link_RowClass,
  FinancialItemContainerModel,
  FinancialItemService,
  InternalInboundStockTransferService,
  InternalPurchaseGoodsReceivedNotes,
  Pagination,
  SubQueryService,
  ThemeInstanceHeaderContainerModel,
} from "blg-akaun-ts-lib";
import {
  pageFiltering,
  pageSorting,
} from "projects/shared-utilities/listing.utils";
import { SearchQueryModel } from "projects/shared-utilities/models/query.model";
import { PaginationComponent } from "projects/shared-utilities/utilities/pagination/pagination.component";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { forkJoin, iif, Observable, of, Subject } from "rxjs";
import { filter, map, mergeMap, switchMap, tap } from "rxjs/operators";
import { SubSink } from "subsink2";
import { ViewColumnFacade } from "../../../../facades/view-column.facade";
import { internalDeliveryOrderSearchModel } from "../../../../models/advanced-search-models/internal-delivery-order.model";
import { LinkSelectors } from "../../../../state-controllers/draft-controller/store/selectors";
//import { ItemActions } from "../../../../state-controllers/draft-controller/store/actions";
import { DraftStates } from "../../../../state-controllers/draft-controller/store/states";
import { InternalDeliveryOrderActions } from "../../../../state-controllers/internal-delivery-order-controller/store/actions";
import { InternalDeliveryOrderSelectors } from "../../../../state-controllers/internal-delivery-order-controller/store/selectors";
import { InternalDeliveryOrderStates } from "../../../../state-controllers/internal-delivery-order-controller/store/states";

interface LocalState {
  rowIndexList: any;
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRowIndex: number;
}

@Component({
  selector: 'app-internal-delivery-order-ko-for-ST-GRN',
  templateUrl: './internal-delivery-order-ko-for-ST-GRN.component.html',
  styleUrls: ['./internal-delivery-order-ko-for-ST-GRN.component.css']
})

export class InternalDeliveryOrderKOForSTGRNComponent
  extends ViewColumnComponent
  implements OnInit
{
  @Input() localState: any;
  @Output() item = new EventEmitter<FinancialItemContainerModel>();
  // protected localState: LocalState;
  protected subs = new SubSink();
  // readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  editMode$ = this.store.select(InternalDeliveryOrderSelectors.selectEditMode);

  prevIndex: number;
  protected prevLocalState: any;

  // Need to change
  searchModel = internalDeliveryOrderSearchModel;

  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    floatingFilter: true,
  };

  gridApi;
  isChecked = true;
  rowSelection = 'multiple';
  editMode;
  hdr: bl_fi_generic_doc_hdr_RowClass;
  enableListing;
  draftLinks;
  draftLinksIds = [];

  columnsDefs = [
    // { headerName: "No.", field: "no" },
    {
      headerName: "ST GRN No",
      field: "stgrn_no",
      cellStyle: () => ({ "text-align": "left" }),
      maxWidth: 100,
      checkboxSelection: true 
    },
    {
      headerName: "SERVER DOC TYPE",
      field: "server_doc_type_line",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "TXN DATE",
      field: "txn_date",
      cellStyle: () => ({ "text-align": "middle" }),
    },
    {
      headerName: "Item Code",
      field: "item_code",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Item Name",
      field: "item_name",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "UOM",
      field: "uom",
      cellStyle: () => ({ "text-align": "left" }),
    },
    {
      headerName: "Unit Price",
      field: "unit_price",
      cellStyle: () => ({ "text-align": "right" }),
    },
    { headerName: "Base Qty", 
      field: "order_qty", 
      // type: "numericColumn",
      cellStyle: () => ({ "text-align": "middle" }) 
    },
    { headerName: 'Balance Qty.', type: 'numericColumn', maxWidth: 100,
      valueGetter: (params) => { return params.data.open_qty }
    },
    { headerName: 'Knockoff Qty.', type: 'numericColumn', maxWidth: 150,
      editable: true,
      valueGetter: (params) => { return params.data.knock_off_qty; },
      valueSetter: (params) => {
        let newKOvalue = parseInt(params.newValue);
        let valueChanged = newKOvalue <= params.data.open_qty && newKOvalue >= 0;
        if (valueChanged) {
          params.data.knock_off_qty = newKOvalue;
        }
        return valueChanged;
      }
    },
    /*
    {
      headerName: "Tracking ID",
      field: "tracking_id",
      cellStyle: () => ({ "text-align": "left" }),
    },
    */
    
  ];
  

  SQLGuids: string[] = null;
  pagination = new Pagination();
  snapshot: any;
  totalCount: number;
  totalRecords$: Subject<number> = new Subject<number>();
  emptyGrid: boolean;
  criteriaList: { columnName: string; operator: string; value: string; }[];
 

  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  arraySize = [];
  arrayPromise = [];
  arrayData = [];

  constructor(
    private sqlService: SubQueryService,
    private stgrnService: InternalInboundStockTransferService,
    private viewColFacade: ViewColumnFacade,
    private subQueryService: SubQueryService,
    private componentStore: ComponentStore<LocalState>,
    private store: Store<InternalDeliveryOrderStates>,
    private readonly draftStore: Store<DraftStates>
  ) {
    super();
    this.editMode$.subscribe((mode) => this.editMode = mode);
  }

  ngOnInit() {
    // this.subs.sink = this.localState$.subscribe((a) => {
    //   this.localState = a;
    //   this.componentStore.setState(a);
    // });
    this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.selectDraft).subscribe((cont) => {
      let hdr = cont.bl_fi_generic_doc_hdr;
      this.hdr = hdr;
    })
    this.subs.sink = this.draftStore.select(LinkSelectors.selectLinkState).subscribe(
      resolve => {
        console.log(resolve);
        this.draftLinks = resolve.entities;
        this.draftLinksIds = resolve.ids;
      }
    )
  }

  toggle(event: MatSlideToggleChange) {
    if(event.checked) {
      this.rowSelection = 'multiple';
    } else {
      this.rowSelection = 'single';
      this.gridApi.deselectAll();
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  searchQuery(query: string) {

    const query$ = this.subQueryService
      .post({ 'subquery': query, 'table': 'bl_fi_generic_doc_line' }, AppConfig.apiVisa)
      .pipe(
        switchMap(resp => of(resp))
      );
    this.subs.sink = query$.pipe(
      tap(a =>
        console.log(a, 'this is a')
      ), filter((resp: ApiResponseModel<any>) => resp.data.length > 0)
    ).subscribe(resp => {
      const criteria = [
        // combine all the guids together separated by commas
        { columnName: 'guids', operator: '=', value: resp.data.join(',') }
      ];
      this.criteriaList = criteria;
      this.setGridData(criteria);
      this.setTotalRecordCount(resp.data.length);
      this.emptyGrid = false;
    });
    this.subs.sink = query$.pipe(
      filter((resp: ApiResponseModel<any>) => resp.data.length === 0))
      .subscribe(resp => {
        this.criteriaList = [];
        this.emptyGrid = true;
        this.setTotalRecordCount(0);
        this.clear();
      });
  }

clear() {
  const dataSource = {
    getRows(params: any) {
      params.successCallback([], 0);
    }
  };
  this.gridApi.setServerSideDatasource(dataSource);
}

setTotalRecordCount(totalCount: number) {
  this.totalRecords$.next(totalCount);
}
    
setGridData(criteria?: any) {
  this.snapshot = null;
  const apiVisa = AppConfig.apiVisa;

  const datasource = {
    getRows: grid => {
      const sortModel = grid.request.sortModel;
      const sortCriteria = [];
      if (sortModel.length > 0) {
        sortModel.forEach(element => {
          const columnName = element.colId.split(".")[1]
          sortCriteria.push({ columnName: 'orderBy', value: columnName })
        });
        sortCriteria.push({ columnName: 'order', value: sortModel[0].sort.toUpperCase() });
        
      }
      sortCriteria.push({ columnName: 'guid_comp', value: this.hdr.guid_comp? this.hdr.guid_comp:'' });

      const limit = grid.request.endRow - grid.request.startRow;
      const pagination = new Pagination(0, limit, criteria, sortCriteria, this.snapshot);

      this.subs.sink = this.stgrnService.getGenericDocHdrLineLinkByCriteria(
        pagination,
        apiVisa).subscribe(
          (res) => {
            if (res.data.length > 0) {
              this.snapshot = res.data[res.data.length - 1].bl_fi_generic_doc_hdr.guid.toString();
            }

            let newArrData = new Array;
            newArrData = [...res.data].map((doc) => {
              //console.log("original container", doc);
              let aggContra = 0;
              (<any>doc).bl_fi_generic_doc_links.forEach((link) => {
                if (
                  link.guid_doc_1_hdr ===
                    doc.bl_fi_generic_doc_hdr.guid &&
                  link.guid_doc_1_line ===
                    doc.bl_fi_generic_doc_line.guid &&
                  link.txn_type === "IIST_IODO"
                ) {
                  let contra =
                    Number(link.quantity_signum) *
                    Number(link.quantity_contra);
                  aggContra += contra;
                }
              });
              this.draftLinksIds.forEach(draftLinkId => {
                if(
                  // link.server_doc_type_doc_2_line === "INTERNAL_PURCHASE_INVOICE" &&
                  this.draftLinks[draftLinkId].server_doc_type_doc_1_line === doc.bl_fi_generic_doc_line.server_doc_type &&
                  this.draftLinks[draftLinkId].guid_doc_1_line === doc.bl_fi_generic_doc_line.guid &&
                  this.draftLinks[draftLinkId].guid_doc_1_hdr === doc.bl_fi_generic_doc_hdr.guid
                ) {
                  let contra = Number(this.draftLinks[draftLinkId].quantity_signum) * Number(this.draftLinks[draftLinkId].quantity_contra);
                  aggContra += contra;
                }
              })

              let newObj = null;  
              newObj = {
                tracking_id: doc.bl_fi_generic_doc_line.tracking_id,
                hdr_guid: doc.bl_fi_generic_doc_hdr.guid,
                server_doc_type_hdr: doc.bl_fi_generic_doc_hdr.server_doc_type,
                line_guid: doc.bl_fi_generic_doc_line.guid,
                server_doc_type_line: doc.bl_fi_generic_doc_line.server_doc_type,
                doc_number: doc.bl_fi_generic_doc_hdr.server_doc_1,
                stgrn_no: doc.bl_fi_generic_doc_hdr.server_doc_1,
                item_guid: doc.bl_fi_generic_doc_line.item_guid,
                item_code: doc.bl_fi_generic_doc_line.item_code,
                item_name: doc.bl_fi_generic_doc_line.item_name,
                item_type: 'inbound',
                order_qty: doc.bl_fi_generic_doc_line.quantity_base,
                open_qty: Number(doc.bl_fi_generic_doc_line.quantity_base) + aggContra,
                unit_price: Number(doc.bl_fi_generic_doc_line.unit_price_txn),
                uom: doc.bl_fi_generic_doc_line.uom,
                status: "OPEN",
                txn_type: "IIST_IODO",
                item_txn_type: doc.bl_fi_generic_doc_line.item_txn_type,
                knock_off_qty: Number(doc.bl_fi_generic_doc_line.quantity_base) + aggContra,
                txn_date: doc.bl_fi_generic_doc_line.date_txn? doc.bl_fi_generic_doc_line.date_txn.getDate : null

                // unit_price_std: doc.bl_fi_generic_doc_line.unit_price_std,
                // unit_price_txn: doc.bl_fi_generic_doc_line.unit_price_txn,
                // tax_gst_code: doc.bl_fi_generic_doc_line.tax_gst_code,
                // tax_gst_rate: doc.bl_fi_generic_doc_line.tax_gst_rate,
                // amount_tax_gst: doc.bl_fi_generic_doc_line.amount_tax_gst,
                // tax_wht_code: doc.bl_fi_generic_doc_line.tax_wht_code,
                // tax_wht_rate: doc.bl_fi_generic_doc_line.tax_wht_rate,
                // amount_tax_wht: doc.bl_fi_generic_doc_line.amount_tax_wht,
                
              }
              // console.log("GenDocHdrLine", newObj);
              return newObj;
            })

            if (this.paginationComp.currentPage > this.paginationComp.totalPage.value) {
              this.paginationComp.firstPage()
            }

            // Calculate totalRecords if end reached.
            const start = grid.request.startRow;
            const end = grid.request.endRow;
            const totalRecords = newArrData.length < (end - start) ? start + newArrData.length : null;

            if (!this.totalCount && totalRecords) {
              this.totalCount = totalRecords;
              this.setTotalRecordCount(totalRecords);
            }

            grid.successCallback(newArrData, totalRecords);

            this.gridApi.forEachNode(a => {
              // if (a.data.item_guid === this.localState.selectedItem) {
              //   a.setSelected(true);
              // }
            });
          }, err => {
            console.log(err);
            grid.failCallback();

          })
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }  

  // setGridData() {
  //   const apiVisa = AppConfig.apiVisa;
  //   const datasource = {
  //     getRows: (grid) => {
  //       this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
  //       this.pagination.limit = grid.request.endRow - grid.request.startRow;
  //       this.pagination.conditionalCriteria = [
  //         { columnName: "line_txn_type", operator: "=", value: "PNS" },
  //         { columnName: "calcTotalRecords", operator: "=", value: "true" },
  //         { columnName: "orderBy", operator: "=", value: "updated_date" },
  //         { columnName: "order", operator: "=", value: "DESC" },
  //         {
  //           columnName: "line_guids",
  //           operator: "=",
  //           value: this.SQLGuids
  //             ? this.SQLGuids.slice(
  //                 grid.request.startRow,
  //                 grid.request.endRow
  //               ).toString()
  //             : "",
  //         },
  //       ];
  //       const filter = pageFiltering(grid.request.filterModel);
  //       const sortOn = pageSorting(grid.request.sortModel);
  //       this.subs.sink = this.grnService
  //         .getLinesByCriteria(this.pagination, apiVisa)
  //         .pipe(
  //           mergeMap((b) => {
  //             const source: Observable<any>[] = [];
  //             b.data.forEach((doc) =>
  //               source.push(
  //                 this.grnService
  //                   .getByGuid(
  //                     doc.bl_fi_generic_doc_hdr.guid.toString(),
  //                     apiVisa
  //                   )
  //                   .pipe(
  //                     map((b_a) => {
  //                       let aggContra = 0; // aggregate Contra [[[its doing it for all items in the header!!! FIX THIS]]]
  //                       b_a.data.bl_fi_generic_doc_link.forEach((link) => {
  //                         if (
  //                           link.guid_doc_1_hdr ===
  //                             doc.bl_fi_generic_doc_hdr.guid &&
  //                           link.guid_doc_1_line ===
  //                             doc.bl_fi_generic_doc_line[0].guid &&
  //                           link.txn_type === "IOST_IPGRN"
  //                         ) {
  //                           let contra =
  //                             Number(link.quantity_signum) *
  //                             Number(link.quantity_contra);
  //                           aggContra += contra;
  //                         }
  //                       });
  //                       const data = {
  //                         // Tracking id of data should be tracking id of line, not hdr
  //                         //tracking_id: doc.bl_fi_generic_doc_hdr.tracking_id,
  //                         tracking_id: doc.bl_fi_generic_doc_line[0].tracking_id,
  //                         hdr_guid: doc.bl_fi_generic_doc_hdr.guid,
  //                         server_doc_type_hdr:
  //                           doc.bl_fi_generic_doc_hdr.server_doc_type,
  //                         line_guid: doc.bl_fi_generic_doc_line[0].guid,
  //                         server_doc_type_line:
  //                           doc.bl_fi_generic_doc_line[0].server_doc_type,
  //                         doc_number: doc.bl_fi_generic_doc_hdr.server_doc_1,
  //                         grn_no: doc.bl_fi_generic_doc_hdr.server_doc_1,
  //                         item_guid: doc.bl_fi_generic_doc_line[0].item_guid,
  //                         item_code: doc.bl_fi_generic_doc_line[0].item_code,
  //                         item_name: doc.bl_fi_generic_doc_line[0].item_name,
  //                         item_type: "grn",
  //                         order_qty:
  //                           doc.bl_fi_generic_doc_line[0].quantity_base,
  //                         open_qty:
  //                           Number(
  //                             doc.bl_fi_generic_doc_line[0].quantity_base
  //                           ) + aggContra,
  //                         unit_price: null,
  //                         uom: doc.bl_fi_generic_doc_line[0].uom,
  //                         status: "OPEN",
  //                         txn_type: "IOST_IPGRN",
  //                         item_txn_type:
  //                           doc.bl_fi_generic_doc_line[0].item_txn_type,
  //                       };
  //                       //console.log(data);
  //                       return data;
  //                     })
  //                   )
  //               )
  //             );
  //             return iif(
  //               () => b.data.length > 0,
  //               forkJoin(source).pipe(
  //                 map((b_inner) => {
  //                   b.data = <any>b_inner;
  //                   return b;
  //                 })
  //               ),
  //               of(b)
  //             );
  //           })
  //         )
  //         .subscribe(
  //           (resolved) => {
  //             const data = sortOn(resolved.data).filter((entity) =>
  //               filter.by(entity)
  //             );
  //             const totalRecords = filter.isFiltering
  //               ? this.SQLGuids
  //                 ? this.SQLGuids.length
  //                 : resolved.totalRecords
  //               : data.length;
  //             grid.success({
  //               rowData: data,
  //               rowCount: totalRecords,
  //             });
  //           },
  //           (err) => {
  //             console.error(err);
  //             grid.fail();
  //           }
  //         );
  //     },
  //   };
  //   this.gridApi.setServerSideDatasource(datasource);
  // }

  // onSearch(e: SearchQueryModel) {
  //   if (!e.isEmpty) {
  //     const sql = {
  //       subquery: e.queryString,
  //       table: e.table,
  //     };
  //     this.subs.sink = this.sqlService.post(sql, AppConfig.apiVisa).subscribe({
  //       next: (resolve) => {
  //         this.SQLGuids = resolve.data;
  //         this.paginationComp.firstPage();
  //         this.gridApi.refreshServerSideStore();
  //       },
  //     });
  //   } else {
  //     this.SQLGuids = null;
  //     this.paginationComp.firstPage();
  //     this.gridApi.refreshServerSideStore();
  //   }
  // }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      this.searchQuery(e.queryString);
      console.log('searchQuery', e);

    } else {
      this.criteriaList = [];
      this.emptyGrid = false;
      this.setGridData();
      this.setTotalRecordCount(this.totalCount);
    }
  }

  
  onRowClicked(item) {
    if (!this.isChecked){
      console.log("selected grn item: ", item);
      // if (item) {
      //   this.draftStore.dispatch(ItemActions.resetAllItemsState());
      this.store.dispatch(
        InternalDeliveryOrderActions.selectItem({ entity: item })
      );
      this.viewColFacade.updateInstance(4, {
        ...this.localState,
        deactivateAdd: true,
        deactivateList: false,
        deactivateReturn: true,
      });
      this.viewColFacade.onNextAndReset(4, 12);
      // }
    }
  }

  onKnockOff(){
    const selectedLines = this.gridApi.getSelectedRows();
    console.log(selectedLines);
    selectedLines.forEach(a => {
      const line = new bl_fi_generic_doc_line_RowClass();
      line.guid = UUID.UUID().toLowerCase(); 
      line.item_guid = a.item_guid;
      line.item_code = a.item_code;
      line.item_name = a.item_name;
      line.amount_discount = a.amount_discount; 
      line.amount_net = a.amount_net;
      line.amount_std = a.amount_std;
      line.amount_tax_gst = a.amount_tax_gst;
      line.tax_wht_code = a.tax_wht_code;
      line.amount_tax_wht = a.amount_tax_wht;
      line.quantity_base = a.knock_off_qty;
      line.unit_price_std = a.unit_price_std;
      line.unit_price_txn = a.unit_price_txn;
      line.tax_gst_code = a.tax_gst_code;
      line.tax_gst_rate = a.tax_gst_rate;
      line.amount_txn = a.amount_txn;
      
      line.tax_wht_rate = a.tax_wht_rate;
      
      
      
      line.item_remarks = a.item_remarks;
      line.txn_type = "PNS";
      line.item_txn_type = a.item_txn_type;
      line.guid_dimension = a.guid_dimension;
      line.guid_profit_center = a.guid_profit_center;
      line.guid_project = a.guid_project;
      line.guid_segment = a.guid_segment;
      line.item_property_json = a.item_property_json;
      line.line_property_json = a.line_property_json;
      
      line.uom = a.uom;
      line.uom_to_base_ratio = a.uom_to_base_ratio;
      line.qty_by_uom = a.qty_by_uom;
      line.unit_price_std_by_uom = a.unit_price_std_by_uom;
      line.unit_price_txn_by_uom = a.unit_price_txn_by_uom;
      line.unit_disc_by_uom = a.unit_disc_by_uom;
      line.quantity_signum = -1;
      line.amount_signum = 0;
      line.server_doc_type = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
      line.client_doc_type = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
      line.tracking_id = a.tracking_id;
      line.date_txn = new Date();
      line.status = 'ACTIVE';

      // To confirm whether to copy these values
      // line.serial_no = a.serial_no;
      // line.bin_no = a.bin_no;
      // line.batch_no = a.batch_no;

      const link = new bl_fi_generic_doc_link_RowClass();
      link.guid = UUID.UUID().toLowerCase();
      link.guid_doc_2_line = line.guid;
      link.guid_doc_1_hdr = a.hdr_guid;
      link.guid_doc_1_line = a.line_guid;
      link.server_doc_type_doc_1_hdr = a.server_doc_type_hdr;
      link.server_doc_type_doc_1_line = a.server_doc_type_line;
      link.server_doc_type_doc_2_hdr = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
      link.server_doc_type_doc_2_line = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
      link.txn_type = 'IIST_IODO';
      link.quantity_signum = -1;
      // link.quantity_contra = line.quantity_base;
      link.quantity_contra = a.knock_off_qty;
      link.date_txn = new Date();

      if (this.editMode){
        this.viewColFacade.addLineItemToDraftEdit(line);
      }
      else{
        this.viewColFacade.addLineItemToDraft(line);
      }

      this.viewColFacade.addLink(link);
    })

    this.store.dispatch(InternalDeliveryOrderActions.updateAgGrid({update:true}))

  }


  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
