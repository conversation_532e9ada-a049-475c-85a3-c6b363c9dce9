import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderSelectCustomerComponent } from './internal-delivery-order-select-customer.component';

describe('InternalDeliveryOrderSelectCustomerComponent', () => {
  let component: InternalDeliveryOrderSelectCustomerComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderSelectCustomerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderSelectCustomerComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderSelectCustomerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
