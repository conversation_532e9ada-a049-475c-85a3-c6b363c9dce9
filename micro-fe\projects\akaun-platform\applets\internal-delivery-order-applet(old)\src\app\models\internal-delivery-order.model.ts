export interface InternalDOMain {
  company: string;
  branch: string;
  location: string;
  reference: string;
  deliveryDate: Date;
  shipVia: string;
  trackingID: string;
  remarks: string;
  server_doc_type: string;
  server_doc_1: string;
  server_doc_2: string;
  server_doc_3: string;
}

export interface InternalDODepartment {
  segment: string;
  dimension: string;
  profitCenter: string;
  project: string;
}

export interface StatusInfo {
  status1: string;
  status2: string;
  status3: string;
  status4: string;
  status5: string;
}

export interface ShippingInfo {
  name: string;
  email: string;
  phoneNo: number;
}

export interface ShippingAddress {
  shippingAddress: string,
  addressLine1: string,
  addressLine2: string,
  addressLine3: string,
  addressLine4: string,
  addressLine5: string,
  country: string,
  city: string,
  state: string,
  postcode: string
}