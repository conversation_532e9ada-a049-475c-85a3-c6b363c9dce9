import { createAction, props } from '@ngrx/store';
import {  JobOrderMain, JobOrderNoDepartment } from '../../../../models/internal-job-order.model';

export const updateMain = createAction('[Draft: Job Order No] Update Main', props<{ form: JobOrderMain }>());
export const updateDepartment = createAction('[Draft: Job Order No] Update Department', props<{ form: JobOrderNoDepartment }>());
export const reset = createAction('[Draft: Job Order No] Reset');
