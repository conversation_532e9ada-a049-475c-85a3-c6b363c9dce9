<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
              [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Process Instance Details
      </span>
    </div>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">


  <mat-tab label="Details">
    <app-details></app-details>
  </mat-tab>
  <mat-tab label="Process Template">
    <app-process-template-listing></app-process-template-listing>
  </mat-tab>
  <mat-tab label="Output Summary">
    <app-output-summary></app-output-summary>
  </mat-tab>
  <mat-tab label="Input Summary">
    <app-input-summary></app-input-summary>
  </mat-tab>
</mat-tab-group>