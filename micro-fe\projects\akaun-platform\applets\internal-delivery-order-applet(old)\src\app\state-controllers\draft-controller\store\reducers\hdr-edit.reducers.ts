import { Action, createReducer, on } from '@ngrx/store';
import { bl_fi_generic_doc_hdr_RowClass } from 'blg-akaun-ts-lib';
import { HDREditActions, PNSActions, SettlementEditActions } from '../actions';
import { initState } from '../states/hdr-edit.states';

export const hdrReducers = createReducer(
    initState,
    // on(InternalSalesOrderActions.selectEntityInit, (state, action) => ({
    //     ...state,
    //     ...action.entity.bl_fi_generic_doc_hdr
    // })),
    on(HDREditActions.updateMain, (state, action) => {
        // TODO: get confirmation on data storage
        state.guid_branch = action.form.branch;
        state.guid_store = action.form.location;
        state.doc_reference = action.form.reference;
        state.date_txn = action.form.transactionDate;
        state.doc_ccy = action.form.currency;
        state.amount_discount = action.form.groupDiscountAmount;
        state.doc_remarks = action.form.remarks;
        state.contact_key_guid = action.form.crmContact;
        state.member_guid = action.form.memberCard;
        state.doc_entity_hdr_json = <any>{
            ...state.doc_entity_hdr_json,
            salesAgent: action.form.salesAgent,
            groupDiscount: action.form.groupDiscount,
            salesLead: action.form.salesLead,
            shipVia: action.form.shipVia,
            trackingID: action.form.trackingID,
            creditTerms: action.form.creditTerms,
            creditLimit: action.form.creditLimit,
            permitNo: action.form.permitNo
        };
        return state;
    }),
    on(HDREditActions.updateDepartment, (state, action) => {
        state.guid_segment = action.form.segment;
        state.guid_dimension = action.form.dimension;
        state.guid_profit_center = action.form.profitCenter;
        state.guid_project = action.form.project;
        return state;
    }),
    on(HDREditActions.updateBillingInfo, (state, action) => {
        state.billing_json = <any>{
            ...state.billing_json,
            billingInfo: {...action.form}
        };
        return state;
    }),
    on(HDREditActions.updateShippingInfo, (state, action) => {
        state.delivery_entity_json = <any>{
          ...state.delivery_entity_json,
          shippingInfo: { ...action.form }
        };
        return state;
    }),
    on(HDREditActions.updateShippingAddress, (state, action) => {
        state.delivery_entity_json = <any>{
          ...state.delivery_entity_json,
          shippingAddress: {
            name: action.form.shippingAddress,
            address_line_1: action.form.addressLine1,
            address_line_2: action.form.addressLine2,
            address_line_3: action.form.addressLine3,
            address_line_4: action.form.addressLine4,
            address_line_5: action.form.addressLine5,
            country: action.form.country,
            city: action.form.city,
            state: action.form.state,
            postal_code: action.form.postcode
          }
        };
        return state;
      }),
    // on(InternalSalesOrderActions.selectCustomerEdit, (state, action) =>
    // ({...state,
    //     doc_entity_hdr_guid: action.entity.entity.bl_fi_mst_entity_hdr.guid,
    //     doc_entity_hdr_json: <any>{
    //       ...state.doc_entity_hdr_json,
    //       entityId: action.entity.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'CUSTOMER_CODE')?.value_string,
    //       entityName: `${action.entity.entity.bl_fi_mst_entity_hdr.name} : ${action.entity.contact.name}`,
    //       status: action.entity.entity.bl_fi_mst_entity_hdr.status,
    //       email: action.entity.entity.bl_fi_mst_entity_hdr.email,
    //       phoneNumber: action.entity.entity.bl_fi_mst_entity_hdr.phone,
    //       glCode: action.entity.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'GLCODE_INFO')?.value_string,
    //       idNumber: action.entity.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'ID_NO')?.value_string,
    //       entityType: action.entity.entity.bl_fi_mst_entity_hdr.txn_type,
    //       identityType: action.entity.entity.bl_fi_mst_entity_hdr.id_type,
    //       description: action.entity.entity.bl_fi_mst_entity_hdr.descr,
    //       currency: action.entity.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'CURRENCY')?.value_string,
    //       creditTerms: null,
    //       creditLimit: null
    //     },
    //     billing_json: null,
    //     delivery_entity_json: null
    // })),
    // on(InternalSalesOrderActions.selectShippingAddress, (state, action) =>
    // ({...state, delivery_entity_json: {...action.ext.value_json}})),
    // on(InternalSalesOrderActions.selectBillingAddress, (state, action) =>
    // ({...state, billing_json: {...action.ext.value_json}})),
    on(PNSActions.addPNS, (state, action) => ({
        ...state,
        amount_discount: <any>((parseFloat(<any>state.amount_discount)) + parseFloat(<any>(action.pns.amount_discount))).toFixed(2),
        amount_net: <any>((parseFloat(<any>state.amount_net)) + parseFloat(<any>(action.pns.amount_net))).toFixed(2),
        amount_std: <any>((parseFloat(<any>state.amount_std)) + parseFloat(<any>(action.pns.amount_std))).toFixed(2),
        amount_tax_gst: <any>((parseFloat(<any>state.amount_tax_gst)) + parseFloat(<any>(action.pns.amount_tax_gst))).toFixed(2),
        amount_tax_wht: <any>((parseFloat(<any>state.amount_tax_wht)) + parseFloat(<any>(action.pns.amount_tax_wht))).toFixed(2),
        amount_txn: <any>((parseFloat(<any>state.amount_txn)) + parseFloat(<any>(action.pns.amount_txn))).toFixed(2),
        amount_open_balance: <any>((parseFloat(<any>state.amount_open_balance)) + parseFloat(<any>(action.pns.amount_txn))).toFixed(2)
    })),
    on(SettlementEditActions.addSettlementSuccess, (state, action) => ({
        ...state,
        amount_open_balance:
        <any>((parseFloat(<any>state.amount_open_balance)) - parseFloat(<any>(action.settlement.amount_txn))).toFixed(2)
    })),
    on(HDREditActions.resetHDRSuccess, (state, action) => ({
        ...state,
        ...action.hdr
    })),
    on(HDREditActions.updateDeliveryType, (state, action) => {
        state.track_delivery_logic = action.deliveryType;
        return state;
    }),
);

export function reducers(state: bl_fi_generic_doc_hdr_RowClass | undefined, action: Action) {
    return hdrReducers(state, action);
}
