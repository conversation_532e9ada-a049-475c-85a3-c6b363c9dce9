const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/internal-job-order-applet/runtime.js',
    './dist/internal-job-order-applet/polyfills-es5.js',
    './dist/internal-job-order-applet/scripts.js',
    './dist/internal-job-order-applet/main.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/internal-job-order-applet');
  await concat(files, './elements/akaun-platform/applets/internal-job-order-applet/internal-job-order-appletements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
