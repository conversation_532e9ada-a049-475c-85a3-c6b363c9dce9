<form [formGroup]="form">
  <div fxLayout="column" class="view-col-forms">
    <div fxLayout="row wrap" fxFlexAlign="center" class="view-col-forms">    

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline" >
      <mat-label>Job Order No</mat-label>
      <input [readonly]="!jobOrderNoRestriction || editMode" matInput placeholder="Job Order No"
        [formControl]="form.controls['jobOrderNo']" type="text" autocomplete="off">
        <ng-container *ngFor="let message of messageList">
          <mat-error *ngIf="form.controls['jobOrderNo'].hasError('exist')" class="text-danger message">{{message}}</mat-error>
        </ng-container>

    </mat-form-field>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Job Order Date</mat-label>
          <input matInput placeholder="Job Order Date" [matDatepicker]="jobOrderDate"
            [formControl]="form.controls['jobOrderDate']" autocomplete="off" readonly
            (click)="jobOrderDate.open()" (dateChange)="updateMain.emit(form.value)">
          <mat-error>Job Order Date is <strong>not valid</strong></mat-error>
          <mat-datepicker-toggle matSuffix [for]="jobOrderDate"></mat-datepicker-toggle>
          <mat-datepicker touchUi="true" #jobOrderDate></mat-datepicker>
        </mat-form-field>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Item Code</mat-label>
          <input style="cursor: pointer" matInput placeholder="Item Code" [formControl]="form.controls['itemCode']" type="text" (click)="itemCode.emit()"
            (change)="updateMain.emit(form.value)">
        </mat-form-field>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Item Name</mat-label>
          <input style="cursor: pointer" matInput placeholder="Item Name" [formControl]="form.controls['itemName']" type="text" (click)="itemCode.emit()"
            (change)="updateMain.emit(form.value)">
        </mat-form-field>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>UOM</mat-label>
          <input matInput placeholder="UOM" [formControl]="form.controls['uom']" type="text"
            (change)="updateMain.emit(form.value)">
        </mat-form-field>

        <blg-select-branch-drop-down  [apiVisa]="apiVisa" [(branch)]="form.controls['branch']" (branchSelected)="onBranchSelected($event);" class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline" required></blg-select-branch-drop-down>
        <blg-select-location-drop-down  [branchSelectedGuid]="selectedBranch" [apiVisa]="apiVisa" (locationSelected)="onLocationSelected($event)" [(location)]="form.controls['location']" class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline" required></blg-select-location-drop-down>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline" *ngIf="editMode">
          <mat-label>Sales Order Quantity</mat-label>
          <input matInput placeholder="Container Quantity" [formControl]="form.controls['containerQty']" type="number"
            (change)="updateMain.emit(form.value); onCalculateTotalContainerMeasure()">
        </mat-form-field>

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline" *ngIf="editMode">
          <mat-label>Ad-Hoc Quantity</mat-label>
          <input matInput placeholder="Ad-Hoc Quantity" [formControl]="form.controls['adHocQty']" type="number"
            (change)="updateMain.emit(form.value); onCalculateTotalContainerMeasure()">
        </mat-form-field>
        
        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline" *ngIf="editMode">
          <mat-label>Total Quantity</mat-label>
          <input matInput placeholder="Total Container Measure" [formControl]="form.controls['totalContainerMeasure']" type="number"
            (change)="updateMain.emit(form.value)">
        </mat-form-field>


    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label>Process Status</mat-label>
      <mat-select matNativeControl formControlName="process_status" (selectionChange)="updateMain.emit(form.value)">
        <mat-option *ngFor="let status1 of PROCESS_STATUS" [value]="status1">
          {{ status1 }}
        </mat-option>
      </mat-select>
    </mat-form-field>

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Estimated Packing Date</mat-label>
          <input matInput [matDatepicker]="estimatedPackingDate" [formControl]="form.controls['estimatedPackingDate']" autocomplete="off" readonly (click)="estimatedPackingDate.open()" (dateInput)="updateMain.emit(form.value)">
          <mat-error>Estimated Packing Date is <strong>not valid</strong></mat-error>
          <mat-datepicker-toggle matSuffix [for]="estimatedPackingDate"></mat-datepicker-toggle>
          <mat-datepicker touchUi="true" #estimatedPackingDate></mat-datepicker>
     </mat-form-field>

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Completion Date</mat-label>
          <input matInput [matDatepicker]="completionDate" [formControl]="form.controls['completionDate']" autocomplete="off" readonly (click)="completionDate.open()" (dateInput)="updateMain.emit(form.value)">
          <mat-error>Completion Date is <strong>not valid</strong></mat-error>
          <mat-datepicker-toggle matSuffix [for]="completionDate"></mat-datepicker-toggle>
          <mat-datepicker touchUi="true" #completionDate></mat-datepicker>
    </mat-form-field>

    <!-- <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline" *ngIf="editMode">
      <mat-label>QC Output Status</mat-label>
      <mat-select matNativeControl formControlName="qcOutputStatus">
        <mat-option *ngFor="let status1 of QC_OUTPUT_STATUS" [value]="status1">
          {{ status1 }}
        </mat-option>
      </mat-select>
    </mat-form-field> -->
        <!-- <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Due Date</mat-label>
        <input matInput placeholder="Due Date" [matDatepicker]="dueDatePicker" [formControl]="form.controls['dueDate']"
          autocomplete="off" readonly (click)="dueDatePicker.open()" (change)="updateMain.emit(form.value)">
        <mat-error>Due date is <strong>not valid</strong></mat-error>
        <mat-datepicker-toggle matSuffix [for]="dueDatePicker"></mat-datepicker-toggle>
        <mat-datepicker touchUi="true" #dueDatePicker></mat-datepicker>
      </mat-form-field> -->

        <!-- <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Description</mat-label>
          <input matInput placeholder="Description" [formControl]="form.controls['description']" type="text"
            (change)="updateMain.emit(form.value)">
        </mat-form-field> -->

        <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
          <mat-label>Remarks</mat-label>
          <textarea #remarks matInput placeholder="Remarks" [formControl]="form.controls['remarks']"
            (change)="updateMain.emit(form.value)"></textarea>
          <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
        </mat-form-field>



   
    </div>
  </div>
</form>