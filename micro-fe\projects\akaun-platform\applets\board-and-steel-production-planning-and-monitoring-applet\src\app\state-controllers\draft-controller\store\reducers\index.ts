import { ActionReducerMap } from '@ngrx/store';
import { DraftStates } from '../states';
import * as fromAttachmentReducers from './attachment.reducers';
import * as fromPlannedOutputReducers from './planned-output.reducers';
import * as fromPlannedInputReducers from './planned-input.reducers';
import * as fromPNSEditReducers from './pns-edit.reducers';
import * as fromHDRReducers from './hdr.reducers';
import * as fromHDREditReducers from './hdr-edit.reducers';
import * as fromBinReducers from './bin.reducers';
import * as fromJobOrderNoReducers from './job-order-no.reducers';
import * as fromJobOrderNoEditReducers from './job-order-no-edit.reducers';

export const draftReducers: ActionReducerMap<DraftStates> = {
    attachment: fromAttachmentReducers.reducers,
    plannedOutput: fromPlannedOutputReducers.reducers,
    plannedInput: fromPlannedInputReducers.reducers,
    pnsEdit: fromPNSEditReducers.reducers,
    hdr: fromHDRReducers.reducers,
    hdrEdit: fromHDREditReducers.reducers,
    bin: fromBinReducers.reducers,
    jobOrderNo: fromJobOrderNoReducers.reducers,
    jobOrderNoEdit: fromJobOrderNoEditReducers.reducers
}
