import { createAction, props } from '@ngrx/store';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';

export const cacheInternalSO = createAction('[View Cache] Cache Internal Sales Order', props<{cache: ViewColumnState}>());
export const cacheProcess = createAction('[View Cache] Cache Process', props<{process: string, cache: ViewColumnState}>());
export const addProcess = createAction('[View Cache] Add Process', props<{process: string, cache: ViewColumnState}>());
export const changeProcess = createAction('[View Cache] Change Process', props<{process: string}>());
export const cacheSalesOrder = createAction('[View Cache] Cache Sales Order', props<{cache: ViewColumnState}>());
