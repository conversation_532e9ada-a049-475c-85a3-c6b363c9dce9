import { Component, ChangeDetectionStrategy, ViewChild, OnInit, OnDestroy } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_hdr_RowClass, bl_fi_mst_entity_ext_RowClass, EntityContainerModel } from 'blg-akaun-ts-lib';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { EntityConstants } from '../../../../models/constants/customer-constants';
import { EntitySelectors } from '../../../../state-controllers/customer-controller/store/selectors';
import { EntityStates } from '../../../../state-controllers/customer-controller/store/states';


interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedIndex: number;
}

@Component({
  selector: 'app-customer-create',
  templateUrl: './customer-create.component.html',
  styleUrls: ['./customer-create.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class CustomerCreateComponent extends ViewColumnComponent implements OnInit, OnDestroy {

  protected compName = 'Customer Create';
  protected readonly index = 20;
  private localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.localState.deactivateAdd);
  readonly selectedIndex$ = this.componentStore.select(state => state.localState.selectedIndex);

  prevIndex: number;
  private prevLocalState: any;

  draft$ = this.viewColFacade.draft$;
  entityExt: EntityContainerModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    { headerName: 'Item Code', field: 'item_code' },
    { headerName: 'Batch No', field: 'item_property_json.batchNo' },
    { headerName: 'From Cable Length', field: 'item_property_json.fromCableLength' },
    { headerName: 'Quantity', field: 'item_property_json.quantity' },
    { headerName: 'UOM', field: 'item_property_json.uom' },
    { headerName: 'Packing Date', field: 'item_property_json.packingDate' },
    { headerName: 'Status', field: 'item_property_json.status' },
  ];

  @ViewChild(MatTabGroup, { static: true }) matTab: MatTabGroup;
  isValid: boolean = false;
  selectEntity$: any;
  entity: string;

  constructor(private viewColFacade: ViewColumnFacade,
    private readonly componentStore: ComponentStore<{ localState: LocalState }>,
    private entityStore: Store<EntityStates>
    ) {
    super();
  }

  ngOnInit() {
    this.selectEntity$ = this.entityStore.select(EntitySelectors.selectEntityType).subscribe((data) => {
      this.entity = data.charAt(0).toUpperCase() + data.slice(1); 
    });
    this.viewColFacade.draft$.subscribe(resolve => this.entityExt = resolve);
    this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState({ localState: a });
    });

    
  }

  onReturn() {
    this.viewColFacade.resetDraft(1);
    this.viewColFacade.updateInstance(6, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(6);
  }

  onNext(e) {
    this.localState.deactivateAdd = true;
    this.viewColFacade.onNext(2);
  }

  onGridReady(event) {
    this.gridApi = event.api;
    this.gridApi.closeToolPanel();
  }

  onRowClicked(event: string) {
  }
  createNewEntityExt(
    param_code: string,
    param_name: string,
    param_type: string,
    param_value: any,
  ) {
    const obj = new bl_fi_mst_entity_ext_RowClass();
    obj.param_name = param_name;
    obj.param_code = param_code;
    obj.status = 'ACTIVE';
    obj.param_type = param_type;
    if (param_type.toUpperCase() === 'STRING') {
      obj.value_string = param_value;
    } else if (param_type.toUpperCase() === 'DATE') {
      obj.value_datetime = param_value;
    } else if (param_type.toUpperCase() === 'NUMERIC') {
      obj.value_numeric = param_value;
    } else if (param_type.toUpperCase() === 'JSON') {
      obj.value_json = param_value;
    } else {
      obj.value_file = param_value;
    }
    return obj;
  }

  validDraft(f) {
    this.isValid = f;
  }

  updateDraft(e) {
    // this.deactivateAdd$ = e.valid
    // const form = this.createMain.form;

    const entityrContainerModel = new EntityContainerModel();
    console.log("this,entity", this.entity)
    switch (this.entity){
      case "Customer": {
        entityrContainerModel.bl_fi_mst_entity_hdr.customer_code = e.code;
        break;
      }
      case "Supplier": {
        entityrContainerModel.bl_fi_mst_entity_hdr.supplier_code = e.code;
        break;}
      case "Merchant": {
        entityrContainerModel.bl_fi_mst_entity_hdr.merchant_code = e.code;
        break;}
      case "Employee": {    
        entityrContainerModel.bl_fi_mst_entity_hdr.employee_code = e.code;
        break;}
    }

    entityrContainerModel.bl_fi_mst_entity_hdr.guid = null;
    entityrContainerModel.bl_fi_mst_entity_hdr.name = e.name;
    entityrContainerModel.bl_fi_mst_entity_hdr.descr = e.description;
    entityrContainerModel.bl_fi_mst_entity_hdr.status = e.status;
    entityrContainerModel.bl_fi_mst_entity_hdr.revision = null;
    entityrContainerModel.bl_fi_mst_entity_hdr.vrsn = null;
    entityrContainerModel.bl_fi_mst_entity_hdr.txn_type = e.type;
    entityrContainerModel.bl_fi_mst_entity_hdr.id_type = e.id_type;
    entityrContainerModel.bl_fi_mst_entity_hdr.id_no = e.id_number;
    entityrContainerModel.bl_fi_mst_entity_hdr.tax_reg_number = e.taxID;

    entityrContainerModel.bl_fi_mst_entity_ext.push(
      this.createNewEntityExt(EntityConstants.GLCODE_INFO, EntityConstants.GLCODE_INFO, 'STRING',
        e.glCode,
      ),
      this.createNewEntityExt(EntityConstants.CURRENCY, EntityConstants.CURRENCY, 'JSON',
        {
          'currency': e.currency,
        }
      ),
      this.createNewEntityExt(EntityConstants.GENDER, EntityConstants.GENDER, 'STRING',
        e.gender,
      ),
      // this.createNewCustomerExt(CustomerConstants.CUSTOMER_CODE, CustomerConstants.CUSTOMER_CODE, 'STRING',
      //   e.code,
      // ),
    );
    this.entityExt = entityrContainerModel;
    this.viewColFacade.updateDraftHdr(entityrContainerModel);
  }
  onSave() {
    this.viewColFacade.createCustomer(this.entityExt);
  }

  ngOnDestroy() {
    this.viewColFacade.updateInstance(this.index, { ...this.localState, selectedIndex: this.matTab.selectedIndex });
  }

}
