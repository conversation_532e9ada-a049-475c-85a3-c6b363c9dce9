import { trigger, state, style, transition, animate, keyframes } from '@angular/animations';
import { Component, ComponentFactoryResolver, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { ViewColumnFacade } from '../../facades/view-column.facade';
import { FirstColumnDirective } from './first-column.directive';
import { SecondColumnDirective } from './second-column.directive';
import { SubSink } from 'subsink2';
import { ProcessingPagesService } from '../../services/processing-pages.service';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { ViewColumn } from 'projects/shared-utilities/view-column';

@Component({
  selector: 'app-processing-container',
  templateUrl: './processing-container.component.html',
  styleUrls: ['./processing-container.component.scss'],
  animations: [
    trigger('openClose', [
      state('close', style({ display: 'none', width: '0%' })),
      state('open', style({ display: 'table-cell', width: '50%' })),
      transition('close => open', [
        animate('0.2s', keyframes([
          style({ display: 'table-cell', offset: 0}),
          style({ width: '50%', offset: 1})
        ])),
      ]),
      transition('open => close', [
        animate('0.2s', keyframes([
          style({ width: '0%', offset: 0.8}),
          style({ display: 'none', offset: 1})
        ]))
      ])
    ])
  ],
  providers: [ProcessingPagesService]
})
export class ProcessingContainerComponent implements OnInit, OnDestroy {

  @ViewChild(FirstColumnDirective, {static: true}) firstColumnHost: FirstColumnDirective;
  @ViewChild(SecondColumnDirective, {static: true}) secondColumnHost: SecondColumnDirective;

  private subs = new SubSink();

  leftDrawer: ViewColumn[];
  rightDrawer: ViewColumn[];
  breadCrumbs: ViewColumn[];
  firstCol: ViewColumn;
  secondCol: ViewColumn;

  mobileView = window.matchMedia('(max-width: 768px)');

  constructor(
    private viewColFacade: ViewColumnFacade,
    private componentFactoryResolver: ComponentFactoryResolver,
    private pagesService: ProcessingPagesService) {
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.processingCache$.subscribe(a =>
      a ? this.viewColFacade.setViewColState(a) : this.viewColFacade.setViewColState(this.pagesService.pages)
    );
    this.mobileView.matches ? this.viewColFacade.toggleColumn(true) : this.viewColFacade.toggleColumn(false);
    this.mobileView.addEventListener('change', (e) => {
      this.mobileView.matches ? this.viewColFacade.toggleColumn(true) : this.viewColFacade.toggleColumn(false);
    });

    this.subs.sink = this.viewColFacade.firstCol$.subscribe( resolve => {
      this.firstCol = resolve;
      this.loadComponent(resolve, this.firstColumnHost);
    });
    this.subs.sink = this.viewColFacade.secondCol$.subscribe( resolve => {
      this.secondCol = resolve;
      this.loadComponent(resolve, this.secondColumnHost);
    });
    this.subs.sink = this.viewColFacade.leftDrawer$.subscribe( resolve => this.leftDrawer = resolve );
    this.subs.sink = this.viewColFacade.rightDrawer$.subscribe( resolve => this.rightDrawer = resolve );

    this.subs.sink = this.viewColFacade.breadCrumbs$.subscribe( resolve => this.breadCrumbs = resolve );
  }

  loadComponent( comp: ViewColumn, host: FirstColumnDirective | SecondColumnDirective ) {
    const viewContainerRef = host.viewContainerRef;
    viewContainerRef.clear();
    if (!comp) {
      return;
    }
    const compFactory  = this.componentFactoryResolver.resolveComponentFactory(comp.component);
    viewContainerRef.createComponent<ViewColumnComponent>(compFactory);
  }

  goBackIndex(i: number) {
    this.viewColFacade.goBackIndex(i);
  }

  goForwardIndex(i: number) {
    this.viewColFacade.goForwardIndex(i);
  }

  goToIndex(i: number) {
    this.viewColFacade.goToIndex(i);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.viewColFacade.saveInternalDOState();
  }

}
