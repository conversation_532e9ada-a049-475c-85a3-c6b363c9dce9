import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { map, withLatestFrom } from 'rxjs/operators';
import { InternalJobOrderSelectors } from '../../../internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from '../../../internal-job-order-controller/store/states';
import { HDREditActions } from '../actions';

@Injectable()
export class HDREditEffects {

    // resetHDR$ = createEffect(() => this.actions$.pipe(
    //     ofType(HDREditActions.resetHDRInit),
    //     withLatestFrom(this.store.select(InternalJobOrderSelectors.selectEntity)),
    //     map(([a, b]) => {
    //         return HDREditActions.resetHDRSuccess({hdr: b.bl_fi_generic_doc_hdr})
    //     })
    // ));

    constructor(
        private actions$: Actions,
        private readonly store: Store<InternalJobOrderStates>
    ) { }
}
