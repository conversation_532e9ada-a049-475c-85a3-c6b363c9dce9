<form [formGroup]="form">
  <div fxLayout="column" class="view-col-forms">
    <div fxLayout="row wrap" fxFlexAlign="center" class="view-col-forms">    

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Item Code</mat-label>
        <input style="cursor: pointer" matInput placeholder="Item Code" [formControl]="form.controls['itemCode']" type="text" (click)="itemCode.emit()"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Item Name</mat-label>
        <input style="cursor: pointer" matInput placeholder="Item Name" [formControl]="form.controls['itemName']" type="text" (click)="itemCode.emit()"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Quantity</mat-label>
        <input matInput placeholder="Quantity" [formControl]="form.controls['qty']" type="number"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>UOM</mat-label>
        <input matInput placeholder="UOM" [formControl]="form.controls['uom']" type="text"
          >
      </mat-form-field>

      <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
        <mat-label>Available Stock Balance</mat-label>
        <input matInput placeholder="Available Stock Balance" [formControl]="form.controls['availble_stock_bal']" type="text"
          >
      </mat-form-field>

    </div>

    <button mat-raised-button fxFlexAlign="center" class="stockSummaryBtn p-10" type="button" (click)="goToSummary()">
      <span>Stock Balance Summary</span>
    </button>
  </div>
</form>