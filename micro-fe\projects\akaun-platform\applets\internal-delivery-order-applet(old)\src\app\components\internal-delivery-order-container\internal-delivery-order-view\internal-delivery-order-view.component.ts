import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass, GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { combineLatest } from 'rxjs';
import { map, withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { ShippingAddress, ShippingInfo, StatusInfo } from '../../../models/internal-delivery-order.model';
import { HDRActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDREditSelectors, HDRSelectors, PNSEditSelectors, PNSSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { InternalDeliveryOrderCreateAccountComponent } from '../internal-delivery-order-create/internal-delivery-order-create-account/internal-delivery-order-create-account.component';
import { InternalDeliveryOrderCreateCustomFieldsComponent } from '../internal-delivery-order-create/internal-delivery-order-create-custom-fields/internal-delivery-order-create-custom-fields.component';
import { InternalDeliveryOrderCreateDepartmentComponent } from '../internal-delivery-order-create/internal-delivery-order-create-department/internal-delivery-order-create-department.component';
import { InternalDeliveryOrderCreateLineItemsComponent } from '../internal-delivery-order-create/internal-delivery-order-create-line-items/internal-delivery-order-create-line-items.component';
import { InternalDeliveryOrderCreateMainComponent } from '../internal-delivery-order-create/internal-delivery-order-create-main/internal-delivery-order-create-main.component';
interface LocalState {
  deactivateAdd: boolean;
  deactivateReturn: boolean;
  deactivateEntity: boolean;
  deactivateSalesAgent: boolean;
  deactivateShippingInfo: boolean;
  deactivateBillingInfo: boolean;
  deactivateLineItem: boolean;
  deactivateSettlement: boolean;
  deactivateContactPerson: boolean;
  selectedIndex: number;
  childSelectedIndex: number;
  customFields: {
    label: string,
    formControl: string,
    type: string,
    readonly: boolean
  }[];
  deleteConfirmation: boolean;
}

@Component({
  selector: 'app-internal-delivery-order-view',
  templateUrl: './internal-delivery-order-view.component.html',
  styleUrls: ['./internal-delivery-order-view.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class InternalDeliveryOrderViewComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Internal Delivery Order View';
  protected index = 1;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deactivateEntity$ = this.componentStore.select(state => state.deactivateEntity);
  readonly deactivateSalesAgent$ = this.componentStore.select(state => state.deactivateSalesAgent);
  readonly deactivateShippingInfo$ = this.componentStore.select(state => state.deactivateShippingInfo);
  readonly deactivateBillingInfo$ = this.componentStore.select(state => state.deactivateBillingInfo);
  readonly deactivateLineItem$ = this.componentStore.select(state => state.deactivateLineItem);
  readonly deactivateSettlement$ = this.componentStore.select(state => state.deactivateSettlement);
  readonly deactivateContactPerson$ = this.componentStore.select(state => state.deactivateContactPerson);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly childSelectedIndex$ = this.componentStore.select(state => state.childSelectedIndex);
  readonly customFields$ = this.componentStore.select(state => state.customFields);
  readonly deleteConfirmation$ = this.componentStore.select(state => state.deleteConfirmation);

  toggleColumn$ = this.viewColFacade.toggleColumn$;

  prevIndex: number;
  protected prevLocalState: any;

  draft$ = this.store.select(InternalDeliveryOrderSelectors.selectDraftEdit);
  hdr$ = this.draftStore.select(HDRSelectors.selectHdr);
  pns$ = this.draftStore.select(PNSSelectors.selectAll).pipe(
    map(a => a.filter(l => l.status !== 'DELETED'))
  );
  hdrDraft$ = this.draftStore.select(HDRSelectors.selectHdr);
  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({ ...a, ...b })));
  updatedRowData;

  deleteConfirmation = false;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(InternalDeliveryOrderCreateAccountComponent) account: InternalDeliveryOrderCreateAccountComponent;
  @ViewChild(InternalDeliveryOrderCreateMainComponent) main: InternalDeliveryOrderCreateMainComponent;
  @ViewChild(InternalDeliveryOrderCreateLineItemsComponent) lines: InternalDeliveryOrderCreateLineItemsComponent;
  @ViewChild(InternalDeliveryOrderCreateDepartmentComponent) department: InternalDeliveryOrderCreateDepartmentComponent; // TODO: maybe change into its own child component instead of sharing
  @ViewChild(InternalDeliveryOrderCreateCustomFieldsComponent) customFields: InternalDeliveryOrderCreateCustomFieldsComponent; // TODO: maybe change into its own child component instead of sharing

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected readonly store: Store<InternalDeliveryOrderStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly componentStore: ComponentStore<LocalState>,
    private readonly sessionStore: Store<SessionStates>,
    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.draftStore.select(PNSSelectors.selectAll).subscribe((resolved) => {
      this.updatedRowData = resolved.filter((line) => line.status !== 'DELETED' )
    });
    this.subs.sink = this.draft$.subscribe({next: resolve => {
      const arr = [];
      const fields = resolve.bl_fi_generic_doc_ext.find(x => x.param_code === 'CUSTOM_FIELDS')?.value_json;
      if (fields) {
        Object.entries(fields).forEach(f => {
          let obj = {label: '', formControl: '', type: 'text', readonly: false};
          switch (typeof(f[1])) {
            case 'string':
              obj = {label: f[0], formControl: f[0], type: 'text', readonly: false};
            break;
            case 'number':
              obj = {label: f[0], formControl: f[0], type: 'number', readonly: false};
            break;
            case 'object':
              obj = {label: f[0], formControl: f[0], type: 'date', readonly: false};
            break;
          }
          arr.push(obj);
          console.log("custom fields", this.customFields);
          this.customFields.form.addControl(obj.formControl, new FormControl());
        });
        this.componentStore.patchState({customFields: [...arr]});
      } else {
        this.componentStore.patchState({customFields: this.localState.customFields});
      }
    }});
  }

  onEntity() {
    if (!this.localState.deactivateEntity) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: true,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 6);
    }
  }

  onShippingInfo() {
    if (!this.localState.deactivateShippingInfo) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: true,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 7);
    }
  }

  onBillingInfo() {
    if (!this.localState.deactivateBillingInfo) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: true,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 8);
    }
  }

  onContactPerson() {
    if (!this.localState.deactivateContactPerson) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: true,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 9);
    }
  }

  onReturn() {
    this.store.dispatch(InternalDeliveryOrderActions.setEditMode({ editMode: false }));
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onNextAdd() {
    if (!this.localState.deactivateAdd) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: true,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 4);
    }
  }

  onReset() {
    this.store.dispatch(InternalDeliveryOrderActions.resetDraftEdit());
  }

  onSave() {
    this.store.dispatch(InternalDeliveryOrderActions.editDeliveryOrderInit());
  }

  disableButton() {
    return this.main?.form?.invalid || !this.lines?.rowData.length || this.account?.invalid;
  }

  onLineItem(line: bl_fi_generic_doc_line_RowClass) {
    this.store.dispatch(InternalDeliveryOrderActions.selectLineItemInit({lineItem: line}));
    this.store.dispatch(InternalDeliveryOrderActions.selectPricingSchemeLink({ item: line }));
    if (!this.localState.deactivateLineItem) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateContactPerson: false,
        deactivateLineItem: true
      });
      this.viewColFacade.onNextAndReset(this.index, 2);
    }
  }

  onUpdateMain(form) {
    this.store.dispatch(InternalDeliveryOrderActions.updateMainEdit({model: form}));
  }

  onUpdateDepartment(form) {
    this.store.dispatch(InternalDeliveryOrderActions.updateDepartmentEdit({model: form}));
  }

  onUpdateCustomFields(form) {
    this.store.dispatch(InternalDeliveryOrderActions.updateCustomFields({model: form}));
  }

  onNextSettlement() {
    if (!this.localState.deactivateSettlement) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateEntity: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: true,
        deactivateContactPerson: false,
        deactivateLineItem: false
      });
      this.viewColFacade.onNextAndReset(this.index, 11);
    }
  }

  onChangeCustomFields(e) {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      customFields: e
    });
  }

  onDelete() {
    if (this.deleteConfirmation) {
      this.store.dispatch(InternalDeliveryOrderActions.deleteDeliveryOrderInit());
      this.deleteConfirmation = false;
      this.componentStore.patchState({deleteConfirmation: false});
    } else {
      this.deleteConfirmation = true;
      this.componentStore.patchState({deleteConfirmation: true});
    }
  }

  onPostToFinal(){
    this.subs.sink = this.draft$.subscribe(resolve => {
      const json = {
        posting_status: "FINAL"
      }
      let temp: GenericDocContainerModel = {
        bl_fi_generic_doc_hdr: resolve.bl_fi_generic_doc_hdr,
        bl_fi_generic_doc_event: null,
        bl_fi_generic_doc_ext: null,
        bl_fi_generic_doc_line: null,
        bl_fi_generic_doc_link: null
      };
      if (resolve.bl_fi_generic_doc_hdr.posting_status !== 'FINAL'){
        this.store.dispatch(InternalDeliveryOrderActions.updatePostingStatusInit({status: json , doc: temp}));
      }
      else{
        this.viewColFacade.showFailedToast({message: "This document has already been posted"});
      }
    })
   }

   onUpdateStatus(form: StatusInfo) {
    this.store.dispatch(HDRActions.updateStatus({ form }));
  }

  goToSelectShipping() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 7);
  }

  onUpdateShippingInfo(form: ShippingInfo) {
    this.draftStore.dispatch(HDRActions.updateShippingInfo({ form }));
  }

  onUpdateShippingAddress(form: ShippingAddress) {
    this.draftStore.dispatch(HDRActions.updateShippingAddress({ form }));
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
        childSelectedIndex: this.account.matTab.selectedIndex
      });
    }
    this.subs.unsubscribe();
  }

}
