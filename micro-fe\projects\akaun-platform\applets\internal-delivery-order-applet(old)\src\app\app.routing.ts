import { Routes } from '@angular/router';
import { PermissionSetContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/permission-set/permission-set-container.component';
import { RolePermissionContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/role-permission/role-permission-container.component';
import { TeamPermissionContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/team-permission/team-permission-container.component';
import { UserPermissionContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/user-permission/user-permission-container.component';
import { SidebarComponent } from 'projects/shared-utilities/modules/personalization/sidebar/sidebar.component';
import { FeatureVisibilityComponent } from 'projects/shared-utilities/modules/settings/feature-visibility/feature-visibility.component';
import { WebhookComponent } from 'projects/shared-utilities/modules/settings/webhook/webhook.component';
import { FourOhFourComponent } from 'projects/shared-utilities/utilities/four-oh-four/four-oh-four.component';
import { InternalDeliveryOrderContainerComponent } from './components/internal-delivery-order-container/internal-delivery-order-container.component';
import { PersonalDefaultSettingsComponent } from './components/personalization-container/personal-default-settings/personal-default-settings.component';
import { PersonalizationContainerComponent } from './components/personalization-container/personalization-container.component';
import { DefaultSettingsComponent } from './components/settings-container/default-settings/default-settings.component';
import { PrintableFormatSettingsContainerComponent } from './components/settings-container/printable-format-settings-container/printable-format-settings-container.component';
import { SettingsContainerComponent } from './components/settings-container/settings-container.component';
import { PermissionResolver } from './resolver/permission.resolver';
import { KnockOffSettingsComponent } from './components/settings-container/knock-off-settings/knock-off-settings.component';
import { CustomStatusComponent } from './components/settings-container/custom-status/custom-status.component';
import { PickPackQueueContainerComponent } from './components/pick-pack-queue-container/pick-pack-queue-container.component';
import { ClientSidePermissionSetContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/client-side-permission/client-side-permission-container.component';
import { FieldConfigurationComponent } from 'projects/shared-utilities/modules/permission/field-configuration/field-configuration/field-configuration.component';

export const mainPath = 'applet/tnt/wavelet/erp/internal-delivery-order-applet';

export const AppRoutes: Routes = [
  {
    path: mainPath,
    children: [
      {
        path: 'internal-delivery-order',
        component: InternalDeliveryOrderContainerComponent
      },
      {
        path: 'pick-pack-queue',
        component: PickPackQueueContainerComponent
      },
      {
        path: 'settings',
        component: SettingsContainerComponent,
        children: [
          {
            path: 'default-selection',
            component: DefaultSettingsComponent
          },
          {
            path: 'field-settings',
            component: FieldConfigurationComponent
          },
          {
            path: 'webhook',
            component: WebhookComponent
          },
          {
            path: 'feature-visibility',
            component: FeatureVisibilityComponent
          },
          {
            path: 'client-side-permission-listing',
            component: ClientSidePermissionSetContainerComponent
          },
          {
            path: 'printable-format-settings',
            component: PrintableFormatSettingsContainerComponent
          },
          {
            path: 'custom-status',
            component: CustomStatusComponent
          },
          // {
          //   path: 'knock-off-settings',
          //   component: KnockOffSettingsComponent
          // },
          {
            path: 'permission-set-listing',
            component: PermissionSetContainerComponent,
            resolve: { targetServices: PermissionResolver }
          },
          {
            path: 'user-permission-listing',
            component: UserPermissionContainerComponent
          },
          {
            path: 'team-permission-listing',
            component: TeamPermissionContainerComponent
          },
          {
            path: 'role-permission-listing',
            component: RolePermissionContainerComponent
          },
          {
            path: '',
            redirectTo: 'feature-visibility',
            pathMatch: 'full'
          },
        ]
      },
      {
        path: 'personalization',
        component: PersonalizationContainerComponent,
        children: [
          {
            path: 'personal-default-selection',
            component: PersonalDefaultSettingsComponent
          },
          {
            path: 'sidebar',
            component: SidebarComponent
          },
        ]
      },
      {
        path: '404',
        component: FourOhFourComponent
      },
      {
        path: '',
        redirectTo: `internal-delivery-order`,
        pathMatch: 'full'
      },
      {
        path: '**',
        redirectTo: `404`,
        pathMatch: 'full'
      }
    ]
  },
  {
    path: '404',
    component: FourOhFourComponent
  },
  {
    path: '**',
    redirectTo: `${mainPath}`,
    pathMatch: 'full'
  },
];


