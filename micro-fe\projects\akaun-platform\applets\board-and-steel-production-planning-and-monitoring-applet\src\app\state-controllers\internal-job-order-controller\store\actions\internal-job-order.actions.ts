import { createAction, props } from '@ngrx/store';
import { MrpJobOrderHdrContainerModel, bl_fi_generic_doc_line_RowClass, FinancialItemContainerModel, MrpJobOrderGenDocLinkContainerModel, MrpProcessInstanceContainerModel } from 'blg-akaun-ts-lib';

export const selectItem = createAction('[Job Order] Select Item Init', props<{ entity: FinancialItemContainerModel }>());
export const selectJobOrder = createAction('[Job Order] Select', props<{ entity: MrpJobOrderHdrContainerModel }>());

export const selectLineItemInit = createAction('[Job Order] Select Line Item Init', props<{ line: bl_fi_generic_doc_line_RowClass }>());
export const selectLineItemSuccess = createAction('[Job Order] Select Line Item Success', props<{ entity: FinancialItemContainerModel }>());
export const selectLineItemFailed = createAction('[Job Order] Select Line Item Failed', props<{error: string}>());

export const selectEntity = createAction('[Job Order No] Select', props<{ entity: MrpJobOrderHdrContainerModel }>());

export const createJobOrderNoInit = createAction('[Job Order No] Create Init');
export const createJobOrderNoSuccess = createAction('[Job Order No] Create Success', props<{entity: MrpJobOrderHdrContainerModel}>());
export const createJobOrderNoFailed = createAction('[Job Order No] Create Failed', props<{error: string}>());

export const deleteJobOrderNoInit = createAction('[Job Order No] Delete Init');
export const deleteJobOrderNoSuccess = createAction('[Job Order No] Delete Success', props<{guid: string}>());
export const deleteJobOrderNoFailed = createAction('[Job Order No] Delete Failed', props<{error: string}>());

export const editJobOrderNoInit = createAction('[Job Order No] Edit Init');
export const editJobOrderNoSuccess = createAction('[Job Order No] Edit Success', props<{entity: MrpJobOrderHdrContainerModel}>());
export const editJobOrderNoFailed = createAction('[Job Order No] Edit Failed', props<{error: string}>());

export const issueJobOrderInit = createAction('[Job Order] Issue Init');
export const issueJobOrderSuccess = createAction('[Job Order] Issue Success');
export const issueJobOrderFailed = createAction('[Job Order] Issue Failed', props<{error: string}>());

export const deleteJobOrdersInit = createAction('[Job Order] Delete Init');
export const deleteJobOrderSuccess = createAction('[Job Order] Delete Success');
export const deleteJobOrderFailed = createAction('[Job Order] Delete Failed', props<{error: string}>());

export const editJobOrdersInit = createAction('[Job Order] Edit Init');
export const editJobOrderSuccess = createAction('[Job Order] Edit Success');
export const editJobOrderFailed = createAction('[Job Order] Edit Failed', props<{error: string}>());

export const resetAgGrid = createAction('[Job Order] Reset Ag Grid Update');

export const printJasperPdfInit = createAction('[Job Order] Print Jasper Pdf Init');
export const printJasperPdfSuccess = createAction('[Job Order] Print Jasper Pdf Success');
export const printJasperPdfFailed = createAction('[Job Order] Print Jasper Pdf Failed');

export const resetJobOrder = createAction('[Job Order] Reset');
export const resetJobOrderEdit = createAction('[Job Order] Reset Edit');

export const loadGenDocLinkInit = createAction('[Job Order No] Gen Doc Link Load Init', props<{ request: any }>());
export const loadGenDocLinkSuccess = createAction('[Job Order No] Gen Doc Link Load Success', props<{ totalRecords: number }>());
export const loadGenDocLinkFailed = createAction('[Job Order No] Gen Doc Link Load Failed', props<{ error: string }>());


export const createGenDocLinkInit = createAction(
  "[Job Order No] Create Gen Doc Link Init",
  props<{ genDocLink: MrpJobOrderGenDocLinkContainerModel }>()
);
export const createGenDocLinkSuccess = createAction(
  "[Job Order No] Create Gen Doc Link Success",
  props<{
    genDocLink: MrpJobOrderGenDocLinkContainerModel;
  }>()
);
export const createGenDocLinkFailure = createAction(
  "[Job Order No] Create Gen Doc Link Failure",
  props<{ error: string }>()
);
export const selectGenericDocLinkGuid = createAction('[Job Order No] Delete Gen Doc Link Init', props<{ guid: string }>());

export const deleteGenericDocLinkInit = createAction('[Job Order No] Delete Gen Doc Link Init');
export const deleteGenericDocLinkSuccess = createAction('[Job Order No] Delete Gen Doc Link Success');
export const deleteGenericDocLinkFailed = createAction('[Job Order No] Delete Gen Doc Link Failed', props<{ error: string }>());

export const selectEditMode = createAction('[Job Order No] Select Edit Mode', props<{ editMode: boolean }>())

export const selectTotalContainerQty = createAction('[Sales Order Link] Select Total Container Qty', props<{ totalContainerQty: number }>())

export const generateProcessInstanceInit = createAction('[Process Instance] Create Process Instance Init',props<{ jobOrderGuid: string }>());
export const generateProcessInstanceSuccess = createAction('[Process Instance] Create Process Instance Success');
export const generateProcessInstanceFailed = createAction('[Process Instance] Create Process Instance Failed', props<{error: string}>());

export const loadProcessInstanceInit = createAction('[Process Instance] Process Instance Load Init', props<{ request: any }>());
export const loadProcessInstanceSuccess = createAction('[Process Instance] Process Instance Load Success', props<{ totalRecords: number }>());
export const loadProcessInstanceFailed = createAction('[Process Instance] Process Instance Load Failed', props<{ error: string }>());

export const selectProcessInstance = createAction('[Process Instance] Select Process Instance', props<{ processInstance: any }>());

export const createProcessInstanceInit = createAction(
  "[Job Order] Create Process Instance Init",
  props<{ processInstance: MrpProcessInstanceContainerModel }>()
);
export const createProcessInstanceSuccess = createAction(
  "[Job Order] Create Process Instance Success",
  props<{
    processInstance: MrpProcessInstanceContainerModel;
  }>()
);
export const createProcessInstanceFailed = createAction(
  "[Job Order] Create Process Instance Failure",
  props<{ error: string }>()
);

export const updateProcessInstanceInit = createAction('[Company] Update KO Config Init', props<{ processInstance: MrpProcessInstanceContainerModel }>());
export const updateProcessInstanceSuccess = createAction('[Company] Update KO Config Success', props<{ processInstance: MrpProcessInstanceContainerModel }>());
export const updateProcessInstanceFailure = createAction('[Company] Update KO Config Failure', props<{error: string}>());
