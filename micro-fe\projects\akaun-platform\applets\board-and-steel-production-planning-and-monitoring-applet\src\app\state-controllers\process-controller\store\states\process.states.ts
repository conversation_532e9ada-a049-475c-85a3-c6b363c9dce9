import { bl_fi_generic_doc_single_line, bl_inv_bin_line_RowClass } from 'blg-akaun-ts-lib';

// TODO: This one should be shared across MRP applets
export interface ProcessState {
    currentProcess: string;
    processMap: {[key: string]: string};
    selectedProcess: any;
    selectedBinLine: bl_inv_bin_line_RowClass;
    machineMap: {[key: string]: string[]};
    updatedProcessForm: any;

}

export const initState: ProcessState = {
    currentProcess: null,
    processMap: null,
    selectedProcess: null,
    selectedBinLine: null,
    machineMap: {
        'dc248987-a1e4-a2c1-20e7-87e7eb82c43a': ['CRBD-13']
    },
    updatedProcessForm:null
};
