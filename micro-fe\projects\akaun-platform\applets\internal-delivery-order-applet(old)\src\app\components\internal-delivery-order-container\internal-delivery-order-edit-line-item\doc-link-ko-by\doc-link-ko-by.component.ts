// import { Component, EventEmitter, OnInit, OnDestroy, Input, Output, ViewChild } from '@angular/core';
// import { SubSink } from 'subsink2';
// import { combineLatest, forkJoin, iif, Observable, of, Subject, zip } from 'rxjs';
// import { catchError, exhaustMap, filter, map, mergeMap, switchMap, tap } from 'rxjs/operators';
// import { Pagination, SubQueryService, bl_fi_generic_doc_link_RowClass, bl_fi_generic_doc_line_RowClass, bl_fi_generic_doc_hdr_RowClass, GenericDocHdrService } from 'blg-akaun-ts-lib';
// import { AppConfig } from 'projects/shared-utilities/visa';
// import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
// import { formatMoneyInList } from 'projects/shared-utilities/format.utils';
// import * as moment from 'moment';
// import { UUID } from 'angular2-uuid';

// @Component({
//   selector: 'app-internal-delivery-order-doc-link-ko-by',
//   templateUrl: './doc-link-ko-by.component.html',
//   styleUrls: ['./doc-link-ko-by.component.css']
// })
// export class DocLinkKoByComponent implements OnInit, OnDestroy {

//   @Input() localState: any;
//   @Input() hdr: bl_fi_generic_doc_hdr_RowClass;
//   @Input() service;
//   @Input() serverDocTypeDoc2: string;  
//   @Input() selectedLineItem$: Observable<any>;
//   @Input() links$: Observable<any>;
//   @Input() AppletConstants;

//   protected subs = new SubSink();
  
//   gridApi;
//   enableListing;
//   draftLinks: Map<any, any>; draftPNS;
//   draftLinksIds = []; draftPNSIds = [];

//   apiVisa = AppConfig.apiVisa;

//   @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

//   defaultColDef = {
//     filter: 'agTextColumnFilter',
//     floatingFilter: true,
//     floatingFilterComponentParams: { suppressFilterButton: true },
//     minWidth: 200,
//     flex: 2,
//     sortable: true,
//     resizable: true,
//     suppressCsvExport: true
//   };
  
//   columnsDefs = [
//     { headerName: 'Doc No.', field: 'doc_number', cellStyle: () => ({ 'text-align': 'left' }), maxWidth: 100 },
//     { headerName: 'Server Doc Type', field: 'server_doc_type', cellStyle: () => ({ 'text-align': 'left' }) },    
//     { headerName: 'Txn Date', field: 'date_txn', cellStyle: () => ({ 'text-align': 'left' }), maxWidth: 100, 
//       valueFormatter: params => params.value ? moment(params.value).format('YYYY-MM-DD') : null },
//     { headerName: 'UOM', field: 'uom', cellStyle: () => ({ 'text-align': 'left' }), maxWidth: 100 },
//     { headerName: 'Unit Price (Inc. of Tax)', field: 'unit_price_txn', type: 'numericColumn', maxWidth: 150,
//       valueFormatter: (params) => params.value ? formatMoneyInList(params.value) : null },
//     // { headerName: 'Base Qty.', field: 'order_qty', type: 'numericColumn', maxWidth: 100 },
//     // { headerName: 'Bal. Qty.', field: 'open_qty', type: 'numericColumn', maxWidth: 100 },
//     { headerName: 'Knocked Off Qty.', field: 'ko_qty', type: 'numericColumn' },
//     // { headerName: 'Knocked Off Qty.', field: 'knocked_off_qty', type: 'numericColumn', maxWidth: 100 },
//     // { headerName: 'Knockoff Qty.', type: 'numericColumn', maxWidth: 150,
//     //   editable: true,
//     //   valueGetter: (params) => { return params.data.ko_qty; },
//     //   valueSetter: (params) => {
//     //     let newKOvalue = parseInt(params.newValue);
//     //     let valueChanged = newKOvalue <= params.data.open_qty && newKOvalue >= 0;
//     //     if (valueChanged) {
//     //       params.data.ko_qty = newKOvalue;
//     //     }
//     //     return valueChanged;
//     //   }
//     // },
//     // { headerName: 'Status', field: 'status', cellStyle: () => ({ 'text-align': 'left' }) }
//   ];

//   constructor(
//     private subQueryService: SubQueryService,
//     private genDocHdrService: GenericDocHdrService,
//     ) {
//   }

//   ngOnInit(): void {
//     console.log("this.hdr:: ",this.hdr)
//     if(this.hdr.doc_entity_hdr_guid && this.hdr.guid_comp){
//       this.enableListing = true;
//     } else {
//       this.enableListing = false;
//     }
//   }

  

//   onGridReady(params) {
//     this.gridApi = params.api;
//     this.gridApi.closeToolPanel();
//     this.setGridData();
//   }

//   setGridData() {
//     const datasource = {
//       getRows: grid => {
//         this.subs.sink = combineLatest([this.selectedLineItem$, this.links$]).pipe(
//           mergeMap(([a, b]) => {
//             const source: Observable<any>[] = [];
//             let match = false;
//             b.forEach(link => {
//               if (
//                 link.status !== "DRAFT_TEMP" &&
//                 link.status !== "DELETED" &&
//                 link.guid_doc_1_line === a.guid &&
//                 link.server_doc_type_doc_1_line === this.AppletConstants.docType &&
//                 link.server_doc_type_doc_2_line === this.serverDocTypeDoc2
//               ) {
//                 match = true;
//                 const pagination = new Pagination();
//                 pagination.sortCriteria.push(
//                   { columnName: 'doc_entity_hdr_guid', value: this.hdr.doc_entity_hdr_guid? this.hdr.doc_entity_hdr_guid.toString():'' },
//                   { columnName: 'guid_comp', value: this.hdr.guid_comp? this.hdr.guid_comp.toString():'' },
//                   { columnName: 'line_guids', value: link.guid_doc_1_line },
//                   { columnName: 'guids', value: link.guid_doc_1_hdr }
//                 )
//                 source.push(
//                   zip(
//                     // Get source document no.
//                     this.genDocHdrService.getByGuid(link.guid_doc_1_hdr.toString(), this.apiVisa).pipe(
//                       catchError((err) => of(err))
//                     ),
//                     // Get line item details
//                     <Observable<any>>this.service.getGenericDocHdrLineLinkByCriteria(pagination, this.apiVisa).pipe(
//                       catchError((err) => of(err))
//                     )
//                   ).pipe(map(([hdr, line]) => {
//                     let line1 = line.data.filter(a => a.bl_fi_generic_doc_line.guid === link.guid_doc_1_line)[0];

//                     let temp = {
//                       doc_number: hdr.data.bl_fi_generic_doc_hdr.server_doc_1,
//                       server_doc_type: link.server_doc_type_doc_1_line,
//                       uom: line1.bl_fi_generic_doc_line.uom,
//                       unit_price_txn: line1.bl_fi_generic_doc_line.unit_price_txn,
//                       date_txn: link.date_txn,
//                       ko_qty: link.quantity_contra
//                     }

//                     return temp;
//                   }))
//                 );
//               }
//             });
//             return iif(() => match,
//               forkJoin(source).pipe(map((b_inner) => {
//                 b = b_inner;
//                 return b
//               })),
//               of([])
//             );
//           })
//         ).subscribe(resolved => {
//           grid.success({
//             rowData: resolved,
//             rowCount: resolved.length
//           });
//         }, err => {
//           console.log(err);
//         });
//       }
//     }
//     this.gridApi.setServerSideDatasource(datasource);
//   }

//   onRowClicked(item) {
//     console.log(item);
//     // TODO: to view the link and perform delete operation
    
//   }

//   ngOnDestroy() {
//     this.subs.unsubscribe();
//   }

// }
