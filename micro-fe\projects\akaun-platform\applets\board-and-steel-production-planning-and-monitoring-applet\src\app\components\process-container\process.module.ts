import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { AgGridModule } from 'ag-grid-angular';
import {  } from './process.module';
import { ProcessIssueJobOrderComponent } from './process-issue-job-order/process-issue-job-order.component';
import { IssueJobOrderMainComponent } from './process-issue-job-order/issue-job-order-main/issue-job-order-main.component';
import { IssueJobOrderPlannedOutputComponent } from './process-issue-job-order/issue-job-order-planned-output/issue-job-order-planned-output.component';
import { DropDownModule } from 'blg-akaun-ng-lib';
import { ProcessSelectJobOrderNoComponent } from './process-select-job-order-no/process-select-job-order-no.component';
import { ProcessContainerComponent } from './process-container.component';
import { ProcessJobOrderListingComponent } from './process-job-order-listing/process-job-order-listing.component';
import { ProcessJobOrderViewComponent } from './process-job-order-view/process-job-order-view.component';
import { InternalJobOrderModule } from '../internal-job-order-container/internal-job-order.module';
import { IssueJobOrderPlannedInputComponent } from './process-issue-job-order/issue-job-order-planned-input/issue-job-order-planned-input.component';
import { ProcessSelectBinComponent } from './process-select-bin/process-select-bin.component';
import { ProcessAddPlannedOutputComponent } from './process-add-planned-output/process-add-planned-output.component';
import { AddPlannedOutputMainComponent } from './process-add-planned-output/add-planned-output-main/add-planned-output-main.component';
import { ProcessAddPlannedInputComponent } from './process-add-planned-input/process-add-planned-input.component';
import { AddPlannedInputMainComponent } from './process-add-planned-input/add-planned-input-main/add-planned-input-main.component';
import { JobOrderViewMainComponent } from './process-job-order-view/job-order-view-main/job-order-view-main.component';
import { JobOrderViewPlannedInputComponent } from './process-job-order-view/job-order-view-planned-input/job-order-view-planned-input.component';
import { JobOrderViewPlannedOutputComponent } from './process-job-order-view/job-order-view-planned-output/job-order-view-planned-output.component';
import { ProcessSelectTemplateComponent } from './process-select-template/process-select-template.component';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { processFeatureKey } from '../../state-controllers/process-controller/store/reducers/process.reducers';
import { processReducers } from '../../state-controllers/process-controller/store/reducers';
import { ProcessEffects } from '../../state-controllers/process-controller/effects';

@NgModule({
  declarations: [
    ProcessContainerComponent,
    ProcessJobOrderListingComponent,
    ProcessJobOrderViewComponent,
    ProcessIssueJobOrderComponent,
    IssueJobOrderMainComponent,
    IssueJobOrderPlannedOutputComponent,
    IssueJobOrderPlannedInputComponent,
    ProcessSelectJobOrderNoComponent,
    ProcessSelectBinComponent,
    ProcessAddPlannedOutputComponent,
    AddPlannedOutputMainComponent,
    ProcessAddPlannedInputComponent,
    AddPlannedInputMainComponent,
    JobOrderViewMainComponent,
    JobOrderViewPlannedInputComponent,
    JobOrderViewPlannedOutputComponent,
    ProcessSelectTemplateComponent
  ],
  imports: [
    CommonModule,
    UtilitiesModule,
    AgGridModule,
    InternalJobOrderModule,
    DropDownModule,
    StoreModule.forFeature(processFeatureKey, processReducers.process),
    EffectsModule.forFeature([ProcessEffects]),
  ]
})
export class ProcessModule { }
