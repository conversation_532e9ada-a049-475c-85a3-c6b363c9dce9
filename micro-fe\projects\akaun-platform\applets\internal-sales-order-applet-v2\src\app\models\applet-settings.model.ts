export interface CustomStatusParams {
  name: string;
  descr?: string;
  default?: boolean;
}

export interface AppletSettings {
  ENABLE_TXN_DATE_DEFAULT_FILTER: boolean;
  EDIT_CONTRA_TXN_DATE: boolean;
  DEFAULT_BRANCH: string;
  DEFAULT_LOCATION: string;
  DEFAULT_COMPANY: string;
  DEFAULT_TOGGLE_COLUMN: string;
  DEFAULT_ORIENTATION: string;
  ENABLE_DIMENSION: boolean;
  ENABLE_PROFIT_CENTER: boolean;
  ENABLE_PROJECT: boolean;
  ENABLE_SEGMENT: boolean;
  ENABLE_SST: boolean;
  ENABLE_WHT: boolean;
  PRINTABLE: string;
  DEFAULT_DECIMAL_PRECISION: number;
  DEFAULT_DECIMAL_STEP: string;
  ENABLE_CUSTOM_STATUS_1: boolean;
  ENABLE_CUSTOM_STATUS_2: boolean;
  ENABLE_CUSTOM_STATUS_3: boolean;
  ENABLE_CUSTOM_STATUS_4: boolean;
  ENABLE_CUSTOM_STATUS_5: boolean;
  ENABLE_CUSTOM_STATUS_HDR_1: boolean;
  ENABLE_CUSTOM_STATUS_HDR_2: boolean;
  ENABLE_CUSTOM_STATUS_HDR_3: boolean;
  ENABLE_CUSTOM_STATUS_HDR_4: boolean;
  ENABLE_CUSTOM_STATUS_HDR_5: boolean;
  ENABLE_CUSTOM_STATUS_LINE_1: boolean;
  ENABLE_CUSTOM_STATUS_LINE_2: boolean;
  ENABLE_CUSTOM_STATUS_LINE_3: boolean;
  ENABLE_CUSTOM_STATUS_LINE_4: boolean;
  ENABLE_CUSTOM_STATUS_LINE_5: boolean;
  NAME_CUSTOM_STATUS_HDR_1: CustomStatusParams[];
  NAME_CUSTOM_STATUS_HDR_2: CustomStatusParams[];
  NAME_CUSTOM_STATUS_HDR_3: CustomStatusParams[];
  NAME_CUSTOM_STATUS_HDR_4: CustomStatusParams[];
  NAME_CUSTOM_STATUS_HDR_5: CustomStatusParams[];
  NAME_CUSTOM_STATUS_LINE_1: CustomStatusParams[];
  NAME_CUSTOM_STATUS_LINE_2: CustomStatusParams[];
  NAME_CUSTOM_STATUS_LINE_3: CustomStatusParams[];
  NAME_CUSTOM_STATUS_LINE_4: CustomStatusParams[];
  NAME_CUSTOM_STATUS_LINE_5: CustomStatusParams[];
  LIST_CUSTOM_STATUS_HDR_1: CustomStatusParams[];
  LIST_CUSTOM_STATUS_HDR_2: CustomStatusParams[];
  LIST_CUSTOM_STATUS_HDR_3: CustomStatusParams[];
  LIST_CUSTOM_STATUS_HDR_4: CustomStatusParams[];
  LIST_CUSTOM_STATUS_HDR_5: CustomStatusParams[];
  LIST_CUSTOM_STATUS_LINE_1: CustomStatusParams[];
  LIST_CUSTOM_STATUS_LINE_2: CustomStatusParams[];
  LIST_CUSTOM_STATUS_LINE_3: CustomStatusParams[];
  LIST_CUSTOM_STATUS_LINE_4: CustomStatusParams[];
  LIST_CUSTOM_STATUS_LINE_5: CustomStatusParams[];

  HIDE_UNIT_PRICE_STD_PRICING_SCHEME: boolean;
  HIDE_UNIT_PRICE_STD_INCL_TAX: boolean;
  HIDE_UNIT_PRICE_STD_EXCL_TAX: boolean;
  HIDE_UNIT_PRICE_STD_UOM_INCL_TAX: boolean;
  HIDE_UNIT_PRICE_STD_UOM_EXCL_TAX: boolean;
  HIDE_UNIT_PRICE_NET_UOM_EXCL_TAX: boolean;
  HIDE_UNIT_PRICE_NET_EXCL_TAX: boolean;
  HIDE_UNIT_DISCOUNT: boolean;
  HIDE_QTY_BASE: boolean;
  HIDE_QTY_UOM: boolean;
  HIDE_UOM_TO_BASE_RATIO: boolean;
  HIDE_UNIT_DISCOUNT_UOM_EXCL_TAX: boolean;
  HIDE_UNIT_PRICE_TXN_UOM_INCL_TAX: boolean;
  HIDE_AMOUNT_STD_EXCL_TAX: boolean;
  HIDE_DISCOUNT_AMOUNT_EXCL_TAX: boolean;
  HIDE_AMOUNT_NET_EXCL_TAX: boolean;
  HIDE_TAX_CONFIG_SELECTION: boolean;
  HIDE_WHT_CONFIG_SELECTION: boolean;
  HIDE_UNIT_PRICE_TXN: boolean;
  HIDE_AMOUNT_TXN: boolean;
  HIDE_TRACKING_ID: boolean;
  HIDE_PERMIT_NO: boolean;
  DISABLE_GEN_DOC_LISTING: boolean;
  DISABLE_PICK_PACK_QUEUE_LISTING: boolean;
  HIDE_SERIAL_NUMBER: boolean;
  HIDE_BATCHES: boolean;
  HIDE_BIN: boolean;
  HIDE_STOCK_AVAILABILITY: boolean;
  HIDE_COSTING_DETAILS: boolean;
  HIDE_ISSUE_LINK: boolean;
  HIDE_ATTACHMENT: boolean;
  HIDE_ECOM_SYNC: boolean;
  HIDE_LAST_PURCHASE_PRICE: boolean;
  DISABLE_LINE_ITEM_QUEUE_LISTING: boolean;
  HIDE_SERVER_DOC_1: boolean;
  HIDE_SERVER_DOC_2: boolean;
  HIDE_SERVER_DOC_3: boolean;
  HIDE_CLIENT_DOC_TYPE: boolean;
  HIDE_CLIENT_DOC_1: boolean;
  HIDE_CLIENT_DOC_2: boolean;
  HIDE_CLIENT_DOC_3: boolean;
  HIDE_CLIENT_DOC_4: boolean;
  HIDE_CLIENT_DOC_5: boolean;
  HIDE_GENDOC_DISCARD_BUTTON: boolean;
  HIDE_GENDOC_FINAL_BUTTON: boolean;
  HIDE_LAST_SALES_PRICE: boolean;
  HIDE_ARAP_PNS: boolean;
  HIDE_ARAP_SETTLEMENT: boolean;
  HIDE_ARAP_DOC_OPEN: boolean;
  HIDE_ARAP_CONTRA: boolean;
  HIDE_ARAP_BAL: boolean;
  HIDE_DESCRIPTION: boolean;
  salesManLabels: [];
  HIDE_LOCATION: boolean;
  HIDE_DELIVERY_BRANCH: boolean;
  HIDE_DELIVERY_LOCATION: boolean;
  ENABLE_BRANCH_FILTER: boolean;
  ENABLE_AUTO_POPUP: boolean;
  ENABLE_CREDIT_LIMIT_FILTER: boolean;
  WORKFLOW_PROCESS_GUID: string;
  FINAL_STATUS_GUID: string;
  HIDE_SEND_EMAIL_BUTTON: string;
  ENABLE_MULTIPLE_KO: boolean;
  SORT_ORDER: string;
  HIDE_DELIVERY_DETAILS_TAB: boolean;
  HIDE_DELIVERY_TRIPS_TAB: boolean;
  HIDE_DELIVERY_PLANS_TAB: boolean;
  HIDE_KO_FOR_TAB: boolean;
  HIDE_SETTLEMENT_TAB: boolean;
  HIDE_DEPARTMENT_HDR_TAB: boolean;
  HIDE_MAIN_ARAP_TAB: boolean;
  HIDE_TRACE_DOCUMENT_TAB: boolean;
  HIDE_MAIN_CONTRA_TAB: boolean;
  HIDE_DOC_LINK_TAB: boolean;
  HIDE_ATTACHMENT_TAB: boolean;
  HIDE_EXPORT_TAB: boolean;
  HIDE_ECOMSYNC_TAB: boolean;
  HIDE_STATUS_TAB: boolean;
  HIDE_EVENTS_TAB: boolean;
  AUTO_UI: boolean;
  AUTO_UI_ENTITY: boolean;
  AUTO_UI_BILLING: boolean;
  AUTO_UI_SHIPPING: boolean;
  AUTO_UI_CREDIT_LIMIT: boolean;
  AUTO_UI_CREDIT_TERM: boolean;
  AUTO_UI_SALES_AGENT: boolean;
  AUTO_UI_LINE_ITEM: boolean;
  HIDE_RECEIPT_VOUCHER_TAB: boolean;
  HIDE_TOTAL_DISCOUNT_AMOUNT: boolean;
  HIDE_ENTITY_CATEGORY:  boolean;
  HIDE_LOGIN:  boolean;
  HIDE_PAYMENT_CONFIG:  boolean;
  HIDE_TAX:  boolean;
  HIDE_CONTACT:  boolean;
  HIDE_BRANCH:  boolean;
  HIDE_ITEM_PRICING:  boolean;
  HIDE_REMARK:  boolean;
  HIDE_CREDIT_TERM_AND_LIMIT:  boolean;
  GL_CODE_CHECK_FILTER:  boolean;
  ENABLE_FILTER_BY_TODAYS_TXN: boolean;
  RESTRICT_EDITING_ITEM_NAME: boolean;
  ENABLE_DRAFT_LOCK_SERIAL_NUMBER_CHECKING: boolean;
  ENABLE_SERIAL_NUMBER_VALIDATION_FINAL: boolean;
  DISALLOW_SELL_BELOW_MIN_PRICE: boolean;
  DISALLOW_SELL_BELOW_REPLACEMENT_PRICE: boolean;
  ENABLE_EDITING_UNIT_PRICE_STD: boolean;
  DISALLOW_SELL_BELOW_MA_COST: boolean;
  HIDE_CREDIT_TERMS: boolean;
  HIDE_CREDIT_LIMIT: boolean;
  HIDE_DUE_DATE: boolean;
  HIDE_CRM_CONTACT: boolean;
  HIDE_SALES_LEAD: boolean;
  HIDE_MULTI_DISCOUNT: boolean;
  HIDE_DELIVERY_INSTRUCTIONS: boolean;
  HIDE_DEPARTMENT: boolean;
  HIDE_DELIVERY_DETAILS: boolean;
  HIDE_DELIVERY_TRIPS: boolean;
  HIDE_MEMBERSHIP_POINTS: boolean;
  HIDE_GENDOC_VOID_BUTTON: boolean;
  HIDE_GENDOC_SAVE_BUTTON: boolean;
  HIDE_SALES_AGENT: boolean;
  HIDE_LISTING_BRANCH: boolean;
  DEFAULT_PRICEBOOK: any;
  DEFAULT_CURRENCY: any;
  HIDE_MARKETPLACE_STATUS_UPDATE_PAGE: boolean;
  HIDE_CREATED_BY_DETAILS: boolean;
  ENABLE_EDIT_SETTLEMENT_DATE: boolean;
  HIDE_UNWRAPPED_ITEMS: boolean;
  HIDE_MEMBERCARD: boolean;
  HIDE_REFERENCE: boolean;
  HIDE_MARKETPLACE_SHIPPING_FEE: boolean;
  HIDE_BASE_CURRENCY: boolean;
  HIDE_CURRENCY_RATE: boolean;
  HIDE_CLIENT_KEY: boolean;
  HIDE_CLIENT_VALUE: boolean;
  HIDE_INTERNAL_REMARKS: boolean;
  HIDE_EXTERNAL_REMARKS: boolean;
  HIDE_SALES_ORDER_STATUS: boolean;
  HIDE_WORKFLOW_STATUS: boolean;
  HIDE_WORKFLOW_RESOLUTION: boolean;
  HIDE_DELETE_BUTTON_IN_DRAFT_MODE: boolean;
  HIDE_REMARKS_MAIN_LISTING: boolean;
  HIDE_REFERENCE_MAIN_LISTING: boolean;

  // Credit card settings
  HIDE_CARD_NO: boolean;
  HIDE_NAME: boolean;
  HIDE_CARD_ISSUER: boolean;
  HIDE_CARD_EXPIRY: boolean;
  HIDE_APPROVAL_CODE: boolean;
  HIDE_BATCH: boolean;
  HIDE_CARD_TYPE: boolean;
  HIDE_CVV: boolean;
  MANDATORY_CARD_NO: boolean;
  MANDATORY_NAME: boolean;
  MANDATORY_CARD_ISSUER: boolean;
  MANDATORY_CARD_EXPIRY: boolean;
  MANDATORY_APPROVAL_CODE: boolean;
  MANDATORY_BATCH: boolean;
  MANDATORY_CARD_TYPE: boolean;
  MANDATORY_CVV: boolean;
  SHOW_BUDGET: boolean;
  DEFAULT_POSTING_STATUS: string;
  SHOW_DRAFT_BUTTON: boolean;
  HIDE_CLOSE_BUTTON: boolean;
  ENABLE_EMPLOYEE_LOGIN_AUTO_DETECTION: boolean;
  ENABLE_ITEM_NAME_MAX_LIMIT: boolean;
  ITEM_NAME_MAX_LIMIT: number;
  HIDE_MAIN_DETAILS_SALES_AGENT: boolean;
  MANDATORY_MAIN_DETAILS_SALES_AGENT: boolean;
  HIDE_LINE_ITEMS_GL_CODE: boolean;
  HIDE_ACCOUNT_BILLING_CONTACT: boolean;
  HIDE_ACCOUNT_SHIPPING_CONTACT: boolean;
  ENABLE_MRP_JOB_ORDER_LINK: boolean;
  HIDE_MRP_JOB_ORDER_LINK_CREATE_BUTTON: boolean;
  HIDE_SOURCE_DOC_NO: boolean;
  HIDE_CLONE_BUTTON: boolean;
  SHOW_FOREX_DATA_SOURCE: boolean;
  CANNOT_EDIT_CURRENCY_RATE: boolean;
  MANDATORY_SEGMENT: boolean;
  MANDATORY_DIMENSION: boolean;
  MANDATORY_PROFIT_CENTER: boolean;
  MANDATORY_PROJECT: boolean;
  HIDE_SEGMENT: boolean;
  HIDE_DIMENSION: boolean;
  HIDE_PROFIT_CENTER: boolean;
  HIDE_PROJECT: boolean;
  VERTICAL_ORIENTATION: boolean;
  EXPAND_MAIN_DETAILS: boolean;
  EXPAND_ACCOUNT: boolean;
  EXPAND_LINE_ITEMS: boolean;
  EXPAND_KO_FOR: boolean;
  EXPAND_DELIVERY_DETAILS: boolean;
  EXPAND_MAIN_ARAP: boolean;
  EXPAND_DEPARTMENT_HDR: boolean;
  EXPAND_TRACE_DOCUMENT: boolean;
  EXPAND_DOC_LINK: boolean;
  EXPAND_ATTACHMENT: boolean;
  EXPAND_EXPORT: boolean;
  EXPAND_MAIN_CONTRA: boolean;
  EXPAND_SETTLEMENT: boolean;
  EXPAND_POSTING: boolean;
  EXPAND_DELIVERY_TRIPS: boolean;
  EXPAND_EVENTS: boolean;

    //for sidebar
    HIDE_SO_DETAILED_REPORT_MENU: boolean;
    HIDE_PICK_PACK_QUEUE_MENU: boolean;
    HIDE_DELIVERY_ITEMS_MENU: boolean;
    HIDE_ECOMSYNC_MENU: boolean;
    HIDE_BATCH_PRINTING_MENU: boolean;
    HIDE_DELIVERY_PLAN_MENU: boolean;
    HIDE_CALENDER_MENU: boolean;
    HIDE_FILE_EXPORT_MENU: boolean;

  // Line items
  DISALLOW_SELL_BELOW_UNIT_PRICE_STD_INCL_TAX: boolean;
  DISALLOW_SELL_ABOVE_UNIT_PRICE_STD_INCL_TAX: boolean;
  HIDE_SELL_BELOW_MIN_PRICE: boolean;
  HIDE_SELL_BELOW_REPLACEMENT_PRICE: boolean;
  HIDE_SELL_BELOW_MA_COST: boolean;
  HIDE_LINE_ITEM_CLIENT_DOC_1: boolean;

  MANDATORY_TRANSACTION_NO: boolean;
  HIDE_TRANSACTION_NO: boolean;
  HIDE_DELETE_CONTRA: boolean;
  HIDE_LINE_LISTING_TAX_AMOUNT: boolean;
  HIDE_GROSS_PROFIT_TAB: boolean;
}
export class DEFAULTS {
  public static DECIMAL_PRECISION: number = 2;
  public static DECIMAL_STEP: string = ".01";
}
