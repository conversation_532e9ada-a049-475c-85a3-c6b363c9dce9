import { Component, OnInit, ChangeDetectionStrategy, Input, EventEmitter, Output, OnD<PERSON>roy } from '@angular/core';
import { Store } from '@ngrx/store';
import {
  BranchService,
  CompanyService,
  CustomerService,
  GenericDocContainerModel,
  LocationService,
  Pagination,
  InternalSalesOrderService } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable, zip, forkJoin, EMPTY, of } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { internalDeliveryOrderSearchModel } from '../../../../models/advanced-search-models/internal-delivery-order.model';

@Component({
  selector: 'app-internal-delivery-order-create-line-item-quotation',
  templateUrl: './internal-delivery-order-create-line-item-quotation.component.html',
  styleUrls: ['./internal-delivery-order-create-line-item-quotation.component.css']
})
export class InternalDeliveryOrderCreateLineItemQuotationComponent implements OnInit, OnDestroy {

  @Input() localState: any;

  @Output() next = new EventEmitter<any>();

  private subs = new SubSink();

  searchModel = internalDeliveryOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Sales Order No', field: 'bl_fi_generic_doc_hdr.client_doc_1'},
    {headerName: 'Branch', field: 'bl_fi_generic_doc_hdr.guid_branch'},
    {headerName: 'Location', field: 'bl_fi_generic_doc_hdr.guid_store'},
    {headerName: 'Customer Name', field: 'bl_fi_generic_doc_hdr.doc_entity_hdr_guid'},
    {headerName: 'Created Date', field: 'bl_fi_generic_doc_hdr.created_date',
      valueFormatter: (params) => params.value ? params.value.split('T')[0] : ''
    },
    {headerName: 'Status', field: 'status'},
  ];

  constructor(
    // private readonly store: Store<InternalSalesOrderStates>,
    private soService: InternalSalesOrderService,
    private compService: CompanyService,
    private brchService: BranchService,
    private lctnService: LocationService,
    private cstmrService: CustomerService) { }

  ngOnInit() {
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        // this.store.dispatch(InternalSalesOrderActions.loadSalesOrdersInit({request: grid.request}));
        this.subs.sink = this.soService.getByCriteria(new Pagination(grid.request.startRow, grid.request.endRow, [
          {columnName: 'calcTotalRecords', operator: '=', value: 'true'}
        ]), apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<GenericDocContainerModel>[] = [];
            b.data.forEach( doc => source.push(
              zip(
                this.compService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_comp.toString(), apiVisa).pipe(
                  catchError((err) => {
                    // this.store.dispatch(InternalSalesOrderActions.loadSalesOrderFailed({error: err.message}));
                    return of(err);
                  })
                ),
                this.brchService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_branch.toString(), apiVisa).pipe(
                  catchError((err) => {
                    // this.store.dispatch(InternalSalesOrderActions.loadSalesOrderFailed({error: err.message}));
                    return of(err);
                  })
                ),
                this.lctnService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_store.toString(), apiVisa).pipe(
                  catchError((err) => {
                    // this.store.dispatch(InternalSalesOrderActions.loadSalesOrderFailed({error: err.message}));
                    return of(err);
                  })
                ),
                this.cstmrService.getByGuid(doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString(), apiVisa).pipe(
                  catchError((err) => {
                    // this.store.dispatch(InternalSalesOrderActions.loadSalesOrderFailed({error: err.message}));
                    return of(err);
                  })
                )).pipe(
                  map(([b_a, b_b, b_c, b_d]) => {
                    doc.bl_fi_generic_doc_hdr.guid_comp = b_a.error ? b_a.error.code : b_a.data.bl_fi_mst_comp.name;
                    doc.bl_fi_generic_doc_hdr.guid_branch = b_b.error ? b_b.error.code : b_b.data.bl_fi_mst_branch.name;
                    doc.bl_fi_generic_doc_hdr.guid_store = b_c.error ? b_c.error.code : b_c.data.bl_inv_mst_location.name;
                    doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid = b_d.error ? b_d.error.code : b_d.data.bl_fi_mst_entity_hdr.name;
                    return doc;
                  })
                )
            ));
            return forkJoin(source).pipe(
              map(b_inner =>  {
                b.data = b_inner;
                return b;
              })
            );
          })
        ).subscribe( resolved => {
          // this.store.dispatch(InternalSalesOrderActions.loadSalesOrderSuccess({totalRecords: resolved.totalRecords}));
          grid.successCallback(resolved.data, resolved.totalRecords);
        }, err => {
          // this.store.dispatch(InternalSalesOrderActions.loadSalesOrderFailed({error: err.message}));
          grid.failCallback();
        });
        // this.soService.getByCriteria(
        //   new Pagination(grid.request.startRow, grid.request.endRow,
        //     [{columnName: 'calcTotalRecords', operator: '=', value: 'true'}]), apiVisa).subscribe( a => {
        //   grid.successCallback(a.data, a.totalRecords);
        // }, err => grid.failCallback());
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onRowClicked(entity) {
    if (entity) {
      // this.store.dispatch(InternalSalesOrderActions.selectEntity({entity}));
      if (!this.localState.deactivateSOList) {
        this.next.emit({index: 4, type: 'salesOrder'});
      }
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}

