import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AppLoginContainerModel, bl_alg_cc_agent_ext_RowClass, bl_fi_generic_doc_hdr_RowClass } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../models/applet-settings.model';

@Component({
  selector: 'app-issue-job-order-main',
  templateUrl: './issue-job-order-main.component.html',
  styleUrls: ['./issue-job-order-main.component.css']
})
export class IssueJobOrderMainComponent implements OnInit, OnDestroy {

  @Input() hdr$: Observable<bl_fi_generic_doc_hdr_RowClass>;
  @Input() appletSettings$: Observable<AppletSettings>;
  @Input() userProfile$: Observable<AppLoginContainerModel>;
  @Input() process$: Observable<string>;
  @Input() machines$: Observable<string[]>;

  @Output() itemCode = new EventEmitter();
  @Output() updateMain = new EventEmitter();
  @Output() selectTemplate = new EventEmitter();

  apiVisa = AppConfig.apiVisa;

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Branch', formControl: 'branch', type: 'branch', readonly: false},
    {label: 'Issued By', formControl: 'issuedBy', type: 'text', readonly: true},
    {label: 'Machine Code', formControl: 'machineCode', type: 'machineCode', readonly: false},
  ];

  rightColControls = [
    {label: 'Location', formControl: 'location', type: 'location', readonly: false},
    {label: 'Date', formControl: 'date', type: 'date', readonly: false},
    {label: 'Process Template', formControl: 'processTemplate', type: 'processTemplate', readonly: false},
  ];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      branch: new FormControl('', Validators.required),
      location: new FormControl('', Validators.required),
      issuedBy: new FormControl(),
      machineCode: new FormControl('', Validators.required),
      date: new FormControl('', Validators.required),
      processTemplate: new FormControl('', Validators.required),
      remarks: new FormControl(),
    });
    this.subs.sink = this.hdr$.pipe(
      withLatestFrom(
        this.appletSettings$,
        this.userProfile$,
        this.process$
      )
    ).subscribe(([a, b, c, d]) => {
      this.form.patchValue({
        branch: a.guid_branch ?? b.DEFAULT_BRANCH,
        location: a.guid_store ?? b.DEFAULT_LOCATION,
        issuedBy: c?.appLoginSubject.name,
        machineCode: a.doc_desc,
        date: a.date_txn ? a.date_txn : new Date().toISOString().split('T')[0],
        // processName: d,
        remarks: a.doc_remarks
      });
    });
  }


  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
