import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ViewChild,
} from "@angular/core";
import { MatTabGroup } from "@angular/material/tabs";
import { ComponentStore } from "@ngrx/component-store";
import { Store } from "@ngrx/store";
import { UUID } from "angular2-uuid";
import {
  bl_fi_generic_doc_line_RowClass,
  bl_fi_generic_doc_link_RowClass,
  FinancialItemService,
  TaxCodeCfgService,
} from "blg-akaun-ts-lib";
import { ClientSidePermissionsSelectors } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors";
import { ClientSidePermissionStates } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/states";
import { SessionSelectors } from "projects/shared-utilities/modules/session/session-controller/selectors";
import { SessionStates } from "projects/shared-utilities/modules/session/session-controller/states";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { map, switchMap, withLatestFrom } from "rxjs/operators";
import { SubSink } from "subsink2";
import { ViewColumnFacade } from "../../../../facades/view-column.facade";
import { AppletSettings } from "../../../../models/applet-settings.model";
import { LinkActions, PNSActions } from "../../../../state-controllers/draft-controller/store/actions";
import {
  // EditOutboundSelectors,
  HDREditSelectors,
  HDRSelectors,
  LinkSelectors,
  PNSSelectors,
} from "../../../../state-controllers/draft-controller/store/selectors";
import { DraftStates } from "../../../../state-controllers/draft-controller/store/states";
import { InternalDeliveryOrderActions } from "../../../../state-controllers/internal-delivery-order-controller/store/actions";
import { InternalDeliveryOrderSelectors } from "../../../../state-controllers/internal-delivery-order-controller/store/selectors";
import { InternalDeliveryOrderStates } from "../../../../state-controllers/internal-delivery-order-controller/store/states";
import { InternalDeliveryOrderEditLineItemComponent } from "../../internal-delivery-order-edit-line-item/internal-delivery-order-edit-line-item.component";
import { ItemDetailsComponent } from "../../internal-delivery-order-edit-line-item/item-details/item-details.component";
import { AddLineItemItemDetailsComponent } from "./item-details/add-line-item-item-details.component";
import { DeliveryInstructionComponent } from "./item-details/delivery-instructions/delivery-instructions.component";
// import { LineItemAddBatchNumberComponent } from "./add-line-item-batch-number/add-line-item-batch-number.component";
// import { LineItemAddBinNumberComponent } from "./add-line-item-bin-number/add-line-item-bin-number.component";
import { InternalDeliveryOrderAddLineItemMainComponent } from "./item-details/main-details/internal-delivery-order-add-line-item-main.component";
// import { AddLineItemSerialNumberComponent } from "./add-line-item-serial-number/add-line-item-serial-number.component";

interface LocalState {
  deactivateReturn: boolean;
  // deactivateBatchNo: boolean;
  deactivateIssueLink: boolean;
  selectedIndex: number;
  itemDetailsSelectedIndex: number;
  serialNumberSelectedIndex: number;
}

@Component({
  selector: "app-internal-delivery-order-add-line-item",
  templateUrl:
    "./internal-delivery-order-add-line-item.component.html",
  styleUrls: ["./internal-delivery-order-add-line-item.component.css"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore],
})
export class InternalDeliveryOrderAddLineItemComponent extends ViewColumnComponent {
  protected subs = new SubSink();

  protected compName = "Add Line Item";
  protected index = 12;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(
    (state) => state.deactivateReturn
  );
  // readonly deactivateBatchNo$ = this.componentStore.select(state => state.deactivateBatchNo);
  readonly selectedIndex$ = this.componentStore.select(
    (state) => state.selectedIndex
  );
  readonly itemDetailsSelectedIndex$ = this.componentStore.select(
    (state) => state.itemDetailsSelectedIndex
  );
  readonly serialNumberSelectedIndex$ = this.componentStore.select(
    (state) => state.serialNumberSelectedIndex
  );

  GenDocActions = InternalDeliveryOrderActions;
  LinkSelectors = LinkSelectors;
  LinkActions = LinkActions;
  PNSSelectors = PNSSelectors;
  PNSActions = PNSActions;

  prevIndex: number;
  protected prevLocalState: any;
  apiVisa = AppConfig.apiVisa;
  subItemType;
  // batch$ = this.store.select(InternalSalesOrderSelectors.selectBatch);
  item$ = this.store.select(InternalDeliveryOrderSelectors.selectLineItem);
  tax$ = this.taxService.get(this.apiVisa).pipe(
    map((d) => ({
      sst: d.data.filter((s) => s.bl_fi_cfg_tax_code.tax_gst_type),
      wht: d.data.filter((s) => s.bl_fi_cfg_tax_code.tax_wht_type),
    }))
  );
  line$ = this.store.select(
    InternalDeliveryOrderSelectors.selectLineItem);
  // draft$ = this.draftStore.select(HDRSelectors.selectHdr);
  invItem$ = this.store.select(InternalDeliveryOrderSelectors.selectInvItem);
  serial$ = this.store.select(InternalDeliveryOrderSelectors.selectSerial);

  draftCreateHdr$ = this.draftStore.select(HDRSelectors.selectHdr);
  draftEditHdr$ = this.draftStore.select(HDREditSelectors.selectHdr);
  // draft$ = this.draftStore
  //   .select(EditOutboundSelectors.selectEditOutboundState)
  //   .pipe(
  //     withLatestFrom(this.draftCreateHdr$, this.draftEditHdr$),
  //     map(([editOutbound, createHdr, editHdr]) => {
  //       if (editOutbound) {
  //         return editHdr;
  //       } else {
  //         return createHdr;
  //       }
  //     })
  //   );
  draft$ = this.draftCreateHdr$;
  editMode$ = this.store.select(InternalDeliveryOrderSelectors.selectEditMode);

  targetIndex: number;
  editMode: boolean;
  appletSettings: AppletSettings;
  showCostingDetails;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(InternalDeliveryOrderAddLineItemMainComponent) main: InternalDeliveryOrderAddLineItemMainComponent;
  @ViewChild(AddLineItemItemDetailsComponent) itemDetails: AddLineItemItemDetailsComponent;
  @ViewChild(DeliveryInstructionComponent) delivery: DeliveryInstructionComponent

  // @ViewChild(AddLineItemSerialNumberComponent)
  // serialNumber: AddLineItemSerialNumberComponent;
  // @ViewChild(LineItemAddBinNumberComponent)
  // binNumber: LineItemAddBinNumberComponent;
  // @ViewChild(LineItemAddBatchNumberComponent)
  // batchNumber: LineItemAddBatchNumberComponent;
  masterSettings$ = this.sessionStore.select(SessionSelectors.selectMasterSettings);
  clientSidePermissions$ = this.permissionStore.select(ClientSidePermissionsSelectors.selectAll)


  constructor(
    private viewColFacade: ViewColumnFacade,
    private taxService: TaxCodeCfgService,
    private fiService: FinancialItemService,
    private readonly componentStore: ComponentStore<LocalState>,
    private readonly store: Store<InternalDeliveryOrderStates>,
    private readonly draftStore: Store<DraftStates>,
    protected readonly permissionStore: Store<ClientSidePermissionStates>,
    private readonly sessionStore: Store<SessionStates>,
    private cdr: ChangeDetectorRef
  ) {
    super();
    this.editMode$.subscribe((mode) => this.editMode = mode);
  }

  ngOnInit() {
    this.subs.sink = this.masterSettings$.subscribe({ next: (resolve: AppletSettings) => { 
      this.appletSettings = resolve } });
    this.subs.sink = this.clientSidePermissions$.subscribe({
      next: (resolve) => {
        resolve.forEach(permission => {
          if (permission.perm_code === "SHOW_COSTING_DETAILS") {
            this.showCostingDetails = true;
            console.log(this.showCostingDetails);
          }
        })
      }
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(
      (resolve) => (this.prevIndex = resolve)
    );
    this.subs.sink = this.viewColFacade
      .prevLocalState$()
      .subscribe((resolve) => (this.prevLocalState = resolve));
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    // TODO: Optimize this
    this.subs.sink = this.viewColFacade.breadCrumbs$.subscribe(
      (a) => (this.targetIndex = a[1].index)
    );

    this.subs.sink = this.line$
      .pipe(
        switchMap((item: any) =>
          this.fiService.getByGuid(item.item_guid, this.apiVisa)
        )
      )
      .subscribe({
        next: (resolve: any) => {
          if (resolve.data) {
            this.subItemType = resolve.data?.bl_fi_mst_item_hdr?.sub_item_type;
            this.cdr.detectChanges(); // to show serial number tab immediately
          }
        },
      });
  }

  disableAdd() {
    return this.itemDetails?.main.form.invalid;
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateFIList: false,
      deactivateReturn: false,
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAdd() {
    console.log(this.itemDetails);
    const line = new bl_fi_generic_doc_line_RowClass();
    line.guid = UUID.UUID().toLowerCase();
    line.item_guid = this.itemDetails.main.form.value.itemGuid;
    line.item_code = this.itemDetails.main.form.value.itemCode;
    line.item_name = this.itemDetails.main.form.value.itemName;
    line.quantity_base = this.itemDetails.main.form.value.qty;
    line.amount_std = Number(this.itemDetails.main.form.value.stdAmt) ? this.itemDetails.main.form.value.stdAmt : 0;
    console.log("infinity", Number((this.itemDetails.main.form.value.stdAmt)));
    line.amount_discount = Number(this.itemDetails.main.form.value.discountAmt) ? this.itemDetails.main.form.value.discountAmt : 0;
    line.amount_net = Number(this.itemDetails.main.form.value.netAmt) ? this.itemDetails.main.form.value.netAmt : 0;
    line.tax_gst_code = this.itemDetails.main.form.value.taxCode;
    line.tax_gst_rate = Number(this.itemDetails.main.form.value.taxPercent) ? this.itemDetails.main.form.value.taxPercent : 0;
    line.amount_tax_gst = Number(this.itemDetails.main.form.value.taxAmt) ? this.itemDetails.main.form.value.taxAmt : 0;
    line.tax_wht_code = this.itemDetails.main.form.value.whtCode;
    line.tax_wht_rate = Number(this.itemDetails.main.form.value.whtPercent) ? this.itemDetails.main.form.value.whtPercent : 0;
    line.amount_tax_wht = Number(this.itemDetails.main.form.value.whtAmt) ? this.itemDetails.main.form.value.whtAmt : 0;
    line.amount_txn = Number(this.itemDetails.main.form.value.txnAmt) ? this.itemDetails.main.form.value.txnAmt : 0;
    line.item_remarks = this.itemDetails.main.form.value.remarks;
    line.item_txn_type = this.itemDetails.main.form.value.item_txn_type;
    line.item_sub_type = this.itemDetails.main.form.value.item_sub_type;
    // line.guid_dimension = this.itemDetails.dept.form.value.dimension;
    // line.guid_profit_center = this.itemDetails.dept.form.value.profitCenter;
    // line.guid_project = this.itemDetails.dept.form.value.project;
    // line.guid_segment = this.itemDetails.dept.form.value.segment;
    line.item_property_json = { ...this.itemDetails.main.form.value };
    line.unit_price_std = Number(this.itemDetails.main.form.value.unitPriceStdWithoutTax) ? this.itemDetails.main.form.value.unitPriceStdWithoutTax : 0;
    line.unit_price_txn = Number(this.itemDetails.main.form.value.unitPriceTxn) ? this.itemDetails.main.form.value.unitPriceTxn : 0;
    line.unit_price_net = Number(this.itemDetails.main.form.value.unitPriceNet) ? this.itemDetails.main.form.value.unitPriceNet : 0;
    line.unit_price_std_by_uom = Number(this.itemDetails.main.form.value.unitPriceStdUom) ? this.itemDetails.main.form.value.unitPriceStdUom : 0;
    line.unit_price_txn_by_uom = Number(this.itemDetails.main.form.value.unitPriceTxnUom) ? this.itemDetails.main.form.value.unitPriceTxnUom : 0;
    line.unit_disc_by_uom = Number(this.itemDetails.main.form.value.unitDiscountUom) ? this.itemDetails.main.form.value.unitDiscountUom : 0;
    line.uom = this.itemDetails.main.form.value.uom;
    line.uom_to_base_ratio = this.itemDetails.main.form.value.uomBaseRatio;
    line.qty_by_uom = parseFloat(this.itemDetails.main.form.value.qtyUom);
    line.status = "ACTIVE";
    line.line_property_json = <any>{ delivery_instructions: { ...this.itemDetails.delivery.form.value } };
    line.tracking_id = this.itemDetails.main.form.value.trackingId;
    line.server_doc_type = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
    line.client_doc_type = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
    // line.date_txn = this.lineItem.date_txn; // should be new date or keep old one?
    // line.serial_no = this.subItemType === this.SUB_ITEM_TYPE.serialNumber ? <any>{ serialNumbers: this.serialNumber.serialNumbers } : null;
    // line.batch_no = this.subItemType === this.SUB_ITEM_TYPE.batchNumber ? <any>{ batches: this.batchNumber.batchNumbers } : null;
    // line.bin_no = this.subItemType === this.SUB_ITEM_TYPE.binNumber ? <any>{ bins: this.binNumber.binNumbers } : null;

    console.log('line', line);

    // Update existing hdr balance by calculating delta (line2 - line1)
    // const diffLine = new bl_fi_generic_doc_line_RowClass();
    // diffLine.amount_discount = <any>(parseFloat(<any>line.amount_discount) - parseFloat(<any>this.lineItem.amount_discount));
    // diffLine.amount_net = <any>(parseFloat(<any>line.amount_net) - parseFloat(<any>this.lineItem.amount_net));
    // diffLine.amount_std = <any>(parseFloat(<any>line.amount_std) - parseFloat(<any>this.lineItem.amount_std));
    // diffLine.amount_tax_gst = <any>(parseFloat(<any>line.amount_tax_gst) - parseFloat(<any>this.lineItem.amount_tax_gst));
    // diffLine.amount_tax_wht = <any>(parseFloat(<any>line.amount_tax_wht) - parseFloat(<any>this.lineItem.amount_tax_wht));
    // diffLine.amount_txn = <any>(parseFloat(<any>line.amount_txn) - parseFloat(<any>this.lineItem.amount_txn));

    if (this.itemDetails.main.form.value.itemType) {
      const link = this.getLink(line.guid.toString());
      if (link) {
        link.quantity_contra = line.quantity_base;
        console.log('link', link);
        this.draftStore.dispatch(LinkActions.editLink({ link }));
      }
    }
    this.draftStore.dispatch(PNSActions.editPNS({ pns: line }));
    if (this.editMode) {
      this.viewColFacade.addLineItemToDraftEdit(line);
    }
    else {
      this.viewColFacade.addLineItemToDraft(line);
    }

    this.onReturn();
  }

  getLinkTxnType(itemType: string): string {
    switch (itemType) {
      case "inbound":
        return "IIST_IODO";
      case "salesOrder":
        return "ISO_IODO";
    }
  }

  getLink(lineGuid: string): bl_fi_generic_doc_link_RowClass {
    let link;
    if (!this.editMode) {
      this.subs.sink = this.draftStore.select(LinkSelectors.selectAll).subscribe(resolved => {
        link = resolved.find(x => x.guid_doc_2_line === lineGuid);
      })
    }
    else {
      this.subs.sink = this.draftStore.select(LinkSelectors.selectAll).subscribe(resolved => {
        link = resolved.find(x => x.guid_doc_2_line === lineGuid);
      })
    }
    return link;
  }

  goToBatchListing() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
    });
    this.viewColFacade.onNextAndReset(this.index, 14);
  }

  goToBinListing() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
    });
    this.viewColFacade.onNextAndReset(this.index, 15);
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
        serialNumberSelectedIndex:
          // this.subItemType === "SERIAL_NUMBER"
          //   ? this.serialNumber.matTab.selectedIndex
          //   : null,
          null,
      });
    }
    this.subs.unsubscribe();
  }
}
