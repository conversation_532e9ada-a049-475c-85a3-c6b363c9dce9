import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderSelectShippingAddressComponent } from './internal-delivery-order-select-shipping-address.component';

describe('InternalDeliveryOrderSelectShippingAddressComponent', () => {
  let component: InternalDeliveryOrderSelectShippingAddressComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderSelectShippingAddressComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderSelectShippingAddressComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderSelectShippingAddressComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
