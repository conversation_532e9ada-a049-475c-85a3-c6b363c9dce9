import { Action, createReducer, on } from '@ngrx/store';
import { PlannedOutputActions } from '../actions';
import { initState, plannedOutputAdapter, PlannedOutputState } from '../states/planned-output.states';

export const plannedOutputReducers = createReducer(
    initState,
    on(PlannedOutputActions.addPlannedOutput, (state, action) => plannedOutputAdapter.addOne(action.line, state)),
    on(PlannedOutputActions.addManyPlannedOutput, (state, action) => plannedOutputAdapter.addMany(action.lines, state)),
    on(PlannedOutputActions.deletePlannedOutput, (state, action) => plannedOutputAdapter.removeOne(action.guid, state)),
    on(PlannedOutputActions.editPlannedOutput, (state, action) => plannedOutputAdapter.upsertOne(action.line, state)),
    on(PlannedOutputActions.resetPlannedOutput, (state, action) => plannedOutputAdapter.removeAll(state))
);

export function reducers(state: PlannedOutputState | undefined, action: Action) {
    return plannedOutputReducers(state, action);
}
