import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { bl_fi_generic_doc_ext_RowClass } from 'blg-akaun-ts-lib';
import * as moment from 'moment';
import { internalJobOrderSearchModel } from '../../../../models/advanced-search-models/internal-job-order.model';
@Component({
  selector: 'app-add-line-item-issue-link',
  templateUrl: './add-line-item-issue-link.component.html',
  styleUrls: ['./add-line-item-issue-link.component.css']
})
export class AddLineItemIssueLinkComponent implements OnInit {

  @Input() localState: any;
  @Input() rowData: any[] = [];

  @Output() issue = new EventEmitter();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Project', field: 'project', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Issue Number', field: 'issueNumber', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Issue Summary', field: 'issueSummary', type: 'numericColumn'},
    {headerName: 'Issue Description', field: 'issueDescription', type: 'numericColumn'},
    {headerName: 'Assignee', field: 'assignee', type: 'numericColumn'},
    {headerName: 'Created Date  ', field: 'createdDate', type: 'numericColumn'},
    {headerName: 'Resolved Date', field: 'resolvedDate', type: 'numericColumn'},
    {headerName: 'Status', field: 'status', type: 'numericColumn'},
  ];

  searchModel = internalJobOrderSearchModel;

  constructor() { }

  ngOnInit() {
    this.rowData.push({
      project: 'Dummy Project',
      issueNumber: 'ABC-123',
      issueSummary: 'This is a dummy issue',
      issueDescription: 'This is a dummy issue',
      assignee: 'YV',
      createdDate: '2021-03-15',
      resolvedDate: '',
      status: 'Pending',
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

  onRowClicked(e) {
    this.issue.emit(e);
  }

}
