<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
         Sales Order Details
      </span>
    </div>
    <div fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="5px">
      <button mat-raised-button color="primary" type="button"
      [disabled]="disableButton()"
      (click)="onSave()">SAVE</button>
    </div>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Main Details">
    <div class="with-delete">
      <app-sales-order-main (updateForm)="onUpdate($event)"></app-sales-order-main>
    </div>
  </mat-tab>

  <mat-tab label="Item Details">
     <app-item-details></app-item-details>
  </mat-tab>

</mat-tab-group>

