const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/supervisor-applet/runtime-es2015.js',
    './dist/supervisor-applet/polyfills-es2015.js',
    './dist/supervisor-applet/scripts.js',
    './dist/supervisor-applet/main-es2015.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/supervisor-applet');
  await concat(files, './elements/akaun-platform/applets/supervisor-applet/supervisor-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
