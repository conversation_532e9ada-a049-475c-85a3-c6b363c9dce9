import {Component} from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import {RowNode} from 'ag-grid-community';
import * as moment from 'moment';


@Component({
    selector: 'group-row-cell',
    template: `
    <table>
        <tr>
            <td class="td_a">Job Id: </td>
            <td class="td_b">{{job_id}}</td>
            <td class="td_a">Date Txn: </td>
            <td class="td_b">{{date_txn}}</td>
            <td class="td_a">SO No: </td>
            <td class="td_b">{{server_doc_1}}</td>
            <td class="td_a">SO Ref No: </td>
            <td class="td_b">{{server_doc_2}}</td>
       </tr>
        <tr>
            <td class="td_a">Shipping Address:</td>
            <td class="td_b">{{shipping_address_city}},{{shipping_address_state}}</td>
            <td class="td_a">Ext amount fee:</td>
            <td class="td_b">{{ext_amount_fee}}</td>
            <td class="td_a">Job Created By: </td>
            <td class="td_b">{{job_hdr_created_by}}</td>
            <td class="td_a">SO Created By: </td>
            <td class="td_b">{{gendoc_hdr_created_by}}</td>
        </tr>
    </table>
 `,
    styles: [
        `
        .row {
            display: inline-block;
        }
        td {
            text-align: left;
            

        }
        .td_a
        {
            font-family: Arial; 
            font-size: 12px;
            padding-left:8px;
            width:30px;
            font-weight: bold;
            line-height: 10px
        }
        .td_b
        {
            font-family: Arial; 
            font-size: 12px;
            padding-left:8px;
            width:50px;
            font-weight: lighter;
            line-height: 10px

        }
        
       
    `
    ]
})
export class GroupRowInnerRenderer implements ICellRendererAngularComp {

   
    protected node: RowNode;
    job_id: any;
    gen_doc_no: any;
    server_doc_1:any;
    server_doc_2:any;
    ext_amount_fee: any;
    date_txn:any;
    shipping_address_city:any;
    shipping_address_state:any;
    invoice_no:any;
    job_hdr_created_by: any;
    gendoc_hdr_created_by: any;
    
    agInit(params: any): void {
        console.log(params)
        console.log("GROUP_ROW")
        this.node = params.node;
        console.log("this.node");
        console.log(this.node);
        if (this.node.field == 'bl_del_job_hdr.running_number_01')
        {
            this.job_id = this.node.allLeafChildren[0].data.bl_del_job_hdr.running_number_01;
            this.server_doc_1 = this.node.allLeafChildren[0].data.bl_fi_generic_doc_hdr?.server_doc_1;
            this.server_doc_2 = this.node.allLeafChildren[0].data.bl_fi_generic_doc_hdr?.server_doc_2;
            this.ext_amount_fee = this.node.allLeafChildren[0].data.bl_del_job_hdr.ext_amount_fee;
            this.date_txn = moment(this.node.allLeafChildren[0].data.bl_fi_generic_doc_hdr?.date_txn).format("YYYY-MM-DD");
            this.shipping_address_city = this.node.allLeafChildren[0].data.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress?.city;
            this.shipping_address_state = this.node.allLeafChildren[0].data.bl_fi_generic_doc_hdr?.delivery_entity_json?.shippingAddress?.state;
            this.job_hdr_created_by = this.node.allLeafChildren[0].data.job_created_by_name;
            this.gendoc_hdr_created_by = this.node.allLeafChildren[0].data.gendoc_created_by_name;

            console.log ("1");
            console.log (
            this.node.allLeafChildren[0].data.generic_doc_hdr_1_doc_link);
        }
        

          for(var i = 0;i<this.node.allLeafChildren[0].data.generic_doc_hdr_1_doc_link?.length;i++) {
            if (i == 0) 
            {
            this.invoice_no = this.node.allLeafChildren[0].data.generic_doc_hdr_1_doc_link[i].server_doc_1;
            }
            else
            {
                this.invoice_no = this.invoice_no + ", " + this.node.allLeafChildren[0].data.generic_doc_hdr_1_doc_link[i].server_doc_1;
            }
         }
       

        //this.flagCode = params.flagCodes[params.node.key];
        
    }

    refresh(): boolean {
        return false;
    }
}
