<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px">
      </button>
      <span>
        View Job Order
      </span>
    </div>
    <button mat-raised-button color="primary" type="button" (click)="onSave()">Save</button>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Main">
      <app-job-order-view-main  [appletSettings$]="appletSettings$" [userProfile$]="userProfile$" [process$]="process$" [machines$]="machines$" (updateMain)="onUpdateMain($event)"></app-job-order-view-main>
  </mat-tab>
  <mat-tab label="Planned Output">
    <app-job-order-view-planned-output [localState]="localState$ | async"  (next)="onAddOutput()"></app-job-order-view-planned-output>
  </mat-tab>
  <mat-tab label="Planned Input">
    <app-job-order-view-planned-input [localState]="localState$ | async"  (next)="onAddInput()"></app-job-order-view-planned-input>
  </mat-tab>
  <mat-tab label="Department">
    <app-internal-job-order-create-department [appletSettings$]="appletSettings$"  (updateDepartment)="onUpdateDepartment($event)"></app-internal-job-order-create-department>
  </mat-tab>
</mat-tab-group>
<!-- <div style="padding: 5px;">
  <button mat-raised-button color="warn" type="button" (click)="onDelete()">
    <span>{{ (deleteConfirmation$ | async) ? 'CLICK AGAIN TO CONFIRM' : 'DELETE'}}</span>
  </button>
</div> -->
