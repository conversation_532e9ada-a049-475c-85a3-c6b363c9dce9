import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { LocationService, Pagination, FinancialItemService, FinancialItemContainerModel } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../../models/advanced-search-models/internal-job-order.model';

@Component({
  selector: 'app-line-quotation-item',
  templateUrl: './line-quotation-item.component.html',
  styleUrls: ['./line-quotation-item.component.css']
})
export class LineQuotationItemComponent implements OnInit {

  @Output() item = new EventEmitter<FinancialItemContainerModel>();

  protected subs = new SubSink();

  prevIndex: number;
  protected prevLocalState: any;

  searchModel = internalJobOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Quotation No', field: 'bl_fi_mst_item_hdr.code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Code', field: 'bl_fi_mst_item_hdr.code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'bl_fi_mst_item_hdr.name', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Quotation Qty', field: 'bl_fi_mst_item_hdr.category', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Delivered Qty', field: 'bl_fi_mst_item_hdr.category', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Open Qty', field: 'bl_fi_mst_item_hdr.category', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'UOM', field: 'bl_fi_mst_item_hdr.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Unit Price', field: 'stockBalQty'},
    {headerName: 'Status', field: 'stockBalQty'},
  ];

  constructor(
    protected fiService: FinancialItemService,
    protected lctnService: LocationService) {
  }

  ngOnInit() {}

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        // this.store.dispatch(InternalPackingOrderActions.loadPackingOrdersInit({request: grid.request}));
        this.subs.sink = this.fiService.getByCriteria(new Pagination(grid.request.startRow, grid.request.endRow, [
          {columnName: 'calcTotalRecords', operator: '=', value: 'true'}
        ]), apiVisa).pipe(
        ).subscribe( resolved => {
          // TODO: Get data from correct endpoint
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderSuccess({totalRecords: resolved.totalRecords}));
          grid.success({
            rowData: [],
            rowCount: 0
          });
        }, err => {
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderFailed({error: err.message}));
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onRowClicked(entity: FinancialItemContainerModel) {
    if (entity) {
      this.item.emit(entity)
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}