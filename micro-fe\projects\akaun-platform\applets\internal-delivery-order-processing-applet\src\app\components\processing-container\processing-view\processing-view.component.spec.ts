import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProcessingViewComponent } from './processing-view.component';

describe('ProcessingViewComponent', () => {
  let component: ProcessingViewComponent;
  let fixture: ComponentFixture<ProcessingViewComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ProcessingViewComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProcessingViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
