import { AfterViewChecked, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';
import { GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { AccountAddressComponent } from './account-address/account-address.component';
import { AccountEntityDetailsComponent } from './account-entity-details/account-entity-details.component';
@Component({
  selector: 'app-internal-delivery-order-create-account',
  templateUrl: './internal-delivery-order-create-account.component.html',
  styleUrls: ['./internal-delivery-order-create-account.component.css']
})
export class InternalDeliveryOrderCreateAccountComponent implements OnInit, AfterViewChecked, OnD<PERSON>roy {

  @Input() draft$: Observable<GenericDocContainerModel>;
  @Input() hdrDraft$: Observable<GenericDocContainerModel>;
  @Input() toggleColumn$;
  @Input() childSelectedIndex$;

  @Output() entity = new EventEmitter();
  @Output() shippingInfo = new EventEmitter();
  @Output() billingInfo = new EventEmitter();
  @Output() contactPerson = new EventEmitter();
  @Output() selectShipping = new EventEmitter();
  @Output() updateShipTo = new EventEmitter();
  @Output() updateShippingAddress = new EventEmitter();


  private subs = new SubSink();

  invalid = true;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(AccountEntityDetailsComponent) entityDetails: AccountEntityDetailsComponent;
  @ViewChild(AccountAddressComponent) address: AccountAddressComponent;

  constructor() { }

  ngOnInit() {
  }

  ngAfterViewChecked() {
    this.matTab.realignInkBar();
    this.invalid = this.entityDetails?.form.invalid || this.address?.billingForm.invalid || this.address?.shippingForm.invalid;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
