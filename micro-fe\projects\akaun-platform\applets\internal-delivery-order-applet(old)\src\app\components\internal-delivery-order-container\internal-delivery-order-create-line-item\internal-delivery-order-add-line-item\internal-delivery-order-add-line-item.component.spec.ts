import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalOutboundStockTransferAddLineItemComponent } from './internal-delivery-order-add-line-item.component';

describe('InternalOutboundStockTransferSelectItemComponent', () => {
  let component: InternalOutboundStockTransferAddLineItemComponent;
  let fixture: ComponentFixture<InternalOutboundStockTransferAddLineItemComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalOutboundStockTransferAddLineItemComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalOutboundStockTransferAddLineItemComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
