import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { bl_fi_generic_doc_hdr_RowClass, bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-issue-job-order-planned-output',
  templateUrl: './issue-job-order-planned-output.component.html',
  styleUrls: ['./issue-job-order-planned-output.component.css']
})
export class IssueJobOrderPlannedOutputComponent implements OnInit {

  @Input() localState: any;
  @Input() rowData: bl_fi_generic_doc_line_RowClass[] = [];

  @Output() next = new EventEmitter();
  @Output() lineItem = new EventEmitter<bl_fi_generic_doc_line_RowClass>();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    onCellClicked: (params) => this.onRowClicked(params)
  };

  gridApi;

  columnsDefs = [
    // {headerName: 'Job Order No', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Size of Wire', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Required Length', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Priority', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Remarks', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Location', field: 'location', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Job Order No', field: 'batch_no.bin_code'},
    {headerName: 'Container Qty', field: 'batch_no.container_measure'},
    {headerName: 'Container Measure', field: 'batch_no.container_qty'},
    {headerName: 'Remarks', field: 'item_remarks', cellStyle: () => ({'text-align': 'left'})},
  ];

  selectedRowIndex = null;

  constructor() { }

  ngOnInit() {
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    // this.gridApi.getRowNode(this.localState.selectedLineItemRowIndex)?.setSelected(true);
  }

  onNext() {
    this.next.emit();
  }

  onRowClicked(e) {
    // this.selectedRowIndex = this.localState.selectedLineItemRowIndex === null ? e.rowIndex : null;
    this.lineItem.emit(e.data);
  }

}
