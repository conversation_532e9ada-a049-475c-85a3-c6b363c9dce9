import {
  GenericDocContainerModel
} from 'blg-akaun-ts-lib';

  export interface ProcessingState {
  selectedEntity: GenericDocContainerModel;
  totalRecords: number;
  errorLog: {timeStamp: Date, log: string}[];
  draft: GenericDocContainerModel;
  draftEdit: GenericDocContainerModel;
  updateAgGrid: boolean;
}

export const initState: ProcessingState = {
  selectedEntity: null,
  totalRecords: 0,
  errorLog: [],
  draft: new GenericDocContainerModel(),
  draftEdit: null,
  updateAgGrid: false,
};
