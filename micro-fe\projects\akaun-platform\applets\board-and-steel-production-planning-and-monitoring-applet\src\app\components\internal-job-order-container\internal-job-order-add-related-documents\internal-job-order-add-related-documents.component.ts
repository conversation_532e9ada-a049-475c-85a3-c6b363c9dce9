import { Component, ChangeDetectionStrategy } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { Pagination, BranchService, InternalSalesOrderService, GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../models/advanced-search-models/internal-job-order.model';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { forkJoin, iif, Observable, of } from 'rxjs';
import * as moment from 'moment';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';

interface LocalState {
  deactivateReturn: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-internal-job-order-add-related-documents',
  templateUrl: './internal-job-order-add-related-documents.component.html',
  styleUrls: ['./internal-job-order-add-related-documents.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalJobOrderAddRelatedDocumentsComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Add Related Documents';
  protected readonly index = 10;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  prevIndex: number;
  protected prevLocalState: any;

  searchModel = internalJobOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Doc No', field: 'bl_fi_generic_doc_hdr.server_doc_1', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Branch', field: 'bl_fi_generic_doc_hdr.code_branch', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Server Doc Type', field: 'bl_fi_generic_doc_hdr.server_doc_type', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Status', field: 'bl_fi_generic_doc_hdr.status', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Date', field: 'bl_fi_generic_doc_hdr.date_created', cellStyle: () => ({'text-align': 'left'}),
    valueFormatter: params => moment(params.value).format('YYYY-MM-DD')},
  ];

  constructor(
    private viewColFacade: ViewColumnFacade,
    private soService: InternalSalesOrderService,
    private brnchService: BranchService,
    protected readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        // this.store.dispatch(InternalPackingOrderActions.loadPackingOrdersInit({request: grid.request}));
        this.subs.sink = this.soService.getByCriteria(new Pagination(grid.request.startRow, grid.request.endRow, [
          {columnName: 'calcTotalRecords', operator: '=', value: 'true'}
        ]), apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<GenericDocContainerModel>[] = [];
            b.data.forEach( doc => source.push(
              forkJoin([
                this.brnchService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_branch.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                )]).pipe(
                  map(([b_a]) => {
                    doc.bl_fi_generic_doc_hdr.code_branch = b_a.error ? b_a.error.code : b_a.data.bl_fi_mst_branch.code;
                    return doc;
                  })
                )
            ));
            return iif(() => b.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                b.data = b_inner;
                return b
              })),
              of(b)
            );
          })
        ).subscribe( resolved => {
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderSuccess({totalRecords: resolved.totalRecords}));
          grid.success({
            rowData: resolved.data,
            rowCount: resolved.totalRecords
          });
        }, err => {
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderFailed({error: err.message}));
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAddDoc: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
