import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ActivatedRoute } from '@angular/router';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { combineLatest, EMPTY, iif, of } from 'rxjs';
import { delay, map, mergeMap, withLatestFrom } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { JobOrderMain, JobOrderNoDepartment } from '../../../models/internal-job-order.model';
import { HDRActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDRSelectors, PlannedInputSelectors, PlannedOutputSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { ProcessSelectors } from '../../../state-controllers/process-controller/store/selectors';
import { ProcessStates } from '../../../state-controllers/process-controller/store/states';
import { InternalJobOrderEditLineItemDepartmentComponent } from '../../internal-job-order-container/internal-job-order-edit-line-item/edit-line-item-item-details/internal-job-order-edit-line-item-department/internal-job-order-edit-line-item-department.component';
import { IssueJobOrderMainComponent } from '../process-issue-job-order/issue-job-order-main/issue-job-order-main.component';
import { IssueJobOrderPlannedInputComponent } from '../process-issue-job-order/issue-job-order-planned-input/issue-job-order-planned-input.component';
import { IssueJobOrderPlannedOutputComponent } from '../process-issue-job-order/issue-job-order-planned-output/issue-job-order-planned-output.component';
import { ProcessActions } from '../../../state-controllers/process-controller/store/actions';

interface LocalState {
  deactivateAddInput: boolean;
  deactivateAddOutput: boolean;
  deactivateReturn: boolean;
  selectedIndex: number;
  deleteConfirmation: boolean;
}

@Component({
  selector: 'app-process-job-order-view',
  templateUrl: './process-job-order-view.component.html',
  styleUrls: ['./process-job-order-view.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessJobOrderViewComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'View Job Order';
  protected index = 1;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly deleteConfirmation$ = this.componentStore.select(state => state.deleteConfirmation);

  prevIndex: number;
  protected prevLocalState: any;

  dept$ = combineLatest(
    [
      this.draftStore.select(HDRSelectors.selectDimension),
      this.draftStore.select(HDRSelectors.selectProfitCenter),
      this.draftStore.select(HDRSelectors.selectProject),
      this.draftStore.select(HDRSelectors.selectSegment)
  ]).pipe(
    map(([a, b, c, d]) => ({guid_dimension: a, guid_profit_center: b, guid_project: c, guid_segment: d}))
  );
  // hdr$ = this.store.select(InternalJobOrderSelectors.selectJobOrder).pipe(
  //   map(a => a.bl_fi_generic_doc_hdr)
  // );
  // plannedOutput$ = this.store.select(InternalJobOrderSelectors.selectJobOrder).pipe(
  //   map(a => a.bl_fi_generic_doc_line.filter(l => l.txn_type = 'OUTPUT'))
  // );
  // plannedInput$ = this.store.select(InternalJobOrderSelectors.selectJobOrder).pipe(
  //   map(a => a.bl_fi_generic_doc_line.filter(l => l.txn_type = 'INPUT'))
  // );
  // hdr$ = this.draftStore.select(HDRSelectors.selectHdr);
  // plannedOutput$ = this.draftStore.select(PlannedOutputSelectors.selectAll);
  // plannedInput$ = this.draftStore.select(PlannedInputSelectors.selectAll);
  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({...a, ...b})));
  userProfile$ = this.sessionStore.select(SessionSelectors.selectUserProfile);
  process$ = this.route.params.pipe(
    map(a => a.process),
    withLatestFrom(this.processStore.select(ProcessSelectors.selectProcessMap)),
    map(([guid, process]) => process[guid])
  );
  machines$ = this.route.params.pipe(
    map(a => a.process),
    withLatestFrom(this.processStore.select(ProcessSelectors.selectMachineMap)),
    map(([guid, machine]) => machine[guid])
  );

  deleteConfirmation = false;

  @ViewChild(IssueJobOrderMainComponent) main: IssueJobOrderMainComponent;
  @ViewChild(IssueJobOrderPlannedOutputComponent) output: IssueJobOrderPlannedOutputComponent;
  @ViewChild(IssueJobOrderPlannedInputComponent) input: IssueJobOrderPlannedInputComponent;
  @ViewChild(InternalJobOrderEditLineItemDepartmentComponent) department: InternalJobOrderEditLineItemDepartmentComponent;
  @ViewChild(MatTabGroup) matTab: MatTabGroup;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected readonly sessionStore: Store<SessionStates>,
    protected readonly store: Store<InternalJobOrderStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly processStore: Store<ProcessStates>,
    protected readonly componentStore: ComponentStore<LocalState>,
    private route: ActivatedRoute
  ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.subs.sink = this.process$.subscribe(x => console.log(x))

    this.subs.sink = this.deleteConfirmation$.pipe(
      mergeMap(a => {
        return iif(() => a, of(a).pipe(delay(3000)), of(EMPTY));
      })
    ).subscribe(resolve => {
      if (resolve === true) {
        this.componentStore.patchState({deleteConfirmation: false});
        this.deleteConfirmation = false;
      }
    });
  }

  onSave() {
    this.store.dispatch(ProcessActions.updateProcess());
  }

  disableSave() {
    return this.main?.form.invalid || !this.output?.rowData.length || !this.input?.rowData.length;
  }

  onDelete() {
    if (this.deleteConfirmation) {
      // const line = {...this.lineItem, status: 'DELETED'};
      // this.viewColFacade.editLineItem(line, this.prevIndex);
      // this.viewColFacade.deleteLineItem(this.lineItem.guid.toString(), this.prevIndex);
      this.deleteConfirmation = false;
      this.componentStore.patchState({deleteConfirmation: false});
    } else {
      this.deleteConfirmation = true;
      this.componentStore.patchState({deleteConfirmation: true});
    }
  }

  onUpdateMain(form: any) {
    console.log("Form Updated",form);
    this.processStore.dispatch(ProcessActions.updateProcessForm({form}));  }

  onUpdateDepartment(form: JobOrderNoDepartment) {
    this.draftStore.dispatch(HDRActions.updateDepartment({form}));
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAddInput() {
    if (!this.localState.deactivateAddInput) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAddInput: true,
      deactivateAddOutput: false
    });
      this.viewColFacade.onNextAndReset(this.index, 4);
    }
  }

  onAddOutput() {
    if (!this.localState.deactivateAddOutput) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAddInput: false,
      deactivateAddOutput: true
    });
      this.viewColFacade.onNextAndReset(this.index, 3);
    }
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex
      });
    }
    this.subs.unsubscribe();
  }

}
