import { Action, createReducer, on } from '@ngrx/store';
import { ViewCacheActions } from '../actions';
import { initialState, ViewCacheState } from '../states/view-cache.states';

export const viewCacheFeatureKey = 'viewCache';

export const sideNavReducer = createReducer(
  initialState,
  on(ViewCacheActions.cacheInternalDO, (state, action) => ({...state, internalDO: action.cache})),
);

export function reducer(state: ViewCacheState | undefined, action: Action) {
  return sideNavReducer(state, action);
}

