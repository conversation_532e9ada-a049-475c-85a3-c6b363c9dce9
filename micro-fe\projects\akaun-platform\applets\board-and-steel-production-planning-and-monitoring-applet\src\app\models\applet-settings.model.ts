export interface CustomStatusParams {
    name: string;
    descr?: string;
    default?: boolean;
}

export interface AppletSettings {
    DEFAULT_BRANCH: string;
    DEFAULT_LOCATION: string;
    ENABLE_DIMENSION: boolean;
    ENABLE_PROFIT_CENTER: boolean;
    ENABLE_PROJECT: boolean;
    ENABLE_SEGMENT: boolean;
    ENABLE_SST: boolean;
    ENABLE_WHT: boolean;
    PRINTABLE: string;
    ENABLE_CUSTOM_STATUS_1: boolean;
    ENABLE_CUSTOM_STATUS_2: boolean;
    ENABLE_CUSTOM_STATUS_3: boolean;
    ENABLE_CUSTOM_STATUS_4: boolean;
    ENABLE_CUSTOM_STATUS_5: boolean;
    ENABLE_CUSTOM_STATUS_HDR_1: boolean;
    ENABLE_CUSTOM_STATUS_HDR_2: boolean;
    ENABLE_CUSTOM_STATUS_HDR_3: boolean;
    ENABLE_CUSTOM_STATUS_HDR_4: boolean;
    ENABLE_CUSTOM_STATUS_HDR_5: boolean;
    ENABLE_CUSTOM_STATUS_LINE_1: boolean;
    ENABLE_CUSTOM_STATUS_LINE_2: boolean;
    ENABLE_CUSTOM_STATUS_LINE_3: boolean;
    ENABLE_CUSTOM_STATUS_LINE_4: boolean;
    ENABLE_CUSTOM_STATUS_LINE_5: boolean;
    NAME_CUSTOM_STATUS_HDR_1: CustomStatusParams[];
    NAME_CUSTOM_STATUS_HDR_2: CustomStatusParams[];
    NAME_CUSTOM_STATUS_HDR_3: CustomStatusParams[];
    NAME_CUSTOM_STATUS_HDR_4: CustomStatusParams[];
    NAME_CUSTOM_STATUS_HDR_5: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_1: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_2: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_3: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_4: CustomStatusParams[];
    NAME_CUSTOM_STATUS_LINE_5: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_1: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_2: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_3: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_4: CustomStatusParams[];
    LIST_CUSTOM_STATUS_HDR_5: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_1: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_2: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_3: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_4: CustomStatusParams[];
    LIST_CUSTOM_STATUS_LINE_5: CustomStatusParams[];
}
