import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { LicenseManager } from 'ag-grid-enterprise';
import { environment } from 'src/environments/environment';

LicenseManager.setLicenseKey
  ('CompanyName=WAVELET SOLUTIONS SDN. BHD.,LicensedApplication=akaun.com,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-026234,ExpiryDate=23_April_2023_[v2]_MTY4MjIwNDQwMDAwMA==674cd70d9fb70c2f7af064e2539295d0');

if (environment.production) {
  enableProdMode();
  console.log = function () {};
} else {
  // cloud
  //localStorage.setItem('authToken', 'eyJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NYS7FBygIDW_mdx9CdBX8zZ91KjxZw9bVqGszYWp364cln8RPrTT6KH60ZD6SGAsAMStbGzIpTQDfEGUzyp5YZDaq0mB9k5Adim9Tsy9Dk8yO9I3hi1iud0C0y9pKBYis-czcWYvXrnQwiYc8Tj9hxlJTo6XHcZ6wjxxWyAbKsI');
  //localStorage.setItem('guid', 'b17e4268-a091-4e60-9a6f-2dd4e9e17a54');
 // localStorage.setItem('email', '<EMAIL>');
  // sessionStorage.setItem('appletGuid', 'd9fd529d-3194-41ce-9bd1-c6cc07640340');
  // sessionStorage.setItem('appletCode', 'erp_internal_sales_order_applet_v2');
  // sessionStorage.setItem('appletLoginSubjectLinkGuid', '90323b76-f08e-4c9d-a792-2acacf14bd9c');
  // sessionStorage.setItem('tenantCode', 'staging_tenant');
  // prod
  // localStorage.setItem('guid', 'c230c492-4098-4f9b-bc50-8a61d0f9f99e');
  // localStorage.setItem('email', '<EMAIL>');
  // sessionStorage.setItem('appletGuid', '0ebfe87f-4bbc-46c3-a51d-e9f75c05fc65');
  // sessionStorage.setItem('appletCode', 'erp_internal_sales_order_applet_v2');
  // sessionStorage.setItem('appletLoginSubjectLinkGuid', '35cb631e-7c94-4b91-8e1c-f42760ae0193');
  // sessionStorage.setItem('tenantCode', 'testing');
  // dev
  // localStorage.setItem('guid', 'a6a9a21d-8a78-4903-bd43-52742df628bf');
  // localStorage.setItem('email', '<EMAIL>');
  // sessionStorage.setItem('appletGuid', 'b4157fd4-bb45-4261-989c-87b8c203a5e4');
  // sessionStorage.setItem('appletCode', 'erp_internal_sales_order_applet');
  // sessionStorage.setItem('appletLoginSubjectLinkGuid', '9910bba6-0605-4097-bf19-43aa4925c856');
  // sessionStorage.setItem('tenantCode', 'development_tenant');
  // akaun.com
  // localStorage.setItem('guid', 'b17e4268-a091-4e60-9a6f-2dd4e9e17a54');
  // localStorage.setItem('email', '<EMAIL>');
  // sessionStorage.setItem('appletGuid', '0ebfe87f-4bbc-46c3-a51d-e9f75c05fc65');
  // sessionStorage.setItem('appletCode', 'erp_internal_sales_order_applet_v2');
  // sessionStorage.setItem('appletLoginSubjectLinkGuid', '5fa4efa2-6a19-4044-affc-bdefcffd0f5e');
  // sessionStorage.setItem('tenantCode', 'testing');
  // sessionStorage.setItem('tenantCode', 'viewnet');
  // tsfitness tenant
  // localStorage.setItem('guid', '7c585a75-2d1d-41fc-b8a3-6ab6fb002ef4');
  // localStorage.setItem('email', '<EMAIL>');
  // sessionStorage.setItem('appletGuid', '0ebfe87f-4bbc-46c3-a51d-e9f75c05fc65');
  // sessionStorage.setItem('appletCode', 'erp_internal_sales_order_applet_v2');
  // sessionStorage.setItem('appletLoginSubjectLinkGuid', 'c8175382-8127-47af-95a9-f5d74ac06f3f');
  // sessionStorage.setItem('tenantCode', 'tsfitness');
  // cellabs tenant
  localStorage.setItem('guid', '810e584a-89d2-4cb1-a760-ffdc93573250');
  localStorage.setItem('email', '<EMAIL>');
  localStorage.setItem('authToken', '************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************');
  sessionStorage.setItem('appletGuid', '0ebfe87f-4bbc-46c3-a51d-e9f75c05fc65');
  sessionStorage.setItem('appletCode', 'erp_internal_sales_order_applet_v2');
  sessionStorage.setItem('appletLoginSubjectLinkGuid', '8acb1484-4711-496e-8598-f935aabb5109');
  sessionStorage.setItem('tenantCode', 'testing');

}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
