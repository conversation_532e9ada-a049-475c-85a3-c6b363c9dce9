<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div> <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button" (click)="onReturn()"> <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px"> </button> <span> Credit Limit Edit </span> </div> <button
      mat-raised-button color={{isClicked}} type="button" (click)="onSave()"
      [disabled]="!form.valid">{{addSuccess}}</button>
  </div>
</mat-card-title>

<form [formGroup]="form" #formDirectives="ngForm">
  <div fxLayout="column" class="view-col-forms">
    <div fxLayout="row wrap" fxFlexAlign="center" class="row">
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Credit Limit Code </mat-label> <input matInput placeholder="Credit Limit Code"
            [formControl]="form.controls['code']" required maxlength="255" style="text-transform:uppercase"
            oninput="this.value = this.value.toUpperCase()" readonly>
        </mat-form-field>
      </div>

      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Credit Limit Name </mat-label> <input matInput placeholder="Credit Limit Name"
            [formControl]="form.controls['name']" maxlength="255">
        </mat-form-field>

      </div>
      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Status </mat-label>
          <mat-select placeholder="Status" formControlName="status">
            <mat-option *ngFor="let s of status" [value]="s.value"> {{s.viewValue}} </mat-option>
          </mat-select>
        </mat-form-field>
      </div>

      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field class="example-full-width" appearance="outline">
          <mat-label>Currency</mat-label>
          <mat-select placeholder="Currency" formControlName="currency">
            <mat-option>
              <ngx-mat-select-search (keyup)="applyCurrencyFilter($event.target.value)" [placeholderLabel]="'Currency'"
                [noEntriesFoundLabel]="'No matching records found'" formControlName="currentCurrency" ngDefaultControl>
              </ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let item of newCurrency" [value]="item.display_short">
              {{item.display_short}} - {{ item.display_main }} </mat-option>
          </mat-select>
          <mat-hint *ngIf="form.controls['currency'].hasError('required') && form.controls['currency'].touched"
            class="text-danger font-14">Please insert currency
          </mat-hint>
        </mat-form-field>
      </div>

      <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
        <mat-form-field appearance="outline">
          <mat-label> Credit Limit Amount </mat-label> <input matInput placeholder="xx.xx " type="number"
            [formControl]="form.controls['amount']" maxlength="255" matInput class="right" ng-pattern="/^[0-9]{1,2})?$/"
            step="0.01" />

        </mat-form-field>
      </div>

    </div>
    <mat-divider></mat-divider>

    <!---->
    <div fxLayout="row wrap" fxFlexAlign="left" class="row">

      <div fxLayout="row wrap" fxFlexAlign="left" class="row">
        <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
          <mat-form-field appearance="outline">
            <mat-label> Created By </mat-label> <input matInput placeholder="Created By" formControlName="createdBy"
              style="color: grey" readonly />
          </mat-form-field>

        </div>

        <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
          <mat-form-field appearance="outline">
            <mat-label> Creation Date </mat-label> <input matInput placeholder="Creation Date"
              formControlName="createdDate" style="color: grey" readonly />
          </mat-form-field>

        </div>

        <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
          <mat-form-field appearance="outline">
            <mat-label> Modified By </mat-label> <input matInput placeholder="Modified By" formControlName="modifiedBy"
              style="color: grey" readonly />
          </mat-form-field>
        </div>

        <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
          <mat-form-field appearance="outline">
            <mat-label> Modified Date </mat-label> <input matInput placeholder="Modified Date"
              formControlName="modifiedDate" style="color: grey" readonly />
          </mat-form-field>

        </div>
      </div>
    </div>
  </div>

</form>
<div class=" center" style="margin-top: 10px; width: 50px;">
  <button mat-raised-button color="warn" type="button" (click)="onRemove()">Remove </button>
</div>