import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { InventoryItemService, MrpProcessInstanceLinesService, MrpProcessInstanceService, Pagination, StockConversionJobOrderService, StockConversionService, TenantUserProfileService } from 'blg-akaun-ts-lib';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { Observable, combineLatest, forkJoin, iif, of, zip } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ProcessSelectors } from '../../../state-controllers/process-controller/store/selectors';
import { ProcessStates } from '../../../state-controllers/process-controller/store/states';
import { AppConfig } from 'projects/shared-utilities/visa';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { ViewColumnFacade } from '../../../facades/view-column.facade';

interface LocalState {
  deactivateAddInput: boolean;
  deactivateReturn: boolean;
  selectedIndex: number;
}

@Component({
  selector: 'app-process-add-planned-input',
  templateUrl: './process-add-planned-input.component.html',
  styleUrls: ['./process-add-planned-input.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessAddPlannedInputComponent extends ViewColumnComponent {

  protected compName = 'Add Planned Input';
  protected index = 4;
  protected localState: LocalState;

  // readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  // readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  // readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);

  prevIndex: number;
  protected prevLocalState: any;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
  };

  gridApi;
  pagination = new Pagination();
  SQLGuids: string[] = null;
  private subs = new SubSink();
  job_order_guid;

  columnsDefs = [
    // {headerName: 'Job Order No', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Size of Wire', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Required Length', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Priority', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Code', field: 'bl_inv_stock_conversion_line.item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Description', field: 'description'},
    {headerName: 'Batch No', field: 'batchNo', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Planned Qty', field: 'plannedQty'},
    {headerName: 'New Item Code', field: 'newItemCode'},
    {headerName: 'New Description', field: 'newDesc'},
    {headerName: 'New Batch No', field: 'newBatchNo'},
    {headerName: 'New Planned Qty', field: 'newPlannedQty', cellStyle: () => ({'text-align': 'left'})},
    

  ];

  selectedRowIndex = null;

  constructor(    
    protected viewColFacade: ViewColumnFacade,
    private mrpProcessInstanceService: MrpProcessInstanceService,
    protected stockConvrsionJobOrderService: StockConversionJobOrderService,
    private stockConversionService : StockConversionService,
    private mrpProcessInstanceLinesService: MrpProcessInstanceLinesService,
    private inventoryItemService : InventoryItemService,
    private profileService: TenantUserProfileService,
    protected readonly processStore: Store<ProcessStates>,

    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.processStore.select(ProcessSelectors.selectProcess).subscribe(data => {
      console.log("Process Data",data);
      this.job_order_guid = data.job_order_guid;
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();  
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: "calcTotalRecords", operator: "=", value: "true" },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
          // { columnName: "bl_mrp_process_template_hdr_guid", operator: "=", value: this.process_template_hdr_guid },
          { columnName: 'job_order_hdr_guid', operator: '=', value: this.job_order_guid },
          

          {
            columnName: "guids",
            operator: "=",
            value: this.SQLGuids
              ? this.SQLGuids.slice(
                grid.request.startRow,
                grid.request.endRow
              ).toString()
              : "",
          },
        ];
        let totalrec = 0;
        this.stockConvrsionJobOrderService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {
            console.log("Planned Input",resolved);
            totalrec = resolved.data.length;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                itemperm.bl_inv_stock_conversion_job_order_link.hdr_guid ?
                this.stockConversionService.getByGuid(itemperm.bl_inv_stock_conversion_job_order_link.hdr_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ) : of(null),

                ).pipe(
                  map(([b_a]) => {
                    return b_a;
                  })
                )
            )
            );
            return iif(() => resolved.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              console.log("GG Input",data);

              grid.success({
                rowData: data[0].data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);

  }

  onReturn() {
    this.viewColFacade.updateInstance(1, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(1);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
