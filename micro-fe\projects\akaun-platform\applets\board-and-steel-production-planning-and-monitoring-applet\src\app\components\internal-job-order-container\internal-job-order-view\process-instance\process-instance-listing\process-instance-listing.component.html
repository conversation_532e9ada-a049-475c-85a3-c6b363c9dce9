<div class="view-col-table" fxLayout="column">

  <div fxLayout="row wrap" fxLayoutAlign="end">
    <mat-form-field class="example-full-width" appearance="outline">
      <mat-label>Group Name</mat-label>
      <mat-select placeholder="Group Name" formControlName="processGroup" (selectionChange)="onGroupChange($event)">
        <mat-option>
          <ngx-mat-select-search [placeholderLabel]="'Group Name'"
            [noEntriesFoundLabel]="'No matching records found'" [formControl]="processGroupOptionsFilterCtrl">
          </ngx-mat-select-search>
        </mat-option>
        <mat-option *ngFor="let eachOption of filteredProcessGroupOptions | async" [value]="eachOption.value">
          {{eachOption.viewValue}}</mat-option>
      </mat-select>
    </mat-form-field>
      <div class="delete-btn" *ngIf="editMode && isGroupAvailable">
    <button mat-raised-button color="primary" type="button" (click)="generateProcessInstance()">
      <span>Generate Process Instance</span>
    </button>
  </div>
    <div
      class="blg-accent"
      fxFlex="1 0 25"
      fxLayout="row"
      fxLayoutAlign="space-between center"
      *ngIf="editMode && isGroupAvailable"
    >
      <button
        ngClass.xs="blg-button-mobile"
        #navBtn
        class="blg-button-icon"
        mat-button
        matTooltip="Add Child Item"
        type="button"
        [disabled]="deactivateAdd$ | async"
        (click)="onNext()"
      >
        <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null"
          src="assets/images/add.png"
          alt="add"
          width="40px"
          height="40px"
        />
      </button>
      <!-- <mat-form-field class="example-full-width" appearance="outline" class="search-box">
        <mat-label>Search</mat-label>
        <button mat-icon-button matSuffix (click)="onSearch()">
          <mat-icon>search</mat-icon>
        </button >
        <input matInput placeholder="Search" [formControl]="search" type="text">
      </mat-form-field> -->
     
      <!-- <app-advanced-search class="mobile" fxFlex [id]="'internal-po'" [advSearchModel]="searchModel"
        (search)="onSearch($event)"> </app-advanced-search> -->
      <app-pagination
        fxFlex
        #pagination
        [agGridReference]="agGrid"
      ></app-pagination>
      <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
     
    </div>
   
  </div>
  <!-- <div class="delete-btn">
    <button mat-raised-button color="warn" type="button" (click)="onDelete()">
      <span>Delete</span>
    </button>
  </div> -->
  <div style="height: 100%"  *ngIf="editMode && isGroupAvailable">
    <ag-grid-angular
      #agGrid
      id="grid"
      style="height: 100%"
      class="ag-theme-balham"
      [getRowClass]="pagination.getRowClass"
      [columnDefs]="columnsDefs"
      [rowData]="[]"
      [cacheBlockSize]="pagination.rowPerPage"
      [suppressRowClickSelection]="false"
      [rowSelection]="'multiple'"
      [rowModelType]="'serverSide'"
      [serverSideStoreType]="'partial'"
      [animateRows]="true"
      [sideBar]="true"
      (rowClicked)="onRowClicked($event.data)"
      [defaultColDef]="defaultColDef"
      [paginationPageSize]="pagination.rowPerPage"
      [pagination]="true"
      [frameworkComponents]="frameworkComponents"
      [suppressPaginationPanel]="true"
      [suppressScrollOnNewData]="true"
      (gridReady)="onGridReady($event)"
    >
    </ag-grid-angular>
    <!-- <ag-grid-angular #agGrid
    id = "grid"
    style="height: 100%;"
    class="ag-theme-balham"
    [rowData]="[]"
    [getRowClass]="pagination.getRowClass"
    [columnDefs]="columnsDefs"
    [paginationPageSize]="pagination.rowPerPage"
    [cacheBlockSize]="pagination.rowPerPage"
    [pagination]="true"
    [floatingFilter]="true"
    [animateRows]="true"
    [defaultColDef]="defaultColDef"
    [sideBar]="true"
    [rowModelType]="'serverSide'"
    [serverSideStoreType]="'partial'"
    [frameworkComponents]="frameworkComponents"
    (rowClicked)="onRowClicked($event.data)"
    (gridReady)="onGridReady($event)">
    </ag-grid-angular> -->
  </div>
</div>
