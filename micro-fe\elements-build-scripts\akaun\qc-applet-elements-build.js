const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/qc-applet/runtime-es2015.js',
    './dist/qc-applet/polyfills-es2015.js',
    './dist/qc-applet/scripts.js',
    './dist/qc-applet/main-es2015.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/qc-applet');
  await concat(files, './elements/akaun-platform/applets/qc-applet/qc-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
