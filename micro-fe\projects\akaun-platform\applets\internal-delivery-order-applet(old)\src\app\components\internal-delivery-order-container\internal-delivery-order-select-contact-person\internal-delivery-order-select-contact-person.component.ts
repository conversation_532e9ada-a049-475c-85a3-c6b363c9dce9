import { Component, ChangeDetectionStrategy } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SubSink } from 'subsink2';
import { internalDeliveryOrderSearchModel } from '../../../models/advanced-search-models/internal-delivery-order.model';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { Store } from '@ngrx/store';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { map } from 'rxjs/operators';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { bl_fi_mst_entity_line_RowClass } from 'blg-akaun-ts-lib';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  deactivateReturn: boolean;
}

@Component({
  selector: 'app-internal-delivery-order-select-contact-person',
  templateUrl: './internal-delivery-order-select-contact-person.component.html',
  styleUrls: ['./internal-delivery-order-select-contact-person.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalDeliveryOrderSelectContactPersonComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Select Contact Person';
  protected readonly index = 9;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);

  prevIndex: number;
  protected prevLocalState: any;

  searchModel = internalDeliveryOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    // {headerName: '', field: '', minWidth: 50, checkboxSelection: true},
    {headerName: 'Name', field: 'name', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Position', field: 'contact_json.position', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Mobile No', field: 'contact_json.mobile_no'},
    {headerName: 'Office No', field: 'contact_json.office_no', cellStyle: () => ({'text-align': 'left'})},
  ];

  rowData$ = this.store.select(InternalDeliveryOrderSelectors.selectCustomer).pipe(
    map(a => a.bl_fi_mst_entity_line.filter(b => b.txn_type === 'CONTACT'))
  );

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected store: Store<InternalDeliveryOrderStates>,
    protected readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  // onNext() {
  //   this.viewColFacade.gotoFourOhFour();
    // this.viewColFacade.updateInstance(this.index, {
    //   ...this.localState, deactivateAdd: true, deactivateList: false});
    // this.viewColFacade.onNextAndReset(this.index, 3);
  // }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(line: bl_fi_mst_entity_line_RowClass) {
    if (line) {
      this.store.dispatch(InternalDeliveryOrderActions.selectContactPerson({line}));
    }
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false,
      deactivateCustomer: false,
      deactivateDeliveryAgent: false,
      deactivateShippingInfo: false,
      deactivateBillingInfo: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
