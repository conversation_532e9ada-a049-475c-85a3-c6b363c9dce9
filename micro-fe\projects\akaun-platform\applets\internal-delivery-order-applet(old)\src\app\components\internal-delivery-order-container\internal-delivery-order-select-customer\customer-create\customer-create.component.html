<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div> <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateAdd$ | async" (click)="onReturn()"> <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px"> </button>
      <span> Create  {{ entity }} </span>
    </div>
    <button mat-raised-button color="primary" [disabled]="!isValid" type="button" (click)="onSave()">CREATE</button>
  </div>
</mat-card-title>
<mat-tab-group [selectedIndex]="selectedIndex$ | async" [dynamicHeight]="true">
  <mat-tab label="Main">
    <app-customer-create-main [draft$]="draft$" (validDraft)="validDraft($event)" (updateDraft)="updateDraft($event)">
    </app-customer-create-main>
  </mat-tab>
</mat-tab-group>
