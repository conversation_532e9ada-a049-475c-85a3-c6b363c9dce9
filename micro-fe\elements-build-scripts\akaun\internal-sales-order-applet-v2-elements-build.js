const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/internal-sales-order-applet-v2/runtime.js',
    './dist/internal-sales-order-applet-v2/polyfills-es5.js',
    './dist/internal-sales-order-applet-v2/scripts.js',
    './dist/internal-sales-order-applet-v2/main.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/internal-sales-order-applet-v2');
  await concat(files, './elements/akaun-platform/applets/internal-sales-order-applet-v2/internal-sales-order-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
