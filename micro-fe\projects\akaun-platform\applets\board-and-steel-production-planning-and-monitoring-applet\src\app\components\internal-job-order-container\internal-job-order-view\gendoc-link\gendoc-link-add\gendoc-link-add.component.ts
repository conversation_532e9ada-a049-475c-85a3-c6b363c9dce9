import {
  ChangeDetectionStrategy,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from "@angular/core";
import { FormGroup } from "@angular/forms";
import { ComponentStore } from "@ngrx/component-store";
import { Store } from "@ngrx/store";
import {
  ApiResponseModel,
  CompanyService,
  FinancialItemContainerModel,
  FinancialItemService,
  InternalJobsheetService,
  InternalSalesOrderService,
  ItemPermissionContainerModel,
  MrpJobOrderGenDocLinkContainerModel,
  Pagination,
  SalesInvoiceService,
  ServiceIssueGenericDocLinkContainerModel,
  SubQueryService,
} from "blg-akaun-ts-lib";
import { forkJoin, iif, Observable, of } from "rxjs";
import { SubSink } from "subsink2";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { AppConfig } from "projects/shared-utilities/visa";

import { SearchQueryModel } from "projects/shared-utilities/models/query.model";

import { Pa<PERSON>ationComponent } from "projects/shared-utilities/utilities/pagination/pagination.component";
import { map, mergeMap, switchMap } from "rxjs/operators";
import { pageFiltering, pageSorting } from "projects/shared-utilities/listing.utils";
import { ToastrService } from "ngx-toastr";
import { InternalJobOrderStates } from "../../../../../state-controllers/internal-job-order-controller/store/states";
import { ViewColumnFacade } from "../../../../../facades/view-column.facade";
import { InternalJobOrderActions } from "../../../../../state-controllers/internal-job-order-controller/store/actions";
import { InternalJobOrderSelectors } from "../../../../../state-controllers/internal-job-order-controller/store/selectors";

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}
export class ItemSearchData {
  data: Array<{
    guid: string;
    name?: string;
    code?: string;
    status?: string;
    action?: string;
  }> = [];
}
@Component({
  selector: "app-gendoc-link-add",
  templateUrl: "./gendoc-link-add.component.html",
  styleUrls: ["./gendoc-link-add.component.scss"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore],
})
export class SalesOrderLinkAddComponent
  extends ViewColumnComponent
  implements OnInit, OnDestroy {
  bread = "Sales Order Link Add";
  private localState: LocalState;
  breadCrumbs: any[];
  ui: any;
  pagination = new Pagination();
  // extMap: Map<string, any> = new Map<string, any>();
  changePage = false;
  shippingAddress = false;
  billingAddress = false;
  deactivateReturn$;
  item_guid;

  protected readonly index = 14;
  prevIndex: number;
  private prevLocalState: any;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);

  apiVisa = AppConfig.apiVisa;

  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.localState.deactivateAdd
  );
  readonly deactivateList$ = this.componentStore.select(
    (state) => state.localState.deactivateList
  );

  paging = new Pagination();
  addSuccess = "Add";
  isClicked = "primary";
  getLabel$: Observable<any>;
  gridApi: any;
  toggleColumn$: Observable<boolean>;
  // searchModel = salesInvoiceSearchModel;

  columnsDefs;
  defaultColDef;
  serverSideStoreType;
  rowModelType;
  rowSelection;
  selectedItem: any;
  // users that are already added
  addedEntities = new Map<string, string>();
  itemCategory$: Observable<any[]>;
  selectedEntities = new Map<string, string>();
  protected subs = new SubSink();
  item$: Observable<any>;
  packageListing: any = [];
  financialItemContainer: FinancialItemContainerModel[] = [];

  aggFuncs;
  itemData$: Observable<FinancialItemContainerModel[]>;
  checkInv$: Observable<any>;
  checkUomSave$: Observable<boolean>;
  itemInv$: any;
  SQLGuids: string[] = null;
  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor(
    private toastr: ToastrService,
    private soService: InternalSalesOrderService,
    private companyService : CompanyService,
    protected readonly store: Store<InternalJobOrderStates>,
    private subQueryService: SubQueryService,
    private viewColFacade: ViewColumnFacade,
    private readonly componentStore: ComponentStore<{ localState: LocalState }>
  ) {
    super();
    this.rowSelection = "multiple";
    const customComparator = (valueA, valueB) => {
      if (valueA != null && "" !== valueA && valueB != null && "" !== valueB) {
        return valueA.toLowerCase().localeCompare(valueB.toLowerCase());
      }
    };
    this.columnsDefs = [
      {
        headerName: 'Sales Order No', field: 'doc_number', cellStyle: () => ({ 'text-align': 'left' }),  comparator: customComparator,
        filter: "agTextColumnFilter", suppressSizeToFit: true, checkboxSelection: true
      },
      {
        headerName: 'Item Code', field: 'item_code', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
       
      },
      {
        headerName: 'UOM', field: 'uom', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
       
      },
      {
        headerName: 'Qty', field: 'order_qty', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
       
      },
      {
        headerName: 'Txn Date', field: 'date_txn', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
       
      },
      // {
      //   headerName: 'Posting Status', field: 'posting_status', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
      //   valueFormatter: (params) => params.value ? params.value : 'DRAFT'
      // },
      // { headerName: 'Transaction Date', field: 'date_txn', cellStyle: () => ({ 'text-align': 'left' }),
      //   valueFormatter: params => moment(params.value).format('YYYY-MM-DD'), floatingFilter: true},
      // // { headerName: 'Branch', field: 'branch_name', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true },
      // // { headerName: 'Customer Name', field: 'customer_name', cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true },
      // { headerName: 'Updated Date', field: 'updated_date', cellStyle: () => ({ 'text-align': 'left' }), valueFormatter: params => moment(params.value).format('YYYY-MM-DD')},
      // { headerName: 'Created Date', field: 'created_date', cellStyle: () => ({'text-align': 'left'}), valueFormatter: params => moment(params.value).format('YYYY-MM-DD')},
    ].map((c) => ({
      ...c,
      width: 30,
      filterParams: {
        debounceMs: 1000,
      },
    }));
  
    this.defaultColDef = {
      // floatingFilterComponentParams: { suppressFilterButton: true },
      minWidth: 200,
      flex: 1,
      sortable: true,
      resizable: true,
      suppressCsvExport: true,
      cellStyle: { textAlign: "left" },

      floatingFilter: true,
      filter: "agTextColumnFilter",
      // suppressMenu: true,
      floatingFilterComponentParams: { suppressFilterButton: true },
      menuTabs: ["filterMenuTab"],
    };
    // this.aggFuncs = {
    //   sum: this.sumFunction,
    // };
    this.rowModelType = "serverSide";
    this.serverSideStoreType = "partial";
  }

  ngOnInit() {
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(
      (resolve) => (this.prevIndex = resolve)
    );
    this.store.select(InternalJobOrderSelectors.selectEntity).subscribe(data => {
      this.item_guid = data.bl_mrp_job_order_hdr.item_guid
    })
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false,
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  // sumFunction(params) {
  //   // var result = 0;
  //   const date = DateConvert(params);
  //   return date;
  // }

  onSave() {
    ///dispatch actiond to save selected rows

    this.store.select(InternalJobOrderSelectors.selectEntity).subscribe(data => {
      this.gridApi.getSelectedRows().forEach(item => {
        console.log("Sales order Items:--> ", item);
        let genDocLinkContainer: MrpJobOrderGenDocLinkContainerModel = new MrpJobOrderGenDocLinkContainerModel();
        // genDocLinkContainer.bl_t2t_fi_item_to_tenant_link.other_tenant_guid = host.app_tenant_hdr.guid;
        genDocLinkContainer.bl_mrp_job_order_generic_doc_link.generic_doc_hdr_guid = item.hdr_guid;
        genDocLinkContainer.bl_mrp_job_order_generic_doc_link.generic_doc_line_guid = item.line_guid;
        genDocLinkContainer.bl_mrp_job_order_generic_doc_link.job_order_guid = data.bl_mrp_job_order_hdr.guid;
        genDocLinkContainer.bl_mrp_job_order_generic_doc_link.txn_type = "PNS";
        console.log("Container",genDocLinkContainer);

        this.store.dispatch(InternalJobOrderActions.createGenDocLinkInit({
          genDocLink: genDocLinkContainer
        }));
      })
    })


    this.addSuccess = "Success";
    this.isClicked = "buttonSuccess";
    setTimeout(() => {
      this.addSuccess = "Add";
      this.isClicked = "primary";
      this.onReturn();
    }, 500);
  }


  pageFiltering(filterModel) {
    const noFilters = Object.keys(filterModel).length <= 0;
    if (noFilters) {
      return {
        by: () => true,
        isFiltering: noFilters,
      };
    }
    return {
      by: (viewModel) =>
        Object.keys(filterModel)
          .map((col) => {
            if (!viewModel[col]) {
              return false;
            }
            return typeof viewModel[col] === "number"
              ? +viewModel[col] === +filterModel[col].filter
              : viewModel[col]
                .toLowerCase()
                .includes(filterModel[col].filter.toLowerCase());
          })
          .reduce((p, c) => p && c),
      isFiltering: noFilters,
    };
  }

  pageSorting(sortModel) {
    return (data) => {
      if (sortModel.length <= 0) {
        return data;
      }
      let newData = data.map((o) => o);
      sortModel.forEach((model) => {
        const col = model.colId;
        newData =
          model.sort === "asc"
            ? newData.sort((p, c) => 0 - (p[col] > c[col] ? -1 : 1))
            : newData.sort((p, c) => 0 - (p[col] > c[col] ? 1 : -1));
      });
      return newData;
    };
  }

  // onGridReady(params) {
  //   this.gridApi = params.api;
  //   this.gridApi.closeToolPanel();


  //   // let guidsString = '';
  //   // guidsString = 'where guid not in (select host_fi_item_guid from bl_t2t_fi_item_to_tenant_link where status=\'ACTIVE\' )'

  //   // let query = 'Select guid as requiredGuid from bl_fi_mst_item_hdr ' + guidsString;

  //   // var query$ = this.subQueryService
  //   //   .post({ 'subquery': query, 'table': 'bl_fi_mst_item_hdr' }, this.apiVisa)
  //   //   .pipe(
  //   //     switchMap(res => of(res))
  //   //   );
  //   // query$
  //   //   .subscribe(res => {
     
  //   //   });

  //     this.retrieveData([
  //       // this.setCriteria("guids", res.data),
  //       this.setCriteria("calcTotalRecords", 'true')
  //     ])

  //   // CRUD will triggered this because of requiresUpdate state changed in reducer
  //   // this.subSink.sink = this.store.select(AttributeSetSelectors.selectRequiresUpdate)
  //   //   .subscribe((requiresUpdate) => {
  //   //     if (requiresUpdate)
  //   //       this.gridApi.purgeServerSideCache();
  //   //   })

  // }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        // this.store.dispatch(PurchaseOrderActions.loadPurchaseOrderInit({request: grid.request}));
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;
        this.pagination.conditionalCriteria = [
          { columnName: 'line_txn_type', operator: '=', value: 'PNS' },
          { columnName: 'item_guid', operator: '=', value: this.item_guid },
          { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
          { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          { columnName: 'order', operator: '=', value: 'DESC' },
          { columnName: 'line_guids', operator: '=',
            value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          }
        ];
        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);
        this.subs.sink = this.soService.getLinesByCriteria(this.pagination, apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<any>[] = [];
            b.data.forEach(doc => source.push(
              this.soService.getByGuid(doc.bl_fi_generic_doc_hdr.guid.toString(), apiVisa).pipe(
                map((b_a) => {
                  let aggContra = 0; // aggregate Contra
                  // b_a.data.bl_fi_generic_doc_link.forEach(link => {
                  //   if (link.guid_doc_1_hdr === doc.bl_fi_generic_doc_hdr.guid &&
                  //     link.guid_doc_1_line === doc.bl_fi_generic_doc_line[0].guid && link.txn_type === 'ISI_ICRMA') {
                  //       let contra = Number(link.quantity_signum) * Number(link.quantity_contra);
                  //       aggContra += contra;
                  //   }
                  // });
                  let data;
                  doc.bl_fi_generic_doc_line.forEach(line => {
                    let companyName; 
                    this.companyService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_comp.toString(),this.apiVisa).subscribe(data => {
                      companyName = data.data.bl_fi_mst_comp.name;
                    })
                    data = {
                      hdr_guid: doc.bl_fi_generic_doc_hdr.guid,
                      server_doc_type_hdr: doc.bl_fi_generic_doc_hdr.server_doc_type,
                      line_guid: line.guid,
                      server_doc_type_line: line.server_doc_type,
                      doc_number: doc.bl_fi_generic_doc_hdr.server_doc_1,
                      item_guid: line.item_guid,
                      item_code: line.item_code,
                      item_name: line.item_name,
                      item_type: line.item_sub_type,
                      order_qty: line.quantity_base,
                      open_qty: Number(line.quantity_base) + aggContra,
                      unit_price: null,
                      created_date: line.created_date,
                      date_txn: line.date_txn,
                      posting_status: doc.bl_fi_generic_doc_hdr.posting_status,
                      // uom: 'UNIT',
                      uom: line.uom,
                      serialNo : (<any>line).serial_no?.serialNumbers?.toString(),
                      expiryDate : doc.bl_fi_generic_doc_hdr.due_date,
                      company : doc.bl_fi_generic_doc_hdr.guid_comp,
                      branch : doc.bl_fi_generic_doc_hdr.guid_branch,
                      location : doc.bl_fi_generic_doc_hdr.guid_store,
                      customer: doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid,
                      salesInvoiceDate : line.date_txn,
                      // status: 'OPEN',
                      status: null,
                    } 

                  })
                  console.log("Data",data);
                  return data;
                })
              )
            ));
            return iif(() => b.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                console.log("bbbbb",b);
                b.data = <any>b_inner;
                return b
              })),
              of(b)
            );
          })
        ).subscribe( resolved => {
          // this.store.dispatch(PurchaseOrderActions.loadPurchaseOrderSuccess({ totalRecords: resolved.totalRecords }));
          const data = sortOn(resolved.data).filter(entity => filter.by(entity));
          const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : resolved.totalRecords) : data.length;
          grid.success({
            rowData: data,
            rowCount: totalRecords
          });
          // this.gridApi.forEachNode(a => {
          //   if (a.data.item_guid === this.localState.selectedItem) {
          //     a.setSelected(true);
          //   }
          // });
        }, err => {
          console.log(err);
          // this.store.dispatch(PurchaseOrderActions.loadPurchaseOrderFailed({ error: err.message }));
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    // this.subs.sink = this.store.select(PurchaseOrderSelectors.selectAgGrid).subscribe(resolved => {
    //   if (resolved) {
    //     this.gridApi.refreshServerSideStore({ purge: true });
    //     this.store.dispatch(PurchaseOrderActions.resetAgGrid());
    //   }
    // });
  }

  // searchQuery(query: string, table: string) {
  //   let flag = true;

  //   if (flag) {
  //     flag = false;
  //     var query$ = this.subQueryService
  //       .post({ 'subquery': query, 'table': table }, this.apiVisa)
  //       .pipe(
  //         switchMap(res => of(res))
  //       );
  //     query$
  //       //.filter((res: ApiResponseModel<any>) => res.data.length > 0)
  //       .subscribe(res => {
  //         this.retrieveData([
  //           this.setCriteria("guids", res.data),
  //           this.setCriteria("calcTotalRecords", 'true')
  //         ])
  //       });
  //     query$
  //       //.filter((res: ApiResponseModel<any>) => res.data.length === 0)
  //       .subscribe(res => {
  //         this.clear()
  //       });
  //   }
  // }

  // retrieveData(criteria) {
  //   if (criteria) {
  //     const datasource = {
  //       getRows: this.getRowsFactory(criteria)
  //     };
  //     this.gridApi.setServerSideDatasource(datasource);
  //   }
  // }

  // getRowsFactory(criteria) {
  //   let offset = 0;
  //   let limit = this.paginationComp.rowPerPage;
  //   let callbackTotal = 0;
  //   let callback = false;
  //   let temp_criteria = null;

  //   return grid => {
  //     var filter = this.pageFiltering(grid.request.filterModel);
  //     var sortOn = this.pageSorting(grid.request.sortModel);

  //     if (!Object.keys(grid.request.filterModel).length) {
  //       offset = grid.request.startRow;
  //       limit = grid.request.endRow - offset;

  //       if (criteria.find(x => x.columnName === 'guids')?.value.length > 100) {
  //         callbackTotal = criteria.find(x => x.columnName === 'guids')?.value.length;
  //         callback = true;
  //         let guids = criteria.find(x => x.columnName === 'guids').value.slice(offset, limit);
  //         temp_criteria = [
  //           this.setCriteria("guids", guids),
  //           this.setCriteria("calcTotalRecords", 'true')
  //         ];
  //       }
  //     }

  //     // this.store.dispatch(ServiceNoteActions.loadItemsInit({ request: grid.request }));


  //     this.subs.sink = this.jsService.getByCriteria(
  //       new Pagination(
  //         offset,
  //         limit,
  //         temp_criteria ? temp_criteria : criteria,
  //         [
  //           // { columnName: 'orderBy', value: 'date_updated' },
  //           { columnName: 'order', value: 'DESC' },
  //         ]
  //       ),
  //       this.apiVisa).subscribe(resolved => {

  //         const data = sortOn(resolved.data.filter(o => filter.by(o)));
  //         let totalRecords;
  //         if (!callback) {
  //           totalRecords = filter.isFiltering ? resolved.totalRecords : data.length;
  //         }
  //         else {
  //           totalRecords = callbackTotal;
  //         }

  //         if (this.paginationComp.currentPage > this.paginationComp.totalPage.value)
  //           this.paginationComp.firstPage();

  //         grid.successCallback(data, totalRecords);
  //         // this.store.dispatch(ServiceNoteActions.loadItemSuccess({ totalRecords }));
  //       }, (err) => {
  //         grid.failCallback();
  //         // this.store.dispatch(ServiceNoteActions.loadItemsFailed({ error: err.message }));
  //       });
  //   }
  // }

  // onSearch(e: SearchQueryModel) {
  //   // if (!e.isEmpty) {
  //   //   const sql = {
  //   //     subquery: e.queryString,
  //   //     table: e.table,
  //   //   };
  //   //   this.subs.sink = this.sqlService.post(sql, this.apiVisa).subscribe({
  //   //     next: (resolve) => {
  //   //       this.SQLGuids = resolve.data;
  //   //       this.paginationComp.firstPage();
  //   //       this.gridApi.refreshServerSideStore();
  //   //     },
  //   //   });
  //   // } else {
  //   //   this.SQLGuids = null;
  //   //   this.paginationComp.firstPage();
  //   //   this.gridApi.refreshServerSideStore();
  //   // }
  //   !e.isEmpty ? this.searchQuery(e.queryString, e.table) : this.retrieveData([this.setCriteria('calcTotalRecords', 'true')]);
  // }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({
        next: resolve => {
          this.SQLGuids = resolve.data;
          if(this.SQLGuids.length!==0 || this.SQLGuids.length<=50){
            // this.gridApi.refreshServerSideStore();
            this.pagination.conditionalCriteria = [
              {
                columnName: 'guids', operator: '=',
                value: this.SQLGuids.toString()
              }
            ];
          
          }else
          {
            this.toastr.error("Result Set Too Large. Please Refine Search", "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 2000,
            });
          }
        }
      });
    } else {
      this.SQLGuids = null;
      // this.store.dispatch(JobSheetActions.loadJobSheetInit({pagination: new Pagination(0,50)}))
      // this.gridApi.refreshServerSideStore();
    }
  }


  setCriteria(columnName, value) {
    return { columnName, operator: '=', value }
  }

  clear() {
    const dataSource = {
      getRows(params: any) {
        params.successCallback([], 0);
      },
    };
    this.gridApi.setServerSideDatasource(dataSource);
  }
  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }
  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
