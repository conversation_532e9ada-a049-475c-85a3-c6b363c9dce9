import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderCreateMainComponent } from './internal-delivery-order-create-main.component';

describe('InternalDeliveryOrderCreateMainComponent', () => {
  let component: InternalDeliveryOrderCreateMainComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderCreateMainComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderCreateMainComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderCreateMainComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
