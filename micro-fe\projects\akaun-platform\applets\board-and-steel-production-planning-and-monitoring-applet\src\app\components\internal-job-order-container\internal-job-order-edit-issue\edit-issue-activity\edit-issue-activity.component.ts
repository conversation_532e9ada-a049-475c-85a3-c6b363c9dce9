import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { bl_fi_generic_doc_ext_RowClass } from 'blg-akaun-ts-lib';
import * as moment from 'moment';
import { internalJobOrderSearchModel } from '../../../../models/advanced-search-models/internal-job-order.model';

@Component({
  selector: 'app-edit-issue-activity',
  templateUrl: './edit-issue-activity.component.html',
  styleUrls: ['./edit-issue-activity.component.css']
})
export class EditIssueActivityComponent implements OnInit {

  @Input() localState: any;
  @Input() rowData: any[] = [];

  @Output() issue = new EventEmitter();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Date', field: 'project', type: 'rightAligned'},
    {headerName: 'User', field: 'issueNumber', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Activities', field: 'issueSummary', cellStyle: () => ({'text-align': 'left'})},
  ];

  searchModel = internalJobOrderSearchModel;

  constructor() { }

  ngOnInit() {
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

  onRowClicked(e) {
    this.issue.emit(e);
  }

}