import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { InventoryItemService, MrpProcessTemplateBOMService } from 'blg-akaun-ts-lib';
import { InternalJobOrderSelectors } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';


interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-details',
  templateUrl: './details.component.html',
  styleUrls: ['./details.component.scss']
})

export class DetailsComponent extends ViewColumnComponent {

  @Input() editMode: boolean;
  @Input() selectedIndex: any;
  @Output() updateSalesInvoiceItemDetails = new EventEmitter();
  public form: FormGroup;

  constructor( 
    private mrpProcessTemplateBOMService: MrpProcessTemplateBOMService,
    private inventoryItemService: InventoryItemService,
    private readonly componentStore: ComponentStore<LocalState>,
    private readonly store: Store<InternalJobOrderStates>,

 ) {
    super();
  }

  ngOnInit() {
    this.form = new FormGroup({
      itemName : new FormControl(),
      itemCode : new FormControl(),
      no_of_process: new FormControl,
    });

    

    this.store.select(InternalJobOrderSelectors.selectProcessInstance).subscribe(data => {
      console.log("Process instance main data",data);
      this.form.patchValue({
        itemName : data.output_item_name,
        itemCode : data.output_item_code,
      })
    })
  }

}