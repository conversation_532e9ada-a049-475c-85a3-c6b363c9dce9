<form [formGroup]="form">
  <div fxLayout="column" class="view-col-forms">
    <div fxLayout="row wrap" fxFlexAlign="center" class="view-col-forms">

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
      <mat-label>Job Order No (Tracking ID)</mat-label>
      <input matInput readonly placeholder="Job Order No (Tracking ID)"
      [formControl]="form.controls['jobOrderNo']"
      type="text"
      readonly>
    </mat-form-field>

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
      <mat-label>Job Order Date</mat-label>
      <input matInput placeholder="Job Order Date"
      [formControl]="form.controls['jobOrderDate']"
      type="text"
      readonly>
    </mat-form-field>

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
      <mat-label>Process Type</mat-label>
      <input matInput placeholder="Process Type"
      [formControl]="form.controls['processType']"
      type="text"
      readonly>
    </mat-form-field>

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" appearance="outline">
      <mat-label>Process Template Name</mat-label>
      <input matInput placeholder="Process Template Name"
      [formControl]="form.controls['processTemplateName']"
      type="text"
      readonly
      >
    </mat-form-field>

    <mat-form-field  class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label>Machine Code</mat-label>
      <input matInput placeholder="Machine Code"
      [formControl]="form.controls['machineCode']"
      type="text"
      readonly
      >
    </mat-form-field>

    <mat-form-field class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label>Issued By</mat-label>
      <input matInput placeholder="Issued By"
      [formControl]="form.controls['issuedBy']"
      type="text"
      readonly
      >
    </mat-form-field>

    <mat-form-field class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label>Priority</mat-label>
      <input matInput placeholder="Priority"
      [formControl]="form.controls['priority']"
      type="text"
      (change)="updateMain.emit(form.value)"
      >
    </mat-form-field>

    <mat-form-field class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label> QC Input Status</mat-label>
      <input matInput placeholder=" QC Input Status"
      [formControl]="form.controls['qcOutputStatus']"
      type="text"
      readonly
      >
    </mat-form-field>

    <mat-form-field fxFlex.gt-sm="50" fxFlex="100" class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label>Process Status</mat-label>
      <mat-select matNativeControl formControlName="processStatus">
        <mat-option *ngFor="let status1 of PROCESS_STATUS" [value]="status1">
          {{ status1 }}
        </mat-option>
      </mat-select>
    </mat-form-field>


    <mat-form-field class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label>Remarks</mat-label>
      <textarea #remarks matInput placeholder="Remarks" formControlName="remarks" rows=2></textarea>
      <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
    </mat-form-field>

    <!-- <mat-form-field class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100" appearance="outline">
      <mat-label>Process Status</mat-label>
      <mat-select matNativeControl formControlName="processStatus">
        <mat-option *ngFor="let pStatus of PROCESS_STATUS" [value]="pStatus">
          {{ pStatus }}
        </mat-option>
      </mat-select>
    </mat-form-field> -->


  </div>
</div>
</form>
