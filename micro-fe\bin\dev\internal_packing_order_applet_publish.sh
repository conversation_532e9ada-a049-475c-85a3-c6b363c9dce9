#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=dev --project=internal-packing-order-applet --output-hashing none
node elements-build-scripts/akaun/internal-packing-order-applet-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://development-akaun-applets/bigledger/tonn-cable/internal-packing-order-applet/dev s3://development-akaun-applets/bigledger/tonn-cable/internal-packing-order-applet/dev/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile development-bigledger --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/internal-packing-order-applet/ s3://development-akaun-applets/bigledger/tonn-cable/internal-packing-order-applet/dev --profile development-bigledger --acl public-read --recursive
