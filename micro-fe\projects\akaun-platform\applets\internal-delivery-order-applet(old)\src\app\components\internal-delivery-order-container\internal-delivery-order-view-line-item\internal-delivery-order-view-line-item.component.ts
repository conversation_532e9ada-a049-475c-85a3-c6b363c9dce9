import { Component, ChangeDetectionStrategy } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { Store } from '@ngrx/store';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';

interface LocalState {
  deactivateChange: boolean;
  deactivateReturn: boolean;
}

@Component({
  selector: 'app-internal-delivery-order-view-line-item',
  templateUrl: './internal-delivery-order-view-line-item.component.html',
  styleUrls: ['./internal-delivery-order-view-line-item.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalDeliveryOrderViewLineItemComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Internal Sales Order View Line';
  protected index = 2;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateChange$ = this.componentStore.select(state => state.deactivateChange);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);

  prevIndex: number;
  protected prevLocalState: any;

  form: FormGroup;
  controls = [
    {label: 'Item Code', formControl: 'itemCode', type: 'text', readonly: true},
    {label: 'Item Name', formControl: 'itemName', type: 'text', readonly: true},
    // {label: 'Batch No', formControl: 'batchNo', type: 'text', readonly: true},
    {label: 'UOM', formControl: 'uom', type: 'text', readonly: true},
    {label: 'Quantity', formControl: 'quantity', type: 'text', readonly: true},
    // {label: 'Unit Price (RM)', formControl: 'unitPrice', type: 'number', readonly: true},
    // {label: 'STD Amount', formControl: 'std', type: 'text', readonly: true  },
    // {label: 'Discount amt', formControl: 'discount', type: 'text', readonly: true},
    // {label: 'Net Amount (RM)', formControl: 'net', type: 'date', readonly: true},
    // {label: 'SST/GST/VAT Code', formControl: 'sstCode', type: 'date', readonly: true},
    // {label: 'SST/GST/VAT', formControl: 'sst', type: 'date', readonly: true},
    // {label: 'WHT', formControl: 'wht', type: 'date', readonly: true},
    // {label: 'Txn Amount', formControl: 'txn', type: 'date', readonly: true},
    // {label: 'Packing Date', formControl: 'packingDate', type: 'date', readonly: true},
    // {label: 'Delivery Date', formControl: 'deliveryDate', type: 'date', readonly: true},
    // {label: 'Remarks', formControl: 'remarks', type: 'text-area', readonly: true},
  ];

  constructor(
    private viewColFacade: ViewColumnFacade,
    private readonly store: Store<InternalDeliveryOrderStates>,
    private readonly componentStore: ComponentStore<LocalState>
  ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.form = new FormGroup({
      itemCode: new FormControl(),
      itemName: new FormControl(),
      // batchNo: new FormControl(),
      uom: new FormControl(),
      quantity: new FormControl(),
      // unitPrice: new FormControl(),
      // std: new FormControl(''),
      // discount: new FormControl('', Validators.required),
      // net: new FormControl('', Validators.required),
      // sstCode: new FormControl('', Validators.required),
      // sst: new FormControl('', Validators.required),
      // wht: new FormControl('', Validators.required),
      // txn: new FormControl('', Validators.required),
      // packingDate: new FormControl('', Validators.required),
      // deliveryDate: new FormControl('', Validators.required),
      // remarks: new FormControl()
    });
    this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.selectLineItem).subscribe({next: resolve => {
      console.log(resolve);
      this.form.patchValue({
        itemCode: resolve.item_code,
        itemName: resolve.item_name,
        uom: (<any>resolve.item_property_json).uom,
        quantity: resolve.quantity_base
      });
    }});
  }

  // onClick() {
  //   if (!this.localState.deactivateChange) {
  //     this.viewColFacade.updateInstance(this.index, {
  //       ...this.localState, deactivateReturn: true, deactivateChange: true});
  //     this.viewColFacade.onNext(5);
  //   }
  // }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateList: false,
      deactivateReturn: false,
      deactivateLineItem: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
