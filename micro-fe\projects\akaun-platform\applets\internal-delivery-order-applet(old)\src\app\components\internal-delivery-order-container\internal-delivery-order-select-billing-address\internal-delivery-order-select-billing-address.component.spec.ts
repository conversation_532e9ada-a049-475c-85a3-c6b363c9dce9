import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderSelectBillingAddressComponent } from './internal-delivery-order-select-billing-address.component';

describe('InternalDeliveryOrderSelectBillingAddressComponent', () => {
  let component: InternalDeliveryOrderSelectBillingAddressComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderSelectBillingAddressComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderSelectBillingAddressComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderSelectBillingAddressComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
