import { Action, createReducer, on } from "@ngrx/store";
import { SettlementActions } from "../actions";
import { initState, settlementAdapter, SettlementState } from "../states/settlement.states";

export const settlementReducers = createReducer(
    initState,
    on(SettlementActions.addSettlementSuccess, (state, action) => settlementAdapter.addOne({
        guid: state.ids.length,
        ...action.settlement
    }, state)),
    on(SettlementActions.deleteSettlement, (state, action) => settlementAdapter.removeOne(action.guid, state)),
    on(SettlementActions.editSettlement, (state, action) => settlementAdapter.upsertOne(action.settlement, state)),
    on(SettlementActions.resetSettlement, (state, action) => settlementAdapter.removeAll(state)),
)

export function reducers(state: SettlementState | undefined, action: Action) {
    return settlementReducers(state, action);
}