import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { Store } from '@ngrx/store';
import { ComponentStore } from '@ngrx/component-store';
import { CouponSerialNumberContainerModel, FinancialItemService, MrpProcessTemplateContainerModel, MrpProcessTemplateService, MrpProdsysContainerModel, MrpProdsysService, Pagination, SubQueryService } from 'blg-akaun-ts-lib';
import { forkJoin, iif, Observable, of, zip } from 'rxjs';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { catchError, map, mergeMap } from 'rxjs/operators';
import * as moment from 'moment';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { processTemplateSearchModel } from '../../../models/advanced-search-models/process-template.model';
import { ActivatedRoute } from '@angular/router';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}





interface LocalState {
  deactivateReturn: boolean;
  selectedRowGuid: string;
}
@Component({
  selector: 'app-process-select-template',
  templateUrl: './process-select-template.component.html',
  styleUrls: ['./process-select-template.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class ProcessSelectTemplateComponent extends ViewColumnComponent {

  protected compName = 'Select Template';
  protected readonly index = 7;
  protected localState: LocalState;

  prevIndex: number;
  protected prevLocalState: any;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  deactivateReturn$: Observable<boolean>;
  searchModel = processTemplateSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };


  gridApi;
  SQLGuids: string[] = null;
  pagination = new Pagination();

  columnsDefs = [
    {headerName: 'Process Template Code', field: 'bl_mrp_process_template_hdr.code', comparator: (valueA, valueB) =>
    valueA.toLowerCase().localeCompare(valueB.toLowerCase()), width: 90, suppressSizeToFit: true,
    cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Process Type', field: 'process_code', comparator: (valueA, valueB) =>
    valueA.toLowerCase().localeCompare(valueB.toLowerCase()), width: 110, suppressSizeToFit: true,
    cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Machine Code', field: 'machine_code', suppressSizeToFit: false, cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Output Item Code', field: 'item_code', suppressSizeToFit: true, cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Status', field: 'bl_mrp_process_template_hdr.status', suppressSizeToFit: true, cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Modified Date', field: 'bl_mrp_process_template_hdr.updated_date', type: 'rightAligned',
    valueFormatter: params => moment(params.value).format('YYYY-MM-DD HH:MM:SS')},
    {headerName: 'Created Date', field: 'bl_mrp_process_template_hdr.created_date', type: 'rightAligned',
    valueFormatter: params => moment(params.value).format('YYYY-MM-DD HH:MM:SS')}
  ];

  rowData = [];
  process: string;
  apiVisa = AppConfig.apiVisa;

  private subs = new SubSink();
  
  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  constructor( private readonly draftStore: Store<DraftStates>,
    private viewColFacade: ViewColumnFacade,
    private processTemplateService: MrpProcessTemplateService,
    private prodsysService: MrpProdsysService,
    private financialItemService: FinancialItemService,
    private readonly componentStore: ComponentStore<LocalState>,
    private subQueryService: SubQueryService,
    private route: ActivatedRoute

    ) {
    super();
  }

  

  ngOnInit() {

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.subs.sink = this.prodsysService.getByGuid(this.route.snapshot.paramMap.get('process'),this.apiVisa)
    .subscribe((process: any) => {
      console.log(process)
      this.process = process.data.bl_mrp_prodsys_hdr.code;
    });
  }

  onNext() {
    this.viewColFacade.updateInstance<LocalState>(this.index,
      {
        ...this.localState,
        deactivateAdd: true,
        deactivateList: false
      });
    this.viewColFacade.onNextAndReset(this.index, 1);
  }


  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.setGridData();

    // this.subs.sink = this.processTemplateStore.select(ProcessTemplateSelectors.selectAgGrid).subscribe( a => {
    //   if (a) {
    //     this.gridApi.refreshServerSideStore();
    //     this.processTemplateStore.dispatch(ProcessTemplateActions.updateAgGridDone());
    //   }
    // });
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        // this.processTemplateStore.dispatch(ProcessTemplateActions.loadProcessTemplateInit({ request: grid.request }));

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.conditionalCriteria = [
          { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          { columnName: 'order', operator: '=', value: 'DESC' },
          // { columnName: 'guids', operator: '=',
          //   value: this.SQLGuids ? this.SQLGuids.slice(grid.request.startRow, grid.request.endRow).toString() : ''
          // // },
          { columnName: 'process_guid', operator: '=', value: this.route.snapshot.paramMap.get('process')}
        ];
        //Change to use from process_template_table
        this.subs.sink = this.processTemplateService.getByCriteria(this.pagination, apiVisa).pipe(
          mergeMap(b => {
            const source: Observable<MrpProcessTemplateContainerModel>[] = [];
            b.data.forEach( link => 
              source.push(
              zip(
                this.prodsysService.getByGuid(link.bl_mrp_process_template_hdr.machine_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                this.financialItemService.getByGuid(link.bl_mrp_process_template_hdr.output_fi_item_hdr_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                this.prodsysService.getByGuid(link.bl_mrp_process_template_hdr.process_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),
                ).pipe(
                  map(([b_a,b_b,b_c]) => {
                    link = Object.assign({
                      machine_code: b_a.error ? b_a.error.code : b_a.data.bl_mrp_prodsys_hdr.code,
                      item_code: b_b.error ? b_b.error.code : b_b.data.bl_fi_mst_item_hdr.code,
                      status: b_a.error? b_a.error.code : b_a.data.bl_mrp_prodsys_hdr.status,
                      process_code: b_c.error? b_c.error.code: b_c.data.bl_mrp_prodsys_hdr.code
                    }, link);
                    return link;
                  })
                )
              )
            );
            return iif(() => b.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                b.data = b_inner;
                return b
              })),
              of(b)
            );
          })
        ).subscribe(resolved => {
          console.log(resolved)
          // this.processTemplateStore.dispatch(ProcessTemplateActions.loadProcessTemplateSuccess({ totalRecords: resolved.totalRecords }));
          const data = sortOn(resolved.data).filter(entity => filter.by(entity));
          const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : resolved.data.length) : data.length;
          grid.success({
            rowData: data,
            rowCount: totalRecords
          });
        }, err => {
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);

  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(entity: MrpProcessTemplateContainerModel) {
    // this.processTemplateStore.dispatch(ProcessTemplateActions.selectProcessTemplateForEdit({processTemplate: entity}));  
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateList: false,
    });
    this.viewColFacade.onNextAndReset(this.index, 2)
  }
  
  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({ next: resolve => {
        this.SQLGuids = resolve.data;
        this.paginationComp.firstPage();
        this.gridApi.refreshServerSideStore();
      }});
    } else {
      this.SQLGuids = null;
      this.paginationComp.firstPage();
      this.gridApi.refreshServerSideStore();
    }
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAddOutput: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this. prevIndex);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}

