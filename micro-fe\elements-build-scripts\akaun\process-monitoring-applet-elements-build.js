const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/process-monitoring-applet/runtime.js',
    './dist/process-monitoring-applet/polyfills-es5.js',
    './dist/process-monitoring-applet/scripts.js',
    './dist/process-monitoring-applet/main.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/process-monitoring-applet');
  await concat(files, './elements/akaun-platform/applets/process-monitoring-applet/process-monitoring-appletements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
