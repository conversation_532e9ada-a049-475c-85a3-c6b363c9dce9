// export interface JobOrderNoMain {
//   jobOrderNo: string;
//   jobOrderDate: string;
//   itemCode: string;
//   itemName: string;
//   categoryGroup: string;
//   category: string;
//   uom: string;
//   quantityContainer: string;
//   adHocQty: string;
//   quantityMeasure: string;
//   status: string;
//   estimatedPackingDate: string;
//   description: string;
//   completionDate: string;
//   qcOutputStatus: string;
//   remarks: string;
// }

export interface JobOrderNoDepartment {
  segment: string;
  dimension: string;
  profitCenter: string;
  project: string;
}

export interface JobOrderMain {
  jobOrderNo: string;
  jobOrderDate: string;
  itemCode: string;
  itemName: string;
  itemGuid:string;
  categoryGroup: string;
  category: string;
  uom: string;
  containerQty: number;
  adHocQty: number;
  totalContainerMeasure: number;
  process_status: string;
  estimatedPackingDate: string;
  description: string;
  completionDate: string;
  qcOutputStatus: string;
  remarks: string;
}

interface Option {
  value: string;
  viewValue: string;
}

export class ProcessTypeOption implements Option {
  value: string;
  viewValue: string;
}
export class ProcessGroupOption implements Option {
  value: string;
  viewValue: string;
}

export class MachineOption implements Option {
  value: string;
  viewValue: string;
}

export class ProcessTemplateOption implements Option {
  value: string;
  viewValue: string;
}


