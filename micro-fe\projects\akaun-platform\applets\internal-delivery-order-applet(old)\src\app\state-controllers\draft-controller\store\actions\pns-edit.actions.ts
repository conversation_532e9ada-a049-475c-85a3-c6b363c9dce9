import { createAction, props } from "@ngrx/store";
import { bl_fi_generic_doc_line_RowClass } from "blg-akaun-ts-lib";

export const addPNS = createAction('[Draft: PNS Edit] Add PNS', props<{pns: bl_fi_generic_doc_line_RowClass}>());
export const editPNS = createAction('[Draft: PNS Edit] Edit PNS', props<{pns: bl_fi_generic_doc_line_RowClass}>());
export const deletePNS = createAction('[Draft: PNS Edit] Delete PNS', props<{guid: string}>());
export const resetPNSInit = createAction('[Draft: PNS Edit] Reset Init');
export const resetPNSSuccess = createAction('[Draft: PNS Edit] Reset Success', props<{pns: bl_fi_generic_doc_line_RowClass[]}>());

// export const resetHDRInit = createAction('[Draft: HDR Edit] Reset Init');
// export const resetHDRSuccess = createAction('[Draft: HDR Edit] Reset Success', props<{hdr: bl_fi_generic_doc_hdr_RowClass}>());
