import { InternalDeliveryOrderActions } from '../actions';
import { Action, createReducer, on } from '@ngrx/store';
import { initState } from '../states/internal-delivery-order.states';
import { InternalDeliveryOrderState } from '../states/internal-delivery-order.states';
import { bl_fi_generic_doc_ext_RowClass, GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { Statement } from '@angular/compiler';

export const internalDeliveryOrderFeatureKey = 'deliveryOrder';

export const internalDeliveryOrderReducer = createReducer(
  initState,
  // on(InternalDeliveryOrderActions.loadDeliveryOrderSuccess, (state, action) =>
  //   ({ ...state, totalRecords: action.totalRecords })),
  on(InternalDeliveryOrderActions.loadDeliveryOrderFailed, (state, action) =>
    ({ ...state, errorLog: [...state.errorLog, { timeStamp: new Date(), log: action.error }] })),
  on(InternalDeliveryOrderActions.selectGuid, (state, action) =>
    ({ ...state, selectedGuid: action.guid })),
  on(InternalDeliveryOrderActions.selectEntityInit, (state, action) =>
    ({ ...state, selectedEntity: action.entity, draftEdit: { ...action.entity } })),

  on(InternalDeliveryOrderActions.selectEntity, (state, action) =>
    ({ ...state, selectedCustomer: action.entity.entity })),

  on(InternalDeliveryOrderActions.selectEntityEdit, (state, action) =>
    ({ ...state, selectedCustomer: action.entity.entity })),
  // on(InternalDeliveryOrderActions.selectEntity, (state, action) =>
  // ({
  //   ...state, draft: {
  //     ...state.draft,
  //     bl_fi_generic_doc_hdr: {
  //       ...state.draft.bl_fi_generic_doc_hdr,
  //       doc_entity_hdr_guid: action.entity.bl_fi_mst_entity_hdr.guid,
  //       doc_entity_hdr_json: <any>{
  //         ...state.draft.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
  //         // customerType: action.entity.bl_fi_mst_entity_hdr.txn_type,
  //         // customerCode: action.entity.bl_fi_mst_entity_hdr.customer_code,
  //         entityName: action.entity.bl_fi_mst_entity_hdr.name,
  //         entityId: action.entityId,
  //         // entityId: action.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'ID_NO')?.value_string,
  //         email: action.entity.bl_fi_mst_entity_hdr.email,
  //         phoneNumber: action.entity.bl_fi_mst_entity_hdr.phone,
  //         generalLine: '',
  //         directLine: '',
  //         mobilePhone: action.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'MOBILE_PHONE')?.value_string,
  //         homeNumber: action.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'HOME_PHONE')?.value_string,
  //       },
  //       // customer_code: action.entity.bl_fi_mst_entity_hdr.customer_code,
  //       // supplier_code: action.entity.bl_fi_mst_entity_hdr.supplier_code,
  //       // merchant_code: action.entity.bl_fi_mst_entity_hdr.merchant_code,
  //       // employee_code: action.entity.bl_fi_mst_entity_hdr.employee_code,
  //     }
  //   }, 
  //   selectedCustomer: action.entity,
  // })),
  on(InternalDeliveryOrderActions.selectShippingAddress, (state, action) =>
  ({
    ...state, draft: {
      ...state.draft,
      bl_fi_generic_doc_hdr: {
        ...state.draft.bl_fi_generic_doc_hdr,
        doc_entity_hdr_json: <any>{
          ...state.draft.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
          shippingAddress: { ...action.shipping_address }
        }
      }
    }, selectedShippingAddress: action.shipping_address
  })),
  on(InternalDeliveryOrderActions.selectBillingAddress, (state, action) =>
  ({
    ...state, draft: {
      ...state.draft,
      bl_fi_generic_doc_hdr: {
        ...state.draft.bl_fi_generic_doc_hdr,
        doc_entity_hdr_json: <any>{
          ...state.draft.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
          billingAddress: { ...action.billing_address }
        }
      }
    }, selectedBillingAddress: action.billing_address
  })),
  on(InternalDeliveryOrderActions.selectContactPerson, (state, action) =>
  ({
    ...state, draft: {
      ...state.draft,
      bl_fi_generic_doc_hdr: {
        ...state.draft.bl_fi_generic_doc_hdr,
        doc_entity_hdr_json: <any>{
          ...state.draft.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
          contactPerson: {
            contactName: action.line.name,
            contactId: action.line.id_no,
            designation: (<any>action.line.contact_json)?.position,
            email: action.line.email,
            phoneNumber: (<any>action.line.contact_json)?.phone_no,
            mobileNumber: (<any>action.line.contact_json)?.mobile_no,
            officeNumber: (<any>action.line.contact_json)?.office_no,
            faxNumber: (<any>action.line.contact_json)?.fax_no,
            extensionNumber: (<any>action.line.contact_json)?.extension_no,
            otherNumber: (<any>action.line.contact_json)?.other_no
          }
        }
      }
    }, selectedContactPerson: action.line
  })),
  on(InternalDeliveryOrderActions.updateMain, (state, action) => {
    // state.draft = {...state.draft};
    state.draft.bl_fi_generic_doc_hdr = {
      ...state.draft.bl_fi_generic_doc_hdr,
      guid_comp: action.model.company,
      guid_branch: action.model.branch,
      guid_store: action.model.location,
      // client_doc_1: action.form.docNo,
      doc_reference: action.model.reference,
      date_txn: action.model.deliveryDate,
      doc_remarks: action.model.remarks,
      doc_entity_hdr_json: <any>{
        ...state.draft.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
        shipVia: action.model.shipVia,
        trackingID: action.model.trackingID,
      }
    };
    return state;
  }),
  on(InternalDeliveryOrderActions.resetDraft, (state) => ({
    ...state,
    draft: new GenericDocContainerModel()
  })),
  on(InternalDeliveryOrderActions.selectDoc, (state, action) => ({
    ...state,
    selectedDoc: action.entity
  })),
  on(InternalDeliveryOrderActions.selectItem, (state, action) => ({
    ...state,
    selectedItem: action.entity,
  })),
  on(InternalDeliveryOrderActions.addLineItemToDraft, (state, action) => {
    // state.draft = {...state.draft};
    // action.line.guid = <any>state.draft.bl_fi_generic_doc_line.length;
    state.draft.bl_fi_generic_doc_line = [
      ...state.draft.bl_fi_generic_doc_line,
      action.line
    ];
    return state;
  }),
  on(InternalDeliveryOrderActions.addLinkToDraft, (state, action) => {
    state.draft.bl_fi_generic_doc_link = [...state.draft.bl_fi_generic_doc_link,
    action.link];
    return state;
  }),
  on(InternalDeliveryOrderActions.addLineItemToDraftEdit, (state, action) => {
    // state.draft = {...state.draft};
    // action.line.guid = <any>state.draft.bl_fi_generic_doc_line.length;
    state.draftEdit.bl_fi_generic_doc_line = [
      ...state.draftEdit.bl_fi_generic_doc_line,
      action.line
    ];
    return state;
  }),
  on(InternalDeliveryOrderActions.addLinkToDraftEdit, (state, action) => {
    // state.draft = {...state.draft};
    // action.line.guid = <any>state.draft.bl_fi_generic_doc_line.length;
    state.draftEdit.bl_fi_generic_doc_link = [
      ...state.draftEdit.bl_fi_generic_doc_link,
      action.link
    ];
    return state;
  }),
  on(InternalDeliveryOrderActions.selectLineItemInit, (state, action) => ({
    ...state,
    selectedLineItem: action.lineItem
  })),
  on(InternalDeliveryOrderActions.updateDepartment, (state, action) => {
    // state.draft = {...state.draft};
    state.draft.bl_fi_generic_doc_hdr = {
      ...state.draft.bl_fi_generic_doc_hdr,
      guid_segment: action.model.segment,
      guid_dimension: action.model.dimension,
      guid_profit_center: action.model.profitCenter,
      guid_project: action.model.project,
    };
    return state;
  }),
  on(InternalDeliveryOrderActions.updateCustomFields, (state, action) => {
    let ext = state.draft.bl_fi_generic_doc_ext.find(x => x.param_code === 'CUSTOM_FIELDS');
    if (!ext) {
      ext = new bl_fi_generic_doc_ext_RowClass();
      ext.param_code = 'CUSTOM_FIELDS';
      ext.param_name = 'CUSTOM_FIELDS';
      ext.param_type = 'JSON';
      ext.value_json = { ...action.model };
      state.draft.bl_fi_generic_doc_ext = [...state.draft.bl_fi_generic_doc_ext, ext];
    } else {
      ext.value_json = { ...action.model };
      state.draft.bl_fi_generic_doc_ext = [...state.draft.bl_fi_generic_doc_ext];
    }
    return state;
  }),
  on(InternalDeliveryOrderActions.createDeliveryOrderSuccess, (state, action) => ({
    ...state, draft: new GenericDocContainerModel(), refreshGenDocListing: true
  })),
  on(InternalDeliveryOrderActions.deleteDeliveryOrderSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalDeliveryOrderActions.editDeliveryOrderSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalDeliveryOrderActions.resetAgGrid, (state, action) => ({
    ...state, refreshGenDocListing: false
  })),
  on(InternalDeliveryOrderActions.resetDraftEdit, (state) => ({
    ...state,
    draftEdit: { ...state.selectedEntity }
  })),
  on(InternalDeliveryOrderActions.updateMainEdit, (state, action) => {
    // state.draft = {...state.draft};
    state.draftEdit.bl_fi_generic_doc_hdr = {
      ...state.draftEdit.bl_fi_generic_doc_hdr,
      guid_comp: action.model.company,
      guid_branch: action.model.branch,
      guid_store: action.model.location,
      // client_doc_1: action.form.docNo,
      doc_reference: action.model.reference,
      date_txn: action.model.deliveryDate,
      doc_remarks: action.model.remarks,
      doc_entity_hdr_json: <any>{
        ...state.draftEdit.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
        shipVia: action.model.shipVia,
        trackingID: action.model.trackingID,
      }
    };
    return state;
  }),
  on(InternalDeliveryOrderActions.updateDepartmentEdit, (state, action) => {
    // state.draft = {...state.draft};
    state.draftEdit.bl_fi_generic_doc_hdr = {
      ...state.draftEdit.bl_fi_generic_doc_hdr,
      guid_segment: action.model.segment,
      guid_dimension: action.model.dimension,
      guid_profit_center: action.model.profitCenter,
      guid_project: action.model.project,
    };
    return state;
  }),
  on(InternalDeliveryOrderActions.updateCustomFieldsEdit, (state, action) => {
    let ext = state.draftEdit.bl_fi_generic_doc_ext.find(x => x.param_code === 'CUSTOM_FIELDS');
    if (!ext) {
      ext = new bl_fi_generic_doc_ext_RowClass();
      ext.param_code = 'CUSTOM_FIELDS';
      ext.param_name = 'CUSTOM_FIELDS';
      ext.param_type = 'JSON';
      ext.value_json = { ...action.model };
      state.draftEdit.bl_fi_generic_doc_ext = [...state.draftEdit.bl_fi_generic_doc_ext, ext];
    } else {
      ext.value_json = { ...action.model };
      state.draftEdit.bl_fi_generic_doc_ext = [...state.draftEdit.bl_fi_generic_doc_ext];
    }
    return state;
  }),
  // on(InternalDeliveryOrderActions.selectEntityEdit, (state, action) =>
  // ({
  //   ...state, draftEdit: {
  //     ...state.draftEdit,
  //     bl_fi_generic_doc_hdr: {
  //       ...state.draftEdit.bl_fi_generic_doc_hdr,
  //       doc_entity_hdr_guid: action.entity.bl_fi_mst_entity_hdr.guid,
  //       doc_entity_hdr_json: <any>{
  //         ...state.draftEdit.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
  //         // customerType: action.entity.bl_fi_mst_entity_hdr.txn_type,
  //         // customerCode: action.entity.bl_fi_mst_entity_hdr.customer_code,
  //         entityName: action.entity.bl_fi_mst_entity_hdr.name,
  //         entityId: action.entityId,
  //         // entityId: action.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'ID_NO')?.value_string,
  //         email: action.entity.bl_fi_mst_entity_hdr.email,
  //         phoneNumber: action.entity.bl_fi_mst_entity_hdr.phone,
  //         generalLine: '',
  //         directLine: '',
  //         mobilePhone: action.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'MOBILE_PHONE')?.value_string,
  //         homeNumber: action.entity.bl_fi_mst_entity_ext.find(x => x.param_code === 'HOME_PHONE')?.value_string,
  //         shippingAddress: { },
  //         billingAddress: { },
  //       }
  //     }
  //   }, selectedCustomer: action.entity,
  // })),
  on(InternalDeliveryOrderActions.chosenEntityEdit, (state, action) =>
  ({
    ...state, draftEdit: {
      ...state.draftEdit,
      bl_fi_generic_doc_hdr: {
        ...state.draftEdit.bl_fi_generic_doc_hdr,
        doc_entity_hdr_json: <any>{
          ...state.draftEdit.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
          chosenEntity: action.chosenEntity
        }
      }
    }
  })),
  on(InternalDeliveryOrderActions.selectShippingAddressEdit, (state, action) =>
  ({
    ...state, draftEdit: {
      ...state.draftEdit,
      bl_fi_generic_doc_hdr: {
        ...state.draftEdit.bl_fi_generic_doc_hdr,
        doc_entity_hdr_json: <any>{
          ...state.draftEdit.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
          shippingAddress: { ...action.shipping_address }
        }
      }
    }, selectedShippingAddress: action.shipping_address
  })),
  on(InternalDeliveryOrderActions.selectBillingAddressEdit, (state, action) =>
  ({
    ...state, draftEdit: {
      ...state.draftEdit,
      bl_fi_generic_doc_hdr: {
        ...state.draftEdit.bl_fi_generic_doc_hdr,
        doc_entity_hdr_json: <any>{
          ...state.draftEdit.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
          billingAddress: { ...action.billing_address }
        }
      }
    }, selectedBillingAddress: action.billing_address
  })),
  on(InternalDeliveryOrderActions.selectContactPersonEdit, (state, action) =>
  ({
    ...state, draftEdit: {
      ...state.draftEdit,
      bl_fi_generic_doc_hdr: {
        ...state.draftEdit.bl_fi_generic_doc_hdr,
        doc_entity_hdr_json: <any>{
          ...state.draftEdit.bl_fi_generic_doc_hdr.doc_entity_hdr_json,
          contactPerson: {
            contactName: action.line.name,
            contactId: action.line.id_no,
            designation: (<any>action.line.contact_json)?.position,
            email: action.line.email,
            phoneNumber: (<any>action.line.contact_json)?.phone_no,
            mobileNumber: (<any>action.line.contact_json)?.mobile_no,
            officeNumber: (<any>action.line.contact_json)?.office_no,
            faxNumber: (<any>action.line.contact_json)?.fax_no,
            extensionNumber: (<any>action.line.contact_json)?.extension_no,
            otherNumber: (<any>action.line.contact_json)?.other_no
          }
        }
      }
    }, selectedContactPerson: action.line
  })),
  on(InternalDeliveryOrderActions.selectEntitySuccess, (state, action) => ({
    ...state, selectedCustomerEntity: action.entity
  })),
  on(InternalDeliveryOrderActions.selectLineItemSuccess, (state, action) => ({
    ...state
  })),
  on(InternalDeliveryOrderActions.editLineItem, (state, action) => {
    const lines = [];
    state.draft.bl_fi_generic_doc_line.forEach(l => {
      if (l.guid === action.line.guid) {
        l = { ...action.line };
        lines.push(l);
      } else {
        lines.push(l);
      }
    });
    state.draft.bl_fi_generic_doc_line = [
      ...lines
    ];
    return state;
  }),
  // just update the status to DELETED
  on(InternalDeliveryOrderActions.deleteLineItemFromDraftEdit, (state, action) => {
    const lines = state.draftEdit.bl_fi_generic_doc_line.filter(l => l.guid !== action.line.guid);
    const links = state.draftEdit.bl_fi_generic_doc_link.filter(l => l.guid_doc_2_line !== action.line.guid);

    state.draftEdit.bl_fi_generic_doc_line = [...lines, action.line];
    state.draftEdit.bl_fi_generic_doc_link = [...links, action.link]
    return state;
  }),
  on(InternalDeliveryOrderActions.removeLineItemFromDraft, (state, action) => {
    const lines = state.draft.bl_fi_generic_doc_line.filter(l => l.guid !== action.line.guid);
    const links = state.draft.bl_fi_generic_doc_link.filter(l => l.guid_doc_2_line !== action.line.guid);
    state.draft.bl_fi_generic_doc_line = lines;
    state.draft.bl_fi_generic_doc_link = links;
    return state;
  }),
  on(InternalDeliveryOrderActions.removeLineItemFromDraftEdit, (state, action) => {
    const lines = state.draftEdit.bl_fi_generic_doc_line.filter(l => l.guid !== action.line.guid);
    const links = state.draft.bl_fi_generic_doc_link.filter(l => l.guid_doc_2_line !== action.line.guid);
    state.draftEdit.bl_fi_generic_doc_line = lines;
    state.draftEdit.bl_fi_generic_doc_link = links;
    return state;
  }),
  on(InternalDeliveryOrderActions.editViewLineItem, (state, action) => {
    const lines = [];
    state.draftEdit.bl_fi_generic_doc_line.forEach(l => {
      if (l.guid === action.line.guid) {
        l = { ...action.line };
        lines.push(l);
      } else {
        lines.push(l);
      }
    });
    state.draftEdit.bl_fi_generic_doc_line = [
      ...lines
    ];
    return state;
  }),
  on(InternalDeliveryOrderActions.addViewLineItem, (state, action) => {
    action.line.guid = <any>state.draftEdit.bl_fi_generic_doc_line.length;
    state.draftEdit.bl_fi_generic_doc_line = [
      ...state.draftEdit.bl_fi_generic_doc_line,
      action.line
    ];
    return state;
  }),
  on(InternalDeliveryOrderActions.deleteViewLineItem, (state, action) => {
    const lines = state.draftEdit.bl_fi_generic_doc_line.filter(l => l.guid !== action.guid);
    state.draftEdit.bl_fi_generic_doc_line = [...lines];
    return state;
  }),
  on(InternalDeliveryOrderActions.setEditMode, (state, action) => ({
    ...state, editMode: action.editMode
  })),
  on(InternalDeliveryOrderActions.selectPricingSchemeLinkSuccess, (state, action) => ({
    ...state,
    pricingSchemeLink: action.pricing
  })),

  // For Serial, Bin, batch
  on(InternalDeliveryOrderActions.getInvItem, (state, action) => ({
    ...state, selectedItem: action.entity
  })),
  on(InternalDeliveryOrderActions.selectInvItem, (state, action) => ({
    ...state,
    selectedInvItem: action.invItem
  })),
  on(InternalDeliveryOrderActions.updateKnockoffListingConfig, (state, action) => ({
    ...state,
    knockoffListingConfig: action.settings
  })),
  on(InternalDeliveryOrderActions.selectSerial, (state, action) => ({
    ...state,
    selectedSerial: action.serial
  })),
  on(InternalDeliveryOrderActions.selectBatch, (state, action) => ({
    ...state,
    selectedBatch: action.batch
  })),
  on(InternalDeliveryOrderActions.selectBin, (state, action) => ({
    ...state,
    selectedBin: action.bin
  })),
  on(InternalDeliveryOrderActions.updatePostingStatusSuccess, (state, action) => ({
    ...state,
    updateAgGrid: true
  })),
  on(InternalDeliveryOrderActions.updateAgGrid, (state, action) => ({
    ...state,
    updateAgGrid: action.update
  })),
  on(InternalDeliveryOrderActions.selectCompanyGuid, (state, action) => ({
    ...state,
    selectedCompGuid: action.compGuid
  })),
  on(InternalDeliveryOrderActions.printJasperPdfInit, (state, action) => ({
    ...state,
    selectedPrintableFormatGuid: action.guid
  })),
  on(InternalDeliveryOrderActions.loadExternalJobDocsSuccess, (state, action) => ({
    ...state, loadedExternalJobDocs: action.jobDocs, jobListingStatus: false
  })),
  on(InternalDeliveryOrderActions.loadInternalJobDocsSuccess, (state, action) => ({
    ...state, loadedInternalJobDocs: action.jobDocs, jobListingStatus: false
  })),
  on(InternalDeliveryOrderActions.loadPickupJobDocsSuccess, (state, action) => ({
    ...state, loadedPickupJobDocs: action.jobDocs, jobListingStatus: false
  })),
  on(InternalDeliveryOrderActions.cancelJobDocSuccess, (state, action) => ({
    ...state, jobListingStatus: true
  })),
  on(InternalDeliveryOrderActions.resetJobListing, (state, action) => ({
    ...state, jobListingStatus: false
  })),
  on(InternalDeliveryOrderActions.cancelJobDocSuccess, (state, action) => ({
    ...state, deliveryDetailListingStatus: true
  })),
  on(InternalDeliveryOrderActions.resetDeliveryDetailsListing, (state, action) => ({
    ...state, deliveryDetailListingStatus: false
  })),
  on(InternalDeliveryOrderActions.pickPackQueueAllocationSuccess, (state, action) => ({
    ...state, deliveryDetailListingStatus: true
  })),
  on(InternalDeliveryOrderActions.loadDeliveryOrderSuccess, (state, action) => ({
    ...state, loadedGenDocs: action.deliveryOrders
  })),
  on(InternalDeliveryOrderActions.setEditMode, (state, action) => ({
    ...state, editMode: action.editMode
  })),
  // on(InternalDeliveryOrderActions.selectedEntity, (state, action) => ({
  //   ...state,
  //   entityType: action.entityType,
  //   updateAgGrid: true
  // }))
);

export function reducer(state: InternalDeliveryOrderState | undefined, action: Action) {
  return internalDeliveryOrderReducer(state, action);
}
