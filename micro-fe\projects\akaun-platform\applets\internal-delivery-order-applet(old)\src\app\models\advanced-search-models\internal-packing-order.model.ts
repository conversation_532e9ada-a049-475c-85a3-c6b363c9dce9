import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const internalPackingOrderSearchModel: SearchModel = {
  label: {
    company: 'Company',
    branch: 'Branch',
    location: 'Location',
    creationDate: 'Creation Date',
    packingNo: 'Packing Order No',
    status: 'Status',
    trackingId: 'Tracking Id'
  },
  dataType: {
    company: 'string',
    branch: 'string',
    location: 'string',
    creationDate: 'date',
    packingNo: 'string',
    status: 'string',
    trackingId: 'string'
  },
  form: new FormGroup({
    company: new FormControl(),
    creationDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    branch: new FormControl(),
    location: new FormControl(),
    packingNo: new FormControl(),
    status: new FormControl(),
    trackingId: new FormControl()
  }),
  query: (query) => `hdr.doc_entity_hdr_json ->> 'trackingID' ILIKE '%${query.trim()}%' AND hdr.server_doc_type = 'INTERNAL_OUTBOUND_DELIVERY_ORDER' AND hdr.status = 'ACTIVE'`,
  table: `bl_fi_generic_doc_hdr`,
  queryCallbacks: {
    creationDate: (creationDate) => {
      if (creationDate.from || creationDate.to) {
        var from = creationDate.from ? creationDate.from : creationDate.to;
        var to = creationDate.to ? creationDate.to : creationDate.from;
        return `hdr.created_date >= '${from.format(
          "YYYY-MM-DD HH:mm:ss"
        )}' AND hdr.created_date <= '${to.format("YYYY-MM-DD HH:mm:ss")}'`;
      }
      return "";
    },
    trackingId: (trackingId) => trackingId ? `hdr.doc_entity_hdr_json ->> 'trackingID' ILIKE '%${trackingId.trim()}%'` : ""
  }
};

