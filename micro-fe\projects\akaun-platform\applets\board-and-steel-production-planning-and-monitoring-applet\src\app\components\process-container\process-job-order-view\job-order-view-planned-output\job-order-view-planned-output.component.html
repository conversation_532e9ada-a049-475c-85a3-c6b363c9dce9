<div class="view-col-table" fxLayout="column">
    <div fxLayout="row wrap" fxLayoutAlign="end">
      <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
        <button mat-raised-button style="margin-left: 5px;" color="primary" type="button" (click)="onNext()">
          View Conversion
        </button>
        <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
    <div style="height: 100%;">
      <ag-grid-angular #agGrid
      id="grid"
      style="height: 100%;"
      class="ag-theme-balham"
      [getRowClass]="pagination.getRowClass"
      [columnDefs]="columnsDefs"
      [rowData]="[]"
      [paginationPageSize]="pagination.rowPerPage"
      [cacheBlockSize]="pagination.rowPerPage"
      [pagination]="true"
      [animateRows]="true"
      [defaultColDef]="defaultColDef"
      [suppressRowClickSelection]="true"
      [sideBar]="true"
      rowModelType="serverSide"
      [serverSideStoreType]="'partial'"
      (gridReady)="onGridReady($event)">
      </ag-grid-angular>
    </div>
  </div>
  