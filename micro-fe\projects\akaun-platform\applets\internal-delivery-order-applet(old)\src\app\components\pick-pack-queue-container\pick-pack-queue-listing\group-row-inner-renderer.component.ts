import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { ICellRendererParams, RowNode } from "ag-grid-community";
import * as moment from "moment";

@Component({
  selector: "group-row-cell",
  template: `
    <table style='position: relative'>
      <tr>
        <td class="td_a">DO No:</td>
        <td class="td_b">{{ server_doc_1 }}</td>
        <td class="td_a">DO Ref No:</td>
        <td class="td_b">{{ server_doc_2 }}</td>
      </tr>
      <tr>
        <td class="td_a">INV No:</td>
        <td class="td_b">{{ invoice_no }}</td>
        <td class="td_a">INV Ref No:</td>
        <td class="td_b"></td>
      </tr>
      <tr>
        <td class="td_a">Salesman:</td>
        <td class="td_b">{{ salesman }}</td>
        <td class="td_a">Date Txn:</td>
        <td class="td_b">{{ date_txn }}</td>
      </tr>
      <tr>
        <td class="td_a">Recipient Address:</td>
        <td class="td_b">
          {{ shipping_address_city }}, {{ shipping_address_state }}
        </td>
      </tr>
    </table>
  `,
  styles: [
    `
      .row {
        display: inline-block;
      }
      td {
        text-align: left;
      }
      .td_a {
        font-family: Arial;
        font-size: 12px;
        padding-left: 8px;
        width: 30px;
        font-weight: bold;
        line-height: 10px;
      }
      .td_b {
        font-family: Arial;
        font-size: 12px;
        padding-left: 8px;
        width: 50px;
        font-weight: lighter;
        line-height: 10px;
      }
    `,
  ],
})
export class GroupRowInnerRenderer implements ICellRendererAngularComp {
  protected node: RowNode;
  server_doc_1: any;
  server_doc_2: any;
  date_txn: any;
  salesman: any;
  shipping_address_city: any;
  shipping_address_state: any;
  invoice_no: any;

  agInit(params: any): void {
    this.node = params.node;
    if (this.node.field == "bl_fi_generic_doc_hdr.server_doc_1") {
      this.server_doc_1 = this.node.key;
      this.salesman = this.node.allLeafChildren[0].data.salesMan;
      this.date_txn = moment(
        this.node.allLeafChildren[0].data.bl_fi_generic_doc_hdr.date_txn
      ).format("YYYY-MM-DD");

      this.shipping_address_city =
        this.node.allLeafChildren[0].data.bl_fi_generic_doc_hdr.delivery_entity_json[
          "shippingAddress"
        ]?.city;
      this.shipping_address_state =
        this.node.allLeafChildren[0].data.bl_fi_generic_doc_hdr.delivery_entity_json[
          "shippingAddress"
        ]?.state;

      console.log(this.node.allLeafChildren[0].data.generic_doc_hdr_1_doc_link);
    }

    for (
      var i = 0;
      i < this.node.allLeafChildren[0].data.generic_doc_hdr_1_doc_link?.length;
      i++
    ) {
      if (i == 0) {
        this.invoice_no =
          this.node.allLeafChildren[0].data.generic_doc_hdr_1_doc_link[
            i
          ].server_doc_1;
      } else {
        this.invoice_no =
          this.invoice_no +
          ", " +
          this.node.allLeafChildren[0].data.generic_doc_hdr_1_doc_link[i]
            .server_doc_1;
      }
    }
  }

  refresh(params: ICellRendererParams): boolean {
    return true;
  }
}
