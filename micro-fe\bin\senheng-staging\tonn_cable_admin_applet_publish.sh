#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=senheng-staging --project=wavelet-erp-admin-applet --output-hashing none
node elements-build-scripts/akaun/wavelet-erp-admin-applet-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://senheng-applets/bigledger/wavelet-erp/wavelet-erp-admin-applet/staging s3://senheng-applets/bigledger/wavelet-erp/wavelet-erp-admin-applet/staging/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile senheng-staging --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/wavelet-erp-admin-applet/ s3://senheng-applets/bigledger/wavelet-erp/wavelet-erp-admin-applet/staging --profile senheng-staging --acl public-read --recursive
