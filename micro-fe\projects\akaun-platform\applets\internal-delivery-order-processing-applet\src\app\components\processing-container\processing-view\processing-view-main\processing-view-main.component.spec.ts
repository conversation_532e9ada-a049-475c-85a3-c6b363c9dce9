import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { ProcessingViewMainComponent } from './processing-view-main.component';

describe('ProcessingViewMainComponent', () => {
  let component: ProcessingViewMainComponent;
  let fixture: ComponentFixture<ProcessingViewMainComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ ProcessingViewMainComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ProcessingViewMainComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
