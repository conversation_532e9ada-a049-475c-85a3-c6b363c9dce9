import { CreditLimitContainerModel } from "blg-akaun-ts-lib";
import * as moment from "moment";
import { EntityConstants } from "./constants/customer-constants";


export class EntityViewModel {
  guid: string;
  code: string;
  name: string;
  status: string;
  updated_date: string;

}

export function containerToViewModelCreditLimit(data: CreditLimitContainerModel): EntityViewModel {
  var hdr = data.bl_fi_entity_credit_limit_hdr;

  //var create_date = moment(hdr.created_date).format(CustomerConstants.DateTimeFormat)
  var updated_date = moment(hdr.updated_date).format(EntityConstants.DateTimeFormat)
  return {
    guid: hdr.guid,
    code: hdr.code,
    name: hdr.name,
    status: hdr.status,
    updated_date: updated_date,

  } as EntityViewModel
}

