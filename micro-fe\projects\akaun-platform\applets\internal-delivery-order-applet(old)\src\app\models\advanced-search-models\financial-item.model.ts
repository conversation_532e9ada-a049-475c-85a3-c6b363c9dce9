import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const financialItemSearchModel: SearchModel = {
  label: {
    itemName: 'Item Name',
    creationDate: 'Creation Date',
    branch: 'Branch',
    location: 'Location',
    category: 'Category',
    subType: 'Sub Type'
  },
  dataType: {
    itemName: 'string',
    creationDate: 'date',
    branch: 'string',
    location: 'string',
    category: 'string',
    subType: 'string'
  },
  form: new FormGroup({
    itemName: new FormControl(),
    creationDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    branch: new FormControl(),
    location: new FormControl(),
    category: new FormControl(),
    subType: new FormControl()
  }),
  query: () => '',
  queryCallbacks: {}
};

