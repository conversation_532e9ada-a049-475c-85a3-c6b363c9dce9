<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Create Generic
      </span>
    </div>
    <button mat-raised-button color="primary" type="button" (click)="onSave()">SAVE</button>
  </div>
</mat-card-title>
<mat-tab-group [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Main">
    <app-generic-create-main (shippingInfo)="onShippingInfo()" (billingInfo)="onBillingInfo()"></app-generic-create-main>
  </mat-tab>
  <mat-tab label="Line Items">
    <app-generic-create-line-items [localState]="localState$ | async" (next)="onNextAdd()"></app-generic-create-line-items>
  </mat-tab>
</mat-tab-group>
