<div class="view-col-table" fxLayout="column">
    <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
        <div fxFlex="3 0 0">
          <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
            <button ngClass.xs="blg-button-mobile" #navBtn class="blg-button-icon" mat-button matTooltip="Create" type="button">
                <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="assets/images/add.png" alt="add" width="40px" height="40px">
            </button>
            <app-advanced-search fxFlex fxFlex.lt-sm="100" [id]="'issue-link'" [advSearchModel]="searchModel"></app-advanced-search>
          </div>
        </div>
        <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
          <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
          <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
        </div>
      </div>
    <div style="height: 100%;">
      <ag-grid-angular #agGrid
      style="height: 100%;"
      class="ag-theme-balham"
      rowSelection="single"
      [getRowClass]="pagination.getRowClass"
      [columnDefs]="columnsDefs"
      [rowData]="rowData"
      [paginationPageSize]="pagination.rowPerPage"
      [animateRows]="true"
      [defaultColDef]="defaultColDef"
      [suppressRowClickSelection]="false"
      [sideBar]="true"
      (rowClicked)="onRowClicked($event.data)"
      (gridReady)="onGridReady($event)">
      </ag-grid-angular>
    </div>
</div>
  