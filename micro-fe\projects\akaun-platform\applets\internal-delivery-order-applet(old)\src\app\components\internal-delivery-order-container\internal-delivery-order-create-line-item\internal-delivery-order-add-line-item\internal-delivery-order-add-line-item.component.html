<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px" />
      </button>
      <span> Add Line Item </span>
    </div>
    <button mat-raised-button color="primary" type="button" [disabled]="disableAdd()" (click)="onAdd()">
      ADD
    </button>
  </div>
</mat-card-title>
<mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Item Details">
    <app-add-line-item-item-details [item$]="item$" [tax$]="tax$"></app-add-line-item-item-details>
  </mat-tab>
  <mat-tab label="Costing Details" *ngIf="!appletSettings.HIDE_COSTING_DETAILS || showCostingDetails">
    <app-add-line-item-costing-details></app-add-line-item-costing-details>
  </mat-tab>
  <mat-tab label="Pricing Details">
    <app-add-line-item-pricing-details></app-add-line-item-pricing-details>
  </mat-tab>
  <mat-tab label="Issue Link">
    <app-add-line-item-issue-link></app-add-line-item-issue-link>
  </mat-tab>
  <!-- <mat-tab *ngIf="subItemType === 'SERIAL_NUMBER'" label="Serial Number">
    <app-add-line-item-serial-number
      [childSelectedIndex$]="serialNumberSelectedIndex$"
      [editMode]="false"
      [lineItem$]="line$"
      [draft$]="draft$"
      [invItem$]="invItem$"
      [serial$]="serial$"
    >
    </app-add-line-item-serial-number>
  </mat-tab>
  <mat-tab *ngIf="subItemType === 'BIN_NUMBER'" label="Bin Number">
    <app-add-line-item-bin-number
      [editMode]="false"
      [lineItem$]="line$"
      (selectBin)="goToBinListing()"
    ></app-add-line-item-bin-number>
  </mat-tab>
  <mat-tab *ngIf="subItemType === 'BATCH_NUMBER'" label="Batch Number">
    <app-add-line-item-batch-number
      [editMode]="false"
      [lineItem$]="line$"
      (selectBatch)="goToBatchListing()"
    ></app-add-line-item-batch-number>
  </mat-tab> -->
</mat-tab-group>