import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const internalDeliveryOrderSearchModel: SearchModel = {
  label: {
    branch: 'Branch',
    customerName: 'Customer Name',
    creationDate: 'Creation Date'
  },
  dataType: {
    branch: 'string',
    customerName: 'string',
    creationDate: 'date'
  },
  form: new FormGroup({
    branch: new FormControl(),
    customerName: new FormControl(),
    creationDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    })
  }),
  query: (query) => `hdr.server_doc_1 ILIKE '%${query}%' AND hdr.server_doc_type = 'INTERNAL_OUTBOUND_DELIVERY_ORDER' AND hdr.status = 'ACTIVE'`,
  table: 'bl_fi_generic_doc_hdr',
  joins: [
    {
      type: 'INNER JOIN',
      table: 'bl_fi_mst_entity_hdr',
      alias: 'entityHdr',
      onCondition: `hdr.doc_entity_hdr_guid = entityHdr.guid`,
      joinOnBasic: false
    },
    {
      type: 'INNER JOIN',
      table: 'bl_fi_mst_branch',
      alias: 'branch',
      onCondition: `hdr.guid_branch = branch.guid`,
      joinOnBasic: false
    },
  ],
  queryCallbacks: {
    branch: query => query ? `(branch.name ILIKE '%${query}%' OR branch.code ILIKE '%${query}%') AND branch.status = 'ACTIVE'` : '',
    customerName: query => query ? `entityHdr.name ILIKe '%${query}%' AND entityHdr.status = 'ACTIVE'` : '',
    creationDate: query => {
      if (query.from || query.to) {
        // assign creationDate.from to itself or creationDate.to if null
        const from = query.from ? `hdr.created_date >= '${query.from.format('YYYY-MM-DD')}'` : '';
        // assign creationDate.to to itself or creationDate.from if null
        const to = query.to ? `hdr.created_date <= '${query.to.format('YYYY-MM-DD')}'` : '';
        return `${from} ${(from && to) ? 'AND' : ''} ${to}`;
      }
      return '';
    }
  }
};

