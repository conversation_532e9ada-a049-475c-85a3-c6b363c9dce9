import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderCreateAccountComponent } from './internal-delivery-order-create-account.component';

describe('InternalDeliveryOrderCreateAccountComponent', () => {
  let component: InternalDeliveryOrderCreateAccountComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderCreateAccountComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderCreateAccountComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderCreateAccountComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
