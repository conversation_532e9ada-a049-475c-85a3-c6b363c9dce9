import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InternalDeliveryOrderTransferSalesOrderMainComponent } from './internal-delivery-order-transfer-sales-order-main.component';

describe('InternalDeliveryOrderTransferSalesOrderMainComponent', () => {
  let component: InternalDeliveryOrderTransferSalesOrderMainComponent;
  let fixture: ComponentFixture<InternalDeliveryOrderTransferSalesOrderMainComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InternalDeliveryOrderTransferSalesOrderMainComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InternalDeliveryOrderTransferSalesOrderMainComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
