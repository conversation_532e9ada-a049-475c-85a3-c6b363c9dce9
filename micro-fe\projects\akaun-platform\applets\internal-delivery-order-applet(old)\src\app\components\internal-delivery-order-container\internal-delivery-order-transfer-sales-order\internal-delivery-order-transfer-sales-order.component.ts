import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';

interface LocalState {
  deactivateReturn: boolean;
  selectedIndex: number;
}

@Component({
  selector: 'app-internal-delivery-order-transfer-sales-order',
  templateUrl: './internal-delivery-order-transfer-sales-order.component.html',
  styleUrls: ['./internal-delivery-order-transfer-sales-order.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalDeliveryOrderTransferSalesOrderComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Transfer From Sales Order';
  protected index = 5;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);

  toggleColumn$ = this.viewColFacade.toggleColumn$;
  selectedDoc$ = this.store.select(InternalDeliveryOrderSelectors.selectDoc);
  
  editMode: boolean;
  prevIndex: number;
  protected prevLocalState: any;

  selectedRows: bl_fi_generic_doc_line_RowClass[] = [];

  @ViewChild(MatTabGroup, { static: true } ) matTab: MatTabGroup;

  constructor(
    private readonly store: Store<InternalDeliveryOrderStates>,
    private viewColFacade: ViewColumnFacade,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
    this.store.select(InternalDeliveryOrderSelectors.selectEditMode).subscribe(editMode => this.editMode = editMode);
    console.log("editMode", this.editMode);
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateReturn: false,
      deactivateSOList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAdd() {
    this.selectedRows.forEach((row) => {
      if(this.editMode){
        this.viewColFacade.addLineItemToDraft(row);
      } else {
        this.selectedRows.forEach((row) => this.viewColFacade.addLineItemToDraft(row));
      }
    })
  }

  disableAdd() {
    return !this.selectedRows.length;
  }

  onSelectedRows(e) {
    this.selectedRows = [];
    e.forEach(r => this.selectedRows.push(r.data));
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {...this.localState, selectedIndex: this.matTab.selectedIndex});
    }
    this.subs.unsubscribe();
  }

}
