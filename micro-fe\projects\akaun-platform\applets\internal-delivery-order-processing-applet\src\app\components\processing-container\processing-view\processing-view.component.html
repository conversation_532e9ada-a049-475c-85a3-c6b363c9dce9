<mat-card-title class="column-title">
  <div fxLayout="row wrap" fxLayoutAlign="space-between end">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="back" width="40px" height="40px">
      </button>
      <span>
        Process Internal Delivery Order
      </span>
    </div>
    <div fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="5px">
      <button mat-raised-button color="primary" type="button"
      [disabled]="processingStatus.invalid"
      (click)="onSave()">SAVE</button>
    </div>
  </div>
</mat-card-title>
<mat-tab-group [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
  <mat-tab label="Main">
    <app-processing-view-main [draft$]="draft$"></app-processing-view-main>
  </mat-tab>
  <mat-tab label="Line Items">
    <app-processing-view-line-items [localState]="localState$ | async" [rowData]="(draft$ | async).bl_fi_generic_doc_line"></app-processing-view-line-items>
  </mat-tab>
  <mat-tab label="Processing Status">
    <div class="view-col-forms">
      <mat-form-field appearance= "outline">
        <mat-label>Processing Status</mat-label>
        <mat-select [formControl]="processingStatus">
          <mat-option value="Processed">Processed</mat-option>
          <mat-option value="Pending">Pending</mat-option>
          <mat-option value="Cancelled">Cancelled</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </mat-tab>
</mat-tab-group>
