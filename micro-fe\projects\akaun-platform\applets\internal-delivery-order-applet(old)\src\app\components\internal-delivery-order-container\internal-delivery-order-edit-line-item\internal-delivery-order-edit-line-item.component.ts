import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { Store } from '@ngrx/store';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { SubItemType } from 'projects/akaun-platform/applets/internal-sales-order-applet-v2/src/app/models/constants/sub-item-type-constants';
import { bl_fi_generic_doc_line_RowClass, bl_fi_generic_doc_link_RowClass, FinancialItemService, InternalSalesOrderService, InternalInboundStockTransferService } from 'blg-akaun-ts-lib';
import { switchMap, map, tap, delay, mergeMap } from 'rxjs/operators';
import { AppConfig } from 'projects/shared-utilities/visa';
import { iif, of, EMPTY } from 'rxjs';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { PNSActions, LinkActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDRSelectors, LinkSelectors, PNSSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { ItemDetailsComponent } from './item-details/item-details.component';
import { AppletConstants } from '../../../models/constants/applet-constants';
import { MatTabGroup } from '@angular/material/tabs';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { ServerDocTypeConstants } from 'projects/shared-utilities/models/server-doc-types.model';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';

interface LocalState {
  deactivateChange: boolean;
  deactivateReturn: boolean;
  deactivateIssueLinkList: boolean;
  selectedIndex: number;
  itemSelectedIndex: number;
  serialSelectedIndex: number;
  deleteConfirmation: boolean;
}

@Component({
  selector: 'app-internal-delivery-order-edit-line-item',
  templateUrl: './internal-delivery-order-edit-line-item.component.html',
  styleUrls: ['./internal-delivery-order-edit-line-item.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalDeliveryOrderEditLineItemComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Internal Delivery Order Edit Line';
  protected index = 2;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateChange$ = this.componentStore.select(state => state.deactivateChange);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deleteConfirmation$ = this.componentStore.select(state => state.deleteConfirmation);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);

  draft$ = this.draftStore.select(HDRSelectors.selectHdr);
  links$ = this.draftStore.select(LinkSelectors.selectAll);
  GenDocActions = InternalDeliveryOrderActions;
  LinkSelectors = LinkSelectors;
  LinkActions = LinkActions;
  PNSSelectors = PNSSelectors;
  PNSActions = PNSActions;
  AppletConstants = AppletConstants;

  stgrnAdditionalColumns = [
    {
      headerName: "Location From",
      field: "location_from",
      cellStyle: () => ({ "text-align": "left" })
    },
    {
      headerName: "Location To",
      field: "location_to",
      cellStyle: () => ({ "text-align": "left" })
    },
    {
      headerName: "Tracking Id",
      field: "tracking_id",
      cellStyle: () => ({ "text-align": "left" })
    }
  ];

  soAdditionalColumns = [
    {
      headerName: "Branch",
      field: "branch",
      cellStyle: () => ({ "text-align": "left" })
    },
    {
      headerName: "Location From",
      field: "location_from",
      cellStyle: () => ({ "text-align": "left" })
    },
    {
      headerName: "Location To",
      field: "location_to",
      cellStyle: () => ({ "text-align": "left" })
    }
  ];


  readonly lineItem$ = this.store.select(InternalDeliveryOrderSelectors.selectLineItem)
  // .pipe(
  //   (item) => {
  //     let i;
  //     item.subscribe(l => i = l);
  //     console.log("lineItem$", i);
  //     return item;
  //   },
  //   switchMap(item => this.fiService.getByGuid(item.item_guid.toString(), this.apiVisa)),
  //   map(resolve => resolve.data.bl_fi_mst_item_hdr.sub_item_type ?
  //     resolve.data.bl_fi_mst_item_hdr.sub_item_type : this.SUB_ITEM_TYPE.basicQuantity),
  //   tap(type => {
  //     this.subItemType = type.toString();
  //     console.log("sub item type: ", this.subItemType);
  //   })
  // );
  readonly editMode$ = this.store.select(InternalDeliveryOrderSelectors.selectEditMode)
  readonly hdr$ = this.store.select(InternalDeliveryOrderSelectors.selectDraftEdit);

  editMode: boolean;
  prevIndex: number;
  apiVisa = AppConfig.apiVisa;
  protected prevLocalState: any;
  hdr_guid: any;
  lineItem: bl_fi_generic_doc_line_RowClass;
  deleteConfirmation: boolean = false;
  subItemType: string;
  SUB_ITEM_TYPE = SubItemType;
  compGuid: any;
  koForSalesOrder = false;
  koForSTGRN = false;

  constructor(
    public sessionStore: Store<SessionStates>,
    private viewColFacade: ViewColumnFacade,
    private fiService: FinancialItemService,
    private readonly store: Store<InternalDeliveryOrderStates>,
    private readonly draftStore: Store<DraftStates>,
    private readonly componentStore: ComponentStore<LocalState>,
    public InternalSalesOrderService: InternalSalesOrderService,
    public InternalInboundStockTransferService: InternalInboundStockTransferService,
  ) {
    super();
    this.store.select(InternalDeliveryOrderSelectors.selectEditMode).subscribe(editMode => this.editMode = editMode);
  }

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(ItemDetailsComponent) itemDetails: ItemDetailsComponent;

  ngOnInit() {
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.selectLineItem).subscribe(resolved => {
      this.lineItem = resolved;
    })
    this.subs.sink = this.store.select(HDRSelectors.selectHdr).subscribe(resolved => {
      this.hdr_guid = resolved.guid;
      console.log("hdr", this.hdr_guid);
    })
    this.subs.sink = this.deleteConfirmation$.pipe(
      mergeMap(a => {
        return iif(() => a, of(a).pipe(delay(3000)), of(EMPTY));
      })
    ).subscribe(resolve => {
      if (resolve === true) {
        this.componentStore.patchState({ deleteConfirmation: false });
        this.deleteConfirmation = false;
      }
    });

    this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.selectCompanyGuid).subscribe(guid => {
      this.compGuid = guid;
    })

    this.sessionStore.dispatch(SessionActions.getKOSettingsInit({ compGuid: this.compGuid, serverDoc2: ServerDocTypeConstants.INTERNAL_OUTBOUND_DELIVERY_ORDER }))

    this.subs.sink = this.sessionStore.select(SessionSelectors.selectKOSettings).subscribe(response => {
      console.log(response);
      if (response !== null) {
        response.forEach(setting => {
          if (setting.bl_fi_comp_gendoc_flow_config.server_doc_type_1 === ServerDocTypeConstants.INTERNAL_SALES_ORDER && setting.bl_fi_comp_gendoc_flow_config.flow_type == "LINE") {
            this.koForSalesOrder = setting.bl_fi_comp_gendoc_flow_config.is_enabled;
          }

          if (setting.bl_fi_comp_gendoc_flow_config.server_doc_type_1 === ServerDocTypeConstants.INTERNAL_INBOUND_STOCK_TRANSFER && setting.bl_fi_comp_gendoc_flow_config.flow_type == "LINE") {
            this.koForSTGRN = setting.bl_fi_comp_gendoc_flow_config.is_enabled;
          }

        })
      }
    })
  }

  onDelete() {
    if (this.deleteConfirmation) {
      this.deleteLine();
      this.deleteConfirmation = false;
      this.componentStore.patchState({ deleteConfirmation: false });
    } else {
      this.deleteConfirmation = true;
      this.componentStore.patchState({ deleteConfirmation: true });
    }
  }

  deleteLine() {
    if (this.editMode) {
      // check generic_doc_hdr_guid from gen_doc_line
      // if matched, that mean the line item is under delivery order, and we need to soft DELETE it
      if (this.hdr_guid === this.lineItem.generic_doc_hdr_guid) {
        const line = { ...this.lineItem, status: 'DELETED' };
        const link = { ...this.getLink(this.lineItem.guid.toString()), status: 'DELETED' };
        this.store.dispatch(InternalDeliveryOrderActions.deleteLineItemFromDraftEdit({ line: line, link: link }));
        this.draftStore.dispatch(PNSActions.deletePNS({ guid: this.lineItem.guid.toString() }));
        console.log("this line item guid", this.lineItem.guid);
        this.draftStore.dispatch(PNSActions.editPNS({ pns: line }));
        this.draftStore.dispatch(LinkActions.editLink({ link: link }));
      } else {
        // else, just remove it
        this.store.dispatch(InternalDeliveryOrderActions.removeLineItemFromDraftEdit({ line: this.lineItem }));
        this.draftStore.dispatch(PNSActions.deletePNS({ guid: this.lineItem.guid.toString() }));
        this.draftStore.dispatch(LinkActions.deleteLink({ guid: this.getLink(this.lineItem.guid.toString()).guid.toString() }));
      }
    } else {
      // this.store.dispatch(InternalDeliveryOrderActions.removeLineItemFromDraft({ line: this.lineItem }));
      console.log("this.lineItem", this.lineItem);
      this.draftStore.dispatch(PNSActions.deletePNS({ guid: this.lineItem.guid.toString() }));
      this.draftStore.dispatch(LinkActions.deleteLink({ guid: this.getLink(this.lineItem.guid.toString()).guid.toString() }));
    }
    this.viewColFacade.resetIndex(this.prevIndex);
  }

  getLink(lineGuid: string): bl_fi_generic_doc_link_RowClass {
    let link;
    if (!this.editMode) {
      this.subs.sink = this.draftStore.select(LinkSelectors.selectAll).subscribe(resolved => {
        link = resolved.find(x => x.guid_doc_2_line === lineGuid);
      })
    }
    else {
      this.subs.sink = this.draftStore.select(LinkSelectors.selectAll).subscribe(resolved => {
        link = resolved.find(x => x.guid_doc_2_line === lineGuid);
      })
    }
    return link;
  }

  goToBinListing() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      // deactivateAdd: true
    });
    this.viewColFacade.onNextAndReset(this.index, 10);
  }

  goToBatchListing() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      // deactivateAdd: true
    });
    this.viewColFacade.onNextAndReset(this.index, 11);
  }

  onKnockoffAdd(event) {
    console.log("onKnockoffAdd(event)", event);
    this.store.dispatch(InternalDeliveryOrderActions.updateKnockoffListingConfig({ settings: event }));
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateIssueLink: true,
      deactivateAdd: true
    });
    this.viewColFacade.onNextAndReset(this.index, 13);
  }

  onKnockoffEdit(event) {
    console.log("onKnockOffEdit", event);
    this.store.dispatch(InternalDeliveryOrderActions.updateKnockoffListingConfig({ settings: event }));
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateIssueLink: true,
      deactivateAdd: true
    });
    this.viewColFacade.onNextAndReset(this.index, 14);
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateList: false,
      deactivateReturn: false,
      deactivateLineItem: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  disableSave() {
    return false;
    // return this.itemDetails?.main.form.invalid;
  }

  onSave() {
    const line = { ...this.lineItem };
    console.log("form", this.itemDetails.main.form);
    this.itemDetails.main.form.enable();
    line.item_guid = this.itemDetails.main.form.value.itemGuid;
    line.item_code = this.itemDetails.main.form.value.itemCode;
    line.item_name = this.itemDetails.main.form.value.itemName;
    line.quantity_base = this.itemDetails.main.form.value.qty;
    line.amount_std = Number(this.itemDetails.main.form.value.stdAmt) ? this.itemDetails.main.form.value.stdAmt : 0;
    console.log("infinity", Number((this.itemDetails.main.form.value.stdAmt)));
    line.amount_discount = Number(this.itemDetails.main.form.value.discountAmt) ? this.itemDetails.main.form.value.discountAmt : 0;
    line.amount_net = Number(this.itemDetails.main.form.value.netAmt) ? this.itemDetails.main.form.value.netAmt : 0;
    line.tax_gst_code = this.itemDetails.main.form.value.taxCode;
    line.tax_gst_rate = Number(this.itemDetails.main.form.value.taxPercent) ? this.itemDetails.main.form.value.taxPercent : 0;
    line.amount_tax_gst = Number(this.itemDetails.main.form.value.taxAmt) ? this.itemDetails.main.form.value.taxAmt : 0;
    line.tax_wht_code = this.itemDetails.main.form.value.whtCode;
    line.tax_wht_rate = Number(this.itemDetails.main.form.value.whtPercent) ? this.itemDetails.main.form.value.whtPercent : 0;
    line.amount_tax_wht = Number(this.itemDetails.main.form.value.whtAmt) ? this.itemDetails.main.form.value.whtAmt : 0;
    line.amount_txn = Number(this.itemDetails.main.form.value.txnAmt) ? this.itemDetails.main.form.value.txnAmt : 0;
    line.item_remarks = this.itemDetails.main.form.value.remarks;
    line.item_txn_type = this.itemDetails.main.form.value.item_txn_type;
    line.item_sub_type = this.itemDetails.main.form.value.item_sub_type;
    // line.guid_dimension = this.itemDetails.dept.form.value.dimension;
    // line.guid_profit_center = this.itemDetails.dept.form.value.profitCenter;
    // line.guid_project = this.itemDetails.dept.form.value.project;
    // line.guid_segment = this.itemDetails.dept.form.value.segment;
    line.item_property_json = { ...this.itemDetails.main.form.value };
    line.unit_price_std = Number(this.itemDetails.main.form.value.unitPriceStdWithoutTax) ? this.itemDetails.main.form.value.unitPriceStdWithoutTax : 0;
    line.unit_price_txn = Number(this.itemDetails.main.form.value.unitPriceTxn) ? this.itemDetails.main.form.value.unitPriceTxn : 0;
    line.unit_price_net = Number(this.itemDetails.main.form.value.unitPriceNet) ? this.itemDetails.main.form.value.unitPriceNet : 0;
    line.unit_price_std_by_uom = Number(this.itemDetails.main.form.value.unitPriceStdUom) ? this.itemDetails.main.form.value.unitPriceStdUom : 0;
    line.unit_price_txn_by_uom = Number(this.itemDetails.main.form.value.unitPriceTxnUom) ? this.itemDetails.main.form.value.unitPriceTxnUom : 0;
    line.unit_disc_by_uom = Number(this.itemDetails.main.form.value.unitDiscountUom) ? this.itemDetails.main.form.value.unitDiscountUom : 0;
    line.uom = this.itemDetails.main.form.value.uom;
    line.uom_to_base_ratio = this.itemDetails.main.form.value.uomBaseRatio;
    line.qty_by_uom = parseFloat(this.itemDetails.main.form.value.qtyUom);
    line.line_property_json = <any>{ delivery_instructions: { ...this.itemDetails.delivery.form.value } };
    line.tracking_id = this.itemDetails.main.form.value.trackingId;
    line.server_doc_type = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
    line.client_doc_type = "INTERNAL_OUTBOUND_DELIVERY_ORDER";
    console.log('line last:: ', line);
    // line.date_txn = this.lineItem.date_txn; // should be new date or keep old one?
    // line.serial_no = this.subItemType === this.SUB_ITEM_TYPE.serialNumber ? <any>{ serialNumbers: this.serialNumber.serialNumbers } : null;
    // line.batch_no = this.subItemType === this.SUB_ITEM_TYPE.batchNumber ? <any>{ batches: this.batchNumber.batchNumbers } : null;
    // line.bin_no = this.subItemType === this.SUB_ITEM_TYPE.binNumber ? <any>{ bins: this.binNumber.binNumbers } : null;



    // Update existing hdr balance by calculating delta (line2 - line1)
    // const diffLine = new bl_fi_generic_doc_line_RowClass();
    // diffLine.amount_discount = <any>(parseFloat(<any>line.amount_discount) - parseFloat(<any>this.lineItem.amount_discount));
    // diffLine.amount_net = <any>(parseFloat(<any>line.amount_net) - parseFloat(<any>this.lineItem.amount_net));
    // diffLine.amount_std = <any>(parseFloat(<any>line.amount_std) - parseFloat(<any>this.lineItem.amount_std));
    // diffLine.amount_tax_gst = <any>(parseFloat(<any>line.amount_tax_gst) - parseFloat(<any>this.lineItem.amount_tax_gst));
    // diffLine.amount_tax_wht = <any>(parseFloat(<any>line.amount_tax_wht) - parseFloat(<any>this.lineItem.amount_tax_wht));
    // diffLine.amount_txn = <any>(parseFloat(<any>line.amount_txn) - parseFloat(<any>this.lineItem.amount_txn));

    if (this.itemDetails.main.form.value.itemType) {
      const link = this.getLink(line.guid.toString());
      if (link) {
        link.quantity_contra = line.quantity_base;
        console.log('link', link);
        this.draftStore.dispatch(LinkActions.editLink({ link }));
      }
    }
    this.draftStore.dispatch(PNSActions.editPNS({ pns: line }));
    if (this.editMode)
      this.viewColFacade.resetIndex(1);
    else
      this.viewColFacade.resetIndex(3);
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
      });
    }

    this.subs.unsubscribe();
  }

}
