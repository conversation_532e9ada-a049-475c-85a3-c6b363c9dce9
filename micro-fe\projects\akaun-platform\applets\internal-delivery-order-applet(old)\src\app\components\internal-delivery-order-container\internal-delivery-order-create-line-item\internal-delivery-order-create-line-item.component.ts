import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { FlexAlignStyleBuilder } from '@angular/flex-layout';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass, InternalInboundStockTransferService, InternalSalesOrderService, InternalOutboundDeliveryOrderService, InternalPurchaseGoodsReceivedNotes } from 'blg-akaun-ts-lib';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { internalInboundDeliveryOrderSearchModel, internalOutboundDeliveryOrderSearchModel } from '../../../models/advanced-search-models/internal-delivery-order.model';
import { purchaseGRNLineItemSearchModel, salesLineItemSearchModel } from '../../../models/advanced-search-models/line-item.model';
import { AppletSettings } from '../../../models/applet-settings.model';
import { AppletConstants } from '../../../models/constants/applet-constants';
import { LinkActions, PNSActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDRSelectors, LinkSelectors, PNSSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { ServerDocTypeConstants } from 'projects/shared-utilities/models/server-doc-types.model';

interface LocalState {
  deactivateReturn: boolean;
  deactivateSOList: boolean;
  deactivateQuotationList: boolean;
  deactivateInvoiceList: boolean;
  selectedIndex: number;
  selectedItem: any;
}

@Component({
  selector: 'app-internal-delivery-order-create-line-item',
  templateUrl: './internal-delivery-order-create-line-item.component.html',
  styleUrls: ['./internal-delivery-order-create-line-item.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalDeliveryOrderCreateLineItemComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Internal Delivery Order Create Line Item';
  protected readonly index = 4;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deactivateSOList$ = this.componentStore.select(state => state.deactivateSOList);
  readonly deactivateQuotationList$ = this.componentStore.select(state => state.deactivateQuotationList);
  readonly deactivateInvoiceList$ = this.componentStore.select(state => state.deactivateInvoiceList);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);

  toggleColumn$ = this.viewColFacade.toggleColumn$;
  draft$ = this.draftStore.select(HDRSelectors.selectHdr);
  LinkSelectors = LinkSelectors;
  LinkActions = LinkActions;
  PNSSelectors = PNSSelectors;
  PNSActions = PNSActions;

  stgrnAdditionalColumns = [
    {
      headerName: "Location From",
      field: "location_from",
      cellStyle: () => ({ "text-align": "left"})
    },
    {
      headerName: "Location To",
      field: "location_to",
      cellStyle: () => ({ "text-align": "left"})
    },
    {
      headerName: "Tracking Id",
      field: "tracking_id",
      cellStyle: () => ({ "text-align": "left"})
    }
  ];

  soAdditionalColumns = [
    {
      headerName: "Branch",
      field: "branch",
      cellStyle: () => ({ "text-align": "left"})
    },
    {
      headerName: "Location From",
      field: "location_from",
      cellStyle: () => ({ "text-align": "left"})
    },
    {
      headerName: "Location To",
      field: "location_to",
      cellStyle: () => ({ "text-align": "left"})
    }
  ];

  additionalColDef = 
  [
    { headerName: 'Tracking ID', field: 'tracking_id', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Doc Ref', field: 'doc_ref', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Doc Description', field: 'doc_desc', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Location Sending', field: 'location1', cellStyle: () => ({ 'text-align': 'left' }) },
    { headerName: 'Location Receiving', field: 'location2', cellStyle: () => ({ 'text-align': 'left' }) },

  ]




  prevIndex: number;
  protected prevLocalState: any;
  apiVisa = AppConfig.apiVisa;
  AppletConstants = AppletConstants;
  koBySalesOrder = false;
  koForSalesOrder = false;
  koBySTGRN = false;
  koForSTGRN = false;

  //need change
  internalInboundStockTransferSearchModel = internalInboundDeliveryOrderSearchModel;
  internalOutboundStockTransferSearchModel = internalOutboundDeliveryOrderSearchModel;
  internalSalesOrderSearchModel = salesLineItemSearchModel;
  internalPurchaseGRNSearchModel = purchaseGRNLineItemSearchModel;

  @ViewChild(MatTabGroup, { static: true } ) matTab: MatTabGroup;
  compGuid: any;
  koSalesOrder: boolean;
  koSTGRN: boolean;

  constructor(
    public sessionStore: Store<SessionStates>,
    public InternalInboundStockTransferService: InternalInboundStockTransferService,
    public InternalOutboundDeliveryOrderService: InternalOutboundDeliveryOrderService,
    public InternalSalesOrderService: InternalSalesOrderService,
    public InternalPurchaseGoodsReceivedNotes: InternalPurchaseGoodsReceivedNotes,
    private viewColFacade: ViewColumnFacade,
    public readonly draftStore: Store<DraftStates>,
    private readonly store: Store<InternalDeliveryOrderStates>,
    private readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
      console.log("local state", this.localState)
    });
    // this.subs.sink = this.sessionStore.select(SessionSelectors.selectMasterSettings).subscribe({next: (resolve: AppletSettings) => {
    //   console.log("resolve", resolve);
    //   this.koBySalesOrder = (resolve?.KNOCK_OFF_BY_SALES_ORDER)?? this.koBySalesOrder;
    //   this.koBySTGRN = (resolve?.KNOCK_OFF_BY_ST_GRN)?? this.koBySTGRN;
    //   this.koForSalesOrder = (resolve?.KNOCK_OFF_FOR_SALES_ORDER)?? this.koForSalesOrder;
    //   this.koForSTGRN = (resolve?.KNOCK_OFF_FOR_ST_GRN)?? this.koForSalesOrder;
    //  }});

     this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.selectCompanyGuid).subscribe(guid=>{
      this.compGuid = guid;
    })

    this.sessionStore.dispatch(SessionActions.getKOSettingsInit({compGuid:this.compGuid, serverDoc2: ServerDocTypeConstants.INTERNAL_OUTBOUND_DELIVERY_ORDER }))

    this.subs.sink = this.sessionStore.select(SessionSelectors.selectKOSettings).subscribe(response=>{
      console.log(response);
      if(response!==null){
        response.forEach(setting=>{
          if(setting.bl_fi_comp_gendoc_flow_config.server_doc_type_1 === ServerDocTypeConstants.INTERNAL_SALES_ORDER && setting.bl_fi_comp_gendoc_flow_config.flow_type=="LINE"){
            this.koForSalesOrder = setting.bl_fi_comp_gendoc_flow_config.is_enabled;
          }

          if(setting.bl_fi_comp_gendoc_flow_config.server_doc_type_1 === ServerDocTypeConstants.INTERNAL_INBOUND_STOCK_TRANSFER && setting.bl_fi_comp_gendoc_flow_config.flow_type=="LINE"){
            this.koForSTGRN = setting.bl_fi_comp_gendoc_flow_config.is_enabled;
          }
  
        })
      }
    })
  }

  onAdd(lineItem: any){
    
    this.store.dispatch(InternalDeliveryOrderActions.selectLineItemInit({ lineItem: lineItem }));
    this.store.dispatch(InternalDeliveryOrderActions.selectPricingSchemeLink({ item: lineItem }));
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateList: false,
      selectedItem: lineItem.item_guid,
    });
    this.viewColFacade.onNextAndReset(this.index, 12);
  }

  onReturn() {
    console.log("return");
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onNextSOList(entity) {
    // this.store.dispatch(InternalPackingOrderActions.selectPackingOrderEntity({entity}));
    if (!this.localState.deactivateSOList) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateReturn: true,
        deactivateSOList: true,
        deactivateQuotationList: false,
        deactivateInvoiceList: false,
      });
      this.viewColFacade.onNextAndReset(this.index, 5);
    }
  }

  goToAddLineItem(lineItem: any) {
    this.store.dispatch(InternalDeliveryOrderActions.selectLineItemInit({ lineItem }));
    this.store.dispatch(InternalDeliveryOrderActions.selectPricingSchemeLink({ item: lineItem }));
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, 
      deactivateList: false,
      deactivateReturn: true,
      selectedItem: lineItem.item_guid
    });
    this.viewColFacade.onNextAndReset(this.index, 12);
  }

  saveSerialNumber(serial: string) {
    this.store.dispatch(InternalDeliveryOrderActions.selectSerial({ serial }));
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {...this.localState, selectedIndex: this.matTab.selectedIndex});
    }
    this.subs.unsubscribe();
  }

}
