const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/user-management-applet/runtime-es2015.js',
    './dist/user-management-applet/polyfills-es2015.js',
    './dist/user-management-applet/scripts.js',
    './dist/user-management-applet/main-es2015.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/user-management-applet');
  await concat(files, './elements/akaun-platform/applets/user-management-applet/user-management-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
