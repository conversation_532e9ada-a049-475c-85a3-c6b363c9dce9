<div class="view-col-forms no-tab" fxLayout="column">
    <div class="main-container">
      <div appDragDrop (dropEvent)="onChange($event.dataTransfer.files)" class="drop-zone">
        <input hidden #input type="file" multiple (change)="onChange($event.target.files)">
        <h3>
          Drag and drop your file anywhere or
        </h3>
        <label for="input" (click)="input.click()">Upload File</label>
      </div>
    </div>
    <!-- <div style="text-align: right;">
        <mat-checkbox (click)="onSelectAll()">Select All</mat-checkbox>
    </div>
    <fieldset>
        <legend>Serial Numbers</legend>
        <mat-selection-list #matList style="text-align: left; max-height: 30vh; overflow: auto;">
          <ng-container *ngFor="let s of serialNumbers">
            <mat-list-option [value]="s">{{s}}</mat-list-option>
            <mat-divider></mat-divider>
          </ng-container>
        </mat-selection-list>
    </fieldset>
    <div style="text-align: right; margin-top: 10px;">
      <button mat-raised-button color="warn" type="button" (click)="onRemove()">REMOVE</button>
    </div> -->
    <!-- <div class="grid-container">
      <div *ngFor="let f of files$ | async" class="img-container">
        <button style="position: absolute;" mat-icon-button color="warn" type="button" (click)="onDeleteFile(f)"><mat-icon>delete</mat-icon></button>
        <ng-container *ngIf="f.fileSRC === 'icon'; else preview">
          <mat-icon style="transform: translate(0px, 30px);" class="file-preview">description</mat-icon>
        </ng-container>
        <ng-template #preview>
          <img class="file-preview" [src]="f.fileSRC">
        </ng-template>
        <p class="file-name">{{f.fileAttributes.fileName}}</p>
      </div>
    </div> -->
  
  </div>
  