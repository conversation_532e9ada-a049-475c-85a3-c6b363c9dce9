import { createAction, props } from "@ngrx/store";
import { bl_fi_generic_doc_line_RowClass } from "blg-akaun-ts-lib";

export const addSettlementInit = createAction('[Draft: Settlement Edit] Add Settlement Init', props<{pageIndex: number, settlement: bl_fi_generic_doc_line_RowClass}>());
export const addSettlementSuccess = createAction('[Draft: Settlement Edit] Add Settlement Success', props<{settlement: bl_fi_generic_doc_line_RowClass}>());
export const addSettlementFailed = createAction('[Draft: Settlement Edit] Add Settlement Failed');

export const editSettlement = createAction('[Draft: Settlement Edit] Edit Settlement', props<{settlement: bl_fi_generic_doc_line_RowClass}>());
export const deleteSettlement = createAction('[Draft: Settlement Edit] Delete Settlement', props<{guid: string}>());
export const resetSettlementInit = createAction('[Draft: Settlement Edit] Reset Init');
export const resetSettlementSuccess = createAction('[Draft: Settlement Edit] Reset Success', props<{settlement: bl_fi_generic_doc_line_RowClass[]}>());

// export const resetHDRInit = createAction('[Draft: HDR Edit] Reset Init');
// export const resetHDRSuccess = createAction('[Draft: HDR Edit] Reset Success', props<{hdr: bl_fi_generic_doc_hdr_RowClass}>());
