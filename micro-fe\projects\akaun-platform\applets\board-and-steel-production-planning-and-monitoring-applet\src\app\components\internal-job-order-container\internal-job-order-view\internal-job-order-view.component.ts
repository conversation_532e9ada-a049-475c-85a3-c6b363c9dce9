import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass, GenericDocARAPContainerModel } from 'blg-akaun-ts-lib';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { combineLatest, EMPTY, iif, of } from 'rxjs';
import { delay, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { JobOrderMain, JobOrderNoDepartment } from '../../../models/internal-job-order.model';
import { HDREditActions, JobOrderNoEditActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDREditSelectors, JobOrderNoEditSelectors, PNSEditSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { InternalJobOrderCreateAccountComponent } from '../internal-job-order-create/internal-job-order-create-account/internal-job-order-create-account.component';
import { InternalJobOrderCreateLineItemsComponent } from '../internal-job-order-create/internal-job-order-create-line-items/internal-job-order-create-line-items.component';
import { InternalJobOrderCreateMainComponent } from '../internal-job-order-create/internal-job-order-create-main/internal-job-order-create-main.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateReturn: boolean;
  deactivateCustomer: boolean;
  deactivateSalesAgent: boolean;
  deactivateShippingInfo: boolean;
  deactivateBillingInfo: boolean;
  deactivateLineItem: boolean;
  deactivateSettlement: boolean;
  deactivateAddContra: boolean;
  deactivateViewContra: boolean;
  deactivateAddAttachments: boolean;
  selectedIndex: number;
  childSelectedIndex: number;
  selectedLineItemRowIndex: number;
  deleteConfirmation: boolean;
}

@Component({
  selector: 'app-internal-job-order-view',
  templateUrl: './internal-job-order-view.component.html',
  styleUrls: ['./internal-job-order-view.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class InternalJobOrderViewComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'View Job Order No';
  protected index = 1;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deactivateCustomer$ = this.componentStore.select(state => state.deactivateCustomer);
  readonly deactivateSalesAgent$ = this.componentStore.select(state => state.deactivateSalesAgent);
  readonly deactivateShippingInfo$ = this.componentStore.select(state => state.deactivateShippingInfo);
  readonly deactivateBillingInfo$ = this.componentStore.select(state => state.deactivateBillingInfo);
  readonly deactivateLineItem$ = this.componentStore.select(state => state.deactivateLineItem);
  readonly deactivateSettlement$ = this.componentStore.select(state => state.deactivateSettlement);
  readonly deactivateAddContra$ = this.componentStore.select(state => state.deactivateAddContra);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly childSelectedIndex$ = this.componentStore.select(state => state.childSelectedIndex);
  readonly deleteConfirmation$ = this.componentStore.select(state => state.deleteConfirmation);

  prevIndex: number;
  protected prevLocalState: any;

  draft$ = this.draftStore.select(HDREditSelectors.selectHdr);
  // attachments$ = this.store.select(InternalJobOrderSelectors.selectEntity).pipe(
  //   map(a => a.bl_fi_generic_doc_ext.filter(x => x.param_code === 'GEN_DOC_FILE'))
  // );
  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({...a, ...b})));
  deleteConfirmation = false;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(InternalJobOrderCreateAccountComponent) account: InternalJobOrderCreateAccountComponent;
  @ViewChild(InternalJobOrderCreateMainComponent) main: InternalJobOrderCreateMainComponent;
  @ViewChild(InternalJobOrderCreateLineItemsComponent) lines: InternalJobOrderCreateLineItemsComponent;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    private readonly sessionStore: Store<SessionStates>,
    protected readonly store: Store<InternalJobOrderStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly componentStore: ComponentStore<LocalState>
    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    // TODO: Optimize this
    this.subs.sink = this.deleteConfirmation$.pipe(
      mergeMap(a => {
        return iif(() => a, of(a).pipe(delay(3000)), of(EMPTY));
      })
    ).subscribe(resolve => {
      if (resolve === true) {
        this.componentStore.patchState({deleteConfirmation: false});
        this.deleteConfirmation = false;
      }
    });
  }

  onReset() {
    this.viewColFacade.resetDraft(this.index);
  }

  onSave() {
    this.store.dispatch(InternalJobOrderActions.editJobOrderNoInit());
  }

  onDelete() {
    if (this.deleteConfirmation) {
      this.store.dispatch(InternalJobOrderActions.deleteJobOrderNoInit());
      this.deleteConfirmation = false;
      this.componentStore.patchState({deleteConfirmation: false});
    } else {
      this.deleteConfirmation = true;
      this.componentStore.patchState({deleteConfirmation: true});
    }
  }

  disableButton() {
    return this.main?.form?.invalid;
  }

  onUpdateMain(form: JobOrderMain) {
    this.draftStore.dispatch(HDREditActions.updateMain({form}));
  }

  onGoupNameUpdated(e) {
    this.draftStore.dispatch(HDREditActions.updateGroupTemplate({groupGuid : e}));
  }

  onUpdateDepartment(form: JobOrderNoDepartment) {
    this.draftStore.dispatch(HDREditActions.updateDepartment({form}));
  }

  onPrint() {
    this.store.dispatch(InternalJobOrderActions.printJasperPdfInit());
  }

  onReturn() {
    this.store.dispatch(InternalJobOrderActions.selectEditMode({editMode: false}));
    this.store.dispatch(InternalJobOrderActions.selectTotalContainerQty({totalContainerQty : 0.00}))
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onAddAttachments() {
    if (!this.localState.deactivateAddAttachments) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateAdd: false,
        deactivateReturn: true,
        deactivateCustomer: false,
        deactivateShippingInfo: false,
        deactivateBillingInfo: false,
        deactivateSettlement: false,
        deactivateLineItem: false,
        deactivateAddContra: false,
        deactivateAddAttachments: true
      });
      this.viewColFacade.onNextAndReset(this.index, 12);
    }
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
      });
    }
    this.subs.unsubscribe();
  }

}
