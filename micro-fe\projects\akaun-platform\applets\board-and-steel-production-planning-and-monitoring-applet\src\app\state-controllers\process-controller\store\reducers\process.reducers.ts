import { Action, createReducer, on } from '@ngrx/store';
import { PlannedOutputActions } from '../../../draft-controller/store/actions';
import { ProcessActions } from '../actions';
import { initState, ProcessState } from '../states/process.states';

export const processFeatureKey = 'process';

export const processReducer = createReducer(
    initState,
    on(ProcessActions.mapProcess, (state, action) => ({
        ...state, processMap: {...state.processMap, [action.guid]: action.process}
    })),
    on(ProcessActions.selectProcess, (state, action) => ({
        ...state, selectedProcess: action.process
    })),
    on(ProcessActions.selectBinLine, (state, action) => ({
        ...state, selectedBinLine: action.binLine
    })),
    on(ProcessActions.selectCurrentProcess, (state, action) => ({
        ...state, currentProcess: action.process
    })),
    on(PlannedOutputActions.addPlannedOutput, (state, action) => ({
        ...state, selectedJobOrderNo: null
    })),
    on(ProcessActions.updateProcessForm, (state, action) => ({
        ...state, updatedProcessForm: action.form
    })),
);

export function reducer(state: ProcessState | undefined, action: Action) {
    return processReducer(state, action);
}
