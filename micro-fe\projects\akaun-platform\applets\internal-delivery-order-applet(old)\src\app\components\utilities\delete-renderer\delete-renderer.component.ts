import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams } from 'ag-grid-community';

@Component({
  selector: 'app-delete-renderer',
  templateUrl: './delete-renderer.component.html',
  styleUrls: ['./delete-renderer.component.css']
})
export class DeleteRendererComponent implements ICellRendererAngularComp {

  params;

  agInit(params: ICellRendererParams) {
    this.params = params;
  }

  refresh(params: ICellRendererParams): boolean {
    return true;
  }

  onDelete() {
    this.params.onClick(this.params.node.data);
  }
}
