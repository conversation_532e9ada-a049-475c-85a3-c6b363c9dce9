import { Component, ChangeDetectionStrategy, Output, EventEmitter, Input } from '@angular/core';
import { Store } from '@ngrx/store';
import { ComponentStore } from '@ngrx/component-store';
import { bl_mrp_process_template_bom_RowClass, CompanyContainerModel, CompanyService, InventoryItemService, MrpProcessTemplateBOMService, MrpProcessTemplateContainerModel, MrpProdsysContainerModel, MrpProdsysLinkContainerModel, MrpProdsysLinkService, MrpProdsysService, Pagination } from 'blg-akaun-ts-lib';
import { forkJoin, iif, Observable, of, zip } from 'rxjs';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/facades/view-column.facade';
import { AppConfig } from 'projects/shared-utilities/visa';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { InternalJobOrderStates } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/states';
import { InternalJobOrderSelectors } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/internal-job-order-controller/store/selectors';
import { catchError, map } from 'rxjs/operators';


interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}


@Component({
  selector: 'app-input-summary',
  templateUrl: './input-summary.component.html',
  styleUrls: ['./input-summary.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InputSummaryComponent extends ViewColumnComponent {
  
  @Input() localState: any;  
  @Input() draft$: Observable<MrpProcessTemplateContainerModel>;
  @Output() editBOM = new EventEmitter();
  @Output() createBOM = new EventEmitter();

  toggleColumn$: Observable<boolean>;
  pagination = new Pagination();
  pagination2 = new Pagination();

  templateGuid: string;

  SQLGuids: string[] = null;


  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Item Name', field: 'name', comparator: (valueA, valueB) =>
    valueA.toLowerCase().localeCompare(valueB.toLowerCase()), width: 110, suppressSizeToFit: true,
    cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Code', field: 'code', comparator: (valueA, valueB) =>
    valueA.toLowerCase().localeCompare(valueB.toLowerCase()), width: 90, suppressSizeToFit: true,
    cellStyle: () => ({'text-align': 'left'})},

    {headerName: 'Measurement', field: 'ratio', comparator: (valueA, valueB) =>
    valueA.toLowerCase().localeCompare(valueB.toLowerCase()), width: 110, suppressSizeToFit: true,
    cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'UOM', field: 'uom', comparator: (valueA, valueB) =>
    valueA.toLowerCase().localeCompare(valueB.toLowerCase()), width: 110, suppressSizeToFit: true,
    cellStyle: () => ({'text-align': 'left'})},
  ];
  
  frameworkComponents;

  rowData = [];

  private subs = new SubSink();
  process_template_hdr_guid;


  constructor( 
    private viewColFacade: ViewColumnFacade,
    private mrpProcessTemplateBOMService: MrpProcessTemplateBOMService,
    private inventoryItemService: InventoryItemService,
    private readonly componentStore: ComponentStore<LocalState>,
    private readonly store: Store<InternalJobOrderStates>,

 ) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.store.select(InternalJobOrderSelectors.selectProcessInstance).subscribe(data => {
      console.log("Process instance data",data);
      this.process_template_hdr_guid = data.process_template_hdr_guid;
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();  
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: "calcTotalRecords", operator: "=", value: "true" },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
          { columnName: "bl_mrp_process_template_hdr_guid", operator: "=", value: this.process_template_hdr_guid },
          { columnName: 'txn_type', operator: '=', value: 'INPUT' },

          {
            columnName: "guids",
            operator: "=",
            value: this.SQLGuids
              ? this.SQLGuids.slice(
                grid.request.startRow,
                grid.request.endRow
              ).toString()
              : "",
          },
        ];
        let totalrec = 0;
        this.mrpProcessTemplateBOMService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {

            totalrec = resolved.totalRecords;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(itemperm => source.push(
              zip(
                this.inventoryItemService.getByGuid(itemperm.bl_mrp_process_template_bom.inv_item_hdr_guid.toString(), apiVisa).pipe(
                  catchError((err) => of(err))
                ),

                ).pipe(
                  map(([b_a]) => {
                    console.log("B_a",b_a);
                    let obj = {"guid":'', "name": '', "code": '', "ratio": '',"uom" : ''};
                    obj.guid = itemperm.bl_mrp_process_template_bom.guid.toString();
                    obj.name = b_a ? b_a.data.bl_inv_mst_item_hdr.name : '';
                    obj.code = itemperm.bl_mrp_process_template_bom.code.toString();
                    obj.ratio = itemperm.bl_mrp_process_template_bom.ratio.toString();
                    obj.uom = itemperm.bl_mrp_process_template_bom.uom.toString();

                    return obj;
                  })
                )
            )
            );
            return iif(() => resolved.totalRecords > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
      if (resolved) {
        this.gridApi.refreshServerSideStore({ purge: true });
      }
    });
  }

  // setGridData() {
  //   const apiVisa = AppConfig.apiVisa;
  //   const datasource = {
  //     getRows: (grid) => {

  //       const filter = pageFiltering(grid.request.filterModel);
  //       const sortOn = pageSorting(grid.request.sortModel);
  //       this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
  //       this.pagination.limit = grid.request.endRow - grid.request.startRow;
  //       this.pagination.conditionalCriteria = [
  //         { columnName: "calcTotalRecords", operator: "=", value: "true" },
  //         // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
  //         // { columnName: 'order', operator: '=', value: 'DESC' },
  //         { columnName: "bl_mrp_process_template_hdr_guid", operator: "=", value: this.process_template_hdr_guid },
  //         { columnName: 'txn_type', operator: '=', value: 'INPUT' },

  //         {
  //           columnName: "guids",
  //           operator: "=",
  //           value: this.SQLGuids
  //             ? this.SQLGuids.slice(
  //               grid.request.startRow,
  //               grid.request.endRow
  //             ).toString()
  //             : "",
  //         },
  //       ];
  //       this.subs.sink = this.mrpProcessTemplateBOMService
  //         .getByCriteria(this.pagination, apiVisa)
  //         .subscribe(
  //           (resolved) => {

  //             const data = sortOn(resolved.data).filter((entity) =>
  //               filter.by(entity)
  //             );
  //             const totalRecords = filter.isFiltering
  //               ? this.SQLGuids
  //                 ? this.SQLGuids.length
  //                 : resolved.totalRecords
  //               : data.length;
  //             console.log("Inpurt ",data);
  //             grid.success({
  //               rowData: data,
  //               rowCount: totalRecords,
  //             });
  //             this.gridApi.forEachNode((node) => {
  //               if (
  //                 node.data?.app_tenant_hdr.guid === this.localState.selectedRow
  //               )
  //                 node.setSelected(true);
  //             });
  //           },
  //           (err) => {
  //             grid.fail();

  //           }
  //         );
  //     },
  //   };
  //   this.gridApi.setServerSideDatasource(datasource);
  //   this.gridApi.refreshServerSideStore();

  //   // this.subs.sink = this.store
  //   //   .select(AuditTrailSelectors.updateAgGrid)
  //   //   .subscribe((resolved) => {
  //   //     if (resolved) {
  //   //       this.store.dispatch(
  //   //         AuditTrailActions.updateAgGridDone({ done: false })
  //   //       );
  //   //     }
  //   //   });
  // }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }


  onSearch(e: string) {
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
