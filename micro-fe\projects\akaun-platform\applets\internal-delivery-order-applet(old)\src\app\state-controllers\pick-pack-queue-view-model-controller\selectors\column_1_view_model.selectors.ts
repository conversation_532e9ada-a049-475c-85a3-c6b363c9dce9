import { createFeatureSelector, createSelector } from '@ngrx/store';
import { ColumnViewModelStates } from '../states';
import { columnViewModelFeatureKey } from '../reducers';

export const ViewModelSelector = createFeatureSelector<ColumnViewModelStates>(columnViewModelFeatureKey);

export const selectAdvanceSearch_Customer_Field = createSelector(
   ViewModelSelector, 
  (state: ColumnViewModelStates) => state.column1ViewModel.advanceSearch_Customer_Field
);

export const selectAdvanceSearch_SalesAgent_Field = createSelector(
  ViewModelSelector, 
 (state: ColumnViewModelStates) => state.column1ViewModel.advanceSearch_SalesAgent_Field
);

export const selectAdvanceSearch_DeliveryRegion_Field = createSelector(
  ViewModelSelector, 
 (state: ColumnViewModelStates) => state.column1ViewModel.advanceSearch_DeliveryRegion_Field
);

export const selectAdvanceSearch_Branch_Field = createSelector(
  ViewModelSelector, 
 (state: ColumnViewModelStates) => state.column1ViewModel.advanceSearch_Branch_Field
);

export const selectAdvanceSearch_CreationDateFrom_Field = createSelector(
  ViewModelSelector, 
 (state: ColumnViewModelStates) => state.column1ViewModel.advanceSearch_CreationDateFrom_Field
);

export const selectAdvanceSearch_CreationDateTo_Field = createSelector(
  ViewModelSelector, 
 (state: ColumnViewModelStates) => state.column1ViewModel.advanceSearch_CreationDateTo_Field
);

export const selectAdvanceSearch_TransactionDateFrom_Field = createSelector(
  ViewModelSelector, 
 (state: ColumnViewModelStates) => state.column1ViewModel.advanceSearch_TransactionDateFrom_Field
);

export const selectAdvanceSearch_TransactionDateTo_Field = createSelector(
  ViewModelSelector, 
 (state: ColumnViewModelStates) => state.column1ViewModel.advanceSearch_TransactionDateTo_Field
);