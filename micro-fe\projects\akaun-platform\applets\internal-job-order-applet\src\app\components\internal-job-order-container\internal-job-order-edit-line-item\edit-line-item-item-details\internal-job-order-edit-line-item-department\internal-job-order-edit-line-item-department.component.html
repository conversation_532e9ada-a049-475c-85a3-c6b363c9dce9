
<form [formGroup]="form">
  <div class="view-col-forms">
    <div style="text-align: left;">
      <mat-checkbox [formControl]="copyFromHdr">Copy from Hdr</mat-checkbox>
    </div>
    <div fxLayout="row" fxLayoutGap="5px">
      <div fxFlex="50" fxLayout="column">
        <div *ngFor="let x of leftColControls; let i = index">
          <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
            <mat-label>{{x.label}}</mat-label>
            <input matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
            <mat-hint>{{x.hint}}</mat-hint>
          </mat-form-field>
          <ng-template #input>
            <ng-container [ngSwitch]="x.type">
              <mat-form-field *ngSwitchCase="'segment'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <mat-select [formControl]="form.controls[x.formControl]">
                  <mat-option *ngFor="let y of segment" [value]="y.bl_fi_mst_segment.guid">{{y.bl_fi_mst_segment.code}} | {{y.bl_fi_mst_segment.name}}</mat-option>
                </mat-select>
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'profitCenter'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <mat-select [formControl]="form.controls[x.formControl]">
                  <mat-option *ngFor="let y of profitCenter" [value]="y.bl_fi_mst_profit_center.guid">{{y.bl_fi_mst_profit_center.code}} | {{y.bl_fi_mst_profit_center.name}}</mat-option>
                </mat-select>
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
            </ng-container>
          </ng-template>
        </div>
      </div>
      <div fxFlex="50" fxLayout="column">
        <div *ngFor="let x of rightColControls; let i = index">
          <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
            <mat-label>{{x.label}}</mat-label>
            <input  matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
            <mat-hint>{{x.hint}}</mat-hint>
          </mat-form-field>
          <ng-template #input>
            <ng-container [ngSwitch]="x.type">
              <mat-form-field *ngSwitchCase="'dimension'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <mat-select [formControl]="form.controls[x.formControl]">
                  <mat-option *ngFor="let y of dimension" [value]="y.bl_fi_mst_gl_dimension.guid">{{y.bl_fi_mst_gl_dimension.code}} | {{y.bl_fi_mst_gl_dimension.name}}</mat-option>
                </mat-select>
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
              <mat-form-field *ngSwitchCase="'project'" appearance="outline">
                <mat-label>{{x.label}}</mat-label>
                <mat-select [formControl]="form.controls[x.formControl]">
                  <mat-option *ngFor="let y of project" [value]="y.bl_fi_mst_project.guid">{{y.bl_fi_mst_project.code}} | {{y.bl_fi_mst_project.name}}</mat-option>
                </mat-select>
                <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
              </mat-form-field>
            </ng-container>
          </ng-template>
        </div>
      </div>
    </div>
  </div>
</form>
