import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
// import { Store } from '@ngrx/store';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { BranchService, GenericDocContainerModel, InternalOutboundDeliveryOrderService, LocationService, Pagination, SubQueryService, ThemeInstanceExtensionContainerModel } from 'blg-akaun-ts-lib';
import * as moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { forkJoin, iif, Observable, of, zip } from 'rxjs';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { internalPackingOrderSearchModel } from '../../../models/advanced-search-models/internal-packing-order.model';
import { EntityActions } from '../../../state-controllers/customer-controller/store/actions';
import { EntitySelectors } from '../../../state-controllers/customer-controller/store/selectors';
import { EntityStates } from '../../../state-controllers/customer-controller/store/states';
import { InternalDeliveryOrderActions } from '../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderSelectors } from '../../../state-controllers/internal-delivery-order-controller/store/selectors';
import { InternalDeliveryOrderStates } from '../../../state-controllers/internal-delivery-order-controller/store/states';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

@Component({
  selector: 'app-internal-delivery-order-listing',
  templateUrl: './internal-delivery-order-listing.component.html',
  styleUrls: ['./internal-delivery-order-listing.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class InternalDeliveryOrderListingComponent extends ViewColumnComponent {
  @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

  protected subs = new SubSink();

  protected compName = 'Internal Sales Order Listing';
  protected readonly index = 0;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  toggleColumn$: Observable<boolean>;
  searchModel = internalPackingOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;
  SQLGuids: string[] = null;
  selectEntity$: any;
  entity: string;
  pagination = new Pagination();

  columnsDefs = [
    {
      headerName: 'Doc No', field: 'bl_fi_generic_doc_hdr.server_doc_1', checkboxSelection: true,
      cellStyle: () => ({ 'text-align': 'left' }), floatingFilter: true,
      valueGetter: (params) => { return parseInt(params.data.bl_fi_generic_doc_hdr.server_doc_1) },
      sort: "desc"
    },  
    {headerName: 'Posting Status', field: 'bl_fi_generic_doc_hdr.posting_status', cellStyle: () => ({'text-align': 'left'}),
    valueFormatter: params => params.value? params.value: "DRAFT"},
    {headerName: 'Branch', field: 'bl_fi_generic_doc_hdr.code_branch', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Location', field: 'bl_fi_generic_doc_hdr.code_location', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Entity Name', field: 'bl_fi_generic_doc_hdr.doc_entity_hdr_json', cellStyle: () => ({'text-align': 'left'}),
    valueFormatter: params => params.value?.entityName},
    {headerName: 'Tracking Id', field: 'bl_fi_generic_doc_hdr.doc_entity_hdr_json.trackingID', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Status', field: 'bl_fi_generic_doc_hdr.status', cellStyle: () => ({'text-align': 'left'})},
    {
      headerName: 'Updated Date', field: 'bl_fi_generic_doc_hdr.updated_date', type: 'rightAligned',
      valueFormatter: params => moment(params.value).format('YYYY-MM-DD'), floatingFilter: true
    },
    {
      headerName: 'Created Date', field: 'bl_fi_generic_doc_hdr.created_date', type: 'rightAligned',
      valueFormatter: params => moment(params.value).format('YYYY-MM-DD'), floatingFilter: true
    },
  ];
  deliveryOrders$: Observable<GenericDocContainerModel[]>;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected doService: InternalOutboundDeliveryOrderService,
    private subQueryService: SubQueryService,
    protected brchService: BranchService,
    protected lctnService: LocationService,
    private readonly store: Store<InternalDeliveryOrderStates>,
    protected readonly componentStore: ComponentStore<LocalState>,
    private entityStore: Store<EntityStates>,
    private toastr: ToastrService
    ) {
    super();
  }

  ngOnInit() {
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.refreshGenDocListing).subscribe(a => {
      if (a) {
        this.store.dispatch(InternalDeliveryOrderActions.loadDeliveryOrderInit({pagination: new Pagination(0,50)}));
        this.store.dispatch(InternalDeliveryOrderActions.resetAgGrid());
      }
    });

  }

  onNext() {
    this.store.dispatch(InternalDeliveryOrderActions.resetDeliveryOrder());
    this.store.dispatch(InternalDeliveryOrderActions.resetDraft());
    this.store.dispatch(InternalDeliveryOrderActions.setEditMode({ editMode: false }));
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: false});
    this.viewColFacade.onNextAndReset(this.index, 3);
  }
  
  onPostToFinal(){
    let arr =[];
    const json = {posting_status : "FINAL"};

    let selectedRows = this.gridApi.getSelectedRows();
    console.log("selectedRows::",selectedRows);
    selectedRows.map((row) => {
      //console.log("selectedRows::", row.bl_fi_generic_doc_hdr.guid);
      // let url: String = environment.api_domain + 
      arr.push(row);
    })
    arr = arr.filter(x => x.bl_fi_generic_doc_hdr.posting_status != "FINAL");
    arr.map(
      e => {
        let temp: GenericDocContainerModel = {
          bl_fi_generic_doc_hdr: e.bl_fi_generic_doc_hdr,
          bl_fi_generic_doc_event: e.bl_fi_generic_doc_event,
          bl_fi_generic_doc_ext: e.bl_fi_generic_doc_ext,
          bl_fi_generic_doc_link: e.bl_fi_generic_doc_link,
          bl_fi_generic_doc_line: e.bl_fi_generic_doc_line
        };
        //console.log("Generic Doc Container: ", temp);
        this.store.dispatch(InternalDeliveryOrderActions.updatePostingStatusInit({status: json, doc: temp}));
      });
      this.gridApi.refreshServerSideStore();
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.deliveryOrders$ = this.store.select(InternalDeliveryOrderSelectors.selectDeliveryOrders);

    // const datasource = {
    //   getRows: grid => {

    //     const sortCriteria : any = [];
    //     const pagination = new Pagination(grid.request.startRow, grid.request.endRow - grid.request.startRow);
    //     if (this.SQLGuids){
    //       pagination.conditionalCriteria = [
    //         { columnName: 'calcTotalRecords', operator: '=', value: 'true'}, 
    //         { columnName: "orderBy", operator: "=", value: "updated_date" },
    //         { columnName: "order", operator: "=", value: "DESC" },
    //         { columnName: "guids", operator: "=", value: this.SQLGuids?.slice(grid.request.startRow, grid.request.endRow).toString()}  
    //       ]
    //     }
    //     else {
    //       pagination.conditionalCriteria = [{columnName: 'calcTotalRecords', operator: '=', value: 'true'}];
    //     }

    //     // this.store.dispatch(InternalPackingOrderActions.loadPackingOrdersInit({request: grid.request}));
    //     this.subs.sink = this.doService.getByCriteria(pagination, apiVisa).pipe(
    //       mergeMap(b => {
    //         const source: Observable<GenericDocContainerModel>[] = [];
    //         b.data.forEach( doc => 
    //           source.push(
    //           zip(
    //             // this.compService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_comp.toString(), apiVisa).pipe(
    //             //   catchError((err) => of(err))
    //             // ),
    //             this.brchService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_branch.toString(), apiVisa).pipe(
    //               catchError((err) => of(err))
    //             ),
    //             this.lctnService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_store.toString(), apiVisa).pipe(
    //               catchError((err) => of(err))
    //             )).pipe(
    //               map(([b_b, b_c]) => {
    //                 // doc.bl_fi_generic_doc_hdr.guid_comp = b_a.error ? b_a.error.code : b_a.data.bl_fi_mst_comp.name;
    //                 doc.bl_fi_generic_doc_hdr.code_branch = b_b.error ? b_b.error.code : b_b.data.bl_fi_mst_branch.name;
    //                 doc.bl_fi_generic_doc_hdr.code_location = b_c.error ? b_c.error.code : b_c.data.bl_inv_mst_location.name;
    //                 return doc;
    //               })
    //             )
    //         ));
    //         return iif(() => b.data.length > 0,
    //           forkJoin(source).pipe(map(() => b)),
    //           of(b)
    //         );
    //       })
    //     ).subscribe( resolved => {
    //       // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderSuccess({totalRecords: resolved.totalRecords}));
    //       grid.successCallback(resolved.data, resolved.totalRecords);
    //     }, err => {
    //       // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderFailed({error: err.message}));
    //       grid.failCallback();
    //     });
    //   }
    // };
    // this.gridApi.setServerSideDatasource(datasource);
    // this.subs.sink = this.store.select(InternalDeliveryOrderSelectors.selectAgGrid).subscribe( a => {
    //   if (a) {
    //     this.gridApi.refreshServerSideStore();
    //     this.store.dispatch(InternalDeliveryOrderActions.resetAgGrid());
    //   }
    // });
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(entity: GenericDocContainerModel) {

    this.store.dispatch(InternalDeliveryOrderActions.selectCompanyGuid({ compGuid: entity.bl_fi_generic_doc_hdr?.guid_comp.toString() }));
    this.store.dispatch(InternalDeliveryOrderActions.resetDeliveryOrder());
    this.store.dispatch(InternalDeliveryOrderActions.resetDraftEdit());
    if (entity) {
      // this.entityStore.dispatch(EntityActions.selectedEntity({entityType: entity.bl_fi_generic_doc_hdr?.doc_entity_hdr_json["chosenEntity"]?.toString()}))
      this.store.dispatch(InternalDeliveryOrderActions.selectEntityInit({entity, entityType: entity.bl_fi_generic_doc_hdr?.doc_entity_hdr_json["chosenEntity"]?.toString()}));
      this.store.select(InternalDeliveryOrderSelectors.selectEntity)
      if (!this.localState.deactivateList) {
        this.store.dispatch(InternalDeliveryOrderActions.setEditMode({ editMode: true }));
        this.store.dispatch(InternalDeliveryOrderActions.setEditMode({ editMode: true }));
        this.viewColFacade.updateInstance(this.index, {
          ...this.localState, deactivateAdd: false, deactivateList: true});
        this.viewColFacade.onNextAndReset(this.index, 1);
      }
    }
  }

  onSearch(e: SearchQueryModel) {
    if (!e.isEmpty) {
      const sql = {
        subquery: e.queryString,
        table: e.table
      };
      this.subs.sink = this.subQueryService.post(sql, AppConfig.apiVisa).subscribe({
        next: resolve => {
          this.SQLGuids = resolve.data;
          if(this.SQLGuids.length!==0 || this.SQLGuids.length<=50){
            console.log("true");
            // this.gridApi.refreshServerSideStore();
            this.pagination.conditionalCriteria = [
              {
                columnName: 'guids', operator: '=',
                value: this.SQLGuids.toString()
              }
            ];
            this.store.dispatch(InternalDeliveryOrderActions.loadDeliveryOrderInit({pagination: this.pagination}))
          }else
          {
            this.toastr.error("Result Set Too Large. Please Refine Search", "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 2000,
            });
          }
        }
      });
    } else {
      this.SQLGuids = null;
      this.store.dispatch(InternalDeliveryOrderActions.loadDeliveryOrderInit({pagination: new Pagination(0,50)}))
      // this.gridApi.refreshServerSideStore();
    }
  }
  
  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
