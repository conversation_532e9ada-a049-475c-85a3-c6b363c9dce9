.search-box .mat-form-field-wrapper{
  padding-bottom: 0px;
  border-radius: 5px;
  box-shadow: 0 1px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14), 0 1px 5px 0 rgba(0, 0, 0, 0.12);
}
.advanced-search-container .mat-form-field-wrapper{
  padding-bottom: 0px;
}
.advanced-search-container {
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  // z-index: 1;
  box-shadow: 0 1px 1px -2px rgba(0, 0, 0, 0.2),
  0 2px 2px 0 rgba(0, 0, 0, 0.14),
  0 1px 5px 0 rgba(0, 0, 0, 0.12);
  // position: absolute;
  border: solid 1px #ccc;
  // display: none;
  height: fit-content !important;
}
.advanced-search-form {
  max-height: 40vh;
  overflow: auto;
  padding-right: 15px;
}
