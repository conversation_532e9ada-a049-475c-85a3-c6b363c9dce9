import { Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import {
  bl_fi_generic_doc_ext_RowClass,
  BranchService,
  GenericDocContainerModel, GenericDocLineContainerModel, GenericDocSingleLineContainer, GenericDocSingleLineService, InternalJobOrderService, MrpCreateProcessInstanceService, MrpJobOrderGenDocLinkContainerModel, MrpJobOrderGenDocLinkService, MrpJobOrderHdrContainerModel, MrpJobOrderHdrService, MrpProcessInstanceService
} from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { of } from 'rxjs';
import { catchError, concatMap, exhaustMap, map, mergeMap, withLatestFrom } from 'rxjs/operators';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { BatchModel } from '../../../../models/batch.model';
import { ToastConstants } from '../../../../models/constants/toast.constants';
import { HDREditSelectors, HDRSelectors, JobOrderNoEditSelectors, JobOrderNoSelectors, PlannedInputSelectors, PlannedOutputSelectors } from '../../../draft-controller/store/selectors';
import { DraftStates } from '../../../draft-controller/store/states';
import { ProcessSelectors } from '../../../process-controller/store/selectors';
import { ProcessStates } from '../../../process-controller/store/states';
import { UUID } from 'angular2-uuid';
import { ToastrService } from 'ngx-toastr';
import { SalesOrderStates } from '../states';
import { SalesOrderActions } from '../actions';
import { SalesOrderSelectors } from '../selectors';

// TODO: error handling
@Injectable()
export class SalesOrderEffects {

  apiVisa = AppConfig.apiVisa;

  // createJobOrderNo$ = createEffect(() => this.actions$.pipe(
  //   ofType(InternalJobOrderActions.createJobOrderNoInit),
  //   withLatestFrom(
  //     this.store.select(HDRSelectors.selectHdr)
  //   ),
  //   map(([action, jobOrderNo]) => {
  //     const container = new MrpJobOrderHdrContainerModel();
  //     container.bl_mrp_job_order_hdr = jobOrderNo;
  //     container.bl_mrp_job_order_hdr.guid = UUID.UUID().toLowerCase();
  //     return container;
  //   }),
  //   // exhaustMap((a) => this.branchService.getByGuid(a.bl_mrp_job_order_hdr.guid_branch, this.apiVisa).pipe(
  //   //   map(b_inner => {
  //   //     a.bl_mrp_job_order_hdr.guid_comp = b_inner.data.bl_fi_mst_branch.comp_guid;
  //   //     return a;
  //   //   })
  //   // )),
  //   exhaustMap(b => this.mrpJobOrderHdrService.postOne(b, this.apiVisa).pipe(
  //     map(b_a => {
  //       this.viewColFacade.showSuccessToast(ToastConstants.createJobOrderNoSuccess);
  //       this.viewColFacade.updateInstance(0, {
  //         deactivateAdd: false,
  //         deactivateList: false,
  //         selectedRowIndex: null
  //       });
  //       this.viewColFacade.resetIndex(0);
  //       this.viewColFacade.resetDraft(2);
  //       return InternalJobOrderActions.createJobOrderNoSuccess({entity: b_a.data});
  //     })
  //   )),
  // ));

  editSalesOrder$ = createEffect(() => this.actions$.pipe(
    ofType(SalesOrderActions.updateSalesOrder),
    withLatestFrom(
      this.store.select(SalesOrderSelectors.selectForm)
    ),
    exhaustMap(([action, jobOrderNo]) => this.mrpJobOrderGenDocLinkService.getByGuid(jobOrderNo.guid, this.apiVisa).pipe(
      map(a => {
        const container = new MrpJobOrderGenDocLinkContainerModel();
        container.bl_mrp_job_order_generic_doc_link = a.data.bl_mrp_job_order_generic_doc_link;
        container.bl_mrp_job_order_generic_doc_link.scenario_option = jobOrderNo.scenario_option;
        container.bl_mrp_job_order_generic_doc_link.job_doc_state = jobOrderNo.status;

        return container;
      })
    )),
    exhaustMap(a => this.mrpJobOrderGenDocLinkService.put(a, this.apiVisa).pipe(
      map(a_a => {
        this.viewColFacade.showSuccessToast(ToastConstants.updateSalesOrderSuccess);
        this.viewColFacade.updateInstance(0, {
          deactivateAdd: false,
          deactivateList: false,
          selectedRowIndex: null
        });
        this.viewColFacade.resetIndex(0);
        this.viewColFacade.resetDraft(2);
        return SalesOrderActions.updateSalesOrderSuccess();
      })
    ))
  ));

  // deleteJobOrderNo$ = createEffect(() => this.actions$.pipe(
  //   ofType(InternalJobOrderActions.deleteJobOrderNoInit),
  //   withLatestFrom(
  //     this.store.select(JobOrderNoEditSelectors.selectJobOrderNo)
  //   ),
  //   exhaustMap(([action, jobOrderNo]) => this.mrpJobOrderHdrService.delete(jobOrderNo.guid, this.apiVisa).pipe(
  //     map(a => {
  //       this.viewColFacade.showSuccessToast(ToastConstants.deleteJobOrderNoSuccess);
  //       this.viewColFacade.updateInstance(0, {
  //         deactivateAdd: false,
  //         deactivateList: false,
  //         selectedRowIndex: null
  //       });
  //       this.viewColFacade.resetIndex(0);
  //       this.viewColFacade.resetDraft(2);
  //       return InternalJobOrderActions.deleteJobOrderNoSuccess({guid: jobOrderNo.guid.toString()});
  //     })
  //   ))
  // ));

  // issueJobOrder$ = createEffect(() => this.actions$.pipe(
  //   ofType(InternalJobOrderActions.issueJobOrderInit),
  //   withLatestFrom(
  //     this.store.select(HDRSelectors.selectHdr),
  //     this.store.select(PlannedOutputSelectors.selectAll),
  //     this.store.select(PlannedInputSelectors.selectAll),
  //     this.processStore.select(ProcessSelectors.selectCurrentProcess)
  //   ),
  //   map(([action, hdr, output, input, process]) => {
  //     const container = new GenericDocContainerModel;
  //     const ext = new bl_fi_generic_doc_ext_RowClass;
  //     container.bl_fi_generic_doc_hdr = hdr;
  //     output.concat(input).forEach(l => {
  //       l.date_txn = hdr.date_txn;
  //       delete (<any>l).location;
  //       delete (<any>l).branch;
  //       l.guid = null;
  //     });
  //     ext.param_code = 'PROCESS';
  //     ext.param_name = 'PROCESS';
  //     ext.param_type = 'STRING';
  //     ext.value_string = process;
  //     container.bl_fi_generic_doc_line = [...output, ...input];
  //     container.bl_fi_generic_doc_ext.push(ext);
  //     return container;
  //   }),
  //   exhaustMap((a) => this.branchService.getByGuid(a.bl_fi_generic_doc_hdr.guid_branch.toString(), this.apiVisa).pipe(
  //     map(b_inner => {
  //       a.bl_fi_generic_doc_hdr.guid_comp = b_inner.data.bl_fi_mst_branch.comp_guid;
  //       return a;
  //     }),
  //     exhaustMap((d) => this.joService.postOne(d, this.apiVisa).pipe(
  //       map(d_c => {
  //         this.viewColFacade.showSuccessToast(ToastConstants.issueJobOrderSuccess);
  //         this.viewColFacade.updateInstance(0, {
  //           deactivateAdd: false,
  //           deactivateList: false,
  //           selectedRowIndex: null
  //         });
  //         this.viewColFacade.resetIndex(0);
  //         this.viewColFacade.resetDraft(2);
  //         return InternalJobOrderActions.issueJobOrderSuccess();
  //       })
  //     )),
  //     catchError(err => {
  //       this.viewColFacade.showFailedToast(err);
  //       return of(InternalJobOrderActions.issueJobOrderFailed({error: err.message}));
  //     })
  //   )),
  //   catchError(err => {
  //     this.viewColFacade.showFailedToast(err);
  //     return of(InternalJobOrderActions.issueJobOrderFailed({error: err.message}));
  //   })
  // ));

  // deleteSalesOrder$ = createEffect(() => this.actions$.pipe(
  //   ofType(InternalJobOrderActions.deleteJobOrdersInit),
  //   withLatestFrom(this.store.select(HDREditSelectors.selectHdr)),
  //   exhaustMap(([a, b]) => this.soService.delete(b.guid.toString(), this.apiVisa).pipe(
  //     map(() => {
  //       this.viewColFacade.showSuccessToast(ToastConstants.deleteJobOrderSuccess);
  //       this.viewColFacade.updateInstance(0, {
  //         deactivateAdd: false,
  //         deactivateList: false,
  //         selectedRowIndex: null
  //       });
  //       this.viewColFacade.resetIndex(0);
  //       this.viewColFacade.resetDraft(1);
  //       return InternalJobOrderActions.deleteJobOrderSuccess();
  //     }),
  //     catchError(err => {
  //       this.viewColFacade.showFailedToast(err);
  //       return of(InternalJobOrderActions.deleteJobOrderFailed({error: err.message}));
  //     })
  //   ))
  // ));

  // editSalesOrder$ = createEffect(() => this.actions$.pipe(
  //   ofType(InternalJobOrderActions.editSalesOrdersInit),
  //   withLatestFrom(
  //     this.store.select(HDREditSelectors.selectHdr),
  //     this.store.select(PNSEditSelectors.selectAll),
  //   exhaustMap(([action, hdr, pns]) => this.soService.getByGuid(hdr.guid.toString(), this.apiVisa).pipe(
  //     map((a_a) => {
  //       const container = new GenericDocContainerModel();
  //       hdr.revision = a_a.data.bl_fi_generic_doc_hdr.revision;
  //       pns.forEach(l => {
  //         const lineMatch = a_a.data.bl_fi_generic_doc_line.find(l_inner => l_inner.guid === l.guid);
  //         l.revision = lineMatch ? lineMatch.revision : l.revision;
  //         l.guid = lineMatch ? lineMatch.guid : null;
  //       });
  //       hdr.date_txn = a_a.data.bl_fi_generic_doc_hdr.date_txn;
  //       container.bl_fi_generic_doc_hdr = hdr;
  //       container.bl_fi_generic_doc_line = [...pns];
  //       if (pns.find(l => (<any>l.line_property_json)?.deliveryInstructions.requestedDeliveryDate)) {
  //         a_a.data.bl_fi_generic_doc_ext.forEach(x => {
  //           const exist = pns.find(l => l.guid === x.guid_doc_line && l.guid);
  //           if (exist) {
  //             x.value_datetime = (<any>exist.line_property_json)?.deliveryInstructions.requestedDeliveryDate;
  //           } else {
  //             pns.filter(l => l.guid && a_a.data.bl_fi_generic_doc_ext.find(x_b => x_b.guid_doc_line !== l.guid)).forEach(l_b => {
  //               const ext = new bl_fi_generic_doc_ext_RowClass();
  //               ext.guid_doc_hdr = l_b.generic_doc_hdr_guid.toString();
  //               ext.guid_doc_line = l_b.guid;
  //               ext.param_code = 'REQUESTED_DELIVERY_DATE';
  //               ext.param_name = 'REQUESTED_DELIVERY_DATE';
  //               ext.param_type = 'DATE';
  //               ext.value_datetime = (<any>l_b.line_property_json).deliveryInstructions.requestedDeliveryDate;
  //               container.bl_fi_generic_doc_ext.push(ext);
  //             });
  //           }
  //         });
  //       }
  //       return container;
  //     }),
  //     exhaustMap(c_inner => this.soService.put(c_inner, this.apiVisa).pipe(
  //       map(() => {
  //         this.viewColFacade.showSuccessToast(ToastConstants.updateSalesOrderSuccess);
  //         this.viewColFacade.updateInstance(0, {
  //           deactivateAdd: false,
  //           deactivateList: false,
  //           selectedRowIndex: null
  //         });
  //         this.viewColFacade.resetIndex(0);
  //         this.viewColFacade.resetDraft(1);
  //         return InternalJobOrderActions.editSalesOrderSuccess();
  //       }),
  //       catchError(err => {
  //         this.viewColFacade.showFailedToast(err);
  //         return of(InternalJobOrderActions.editSalesOrderFailed({error: err.message}));
  //       })
  //     )),
  //     catchError(err => {
  //       this.viewColFacade.showFailedToast(err);
  //       return of(InternalJobOrderActions.editSalesOrderFailed({error: err.message}));
  //     })
  //   ))
  // ));

  // selectLineItem$ = createEffect(() => this.actions$.pipe(
  //   ofType(InternalJobOrderActions.selectLineItemInit),
  //   mergeMap(action => this.fiService.getByGuid(action.line.item_guid.toString(), this.apiVisa).pipe(
  //     map(a => InternalJobOrderActions.selectLineItemSuccess({entity: a.data})),
  //     catchError(err => of(InternalJobOrderActions.selectLineItemFailed({error: err.message})))
  //   ))
  // ));

  // printJasperPdf$ = createEffect(() => this.actions$.pipe(
  //   ofType(InternalJobOrderActions.printJasperPdfInit),
  //   withLatestFrom(this.store.select(HDREditSelectors.selectHdr)),
  //   exhaustMap(([action, hdr]) => this.soService.printJasperPdf(
  //     hdr.guid.toString(),
  //     // TODO: Remove hardcoded value after UAT
  //     'CP_COMMERCE_INTERNAL_SALES_ORDERS_JASPER_PRINT_SERVICE',
  //     'eab5b759-e547-429a-8e8e-575ccd02ba7f', AppConfig.apiVisa).pipe(
  //       map(a => {
  //         const downloadURL = window.URL.createObjectURL(a);
  //         const link = document.createElement('a');
  //         link.href = downloadURL;
  //         link.download = `${hdr.server_doc_1}.pdf`;
  //         link.click();
  //         link.remove();
  //         this.viewColFacade.showSuccessToast(ToastConstants.printableGeneratedSuccess);
  //         return InternalJobOrderActions.printJasperPdfSuccess();
  //       }),
  //       catchError(err => {
  //         this.viewColFacade.showFailedToast(err);
  //         return of(InternalJobOrderActions.printJasperPdfFailed());
  //       })
  //     )
  //   )
  // ));

//   createGenDocLink$ = createEffect(() =>
//   this.actions$.pipe(
//     ofType(InternalJobOrderActions.createGenDocLinkInit),
//     mergeMap((action) =>
//       this.mrpJobOrderGenDocLinkService.post(
//         action.genDocLink,
//         AppConfig.apiVisa
//       ).pipe(
//         map((response: any) => {
//           this.viewColFacade.showSuccessToast(
//             "Gen Doc Linked successfully"
//           );
//           this.viewColFacade.updateInstance(1, {
//             deactivateAdd: false,
//             deactivateReturn: false,
//             selectedIndex: 2,
//           });
//           this.viewColFacade.resetIndex(2);
//           return InternalJobOrderActions.createGenDocLinkSuccess({
//             genDocLink: response.data,
//           });
//         }),
//         catchError((err) => {
//           console.log(err);
//           err.message = "Error Linking Gen Doc";
//           this.viewColFacade.showFailedToast(err);

//           return of(
//             InternalJobOrderActions.createGenDocLinkFailure({
//               error: err.message,
//             })
//           );
//         })
//       )
//     )
//   )
// );


//   generateInstance$ = createEffect(() =>
//   this.actions$.pipe(
//     ofType(InternalJobOrderActions.generateProcessInstanceInit),
//     mergeMap((action) =>
//       this.mrpCreateProcessInstanceService.createJobInstancesByGuid(
//         action.jobOrderGuid,
//         {
//           "guid" : action.jobOrderGuid
//         },
//         AppConfig.apiVisa
//       ).pipe(
//         map((response: any) => {
//           this.viewColFacade.showSuccessToast(
//             "Process Instance Generated successfully"
//           );
//           this.viewColFacade.updateInstance(1, {
//             deactivateAdd: false,
//             deactivateReturn: false,
//             selectedIndex: 2,
//           });
//           this.viewColFacade.resetIndex(1);
//           return InternalJobOrderActions.generateProcessInstanceSuccess();
//         }),
//         catchError((err) => {
//           console.log(err);
//           err.message = "Error Generating Process Instance";
//           this.viewColFacade.showFailedToast(err);

//           return of(
//             InternalJobOrderActions.generateProcessInstanceFailed({
//               error: err.message,
//             })
//           );
//         })
//       )
//     )
//   )
// );

// deleteGenericDocLink$ = createEffect(() => this.actions$.pipe(
//   ofType(InternalJobOrderActions.deleteGenericDocLinkInit),
//   withLatestFrom(this.joStore.select(InternalJobOrderSelectors.selectGenDocLinkGuid)),
//   exhaustMap(([a, b]) => this.mrpJobOrderGenDocLinkService.delete(b.toString(), this.apiVisa).pipe(
//     map(() => {
//       this.viewColFacade.showSuccessToast(ToastConstants.deleteGenericDocLinkSuccess);
//       this.viewColFacade.updateInstance(0, {
//         deactivateAdd: false,
//         deactivateList: false,
//       });
//       this.viewColFacade.resetIndex(1);
//       // this.viewColFacade.resetDraft();
//       return InternalJobOrderActions.deleteGenericDocLinkSuccess();
//     }),
//     catchError(err => {
//       this.viewColFacade.showFailedToast(err);
//       return of(InternalJobOrderActions.deleteGenericDocLinkFailed({ error: err.message }));
//     })
//   ))
// ));

// createProcessInstance$ = createEffect(() =>
// this.actions$.pipe(
//   ofType(InternalJobOrderActions.createProcessInstanceInit),
//   mergeMap((action) =>
//     this.mrpProcessInstanceService.post(
//       action.processInstance,
//       AppConfig.apiVisa
//     ).pipe(
//       map((response: any) => {
//         this.viewColFacade.showSuccessToast(
//           "Process Instance Created successfully"
//         );

//         this.viewColFacade.resetIndex(2);
//         return InternalJobOrderActions.createProcessInstanceSuccess({
//           processInstance: response.data,
//         });
//       }),
//       catchError((err) => {
//         console.log(err);
//         err.message = "Error Creating Process Instance";
//         this.viewColFacade.showFailedToast(err);

//         return of(
//           InternalJobOrderActions.createProcessInstanceFailed({
//             error: err.message,
//           })
//         );
//       })
//     )
//   )
// )
// );

// updateProcessInstance$ = createEffect(() => this.actions$.pipe(
//   ofType(InternalJobOrderActions.updateProcessInstanceInit),
//   concatMap(action => this.mrpProcessInstanceService.getByGuid(action.processInstance.bl_mrp_process_instance_hdr.guid.toString(), this.apiVisa)
//     .pipe(
//       map(a => a.data), //optional
//       map(b => {
       
//         b.bl_mrp_process_instance_hdr.is_active = action.processInstance.bl_mrp_process_instance_hdr.is_active;
//         return b;
//       }),
//       concatMap(c => this.mrpProcessInstanceService.put(c, this.apiVisa).pipe(
//         map(c_inner => {
//           this.toaster.success(
//             'The Process Instance has been updated',
//             'Success',
//             {
//               tapToDismiss: true,
//               progressBar: true,
//               timeOut: 1300
//             }
//           );
//           // this.onReturn();
//           return InternalJobOrderActions.updateProcessInstanceSuccess({ processInstance: c_inner.data })
//         }),
//         catchError(err => {
//           this.toaster.error(
//             err.message,
//             'Error',
//             {
//               tapToDismiss: true,
//               progressBar: true,
//               timeOut: 1300
//             }
//           );
//           return of(InternalJobOrderActions.updateProcessInstanceFailure({ error: err.message }))
//         })
//       )),
//       catchError(err => {
//         this.toaster.error(
//           err.message,
//           'Error',
//           {
//             tapToDismiss: true,
//             progressBar: true,
//             timeOut: 1300
//           }
//         );
//         return of(InternalJobOrderActions.updateProcessInstanceFailure({ error: err.message }))
//       })
//     ))
// ));
  constructor(
    private actions$: Actions,
    private mrpJobOrderHdrService: MrpJobOrderHdrService,
    private mrpJobOrderGenDocLinkService : MrpJobOrderGenDocLinkService,
    private branchService: BranchService,
    private viewColFacade: ViewColumnFacade,
    private readonly store: Store<SalesOrderStates>,
    private toaster: ToastrService,
  ) {}
}
