<mat-tab-group mat-stretch-tabs [dynamicHeight]="true">
    <mat-tab label="Pick Pack">
      <div class="view-col-table" fxLayout="column" [formGroup]="form">
        <div fxLayout="row wrap" fxLayoutAlign="end">
          <div fxLayout="row wrap" fxLayoutGap="10px" [style.padding]="'5px'">
            <button
              mat-raised-button
              color="primary"
              type="button"
              (click)="onSendToQueue()"
              style="height: 50px"
            >
              Send To Queue
            </button>
          </div>
          <div
            class="blg-accent"
            fxFlex="1 0 25"
            fxLayout="row"
            fxLayoutAlign="space-between center"
          >
            <app-pagination
              fxFlex
              #pagination
              [agGridReference]="agGrid"
            ></app-pagination>
            <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
          </div>
        </div>
  
        <form
          [formGroup]="form"
          fxLayout="row wrap"
          style="
            width: 500px !important;
            height: 100px !important;
            margin-bottom: 5px !important;
          "
        >
          <div fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px">
            <mat-form-field
              appearance="outline"
              style="width: 170px !important; height: 50px !important"
            >
              <input
                id="trackingID"
                matInput
                type="text"
                placeholder="Tracking ID"
                formControlName="trackingID"
              />
            </mat-form-field>
            <button
              mat-raised-button
              color="primary"
              type="submit"
              style="width: 180px; height: 50px"
              (click)="addTrackingIDToSelectedLines()"
            >
              Apply to Selected
            </button>
          </div>
          <div
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="16px"
            style="margin-top: 5px"
          >
            <mat-form-field
              appearance="outline"
              style="width: 170px !important; height: 50px !important"
            >
              <mat-select
                placeholder="Delivery Type"
                formControlName="deliveryType"
              >
                <mat-option value="INTERNAL_DELIVERY"
                  >INTERNAL_DELIVERY</mat-option
                >
                <mat-option value="EXTERNAL_DELIVERY"
                  >EXTERNAL_DELIVERY</mat-option
                >
                <mat-option value="PICKUP">PICKUP</mat-option>
              </mat-select>
            </mat-form-field>
            <button
              mat-raised-button
              color="primary"
              type="submit"
              style="width: 180px; height: 50px"
              (click)="addDeliveryTypeToSelectedLines()"
            >
              Apply to Selected
            </button>
          </div>
        </form>
        <div style="height: 100%">
          <ag-grid-angular
            #agGrid
            style="height: 100%"
            class="ag-theme-balham"
            rowSelection="single"
            animateRows="true"
            suppressRowClickSelection="false"
            (cellValueChanged)="onCellValueChanged($event)"
            [rowHeight]="40"
            [sideBar]="true"
            [getRowClass]="pagination.getRowClass"
            [columnDefs]="columnsDefs"
            [rowData]="rowData"
            [rowSelection]="'multiple'"
            [paginationPageSize]="pagination.rowPerPage"
            [defaultColDef]="defaultColDef"
            [frameworkComponents]="frameworkComponents"
            (gridReady)="onGridReady($event)"
          >
          </ag-grid-angular>
        </div>
      </div>
    </mat-tab>
    <mat-tab label="External Delivery">
      <app-external-delivery [draft$]="draft$"></app-external-delivery>
    </mat-tab>
    <mat-tab label="Internal Delivery">
      <app-internal-delivery [draft$]="draft$"></app-internal-delivery>
    </mat-tab>
    <mat-tab label="Pickup">
      <app-pickup [draft$]="draft$"></app-pickup>
    </mat-tab>
  </mat-tab-group>
  