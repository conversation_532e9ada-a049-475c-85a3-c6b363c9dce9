import { Injectable } from '@angular/core';
import { ApiVisa, CreditLimitService } from 'blg-akaun-ts-lib';
import { EntityActions } from '../actions';
import { ToastrService } from 'ngx-toastr';
import { map, mergeMap, catchError,concatMap } from 'rxjs/operators';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import {of } from 'rxjs';
import { AppConfig } from 'projects/shared-utilities/visa';

@Injectable()
export class CreditLimitEffect {

    private readonly apiVisa = AppConfig.apiVisa;
    // ApiVisa = {
    //     tenantCode: sessionStorage.getItem("tenantCode"),
    //     api_domain_url: environment.api_domain,
    //     jwt_secret: localStorage.getItem("authToken"),
    //   };

      //EFFECT FOR ENTITY MODULE//
      createLimit$ = createEffect (()=>this.actions$.pipe(
        ofType(EntityActions.addNewCreditLimit),
        mergeMap(action => this.limitservice.post(action.addNewCreditLimit,this.apiVisa).pipe(
            map((a:any) => {
                this.toastr.success (
                    'Credit Limit has been created',
                    'Success',
                    {
                        tapToDismiss: true,
                        progressBar: true,
                        timeOut: 1300
                    }
                );
                return EntityActions.addNewCreditLimitSuccess({entity:a.data});
            }),
            catchError (err => {
                this.toastr.error(
                    err.message,
                    'Error',
                    {
                        tapToDismiss: true,
                        progressBar: true,
                        timeOut: 1300
                    }
                );
                return of(EntityActions.addNewCreditLimitFailed({error:err.message}));
            })
        ))
    ));

    //EFECT FOR CREDIT LIMIT MODULE//
    createEntityLimit$ = createEffect (()=>this.actions$.pipe(
        ofType(EntityActions.createLimitEntity),
        mergeMap(action => this.limitservice.post(action.entityLimit,this.apiVisa).pipe(
            map((a:any) => {
                this.toastr.success (
                    'Credit Limit has been created',
                    'Success',
                    {
                        tapToDismiss: true,
                        progressBar: true,
                        timeOut: 1300
                    }
                );
                return EntityActions.createLimitEntitySuccess({entityLimit:a.data});
            }),
            catchError (err => {
                this.toastr.error(
                    err.message,
                    'Error',
                    {
                        tapToDismiss: true,
                        progressBar: true,
                        timeOut: 1300
                    }
                );
                return of(EntityActions.createEntityFailed({error:err.message}));
            })
        ))
    ));

    //EFECT FOR CREDIT LIMIT MODULE
    updateEntityLimit$ = createEffect(() => this.actions$.pipe (
        ofType(EntityActions.containerLimitDraftUpdateInit),
        concatMap(action => this.limitservice.getByGuid(action.entityLimit.bl_fi_entity_credit_limit_hdr.guid.toString(),this.apiVisa).pipe (
            //map (a => a.data),
            map(b => {
                //mapping value
                action.entityLimit.bl_fi_entity_credit_limit_hdr.revision=b.data.bl_fi_entity_credit_limit_hdr.revision;
                b.data={...action.entityLimit};
                return b.data;
            }),
            concatMap (c => this.limitservice.put(c,this.apiVisa).pipe (
                map (c_inner => {
                    this.toastr.success (
                        'Limit has been update',
                        'Success',
                        {
                            tapToDismiss: true,
                            progressBar: true,
                            timeOut: 1300
                        }
                    );
                    return EntityActions.containerDraftLimitUpdateSuccess({entityLimit: c_inner.data})
                }),
                catchError (err => {
                    this.toastr.error (
                        err.message,
                        'Error',
                        {
                            tapToDismiss: true,
                            progressBar: true,
                            timeOut: 1300
                          }
                    );
                    return of (EntityActions.containerLimitDraftUpdateFailed({status:err.message}))
                })
            ))
        ))
    ));

    
    constructor(
        private actions$: Actions,
        private toastr: ToastrService,
        private limitservice:CreditLimitService,
      ) { } 


}