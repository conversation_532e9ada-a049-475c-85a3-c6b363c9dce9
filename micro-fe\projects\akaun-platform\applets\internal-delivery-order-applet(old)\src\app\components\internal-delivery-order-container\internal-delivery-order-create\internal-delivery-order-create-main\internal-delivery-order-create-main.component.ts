import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BranchService, BranchContainerModel, GenericDocContainerModel, GuidDataFieldInterface, Pagination } from 'blg-akaun-ts-lib';
import * as moment from 'moment';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { InternalDeliveryOrderActions } from '../../../../state-controllers/internal-delivery-order-controller/store/actions';
import { InternalDeliveryOrderStates } from '../../../../state-controllers/internal-delivery-order-controller/store/states';
import { HDRActions } from '../../../../state-controllers/draft-controller/store/actions';
import { AppletSettings } from '../../../../models/applet-settings.model';
import { DraftStates } from '../../../../state-controllers/draft-controller/store/states';
import { debounceTime, distinctUntilChanged } from "rxjs/operators";
import { InternalDeliveryOrderSelectors } from '../../../../state-controllers/internal-delivery-order-controller/store/selectors';


@Component({
  selector: 'app-internal-delivery-order-create-main',
  templateUrl: './internal-delivery-order-create-main.component.html',
  styleUrls: ['./internal-delivery-order-create-main.component.css']
})
export class InternalDeliveryOrderCreateMainComponent implements OnInit, OnDestroy {

  @Input() draft$: Observable<GenericDocContainerModel>;
  @Input() appletSettings$: Observable<AppletSettings>;

  @Output() customer = new EventEmitter();
  @Output() shippingInfo = new EventEmitter();
  @Output() billingInfo = new EventEmitter();
  @Output() updateMain = new EventEmitter();

  apiVisa = AppConfig.apiVisa;
  selectedBranch: GuidDataFieldInterface;
  selectedCompany: GuidDataFieldInterface;
  defaultBranch: string;
  defaultLocation: string;
  defaultCompany: any;

  private subs = new SubSink();

  form: FormGroup;
  editMode$ = this.store.select(InternalDeliveryOrderSelectors.selectEditMode);
  modeValue;

  leftColControls = [
    { label: 'Reference', formControl: 'reference', type: 'text', readonly: false },
    { label: 'Ship Via', formControl: 'shipVia', type: 'text', readonly: false },
    // {label: 'Credit Terms', formControl: 'creditTerms', type: 'creditTerms', readonly: false},
    // {label: 'Sales Agent', formControl: 'salesAgent', type: 'salesAgent', readonly: false},
    // {label: 'Group Discount (%)', formControl: 'groupDiscount', type: 'number', readonly: false},
    // {label: 'Sales Lead', formControl: 'salesLead', type: 'salesLead', readonly: false},
  ];

  rightColControls = [
    { label: 'Transaction Date', formControl: 'transactionDate', type: 'text', readonly: true },
    // {label: 'Currency', formControl: 'currency', type: 'currency', readonly: false},
    // {label: 'Group Discount Amount', formControl: 'groupDiscountAmount', type: 'number', readonly: false},
    { label: 'Tracking ID', formControl: 'trackingID', type: 'text', readonly: false },
  ];

  constructor(
    protected readonly draftStore: Store<DraftStates>,
    protected readonly store: Store<InternalDeliveryOrderStates>,
    protected branchService: BranchService
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      docType: new FormControl(),
      tenantDocNo: new FormControl(),
      companyDocNo: new FormControl(),
      branchDocNo: new FormControl(),
      company: new FormControl('', Validators.required),
      branch: new FormControl('', Validators.required),
      location: new FormControl('', Validators.required),
      reference: new FormControl(),
      transactionDate: new FormControl(moment(new Date()).format('YYYY-MM-DD')),
      shipVia: new FormControl(),
      trackingID: new FormControl(),
      remarks: new FormControl(),
    });

    this.form.controls['docType'].disable();
    this.form.controls['tenantDocNo'].disable();
    this.form.controls['companyDocNo'].disable();
    this.form.controls['branchDocNo'].disable();

    if (this.appletSettings$) {
      this.subs.sink = this.appletSettings$.subscribe(
        resolve => {
          // console.log(resolve);
          this.defaultBranch = resolve.DEFAULT_BRANCH;
          this.defaultLocation = resolve.DEFAULT_LOCATION;
          this.defaultCompany = resolve.DEFAULT_COMPANY;

          this.subs.sink = this.branchService.getByGuid(this.defaultBranch, this.apiVisa).subscribe(
            resolve => {
              if (!this.form.value.currency) {
                this.form.patchValue({
                  currency: resolve.data.bl_fi_mst_branch?.ccy_code ?? 'MYR',
                });
              }
            }
          );
        }
      );
    }
    this.subs.sink = this.draft$.subscribe(resolve => {
      this.form.patchValue({
        docType: resolve.bl_fi_generic_doc_hdr.server_doc_type,
        tenantDocNo: resolve.bl_fi_generic_doc_hdr.server_doc_1,
        companyDocNo: resolve.bl_fi_generic_doc_hdr.server_doc_2,
        branchDocNo: resolve.bl_fi_generic_doc_hdr.server_doc_3,
        company: resolve.bl_fi_generic_doc_hdr.guid_comp ?? this.defaultCompany,
        branch: resolve.bl_fi_generic_doc_hdr.guid_branch ?? this.defaultBranch,
        location: resolve.bl_fi_generic_doc_hdr.guid_store ?? this.defaultLocation,
        reference: resolve.bl_fi_generic_doc_hdr.doc_reference,
        // transactionDate: resolve.bl_fi_generic_doc_hdr.date_txn,
        shipVia: (<any>resolve).bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.shipVia,
        trackingID: (<any>resolve).bl_fi_generic_doc_hdr?.doc_entity_hdr_json?.trackingID,
        remarks: resolve.bl_fi_generic_doc_hdr.doc_remarks,
      });
    });
    this.subs.sink = this.form.valueChanges
      .pipe(debounceTime(100), distinctUntilChanged())
      .subscribe({
        next: (form) => {
          console.log(form);
          this.updateMain.emit(form);
        },
      });

    this.editMode$.subscribe((data) => {
      this.modeValue = data;
    });
  }

  onBranchSelected(branch: BranchContainerModel) {
    this.selectedBranch = branch.bl_fi_mst_branch.guid;
    this.form.patchValue({ company: branch.bl_fi_mst_branch.comp_guid });
    this.selectedCompany = branch.bl_fi_mst_branch.comp_guid;
    this.store.dispatch(InternalDeliveryOrderActions.selectCompanyGuid({ compGuid: this.selectedCompany.toString() }));
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
