import { NgModule } from '@angular/core';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { draftFeatureKey, draftReducers } from '../state-controllers/draft-controller';
import { AttachmentEffects } from '../state-controllers/draft-controller/store/effects/attachment.effects';
import { HDREditEffects } from '../state-controllers/draft-controller/store/effects/hdr-edit.effects';
import { PNSEditEffects } from '../state-controllers/draft-controller/store/effects/pns-edit.effects';

@NgModule({
    imports: [
        StoreModule.forFeature(draftFeatureKey, draftReducers),
        EffectsModule.forFeature([
            AttachmentEffects,
            HDREditEffects,
            PNSEditEffects,
        ])
    ],
})
export class DraftModule {}
