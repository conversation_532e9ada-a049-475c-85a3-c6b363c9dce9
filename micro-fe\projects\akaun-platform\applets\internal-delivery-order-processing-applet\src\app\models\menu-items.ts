export const menuItems = [
  {
    state: '',
    name: sessionStorage.getItem('tenantCode'),
    type: 'tenant',
    icon: 'https'
  },
  {
    state: `internal-delivery-order-processing`,
    name: 'Process Order',
    type: 'link',
    icon: 'list_alt',
  },
];

export const settingItems = [
  {
    group: 'System Configuration',
    child: [
      {
        state: 'field-settings',
        name: 'Field Settings'
      },
      {
        state: 'default-selection',
        name: 'Default Selection'
      },
    ]
  }
];

export const personalizationItems = [
  {
    group: 'System Configuration',
    child: [
      {
        state: 'field-settings',
        name: 'Field Settings'
      },
      {
        state: 'personal-default-selection',
        name: 'Default Selection'
      },
    ]
  }
];
