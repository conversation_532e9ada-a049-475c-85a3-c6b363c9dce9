<div class="view-col-forms">
    <mat-tab-group mat-stretch-tabs [dynamicHeight]="true" [selectedIndex]="selectedIndex$ | async">
      <mat-tab label="Listing">
        <app-serial-number-listing [serialNumbers]="serialNumbers" [draft$]="editMode? draftEdit$ : draft$" [invItem$]="invItem$" (serialNumberList)="addSerialNumberFromScan($event)"></app-serial-number-listing>
      </mat-tab>
      <mat-tab label="Scan">
        <app-serial-number-scan (serialNumber)="addSerialNumberFromScan($event)"></app-serial-number-scan>
      </mat-tab>
      <mat-tab label="Import">
        <app-serial-number-import></app-serial-number-import>
      </mat-tab>
    </mat-tab-group>
    <div style="text-align: right;">
      <span style="float: left;">Quantity: {{ serialNumbers.length }}</span>
      <mat-checkbox [formControl]="selectAll" (click)="onSelectAll()">Select All</mat-checkbox>
    </div>
    <fieldset>
      <legend>Serial Numbers</legend>
      <mat-selection-list #matList style="text-align: left; max-height: 30vh; overflow: auto;">
        <ng-container *ngFor="let s of serialNumbers">
          <mat-list-option [value]="s" (click)="onSelect($event)">{{ s }}</mat-list-option>
          <mat-divider></mat-divider>
        </ng-container>
      </mat-selection-list>
    </fieldset>
    <div style="text-align: right; margin-top: 10px;">
      <button mat-raised-button color="warn" type="button" (click)="onRemove()">REMOVE</button>
    </div>
  </div>