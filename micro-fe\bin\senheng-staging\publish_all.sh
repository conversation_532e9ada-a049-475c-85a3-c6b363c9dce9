#!/bin/bash
echo "~Running batch script STAGING~"
echo "~Deploying QC applet~"
sh ./bin/staging/qc_applet_publish.sh 2>> ./bin/staging/log.txt
echo "~Deploying Operator applet~"
sh ./bin/staging/operator_applet_publish.sh 2>> ./bin/staging/log.txt
echo "~Deploying Supervisor applet~"
sh ./bin/staging/supervisor_applet_publish.sh 2>> ./bin/staging/log.txt
echo "~Deploying Tonn Cable Admin applet~"
sh ./bin/staging/tonn_cable_admin_applet_publish.sh 2>> ./bin/staging/log.txt
echo "~Deploying Internal Sales Order applet~"
sh ./bin/staging/internal_sales_order_applet_publish.sh 2>> ./bin/staging/log.txt
echo "~Deploying Internal Packing Order applet~"
sh ./bin/staging/internal_packing_order_applet_publish.sh 2>> ./bin/staging/log.txt
echo "~Deploying Internal Stock Balance applet~"
sh ./bin/staging/stock_balance_applet_publish.sh 2>> ./bin/staging/log.txt
echo "~Deploying Internal Stock Adjustment applet~"
sh ./bin/staging/stock_adjustment_applet_publish.sh 2>> ./bin/staging/log.txt
echo "~Finish running script~"
