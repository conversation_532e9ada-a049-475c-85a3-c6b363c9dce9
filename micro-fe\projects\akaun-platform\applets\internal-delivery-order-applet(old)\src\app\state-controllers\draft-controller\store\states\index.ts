import * as fromAttachmentStates from './atachment.states';
import * as fromPNSStates from './pns.states';
import * as fromPNSEditStates from './pns-edit.states';
import * as fromSettlementStates from './settlement.states';
import * as fromSettlementEditStates from './settlement-edit.states';
import * as fromLinkStates from './link.states';
import { bl_fi_generic_doc_hdr_RowClass, bl_fi_generic_doc_link_RowClass } from 'blg-akaun-ts-lib';

export interface DraftStates {
    attachment: fromAttachmentStates.AttachmentState,
    pns: fromPNSStates.PNSState,
    pnsEdit: fromPNSEditStates.PNSEditState,
    settlement: fromSettlementStates.SettlementState,
    settlementEdit: fromSettlementEditStates.SettlementEditState,
    hdr: bl_fi_generic_doc_hdr_RowClass,
    hdrEdit: bl_fi_generic_doc_hdr_RowClass
    link: fromLinkStates.LinkState,
}