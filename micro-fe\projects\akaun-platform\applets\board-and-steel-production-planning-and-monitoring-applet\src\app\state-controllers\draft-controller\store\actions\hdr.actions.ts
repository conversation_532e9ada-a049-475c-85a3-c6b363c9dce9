import { createAction, props } from '@ngrx/store';
import { JobOrderMain, JobOrderNoDepartment } from '../../../../models/internal-job-order.model';

export const updateMain = createAction('[Draft: HDR] Update Main', props<{ form: JobOrderMain }>());
export const updateDepartment = createAction('[Draft: HDR] Update Department', props<{ form: JobOrderNoDepartment }>());
export const resetHDR = createAction('[Draft: HDR] Reset');

export const updateGroupTemplate = createAction('[Draft: HDR] Update Group Guid', props<{ groupGuid: string }>());


