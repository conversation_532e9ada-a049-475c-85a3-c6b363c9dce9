import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngrx/store';
import { InventoryItemService, MrpJobOrderHdrService, MrpProcessInstanceLinesService, MrpProcessInstanceService, Pagination, TenantUserProfileService, bl_fi_generic_doc_hdr_RowClass, bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { pageFiltering, pageSorting } from 'projects/shared-utilities/listing.utils';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable, forkJoin, iif, of, zip } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../../../../facades/view-column.facade';
import { SalesOrderStates } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/sales-order-controller/store/states';
import { SalesOrderSelectors } from 'projects/akaun-platform/applets/internal-job-order-applet/src/app/state-controllers/sales-order-controller/store/selectors';

@Component({
  selector: 'app-detailed-job-order-stock',
  templateUrl: './detailed-job-order-stock.component.html',
  styleUrls: ['./detailed-job-order-stock.component.css']
})
export class DetailedJobOrderStockComponent extends ViewColumnComponent implements OnInit {

  protected index = 4;

  @Input() localState: any;
  @Input() rowData: bl_fi_generic_doc_line_RowClass[] = [];

  @Output() next = new EventEmitter();
  @Output() lineItem = new EventEmitter<bl_fi_generic_doc_line_RowClass>();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    onCellClicked: (params) => this.onRowClicked(params)
  };

  gridApi;
  pagination = new Pagination();
  SQLGuids: string[] = null;
  private subs = new SubSink();
  itemGuid;
  prevIndex: number;
  protected prevLocalState: any;

  columnsDefs = [
    // {headerName: 'Job Order No', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Size of Wire', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Required Length', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Priority', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Job Order no', field: 'job_order_no', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Job Order Qty', field: 'job_order_qty'},
    {headerName: 'Process Status', field: 'process_status'},
    {headerName: 'Packing Status', field: 'packing_status'},

  ];

  selectedRowIndex = null;

  constructor(    
    private mrpJobOrderHdrService: MrpJobOrderHdrService,
    private mrpProcessInstanceLinesService: MrpProcessInstanceLinesService,
    private inventoryItemService : InventoryItemService,
    private profileService: TenantUserProfileService,
    protected readonly store: Store<SalesOrderStates>,
    protected viewColFacade: ViewColumnFacade,
    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.store.select(SalesOrderSelectors.selectSalesOrder).subscribe(data => {
      this.itemGuid = data.item_guid;
    })
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();  
    this.setGridData();
  }

  setGridData() {
    const apiVisa = AppConfig.apiVisa;
    const datasource = {
      getRows: grid => {
        this.pagination.offset = this.SQLGuids ? 0 : grid.request.startRow;
        this.pagination.limit = grid.request.endRow - grid.request.startRow;

        const filter = pageFiltering(grid.request.filterModel);
        const sortOn = pageSorting(grid.request.sortModel);

        this.pagination.conditionalCriteria = [
          { columnName: "calcTotalRecords", operator: "=", value: "true" },
          // { columnName: 'orderBy', operator: '=', value: 'updated_date' },
          // { columnName: 'order', operator: '=', value: 'DESC' },
          // { columnName: "bl_mrp_process_template_hdr_guid", operator: "=", value: this.process_template_hdr_guid },
          { columnName: 'item_guid', operator: '=', value: this.itemGuid },

          {
            columnName: "guids",
            operator: "=",
            value: this.SQLGuids
              ? this.SQLGuids.slice(
                grid.request.startRow,
                grid.request.endRow
              ).toString()
              : "",
          },
        ];
        let totalrec = 0;
        this.mrpJobOrderHdrService.getByCriteria
          (this.pagination, apiVisa).subscribe(resolved => {
            console.log("Planned Input",resolved);
            totalrec = resolved.data.length;

            const source: Observable<{}>[] = [];
            resolved.data.forEach(data => {

              let obj = {"job_order_no":'', "job_order_qty": '', "process_status": '',"packing_status" : ''};      
              obj.job_order_no = data.bl_mrp_job_order_hdr.server_doc_1 ? data.bl_mrp_job_order_hdr.server_doc_1.toString() : ''
              obj.job_order_qty = data.bl_mrp_job_order_hdr.total_container_measure ? data.bl_mrp_job_order_hdr.total_container_measure.toString() : ''
              obj.process_status = data.bl_mrp_job_order_hdr.process_status ? data.bl_mrp_job_order_hdr.process_status.toString() : ''
              // obj.packing_status = data.bl_mrp_job_order_hdr.qty_actual ? data.bl_mrp_job_order_hdr.qty_actual.toString() : '0'
             
              
              source.push(of(obj))}
            );
            return iif(() => resolved.data.length > 0,
              forkJoin(source).pipe(map((b_inner) => {
                return b_inner
              })),
              of({})
            ).subscribe((res: []) => {
              const data = res.length > 0 ? sortOn(res).filter((entity) => filter.by(entity)) : res;
              const totalRecords = filter.isFiltering ? (this.SQLGuids ? this.SQLGuids.length : totalrec) : data.length;
              console.log("Objecr Input",data);

              grid.success({
                rowData: data,
                rowCount: totalRecords
              });
            })
          }, err => {
            grid.fail();
          });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
    // this.subs.sink = this.store.select(InternalJobOrderSelectors.selectAgGrid).subscribe(resolved => {
    //   if (resolved) {
    //     this.gridApi.refreshServerSideStore({ purge: true });
    //   }
    // });
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onNext() {
    this.next.emit();
  }

  onRowClicked(e) {
    // this.selectedRowIndex = this.localState.selectedLineItemRowIndex === null ? e.rowIndex : null;
    this.lineItem.emit(e.data);
  }

}
