const fs = require('fs-extra');
const concat = require('concat');

(async function build() {
  const files = [
    './dist/internal-delivery-order-processing-applet/runtime.js',
    './dist/internal-delivery-order-processing-applet/polyfills-es5.js',
    './dist/internal-delivery-order-processing-applet/scripts.js',
    './dist/internal-delivery-order-processing-applet/main.js'
  ];

  await fs.ensureDir('./elements/akaun-platform/applets/internal-delivery-order-processing-applet');
  await concat(files, './elements/akaun-platform/applets/internal-delivery-order-processing-applet/internal-delivery-order-processing-applet-elements.js');
  // await fs.copyFile(
  //   './dist/akaun-platform/applets/developer-maintenance-applet/styles.css',
  //   './elements/akaun-platform/applets/developer-maintenance-applet/styles.css'
  // );
})();
