import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PickPackQueueListingComponent } from './pick-pack-queue-listing/pick-pack-queue-listing.component';
import { PickPackQueueContainerComponent } from './pick-pack-queue-container.component';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { AgGridModule } from 'ag-grid-angular';
import { GroupRowInnerRenderer } from './pick-pack-queue-listing/group-row-inner-renderer.component';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { PickPackQueueEffects } from '../../state-controllers/pick-pack-queue-controller/store/effects';
import { pickPackQueueFeatureKey, PickPackQueueReducers } from '../../state-controllers/pick-pack-queue-controller/store/reducers';
import { DateCellRendererComponent } from '../utilities/date-cell-renderer/date-cell-renderer.component';
import { columnViewModelFeatureKey, columnViewModelReducers } from '../../state-controllers/pick-pack-queue-view-model-controller/reducers';
@NgModule({
  declarations: [
    PickPackQueueContainerComponent,
    PickPackQueueListingComponent,
    GroupRowInnerRenderer
  ],
  imports: [
    CommonModule,
    UtilitiesModule,
    AgGridModule,
    AgGridModule.withComponents([GroupRowInnerRenderer, DateCellRendererComponent]),
    StoreModule.forFeature(pickPackQueueFeatureKey, PickPackQueueReducers.pickPackQueue),
    EffectsModule.forFeature([PickPackQueueEffects]),
    StoreModule.forFeature(columnViewModelFeatureKey, columnViewModelReducers),
  ]
})
export class PickPackQueueModule { }
