import { createAction, props } from "@ngrx/store";
import { ShippingAddress, StatusInfo } from "../../../../models/internal-delivery-order.model";
// import { InternalSOMain, InternalSODepartment, BillingInfo, ShippingInfo } from "../../../../models/internal-sales-order.model";

export const updateMain = createAction('[Draft: HDR] Update Main', props<{ form: any }>());
export const updateDepartment = createAction('[Draft: HDR] Update Department', props<{ form: any }>());
export const updateBillingInfo = createAction('[Draft: HDR] Update Billing', props<{ form: any }>());
export const updateShippingInfo = createAction('[Draft: HDR] Update Shipping', props<{ form: any }>());
export const resetHDR = createAction('[Draft: HDR] Reset');
export const updateStatus = createAction('[Draft: HDR] Update Status', props<{ form: StatusInfo }>());
export const updateShippingAddress = createAction('[Draft: HDR] Update Shipping Address', props<{ form: ShippingAddress }>());
export const updateCompany = createAction('[Draft: HDR] Update Company', props<{ compGuid: string }>());

export const updateDeliveryType = createAction('[Draft: HDR Edit] Update Delivery Type', props<{ deliveryType:string }>());
