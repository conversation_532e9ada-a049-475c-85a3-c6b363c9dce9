
<form [formGroup]="form">
  <div fxLayout="column" class="view-col-forms">
    <div *ngFor="let x of customFields" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
      <mat-form-field *ngIf="x.readonly;else input" appearance="outline">
        <mat-label>{{x.label}}</mat-label>
        <input  matInput readonly [formControl]="form.controls[x.formControl]" autocomplete="off">
      </mat-form-field>
      <ng-template #input>
        <ng-container [ngSwitch]="x.type">
          <mat-form-field *ngSwitchCase="'text'" appearance="outline">
            <mat-label>{{x.label}}</mat-label>
            <input matInput [formControl]="form.controls[x.formControl]" autocomplete="off" type="text">
            <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
            <div matSuffix>
              <button class="blg-small-button-icon" mat-icon-button (click)="onRemoveCustomField(x.formControl)">
                <mat-icon>close</mat-icon>
              </button>
            </div>
          </mat-form-field>
          <mat-form-field *ngSwitchCase="'number'" appearance = "outline">
            <mat-label>{{x.label}}</mat-label>
            <input matInput [formControl]="form.controls[x.formControl]" autocomplete="off" type="number">
            <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
            <div matSuffix>
              <button class="blg-small-button-icon" mat-icon-button (click)="onRemoveCustomField(x.formControl)">
                <mat-icon>close</mat-icon>
              </button>
            </div>
          </mat-form-field>
          <mat-form-field *ngSwitchCase="'text-area'" appearance = "outline">
            <mat-label>{{x.label}}</mat-label>
            <textarea #remarks matInput [formControl]="form.controls[x.formControl]"></textarea>
            <mat-hint align="end">{{remarks.value.length}} characters</mat-hint>
            <div matSuffix>
              <button class="blg-small-button-icon" mat-icon-button (click)="onRemoveCustomField(x.formControl)">
                <mat-icon>close</mat-icon>
              </button>
            </div>
          </mat-form-field>
          <mat-form-field *ngSwitchCase="'date'" appearance = "outline">
            <mat-label>{{x.label}}</mat-label>
            <input matInput [matDatepicker]="datepicker" [formControl]="form.controls[x.formControl]" autocomplete="off" readonly (click)="datepicker.open()">
            <mat-error>{{x.label}} is <strong>not valid</strong></mat-error>
            <mat-datepicker touchUi="true" #datepicker></mat-datepicker>
            <div fxLayout="row" matSuffix>
              <mat-datepicker-toggle [for]="datepicker"></mat-datepicker-toggle>
              <button class="blg-small-button-icon" mat-icon-button (click)="onRemoveCustomField(x.formControl)">
                <mat-icon>close</mat-icon>
              </button>
            </div>
          </mat-form-field>
        </ng-container>
      </ng-template>
    </div>
    <ng-container *ngIf="showCustomFieldForm">
      <form [formGroup]="customFieldForm">
        <div fxLayout="row" fxLayoutGap="5px">
          <mat-form-field fxFlex="50" appearance="outline">
            <mat-label>Field Name</mat-label>
            <input matInput [formControl]="customFieldForm.controls['fieldName']" autocomplete="off" type="text">
            <mat-error>Field Name is <strong>not valid</strong></mat-error>
          </mat-form-field>
          <mat-form-field fxFlex="50" appearance="outline">
            <mat-label>Field Type</mat-label>
            <mat-select [formControl]="customFieldForm.controls['fieldType']">
              <mat-option value="text">Text</mat-option>
              <mat-option value="number">Number</mat-option>
              <mat-option value="date">Date</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      </form>
    </ng-container>
    <div>
      <button *ngIf="!showCustomFieldForm" mat-raised-button color="primary" type="button" (click)="onAddCustomField()">Add Custom Field</button>
      <button *ngIf="showCustomFieldForm" mat-raised-button color="primary" type="button" [disabled]="customFieldForm.invalid"(click)="onProcessCustomField()">Confirm</button>
    </div>
  </div>
</form>
