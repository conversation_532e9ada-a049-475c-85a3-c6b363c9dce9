import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass } from 'blg-akaun-ts-lib';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { JobOrderMain } from '../../../models/internal-job-order.model';
import { HDRActions, JobOrderNoActions } from '../../../state-controllers/draft-controller/store/actions';
import { JobOrderNoSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { InternalJobOrderCreateMainComponent } from './internal-job-order-create-main/internal-job-order-create-main.component';

interface LocalState {
  deactivateAdd: boolean;
  deactivateReturn: boolean;
  deactivateCustomer: boolean;
  deactivateShippingInfo: boolean;
  deactivateBillingInfo: boolean;
  deactivateLineItem: boolean;
  deactivateSettlement: boolean;
  deactivateAddContra: boolean;
  deactivateAddAttachments: boolean;
  selectedIndex: number;
  childSelectedIndex: number;
  selectedLineItemRowIndex: number;
}

@Component({
  selector: 'app-internal-job-order-create',
  templateUrl: './internal-job-order-create.component.html',
  styleUrls: ['./internal-job-order-create.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalJobOrderCreateComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Internal Sales Order Create';
  protected index = 2;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly childSelectedIndex$ = this.componentStore.select(state => state.childSelectedIndex);

  prevIndex: number;
  protected prevLocalState: any;

  draft$ = this.draftStore.select(JobOrderNoSelectors.selectJobOrderNo);

  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({...a, ...b})));

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(InternalJobOrderCreateMainComponent) main: InternalJobOrderCreateMainComponent;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected readonly store: Store<InternalJobOrderStates>,
    protected readonly sessionStore: Store<SessionStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly componentStore: ComponentStore<LocalState>
    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onReset() {
    this.viewColFacade.resetDraft(this.index);
  }

  onSave() {
    this.store.dispatch(InternalJobOrderActions.createJobOrderNoInit());
  }

  disableButton() {
    return this.main?.form.invalid;
  }

  onUpdateMain(form: JobOrderMain) {
    this.draftStore.dispatch(HDRActions.updateMain({form}));
  }
  onGoupNameUpdated(e) {
    this.draftStore.dispatch(HDRActions.updateGroupTemplate({groupGuid : e}));
  }

  // onUpdateDepartment(form: JobOrderNoDepartment) {
  //   this.draftStore.dispatch(HDRActions.updateDepartment({form}));
  // }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onItemCode() {
    // if (!this.localState.deactivateCustomer) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateReturn: true
      });
      this.viewColFacade.onNextAndReset(this.index, 3);
    // }
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
        // childSelectedIndex: this.account.matTab.selectedIndex,
        // selectedLineItemRowIndex: this.lines.selectedRowIndex
      });
    }
    this.subs.unsubscribe();
  }

}
