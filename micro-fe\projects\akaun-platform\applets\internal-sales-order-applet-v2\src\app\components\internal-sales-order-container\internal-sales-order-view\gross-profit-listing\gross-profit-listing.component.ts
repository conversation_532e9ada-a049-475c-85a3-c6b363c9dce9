import { Component, OnInit } from '@angular/core';
import { GridOptions } from 'ag-grid-enterprise';
import { ComponentStore } from '@ngrx/component-store';
import { ListingInputModel } from 'projects/shared-utilities/models/listing-input.model';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { ListingService } from 'projects/shared-utilities/services/listing-service';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { Store } from '@ngrx/store';
import { AppConfig } from 'projects/shared-utilities/visa';
import { ToastrService } from 'ngx-toastr';
import { SubSink } from "subsink2";
import { InternalSalesOrderSelectors } from '../../../../state-controllers/internal-sales-order-controller/store/selectors';
import { InternalSalesOrderStates } from '../../../../state-controllers/internal-sales-order-controller/store/states';
import { ApiService } from '../../../../services/api-service';
import { combineLatest, forkJoin, from, iif, Observable, of, zip } from 'rxjs';
import { HDREditSelectors, PNSEditSelectors} from '../../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../../state-controllers/draft-controller/store/states';

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRow: any;
}

@Component({
  selector: 'app-gross-profit-listing',
  templateUrl: './gross-profit-listing.component.html',
  styleUrls: ['./gross-profit-listing.component.scss']
})
export class GrossProfitListingComponent implements OnInit {
  compId = "salesInvoiceGrossProfit"
  compName = 'Gross Profit Listing';
  protected readonly index = 0;
  private localState: LocalState;

  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);

  private subs= new SubSink;

  rowData = [];
  totalRecords = 0;
  limit = 50;
  searchQuery: SearchQueryModel;

  // api visa
  apiVisa = AppConfig.apiVisa;

  // initial grid state
  gridApi;
  gridColumnApi;

  gridOptions: GridOptions = {
    rowSelection: 'single',
    paginationPageSize: this.limit,
    pagination: false
  };

  columnsDefs = [
    {
      headerName: 'Item Code',
      field: 'item_code',
      type: 'textColumn'
    },
    {
      headerName: 'Item Name ',
      field: 'item_name',
      type: 'textColumn'
    },
    {
      headerName: 'Amount Txn',
      field: 'amount_txn',
      type: 'decimalColumn'
    },
    {
      headerName: 'Cost MA Price',
      field: 'cost_ma_price',
      type: 'decimalColumn'
    },
    {
      headerName: 'Cost MA Amount',
      field: 'cost_ma_amount',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP',
      field: 'gp',
      type: 'decimalColumn'
    },
    {
      headerName: 'GP %',
      field: 'gp_percentage',
      type: 'decimalColumn'
    }
  ];
  genDocContainer;
  constructor(
    private apiService: ApiService,
    protected readonly store: Store<InternalSalesOrderStates>,
    protected readonly draftStore: Store<DraftStates>,
    private listingService: ListingService,
    private toastr: ToastrService,
    private readonly componentStore: ComponentStore<LocalState>
  ) {}

  ngOnInit() {
    this.subs.sink = combineLatest([
      this.store.select(InternalSalesOrderSelectors.selectOrder),
      this.draftStore.select(HDREditSelectors.selectHdr),
      this.draftStore.select(PNSEditSelectors.selectAll),
    ]).pipe(
      ).subscribe({next: async (
        [
          genDoc, hdr, pns
        ]:any) => {
         this.genDocContainer = genDoc;
         this.genDocContainer.bl_fi_generic_doc_hdr = hdr;

         pns = pns.map(props => {
            const { qtyArranged, qtyPending, ppStatus, ...rest } = props;
            return rest;
         });
        console.log('pns', pns, genDoc);
        this.genDocContainer.bl_fi_generic_doc_line = pns;
    }});
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();
    this.createData();

  }

  createData() {
    const genDocContainer = { ...this.genDocContainer };
    delete genDocContainer.bl_fi_generic_doc_hdr['branch_name'];
    delete genDocContainer.bl_fi_generic_doc_hdr['sales_agent'];
    delete genDocContainer.bl_fi_generic_doc_hdr['customer_name'];
    delete genDocContainer.bl_fi_generic_doc_hdr['device_name'];
    if(genDocContainer){
      this.subs.sink = this.apiService.grossProfit(genDocContainer, AppConfig.apiVisa).subscribe(res=>{
        this.gridApi.setRowData(res.data);
     })
    }

  };

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
