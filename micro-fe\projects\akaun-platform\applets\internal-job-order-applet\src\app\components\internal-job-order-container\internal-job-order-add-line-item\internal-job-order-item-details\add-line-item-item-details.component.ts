import { OnInit, Component, ViewChild, Input, AfterViewChecked } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { SubSink } from 'subsink2';
import { InternalJobOrderEditLineItemDepartmentComponent } from '../../internal-job-order-edit-line-item/edit-line-item-item-details/internal-job-order-edit-line-item-department/internal-job-order-edit-line-item-department.component';
import { InternalJobOrderAddLineItemMainComponent } from './internal-job-order-add-line-item-main/internal-job-order-add-line-item-main.component';

@Component({
  selector: 'app-add-line-item-item-details',
  templateUrl: './add-line-item-item-details.component.html',
  styleUrls: ['./add-line-item-item-details.component.css']
})
export class AddLineItemItemDetailsComponent implements OnInit, AfterViewChecked {

  protected subs = new SubSink();

  @Input() appletSettings$;
  @Input() item$;
  @Input() tax$;
  @Input() dept$;
  @Input() line$;
  @Input() childSelectedIndex$;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(InternalJobOrderAddLineItemMainComponent) main: InternalJobOrderAddLineItemMainComponent;
  @ViewChild(InternalJobOrderEditLineItemDepartmentComponent) dept: InternalJobOrderEditLineItemDepartmentComponent;

  ngOnInit() {}

  ngAfterViewChecked() {
    this.matTab.realignInkBar();
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
