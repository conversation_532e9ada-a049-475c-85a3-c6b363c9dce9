import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';

export const salesLineItemSearchModel: SearchModel = {
  label: {
    docNo: 'Sales Order No.',
    itemCode: 'Item Code',
    itemName: 'Item Name',
    createdDate: 'Creation Date'
  },

  dataType: {
    docNo: 'string',
    itemCode: 'string',
    itemName: 'string',
    createdDate: 'date'
  },

  form: new FormGroup({
    docNo: new FormControl(),
    itemCode: new FormControl(),
    itemName: new FormControl(),
    createdDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    })
  }),

  joins: [
    { type: 'INNER JOIN', table: 'bl_fi_generic_doc_hdr', alias: 'hdr', onCondition: 'hdr.guid = line.generic_doc_hdr_guid', joinOnBasic: true },
  ],

  query: (query) =>
    `(hdr.server_doc_1 ILIKE '%${query}%' OR line.item_code ILIKE '%${query}%' OR line.item_name ILIKE '%${query}%')`,

  table: 'bl_fi_generic_doc_line',

  queryCallbacks: {
    docNo: query => query ? `hdr.server_doc_1 ILIKE '%${query.trim()}%'` : '',
    itemCode: query => query ? `line.item_code ILIKE '%${query.trim()}%'` : '',
    itemName: query => query ? `line.item_name ILIKE '%${query.trim()}%'` : '',
    createdDate: (createdDate) => {
      if (createdDate.from || createdDate.to) {
        var from = createdDate.from ? createdDate.from : createdDate.to;
        var to = createdDate.to ? createdDate.to : createdDate.from;
        return `line.created_date >= '${from.format(
          "YYYY-MM-DD HH:mm:ss"
        )}' AND line.created_date <= '${to.format("YYYY-MM-DD HH:mm:ss")}'`;
      }
      return "";
    }
  },
  additionalCondition: ` AND line.server_doc_type = 'INTERNAL_SALES_ORDER' AND line.status = 'ACTIVE'`
};

export const quotationLineItemSearchModel: SearchModel = {
    label: {
      docNo: 'Quotation No.',
      itemCode: 'Item Code',
      itemName: 'Item Name',
    },
  
    dataType: {
      docNo: 'string',
      itemCode: 'string',
      itemName: 'string',
    },
  
    form: new FormGroup({
      docNo: new FormControl(),
      itemCode: new FormControl(),
      itemName: new FormControl(),
    }),
  
    joins: [
      { type: 'INNER JOIN', table: 'bl_fi_generic_doc_line', alias: 'line', onCondition: 'hdr.guid = line.generic_doc_hdr_guid', joinOnBasic: true },
    ],
  
    query: (query) =>
      `(hdr.server_doc_1 ILIKE '%${query}%' OR line.item_code ILIKE '%${query}%' OR line.item_name ILIKE '%${query}%')
      AND line.server_doc_type = 'INTERNAL_SALES_QUOTATION' AND line.status = 'ACTIVE'`,
  
    table: 'bl_fi_generic_doc_hdr',
  
    queryCallbacks: {
      docNo: query => query ? `hdr.server_doc_1 ILIKE '%${query.trim()}%'`: '',
      itemCode: query => query ? `line.item_code ILIKE '%${query.trim()}%'` : '',
      itemName: query => query ? `line.item_name ILIKE '%${query.trim()}%'` : '',
    },
  
    // additionalCondition: ` AND line.server_doc_type = 'INTERNAL_SALES_QUOTATION' AND line.status = 'ACTIVE'`
  }
  

export const jobsheetLineItemSearchModel: SearchModel = {
    label: {
      docNo: 'Jobsheet No.',
      itemCode: 'Item Code',
      itemName: 'Item Name',
    },
  
    dataType: {
      docNo: 'string',
      itemCode: 'string',
      itemName: 'string',
    },
  
    form: new FormGroup({
      docNo: new FormControl(),
      itemCode: new FormControl(),
      itemName: new FormControl(),
    }),
  
    joins: [
      { type: 'INNER JOIN', table: 'bl_fi_generic_doc_line', alias: 'line', onCondition: 'hdr.guid = line.generic_doc_hdr_guid', joinOnBasic: true },
    ],
  
    query: (query) =>
      `(hdr.server_doc_1 ILIKE '%${query}%' OR line.item_code ILIKE '%${query}%' OR line.item_name ILIKE '%${query}%')
      AND line.server_doc_type = 'INTERNAL_JOBSHEET' AND line.status = 'ACTIVE'`,
  
    table: 'bl_fi_generic_doc_hdr',
  
    queryCallbacks: {
      docNo: query => query ? `hdr.server_doc_1 ILIKE '%${query.trim()}%'`: '',
      itemCode: query => query ? `line.item_code ILIKE '%${query.trim()}%'` : '',
      itemName: query => query ? `line.item_name ILIKE '%${query.trim()}%'` : '',
    },
  
    // additionalCondition: ` AND line.server_doc_type = 'INTERNAL_JOBSHEET' AND line.status = 'ACTIVE'`
  }
  

  export const StockTransferInboundItemSearchModel: SearchModel = {
    label: {
      docNo: 'Internal Inbound Stock Transfer No.',
      itemCode: 'Item Code',
      itemName: 'Item Name',
    },
  
    dataType: {
      docNo: 'string',
      itemCode: 'string',
      itemName: 'string',
    },
  
    form: new FormGroup({
      docNo: new FormControl(),
      itemCode: new FormControl(),
      itemName: new FormControl(),
    }),
  
    joins: [
      { type: 'INNER JOIN', table: 'bl_fi_generic_doc_line', alias: 'line', onCondition: 'hdr.guid = line.generic_doc_hdr_guid', joinOnBasic: true },
    ],
  
    query: (query) =>
      `(hdr.server_doc_1 ILIKE '%${query}%' OR line.item_code ILIKE '%${query}%' OR line.item_name ILIKE '%${query}%')
      AND line.server_doc_type = 'INTERNAL_INBOUND_STOCK_TRANSFER' AND line.status = 'ACTIVE'`,
  
    table: 'bl_fi_generic_doc_hdr',
  
    queryCallbacks: {
      docNo: query => query ? `hdr.server_doc_1 ILIKE '%${query.trim()}%'` : '',
      itemCode: query => query ? `line.item_code ILIKE '%${query.trim()}%'` : '',
      itemName: query => query ? `line.item_name ILIKE '%${query.trim()}%'` : '',
    },
  
    // additionalCondition: ` AND line.server_doc_type = 'INTERNAL_SALES_ORDER' AND line.status = 'ACTIVE'`
  };

  export const purchaseGRNLineItemSearchModel: SearchModel = {
    label: {
      docNo: "Purchase GRN No.",
      txnDate: "Txn Date",
      itemCode: "Item Code",
      itemName: "Item Name",
      trackingId: "Tracking ID",
      docRef: "Doc Ref",
      docDesc: "Doc Description"
    },
  
    dataType: {
      docNo: "string",
      itemCode: "string",
      itemName: "string",
      trackingId: "string",
      docRef: "string",
      docDesc: "string",
      txnDate: "date",
  
    },
  
    form: new FormGroup({
      docNo: new FormControl(),
      itemCode: new FormControl(),
      itemName: new FormControl(),
      trackingId: new FormControl(),
      docRef: new FormControl(),
      docDesc: new FormControl(),
      txnDate:  new FormGroup({
        from: new FormControl(),
        to: new FormControl(),
      }),
    }),
  
    joins: [
      {
        type: "INNER JOIN",
        table: "bl_fi_generic_doc_hdr",
        alias: "hdr",
        onCondition: "hdr.guid = line.generic_doc_hdr_guid",
        joinOnBasic: true,
      },
    ],
  
    query: (query) =>
      `(hdr.server_doc_1 ILIKE '%${query}%' 
      OR line.item_code ILIKE '%${query}%' 
      OR line.item_name ILIKE '%${query}%'
      OR line.tracking_id ILIKE '%${query}%'
      OR line.date_txn ILIKE '%${query}%'
      )
      AND line.server_doc_type = 'INTERNAL_PURCHASE_GOODS_RECEIVED_NOTE' AND line.status = 'ACTIVE'`,
  
    table: "bl_fi_generic_doc_line",
  
    queryCallbacks: {
      docNo: (query) =>
        query ? `hdr.server_doc_1 ILIKE '%${query.trim()}%'` : "",
      txnDate: (txnDate) => {
        if (txnDate.from || txnDate.to) {
          var from = txnDate.from ? txnDate.from : txnDate.to;
          var to = txnDate.to ? txnDate.to : txnDate.from;
          return `line.date_txn >= '${from.format(
            "YYYY-MM-DD HH:mm:ss"
          )}' AND line.date_txn <= '${to.format("YYYY-MM-DD HH:mm:ss")}'`;
        }
        return "";
      },
      itemCode: (query) =>
        query ? `line.item_code ILIKE '%${query.trim()}%'` : "",
      itemName: (query) =>
        query ? `line.item_name ILIKE '%${query.trim()}%'` : "",
      trackingId: (query) =>
        query ? `line.tracking_id ILIKE '%${query.trim()}%'` : "",
      docRef: (query) =>
        query ? `hdr.doc_reference ILIKE '%${query.trim()}%'` : "",
      docDesc: (query) =>
        query ? `hdr.doc_desc ILIKE '%${query.trim()}%'` : "",
      
  
    },
  
    additionalCondition: ` AND line.server_doc_type = 'INTERNAL_PURCHASE_GOODS_RECEIVED_NOTE' AND line.status = 'ACTIVE'`,
  };