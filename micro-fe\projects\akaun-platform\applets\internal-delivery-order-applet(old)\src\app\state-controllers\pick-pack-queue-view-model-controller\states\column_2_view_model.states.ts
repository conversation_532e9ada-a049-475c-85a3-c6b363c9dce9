export interface Column2ViewModelState {
    mainDetailsTab_color:string
    mainDetailsTab_branchField,
    accountTab:[],
    linesTab,
    linesTab_LineListing_RowColor,
    linesTabSearch:[],
    settlementTab:[],
    KOForTab:[],
    departmentHdrTab:[],
    postingTab:[]
}

export const initialState: Column2ViewModelState = {
    mainDetailsTab_color: undefined,
    mainDetailsTab_branchField: undefined,
    accountTab: [],
    linesTab: undefined,
    linesTab_LineListing_RowColor: undefined,
    linesTabSearch: [],
    settlementTab: [],
    KOForTab: [],
    departmentHdrTab: [],
    postingTab: []
}