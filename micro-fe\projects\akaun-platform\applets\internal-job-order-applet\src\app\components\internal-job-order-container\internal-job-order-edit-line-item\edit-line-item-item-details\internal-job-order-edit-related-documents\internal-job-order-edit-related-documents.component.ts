import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { bl_fi_generic_doc_line_RowClass, GenericDocContainerModel } from 'blg-akaun-ts-lib';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-internal-job-order-edit-related-documents',
  templateUrl: './internal-job-order-edit-related-documents.component.html',
  styleUrls: ['./internal-job-order-edit-related-documents.component.css']
})
export class InternalJobOrderEditRelatedDocumentsComponent implements OnInit {

  // @Input() localState: any;
  @Input() rowData: bl_fi_generic_doc_line_RowClass[] = [];
  @Input() draft$: Observable<GenericDocContainerModel>;

  @Output() lineItem = new EventEmitter<bl_fi_generic_doc_line_RowClass>();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Doc No', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Branch', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Server Doc Type', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Status', field: 'quantity_base', cellStyle: () => ({'text-align': 'left'}) },
    {headerName: 'Date', field: 'quantity_base'},
  ];

  constructor() { }

  ngOnInit() {
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

  onRowClicked(e: bl_fi_generic_doc_line_RowClass) {
    this.lineItem.emit(e);
  }

}
