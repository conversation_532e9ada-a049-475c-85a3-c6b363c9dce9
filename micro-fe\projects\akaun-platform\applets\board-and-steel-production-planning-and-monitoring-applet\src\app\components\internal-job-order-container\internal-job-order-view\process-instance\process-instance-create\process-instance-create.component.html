<mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div>
      <button
        #navBtn
        mat-button
        class="blg-button-icon"
        matTooltip="Back"
        type="button"
        [disabled]="deactivateReturn$ | async"
        (click)="onReturn()"
      >
        <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null"
          src="../../../assets/images/backbutton.png"
          alt="add"
          width="40px"
          height="40px"
        />
      </button>
      <span> {{ bread }} </span>
    </div>
    <button
      mat-raised-button
      type="button"
      (click)="onSave()"
      color="{{ isClicked }}"
    >
      {{ addSuccess }}
    </button>
  </div>
</mat-card-title>

<div class="view-col-table no-tab" fxLayout="column">
  <mat-card-title class="column-title">
    <div
      [ngStyle.gt-md]="{ 'flex-flow': 'row' }"
      [ngStyle.lt-lg]="{ 'flex-flow': 'row wrap' }"
      fxLayoutAlign="space-between end"
      fxLayoutGap="10px"
    >
      <div fxFlex="60" fxFlex.lt-lg="100">
        <div
          fxLayout="row"
          fxLayoutAlign="space-between center"
          fxLayoutGap="3px"
        >
          <app-advanced-search
            class="mobile"
            fxFlex
            [id]="'internal-po'"
            (search)="onSearch($event)"
          ></app-advanced-search>
          <!-- <app-column-toggle [currentToggle]="toggleColumn$ | async" (toggleColumn)="onToggle($event)" fxHide.lt-sm>
          </app-column-toggle> -->
        </div>
      </div>
      <div
        class="blg-accent"
        fxFlex="1 0 25"
        fxLayout="row"
        fxLayoutAlign="space-between center"
      >
        <app-pagination
          fxFlex
          #pagination
          [agGridReference]="agGrid"
        ></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
  </mat-card-title>
  <div style="height: 80%">
    <ag-grid-angular
      #agGrid
      id="grid"
      style="height: 100%"
      class="ag-theme-balham"
      [getRowClass]="pagination.getRowClass"
      [columnDefs]="columnsDefs"
      [rowData]="[]"
      [paginationPageSize]="pagination.rowPerPage"
      [cacheBlockSize]="pagination.rowPerPage"
      [pagination]="true"
      [animateRows]="true"
      [defaultColDef]="defaultColDef"
      [sideBar]="true"
      [rowModelType]="'serverSide'"
      [rowSelection]="'multiple'"
      (gridReady)="onGridReady($event)"
      [suppressRowClickSelection]="true"
      [serverSideStoreType]="'partial'"
    >
    </ag-grid-angular>
  </div>
</div>
