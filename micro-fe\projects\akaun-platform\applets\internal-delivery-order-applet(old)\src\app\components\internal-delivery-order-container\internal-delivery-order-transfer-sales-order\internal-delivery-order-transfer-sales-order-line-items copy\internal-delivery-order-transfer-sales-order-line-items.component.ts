// import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
// import { GenericDocContainerModel } from 'blg-akaun-ts-lib';

// @Component({
//   selector: 'app-internal-delivery-order-transfer-sales-order-line-items',
//   templateUrl: './internal-delivery-order-transfer-sales-order-line-items.component.html',
//   styleUrls: ['./internal-delivery-order-transfer-sales-order-line-items.component.css']
// })
// export class InternalDeliveryOrderTransferSalesOrderLineItemsComponent implements OnInit {

//   @Input() selectedDoc: GenericDocContainerModel;
//   @Input() localState: any;

//   @Output() selectedRows = new EventEmitter()

//   defaultColDef = {
//     filter: 'agTextColumnFilter',
//     floatingFilterComponentParams: {suppressFilterButton: true},
//     minWidth: 200,
//     flex: 2,
//     sortable: true,
//     resizable: true,
//     suppressCsvExport: true
//   };

//   gridApi;

//   columnsDefs = [
//     {
//       headerName: '',
//       field: 'checkBox',
//       headerCheckboxSelection: true,
//       headerCheckboxSelectionFilteredOnly: false,
//       checkboxSelection: true,
//       minWidth: 50
//     },
//     {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
//     {headerName: 'Item Name', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
//     {headerName: 'Packing Date', field: ''},
//     {headerName: 'Packing Status', field: '', cellStyle: () => ({'text-align': 'left'})},
//     {headerName: 'Delivery Date', field: ''},
//     {headerName: 'Batch No', field: 'batch_no'},
//     {headerName: 'Remarks', field: 'item_remarks', cellStyle: () => ({'text-align': 'left'})},
//     {headerName: 'UOM', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
//     {headerName: 'Qty', field: 'quantity_base'},
//     {headerName: 'Txn Amount', field: 'amount_txn', cellStyle: () => ({'text-align': 'right'})},
//   ];

//   constructor() { }

//   ngOnInit() {
//   }

//   onGridReady(params) {
//     this.gridApi = params.api;
//     this.gridApi.closeToolPanel();
//   }

// }
