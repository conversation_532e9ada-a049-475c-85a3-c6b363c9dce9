import { Injectable } from '@angular/core';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { ViewColumn } from 'projects/shared-utilities/view-column';
import { ProcessAddPlannedInputComponent } from '../components/process-container/process-add-planned-input/process-add-planned-input.component';
import { ProcessAddPlannedOutputComponent } from '../components/process-container/process-add-planned-output/process-add-planned-output.component';
import { ProcessIssueJobOrderComponent } from '../components/process-container/process-issue-job-order/process-issue-job-order.component';
import { ProcessJobOrderListingComponent } from '../components/process-container/process-job-order-listing/process-job-order-listing.component';
import { ProcessJobOrderViewComponent } from '../components/process-container/process-job-order-view/process-job-order-view.component';
import { ProcessSelectBinComponent } from '../components/process-container/process-select-bin/process-select-bin.component';
import { ProcessSelectJobOrderNoComponent } from '../components/process-container/process-select-job-order-no/process-select-job-order-no.component';
import { ProcessSelectTemplateComponent } from '../components/process-container/process-select-template/process-select-template.component';

@Injectable()
export class ProcessPagesService {

  private initialState: ViewColumnState = {
    firstColumn:  new ViewColumn(0, ProcessJobOrderListingComponent, 'Internal Job Order Listing', {
      deactivateList: false,
      deactivateAdd: false
    }),
    secondColumn: null,
    viewCol: [
      new ViewColumn(0, ProcessJobOrderListingComponent, 'Line Items Listing', {
        deactivateList: false,
        deactivateAdd: false
      }),
      new ViewColumn(1, ProcessJobOrderViewComponent, 'Edit Line Item', {
        deactivateAddInput: false,
        deactivateAddOutput: false,
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(2, ProcessIssueJobOrderComponent, 'Issue Job Order', {
        deactivateAddInput: false,
        deactivateAddOutput: false,
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(3, ProcessAddPlannedOutputComponent, 'Add Planned Output', {
        deactivateAddOutput: false,
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(4, ProcessAddPlannedInputComponent, 'Add Planned Input', {
        deactivateAddInput: false,
        deactivateReturn: false,
        selectedIndex: 0,
      }),
      new ViewColumn(5, ProcessSelectJobOrderNoComponent, 'Select Job Order No', {
        deactivateReturn: false,
        selectedRowGuid: null
      }),
      new ViewColumn(6, ProcessSelectBinComponent, 'Select Job Order No', {
        deactivateReturn: false,
        selectedRowGuid: null
      }),
      new ViewColumn(7, ProcessSelectTemplateComponent, 'Select Process Template', {
        deactivateReturn: false,
        selectedRowGuid: null
      }),
    ],
    breadCrumbs: [],
    leftDrawer: [],
    rightDrawer: [],
    singleColumn: false,
    prevIndex: null
  };

  get pages() {
    return this.initialState;
  }

  constructor() { }
}
