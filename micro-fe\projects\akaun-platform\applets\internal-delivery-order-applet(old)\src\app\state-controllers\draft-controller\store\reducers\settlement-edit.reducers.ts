import { Action, createReducer, on } from "@ngrx/store";
import { SettlementEditActions } from "../actions";
import { initState, settlementAdapter, SettlementEditState } from "../states/settlement-edit.states";

export const settlementReducers = createReducer(
    initState,
    // on(InternalSalesOrderActions.selectEntityInit, (state, action) => settlementAdapter.setAll(
    //     action.entity.bl_fi_generic_doc_line.filter(l => l.txn_type === 'STL_MTHD'), state)),
    on(SettlementEditActions.addSettlementSuccess, (state, action) => settlementAdapter.addOne({
        guid: state.ids.length,
        ...action.settlement
    }, state)),
    on(SettlementEditActions.deleteSettlement, (state, action) => settlementAdapter.removeOne(action.guid, state)),
    on(SettlementEditActions.editSettlement, (state, action) => settlementAdapter.upsertOne(action.settlement, state)),
    on(SettlementEditActions.resetSettlementSuccess, (state, action) => settlementAdapter.setAll(action.settlement, state)),
)

export function reducers(state: SettlementEditState | undefined, action: Action) {
    return settlementReducers(state, action);
}