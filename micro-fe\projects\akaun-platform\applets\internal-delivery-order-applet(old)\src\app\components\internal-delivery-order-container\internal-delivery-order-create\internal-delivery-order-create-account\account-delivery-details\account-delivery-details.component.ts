import { Component, EventEmitter, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-account-delivery-details',
  templateUrl: './account-delivery-details.component.html',
  styleUrls: ['./account-delivery-details.component.css']
})
export class AccountDeliveryDetailsComponent implements OnInit, OnDestroy {

  @Output() customer = new EventEmitter();

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Delivery Date', formControl: 'deliveryDate', type: 'date', readonly: false},
    {label: 'Remarks', formControl: 'remarks', type: 'text-area', readonly: false},
  ];

  rightColControls = [
    {label: 'Header', formControl: 'header', type: 'text', readonly: false},
    {label: 'Footer', formControl: 'footer', type: 'text', readonly: false},
    {label: 'Message', formControl: 'message', type: 'text-area', readonly: false},
  ];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      deliveryDate: new FormControl(),
      remarks: new FormControl(),
      header: new FormControl(),
      footer: new FormControl(),
      message: new FormControl(),
    });
  }

  onCheck(a) {
    console.log(a);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
