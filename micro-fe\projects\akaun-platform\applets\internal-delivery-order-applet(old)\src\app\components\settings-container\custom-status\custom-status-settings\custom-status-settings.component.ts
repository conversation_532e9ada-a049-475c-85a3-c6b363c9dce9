import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { AppletSettings, CustomStatusParams } from '../../../../models/applet-settings.model';
import { DeleteRendererComponent } from '../../../utilities/delete-renderer/delete-renderer.component';

@Component({
  selector: 'app-custom-status-settings',
  templateUrl: './custom-status-settings.component.html',
  styleUrls: ['./custom-status-settings.component.css']
})
export class CustomStatusSettingsComponent implements OnInit, OnDestroy {

  @Input() index: number;
  @Input() fieldSet: string;
  @Input() masterSettings$: Observable<AppletSettings>;

  private subs = new SubSink();

  form = new FormGroup({});

  defaultColDef = {
    filter: 'agTextColumnFilter',
    // floatingFilter: true,
    floatingFilterComponentParams: {suppressFilterButton: true},
    flex: 1,
    sortable: true,
    resizable: true,
    editable: true,
    autoHeight: true,
    suppressCsvExport: true
  };

  gridApi;

  columnDefs = [
    { headerName: 'Status Name', field: 'name' },
    { headerName: 'Description', field: 'descr' },
    { headerName: 'Default', field: 'default', checkboxSelection: true, editable: false,
    cellStyle: () => ({'display': 'flex', 'justify-content': 'center'}), valueFormatter: params => ''},
    { headerName: 'Actions', field: 'action', floatingFilter: false, cellRenderer: 'deleteCellRenderer',
      editable: false,  cellRendererParams: { onClick: this.onDelete.bind(this) } },
  ];

  frameworkComponents = {
    deleteCellRenderer: DeleteRendererComponent,
  };

  rowData = [];

  constructor() {}

  ngOnInit(): void {
    this.form.addControl(`NAME_CUSTOM_STATUS_${this.fieldSet}_${this.index}`, new FormControl(''));
    this.subs.sink = this.masterSettings$.subscribe({next: resolve => {
      if (resolve[`LIST_CUSTOM_STATUS_${this.fieldSet}_${this.index}`]) {
        this.form.patchValue({
          ...resolve
        });
        this.rowData = [...resolve[`LIST_CUSTOM_STATUS_${this.fieldSet}_${this.index}`]];
      }
    }});
  }

  getCustomStatus() {
    const status = {...this.form.value};
    const statusList = [];
    this.gridApi.getModel().rowsToDisplay.forEach(r => {
      const newStatusParam: CustomStatusParams = {
        name: r.data.name ?? '',
        descr: r.data.descr ?? '',
        default: r.selected
      };
      statusList.push(newStatusParam);
    });
    status[`LIST_CUSTOM_STATUS_${this.fieldSet}_${this.index}`] = statusList;
    return status;
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.applyTransaction({add: [...this.rowData]});
    this.gridApi.forEachNode(n => {
      if (n.data.default) {
        n.setSelected(true);
      }
    });
  }

  onAdd() {
    this.gridApi.applyTransaction({add: [{name: 'New Status'}]});
  }

  onDelete(e: CustomStatusParams) {
    this.gridApi.applyTransaction({remove: [e]});
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
