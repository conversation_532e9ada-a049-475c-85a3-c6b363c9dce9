import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { map, withLatestFrom } from 'rxjs/operators';
import { PNSEditActions } from '../actions';

@Injectable()
export class PNSEditEffects {

    // resetPNS$ = createEffect(() => this.actions$.pipe(
    //     ofType(PNSEditActions.resetPNSInit),
    //     withLatestFrom(this.store.select(InternalSalesOrderSelectors.selectEntity)),
    //     map(([a, b]) => {
    //         return PNSEditActions.resetPNSSuccess({pns: b.bl_fi_generic_doc_line.filter(l => l.txn_type === 'PNS')})
    //     })
    // ));

    constructor(
        private actions$: Actions,
    ) { }
}
