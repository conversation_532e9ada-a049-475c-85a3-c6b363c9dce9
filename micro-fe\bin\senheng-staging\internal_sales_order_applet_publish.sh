#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=senheng-staging --project=internal-sales-order-applet --output-hashing none
node elements-build-scripts/akaun/internal-sales-order-applet-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://senheng-applets/bigledger/wavelet-erp/internal-sales-order-applet/staging s3://senheng-applets/bigledger/wavelet-erp/internal-sales-order-applet/staging/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile senheng-staging --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/internal-sales-order-applet/ s3://senheng-applets/bigledger/wavelet-erp/internal-sales-order-applet/staging --profile senheng-staging --acl public-read --recursive
