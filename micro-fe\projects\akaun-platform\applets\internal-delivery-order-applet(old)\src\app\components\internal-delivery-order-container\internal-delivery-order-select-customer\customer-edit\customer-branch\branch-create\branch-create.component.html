<!-- <mat-card-title class="column-title">
  <div fxLayout="row" fxLayoutAlign="space-between end">
    <div> <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
        [disabled]="deactivateReturn$ | async" (click)="onReturn()"> <img
          [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png"
          alt="add" width="40px" height="40px"> </button> <span> Branch Create </span> </div> <button mat-raised-button
      type="button" (click)="onSave()" [disabled]="!form.valid" color={{isClicked}}>{{addSuccess}}</button>
  </div>
</mat-card-title>
<form [formGroup]="form">
  <mat-tab-group [dynamicHeight]="true">
    <mat-tab label="Main">
      <div fxLayout="column" class="view-col-forms">
        <div fxLayout="raw wrap" fxFlexAlign="center" class="row">
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Branch Name</mat-label> <input maxlength="255" matInput formControlName="branch_name" required>
              <mat-hint
                *ngIf="form.controls['branch_name'].hasError('required') && form.controls['branch_name'].touched"
                class="text-danger font-14">You must insert Branch Name. </mat-hint>
              <mat-hint *ngIf="form.controls['branch_name'].value?.length === 255" class="text-danger font-14">
                Please
                insert no
                more than 255
              </mat-hint>
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Branch Code</mat-label> <input maxlength="255" matInput formControlName="branch_code" required>
              <mat-hint
                *ngIf="form.controls['branch_code'].hasError('required') && form.controls['branch_code'].touched"
                class="text-danger font-14">You must insert Branch Code. </mat-hint>
              <mat-hint *ngIf="form.controls['branch_code'].value?.length === 255" class="text-danger font-14">
                Please insert no more than 255
              </mat-hint>
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Description</mat-label> <input maxlength="255" matInput formControlName="description">
              <mat-hint
                *ngIf="form.controls['description'].hasError('required') && form.controls['description'].touched"
                class="text-danger font-14">You must insert Description. </mat-hint>
              <mat-hint *ngIf="form.controls['description'].value?.length === 255" class="text-danger font-14">
                Please
                insert
                no
                more than 255
              </mat-hint>
            </mat-form-field>
          </div>

          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label>Fax No</mat-label> <input maxlength="255" matInput formControlName="fax_no">
              <mat-hint *ngIf="form.controls['fax_no'].hasError('required') && form.controls['fax_no'].touched"
                class="text-danger font-14">You must insert Fax No </mat-hint>
              <mat-hint *ngIf="form.controls['fax_no'].value?.length === 255" class="text-danger font-14">Please
                insert no
                more than 255
              </mat-hint>
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label> Phone Number </mat-label>
              <input matInput placeholder="Phone Number" formControlName="phone" type="text" maxlength="255" />
              <mat-hint *ngIf="form.controls['phone'].value?.length === 255" class="text-danger font-14">
                Please insert no more than 255
              </mat-hint>
            </mat-form-field>
          </div>
          <div fxFlex.gt-sm="50" fxFlex="100" class="p-10">
            <mat-form-field appearance="outline">
              <mat-label> Email </mat-label>
              <input matInput placeholder="Email" formControlName="email" type="text" maxlength="255" />
              <mat-hint *ngIf="form.controls['email'].value?.length === 255" class="text-danger font-14">
                Please insert no more than 255
              </mat-hint>
              <mat-hint *ngIf="form.controls['email'].hasError('required') && form.controls['email'].touched"
                class="text-danger font-14"> Please enter valid email </mat-hint>
            </mat-form-field>
          </div>
          <div class="p-10" fxFlex.gt-sm="50" fxFlex.gt-xs="50" fxFlex="100">
            <mat-form-field appearance="outline">
              <mat-label> Status </mat-label>
              <mat-select placeholder="Status" formControlName="status">
                <mat-option *ngFor="let s of status" [value]="s.value"> {{s.viewValue}} </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      </div>
    </mat-tab>
  </mat-tab-group>
</form> -->

<div class="view-col-table no-tab" fxLayout="column">
  <mat-card-title class="column-title">
    <div>
      <button #navBtn mat-button class="blg-button-icon" matTooltip="Back" type="button"
      [disabled]="deactivateReturn$ | async"
      (click)="onReturn()">
        <img [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null" src="../../../assets/images/backbutton.png" alt="add" width="40px" height="40px">
      </button>
      <span>
        Select Branch
      </span>
      <div fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end" fxLayoutGap="5px">
        <button mat-raised-button color="primary" type="button" (click)="onSave()">Add</button>
      </div>
    </div>
    <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
      <div fxFlex="60" fxFlex.lt-lg="100">
        <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
          <app-advanced-search (search)="onSearch($event)" class="mobile" fxFlex [id]="'internal-so-line-item'" [advSearchModel]="searchModel"></app-advanced-search>
        </div>
      </div>
      <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
        <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
        <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
      </div>
    </div>
  </mat-card-title>
  <div style="height: 80%;">
    <ag-grid-angular #agGrid
    style="height: 100%;"
    class="ag-theme-balham"
    [getRowClass]="pagination.getRowClass"
    [columnDefs]="columnsDefs"
    [rowData]="[]"
    [paginationPageSize]="pagination.rowPerPage"
    [cacheBlockSize]="pagination.rowPerPage"
    [pagination]="true"
    [animateRows]="true"
    [defaultColDef]="defaultColDef"
    [suppressRowClickSelection]="false"
    [rowSelection]="'single'"
    [sideBar]="true"
    [rowModelType]="'serverSide'"
    [serverSideStoreType]="'partial'"
    (rowClicked)="onRowClicked($event)"
    (gridReady)="onGridReady($event)">
    </ag-grid-angular>
  </div>
</div>