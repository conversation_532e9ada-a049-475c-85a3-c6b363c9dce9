import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { FileDetails, FilesDetailsRequest, InternalSalesOrderService } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { of } from 'rxjs';
import { catchError, map, mergeMap, withLatestFrom } from 'rxjs/operators';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { ToastConstants } from '../../../../models/constants/toast.constants';
import { AttachmentActions } from '../actions';
import { AttachmentSelectors, HDREditSelectors } from '../selectors';
import { DraftStates } from '../states';
import { AttachmentState } from '../states/atachment.states';

@Injectable()
export class AttachmentEffects {

    uploadAttachments$ = createEffect(() => this.actions$.pipe(
        ofType(AttachmentActions.uploadAttachmentsInit),
        withLatestFrom(this.store.select(AttachmentSelectors.selectAll), this.draftStore.select(HDREditSelectors.selectHdr)),
        mergeMap(([action, attachment, draft]) => {
            const fileRequest = new FilesDetailsRequest();
            attachment.forEach(attach => {
                const fileDetail = new FileDetails(attach.fileAttributes.fileName, attach.fileAttributes);
                fileRequest.addFileDetail(fileDetail);
            });
            return this.soService.addAttachmentsAndDetails(
            draft.guid.toString(), attachment.map(attach => attach.file), AppConfig.apiVisa, fileRequest).pipe(
                map(a => {
                    this.viewColFacade.showSuccessToast(ToastConstants.attachmentAddedSuccess);
                    return AttachmentActions.uploadAttachmentsSuccess({ext: a.data.map(d => d.bl_fi_generic_doc_ext).flat()});
                }),
                catchError(err => {
                    this.viewColFacade.showFailedToast(err);
                    return of(AttachmentActions.uploadAttachmentsFailed());
                })
            );
        })
    ));

    constructor(
        private actions$: Actions,
        private soService: InternalSalesOrderService,
        private viewColFacade: ViewColumnFacade,
        private readonly store: Store<AttachmentState>,
        private readonly draftStore: Store<DraftStates>
    ) { }
}
