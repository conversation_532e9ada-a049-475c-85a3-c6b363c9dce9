#!/bin/sh

set -e
set -x


#compile angular application
ng build --configuration=senheng-staging --project=board-and-steel-production-planning-and-monitoring-applet --output-hashing none
node elements-build-scripts/akaun/board-and-steel-production-planning-and-monitoring-applet-elements-build.js

# WARNING: Backup first
 aws s3 mv s3://senheng-applets/bigledger/wavelet-erp/board-and-steel-production-planning-and-monitoring-applet/staging s3://senheng-applets/bigledger/wavelet-erp/board-and-steel-production-planning-and-monitoring-applet/staging/backups/Backup-`date +%Y-%m-%d:%H:%M:%S` --profile senheng-staging --recursive --exclude "backups/*"

# WARNING: Upload the new  file to s3
 aws s3 cp elements/akaun-platform/applets/board-and-steel-production-planning-and-monitoring-applet/ s3://senheng-applets/bigledger/wavelet-erp/board-and-steel-production-planning-and-monitoring-applet/staging --profile senheng-staging --acl public-read --recursive
