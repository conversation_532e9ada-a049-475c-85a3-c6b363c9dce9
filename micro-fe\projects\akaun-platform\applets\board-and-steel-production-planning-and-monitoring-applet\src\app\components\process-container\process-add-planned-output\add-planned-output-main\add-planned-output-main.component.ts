import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { bl_fi_generic_doc_single_line } from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { Observable } from 'rxjs';
import { SubSink } from 'subsink2';

@Component({
  selector: 'app-add-planned-output-main',
  templateUrl: './add-planned-output-main.component.html',
  styleUrls: ['./add-planned-output-main.component.css']
})
export class AddPlannedOutputMainComponent implements OnInit, OnDestroy {

  @Input() line$: Observable<bl_fi_generic_doc_single_line>;

  @Output() jobOrderNo = new EventEmitter();

  apiVisa = AppConfig.apiVisa;

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Location', formControl: 'location', type: 'text', readonly: true},
    // {label: 'Branch', formControl: 'branch', type: 'text', readonly: true},
    {label: 'Job Order No', formControl: 'jobOrderNo', type: 'jobOrderNo', readonly: false},
    {label: 'Quantity Measure', formControl: 'quantityMeasure', type: 'number', readonly: true},
  ];

  rightColControls = [
    {label: 'Transaction Date', formControl: 'transactionDate', type: 'date', readonly: false},
    {label: 'Item Code', formControl: 'itemCode', type: 'text', readonly: true},
    {label: 'Quantity Container', formControl: 'quantityContainer', type: 'number', readonly: true},
  ];

  constructor() { }

  ngOnInit() {
    this.form = new FormGroup({
      // branch: new FormControl(),
      location: new FormControl(),
      jobOrderNo: new FormControl('', Validators.required),
      quantityMeasure: new FormControl(),
      itemCode: new FormControl(),
      quantityContainer: new FormControl(),
      transactionDate: new FormControl(),
      remarks: new FormControl(),
    });
    this.subs.sink = this.line$.pipe(
    ).subscribe(a => {
      this.form.patchValue({
        // branch: (<any>a)?.branch,
        location: (<any>a)?.location,
        jobOrderNo: (<any>a?.batch_no)?.bin_code,
        quantityMeasure: (<any>a?.batch_no)?.container_measure,
        itemCode: a?.item_code,
        quantityContainer: (<any>a?.batch_no)?.container_qty,
        transactionDate: a?.date_txn ?? new Date().toISOString().split('T')[0],
        remarks: a?.item_remarks
      });
    });
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
