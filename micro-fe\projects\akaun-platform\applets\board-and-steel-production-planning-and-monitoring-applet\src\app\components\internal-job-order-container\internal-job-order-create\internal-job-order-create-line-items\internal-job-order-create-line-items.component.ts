import { AfterViewChecked, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { bl_fi_generic_doc_line_RowClass, bl_fi_generic_doc_hdr_RowClass } from 'blg-akaun-ts-lib';
import { formatMoneyInList } from 'projects/shared-utilities/format.utils';
import { Observable } from 'rxjs';
import { SlideRendererComponent } from '../../../utilities/slide-renderer/slide-renderer.component';

@Component({
  selector: 'app-internal-job-order-create-line-items',
  templateUrl: './internal-job-order-create-line-items.component.html',
  styleUrls: ['./internal-job-order-create-line-items.component.css']
})
export class InternalJobOrderCreateLineItemsComponent implements OnInit, AfterViewChecked {

  @Input() localState: any;
  @Input() rowData: bl_fi_generic_doc_line_RowClass[] = [];
  @Input() draft$: Observable<bl_fi_generic_doc_hdr_RowClass>;

  @Output() next = new EventEmitter();
  @Output() lineItem = new EventEmitter<bl_fi_generic_doc_line_RowClass>();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    onCellClicked: (params) => this.onRowClicked(params)
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Item Code', field: 'item_code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'item_name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Packing Date', field: 'item_property_json.packingDate'},
    // {headerName: 'Packing Status', field: 'item_property_json.packingDate', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Delivery Date', field: 'item_property_json.fromCableLength'},
    // {headerName: 'Batch No', field: 'item_property_json.drumSize'},
    // {headerName: 'Remarks', field: 'item_remarks', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'UOM', field: 'item_property_json.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Delivery', field: 'quantity_base', cellRenderer: 'slideCellRenderer',
    onCellClicked: (params) => null},
    {headerName: 'Qty', field: 'quantity_base', type: 'numericColumn'},
    {headerName: 'Unit Price', field: 'quantity_base', type: 'numericColumn',
    valueFormatter: (params) =>
    formatMoneyInList((parseFloat(params.data.amount_net) + parseFloat(params.data.amount_discount)) / params.value)},
    {headerName: 'SST/VAT/GST', field: 'amount_tax_gst', type: 'numericColumn',
    valueFormatter: (params) => formatMoneyInList(parseFloat(params.value))},
    {headerName: 'Txn Amount', field: 'amount_txn', type: 'numericColumn',
    valueFormatter: (params) => formatMoneyInList(parseFloat(params.value))},
  ];

  total = '0.00';
  tax = '0.00';

  selectedRowIndex = null;

  frameworkComponents = {
    slideCellRenderer: SlideRendererComponent,
  };

  constructor() { }

  ngOnInit() {
  }

  // TODO: maybe can use the component store here?
  ngAfterViewChecked() {
    this.total = this.rowData.length ? this.rowData.map(r =>
      parseFloat(r.amount_txn?.toString())).reduce((acc, c) =>
      (acc + c)).toFixed(2) : '0.00';
    if (this.total === 'NaN') {
      this.total = '0.00';
    }
    this.tax = this.rowData.length ? this.rowData.map(r =>
      parseFloat(r.amount_tax_gst?.toString())).reduce((acc, c) =>
      (acc + c)).toFixed(2) : '0.00';
    if (this.tax === 'NaN') {
      this.tax = '0.00';
    }
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.gridApi.getRowNode(this.localState.selectedLineItemRowIndex)?.setSelected(true);
  }

  onNext() {
    this.next.emit();
  }

  onRowClicked(e) {
    this.selectedRowIndex = this.localState.selectedLineItemRowIndex === null ? e.rowIndex : null;
    this.lineItem.emit(e.data);
  }

}
