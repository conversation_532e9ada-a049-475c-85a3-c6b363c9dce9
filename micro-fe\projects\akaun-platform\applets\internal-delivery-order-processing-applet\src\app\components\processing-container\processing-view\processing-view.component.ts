import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { FormControl, Validators } from '@angular/forms';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { ProcessingActions } from '../../../state-controllers/processing-controller/store/actions';
import { ProcessingSelectors } from '../../../state-controllers/processing-controller/store/selectors';
import { ProcesssingStates } from '../../../state-controllers/processing-controller/store/states';

interface LocalState {
  deactivateReturn: boolean;
  selectedIndex: number;
}

@Component({
  selector: 'app-processing-view',
  templateUrl: './processing-view.component.html',
  styleUrls: ['./processing-view.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class ProcessingViewComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Processing View';
  protected index = 1;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);

  prevIndex: number;
  protected prevLocalState: any;

  draft$ = this.store.select(ProcessingSelectors.selectDraftEdit);

  processingStatus = new FormControl('', Validators.required);

  @ViewChild(MatTabGroup) matTab: MatTabGroup;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected readonly store: Store<ProcesssingStates>,
    protected readonly componentStore: ComponentStore<LocalState>
    ) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.draft$.subscribe({next: resolve => {
      const status = resolve.bl_fi_generic_doc_ext.find(ext => ext.param_code === 'PROCESSING_STATUS')?.value_string;
      this.processingStatus.patchValue(status ? status : 'Pending');
    }});
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  onSave() {
    this.store.dispatch(ProcessingActions.editDeliveryOrderInit({status: this.processingStatus.value}));
  }

  disableButton() {
    return true;
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex
      });
    }
    this.subs.unsubscribe();
  }

}
