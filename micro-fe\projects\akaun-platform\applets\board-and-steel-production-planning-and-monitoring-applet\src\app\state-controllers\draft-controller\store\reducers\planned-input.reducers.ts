import { Action, createReducer, on } from '@ngrx/store';
import { PlannedInputActions } from '../actions';
import { initState, plannedInputAdapter, PlannedInputState } from '../states/planned-input.states';

export const plannedInputReducers = createReducer(
    initState,
    on(PlannedInputActions.addPlannedInput, (state, action) => plannedInputAdapter.addOne(action.line, state)),
    on(PlannedInputActions.deletePlannedInput, (state, action) => plannedInputAdapter.removeOne(action.guid, state)),
    on(PlannedInputActions.editPlannedInput, (state, action) => plannedInputAdapter.upsertOne(action.line, state)),
    on(PlannedInputActions.resetPlannedInput, (state, action) => plannedInputAdapter.removeAll(state))
);

export function reducers(state: PlannedInputState | undefined, action: Action) {
    return plannedInputReducers(state, action);
}
