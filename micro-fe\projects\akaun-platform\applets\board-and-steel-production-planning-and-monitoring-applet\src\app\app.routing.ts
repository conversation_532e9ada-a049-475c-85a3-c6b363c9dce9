import { Routes } from '@angular/router';
import { InternalJobOrderContainerComponent } from './components/internal-job-order-container/internal-job-order-container.component';
import { DefaultSettingsComponent } from './components/settings-container/default-settings/default-settings.component';
import { FieldConfigurationComponent } from './components/settings-container/field-configuration/field-configuration.component';
import { SettingsContainerComponent } from './components/settings-container/settings-container.component';
import { PersonalizationContainerComponent } from './components/personalization-container/personalization-container.component';
import { PersonalDefaultSettingsComponent } from './components/personalization-container/personal-default-settings/personal-default-settings.component';
import { PermissionResolver } from './resolver/permission.resolver';
import { WebhookComponent } from 'projects/shared-utilities/modules/settings/webhook/webhook.component';
import { FeatureVisibilityComponent } from 'projects/shared-utilities/modules/settings/feature-visibility/feature-visibility.component';
import { PermissionSetContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/permission-set/permission-set-container.component';
import { UserPermissionContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/user-permission/user-permission-container.component';
import { RolePermissionContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/role-permission/role-permission-container.component';
import { SidebarComponent } from 'projects/shared-utilities/modules/personalization/sidebar/sidebar.component';
import { FourOhFourComponent } from 'projects/shared-utilities/utilities/four-oh-four/four-oh-four.component';
import { CustomStatusComponent } from './components/settings-container/custom-status/custom-status.component';
import { PrintablesComponent } from './components/settings-container/printables/printables.component';
import { ProcessContainerComponent } from './components/process-container/process-container.component';
import { SalesOrderContainerComponent } from './components/sales-order-container/sales-order-container.component';
import { PermissionWizardContainerComponent } from 'projects/shared-utilities/modules/permission/permission-container/permission-wizard/permission-wizard-container.component';

export const mainPath = 'applet/tnt/wavelet/mrp/internal-job-order-applet';

export const AppRoutes: Routes = [
  {
    path: mainPath,
    children: [
      {
        path: 'master-list',
        component: InternalJobOrderContainerComponent
      },
      {
        path: 'processes',
        component: ProcessContainerComponent
      },
      {
        path: 'sales-order',
        component: SalesOrderContainerComponent
      },
      {
        path: 'settings',
        component: SettingsContainerComponent,
        children: [
          {
            path: 'default-selection',
            component: DefaultSettingsComponent
          },
          {
            path: 'field-settings',
            component: FieldConfigurationComponent
          },
          {
            path: 'printables',
            component: PrintablesComponent
          },
          {
            path: 'custom-status',
            component: CustomStatusComponent
          },
          {
            path: 'webhook',
            component: WebhookComponent
          },
          {
            path: 'feature-visibility',
            component: FeatureVisibilityComponent
          },
          {
            path: 'permission-wizard-listing',
            component: PermissionWizardContainerComponent,
            resolve: { targetServices: PermissionResolver }
          },
          {
            path: 'permission-set-listing',
            component: PermissionSetContainerComponent,
            resolve: {targetServices: PermissionResolver}
          },
          {
            path: 'user-permission-listing',
            component: UserPermissionContainerComponent
          },
          {
            path: 'role-permission-listing',
            component: RolePermissionContainerComponent
          },
          {
            path: '',
            redirectTo: 'feature-visibility',
            pathMatch: 'full'
          },
        ]
      },
      {
        path: 'personalization',
        component: PersonalizationContainerComponent,
        children: [
          {
            path: 'personal-default-selection',
            component: PersonalDefaultSettingsComponent
          },
          {
            path: 'sidebar',
            component: SidebarComponent
          },
        ]
      },
      {
        path: '404',
        component: FourOhFourComponent
      },
      {
        path: '',
        redirectTo: `master-list`,
        pathMatch: 'full'
      },
      {
        path: '**',
        redirectTo: `404`,
        pathMatch: 'full'
      }
    ]
  },
  {
    path: '404',
    component: FourOhFourComponent
  },
  {
    path: '**',
    redirectTo: `${mainPath}`,
    pathMatch: 'full'
  },
];


