import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl } from '@angular/forms';
import { bl_fi_generic_doc_ext_RowClass } from 'blg-akaun-ts-lib';
import * as moment from 'moment';
import { internalJobOrderSearchModel } from '../../../../models/advanced-search-models/internal-job-order.model';

@Component({
  selector: 'app-edit-issue-linked-issues',
  templateUrl: './edit-issue-linked-issues.component.html',
  styleUrls: ['./edit-issue-linked-issues.component.css']
})
export class EditIssueLinkedIssuesComponent implements OnInit {

  @Input() localState: any;
  @Input() rowData: any[] = [];

  @Output() issue = new EventEmitter();

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Project', field: 'project', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Issue Type', field: 'project', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Issue Number', field: 'issueNumber', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Summary', field: 'issueSummary', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Description', field: 'issueSummary', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Assignee', field: 'issueSummary', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Priority', field: 'issueSummary', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Due Date', field: 'issueSummary', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Status', field: 'issueSummary', cellStyle: () => ({'text-align': 'left'})},
  ];

  searchModel = internalJobOrderSearchModel;

  constructor() { }

  ngOnInit() {
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
  }

  onRowClicked(e) {
    this.issue.emit(e);
  }

}
