import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { forkJoin, iif, Observable, observable, of, zip } from 'rxjs';
import { map, catchError, exhaustMap, withLatestFrom, mergeMap, switchMap } from 'rxjs/operators';
import {
  bl_fi_generic_doc_ext_RowClass,
  bl_fi_generic_doc_line_RowClass,
  BranchService,
  CompanyService,
  CustomerService,
  SupplierService,
  MerchantService,
  EmployeeService,
  FinancialItemService,
  GenericDocContainerModel,
  GenericDocLinkContainerModel,
  InternalOutboundDeliveryOrderService,
  Pagination,
  PricingSchemeLinkService,
  PricingSchemeService,
  SubQueryService,
  GenericDocLinkBackofficeEPService,
  GenDocLinkContainerModel,
  PickPackQueueService,
  JobSalesOrderkService,
  TenantUserProfileService,
  LocationService
} from 'blg-akaun-ts-lib';
import { InternalDeliveryOrderActions } from '../actions';
import { ToastrService } from 'ngx-toastr';
import { InternalDeliveryOrderStates } from '../states';
import { Store } from '@ngrx/store';
import { InternalDeliveryOrderSelectors } from '../selectors';
import { HDREditSelectors, HDRSelectors, LinkSelectors, PNSSelectors} from '../../../draft-controller/store/selectors';
import { ViewColumnFacade } from '../../../../facades/view-column.facade';
import { AppConfig } from 'projects/shared-utilities/visa';
import { DraftStates } from '../../../draft-controller/store/states';
import { UUID } from 'angular2-uuid'; 

@Injectable()
export class InternalDeliveryOrderEffects {

  

  apiVisa = AppConfig.apiVisa;
  
  loadDeliveryOrders$ = createEffect(() =>
    this.actions$.pipe(
      ofType(InternalDeliveryOrderActions.loadDeliveryOrderInit),
      switchMap(action =>
        this.doService
          .getByCriteriaSnapshot(action.pagination, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              const source: Observable<GenericDocContainerModel>[] = [];
              b.data.forEach( doc => 
                source.push(
                zip(
                  // this.compService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_comp.toString(), apiVisa).pipe(
                  //   catchError((err) => of(err))
                  // ),
                  this.brchService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_branch?.toString(), AppConfig.apiVisa).pipe(
                    catchError((err) => of(err))
                  ),
                  this.lctnService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_store?.toString(), AppConfig.apiVisa).pipe(
                    catchError((err) => of(err))
                  )).pipe(
                    map(([b_b, b_c]) => {
                      // doc.bl_fi_generic_doc_hdr.guid_comp = b_a.error ? b_a.error.code : b_a.data.bl_fi_mst_comp.name;
                      doc.bl_fi_generic_doc_hdr.code_branch = b_b.error ? b_b.error.code : b_b.data.bl_fi_mst_branch.name;
                      doc.bl_fi_generic_doc_hdr.code_location = b_c.error ? b_c.error.code : b_c.data.bl_inv_mst_location.name;
                      return doc;
                    })
                  )
              ));
              return iif(() => b.data.length > 0,
                forkJoin(source).pipe(map(() => b)),
                of(b)
              );
            })
          ).pipe(
            map((a) => {
              return InternalDeliveryOrderActions.loadDeliveryOrderSuccess({
                deliveryOrders: a.data
              })
            }),
            catchError((err) => {
              console.log(err);
              return of(
                InternalDeliveryOrderActions.loadDeliveryOrderFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  searchDeliveryOrders$ = createEffect(() =>
    this.actions$.pipe(
      ofType(InternalDeliveryOrderActions.searchDeliveryOrderInit),
      switchMap(action =>
        this.doService
          .getBySnapshotCustomQuery(action.searchDto, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              const source: Observable<GenericDocContainerModel>[] = [];
              b.data.forEach( doc => 
                source.push(
                zip(
                  // this.compService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_comp.toString(), apiVisa).pipe(
                  //   catchError((err) => of(err))
                  // ),
                  this.brchService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_branch.toString(), AppConfig.apiVisa).pipe(
                    catchError((err) => of(err))
                  ),
                  this.lctnService.getByGuid(doc.bl_fi_generic_doc_hdr.guid_store.toString(), AppConfig.apiVisa).pipe(
                    catchError((err) => of(err))
                  )).pipe(
                    map(([b_b, b_c]) => {
                      // doc.bl_fi_generic_doc_hdr.guid_comp = b_a.error ? b_a.error.code : b_a.data.bl_fi_mst_comp.name;
                      doc.bl_fi_generic_doc_hdr.code_branch = b_b.error ? b_b.error.code : b_b.data.bl_fi_mst_branch.name;
                      doc.bl_fi_generic_doc_hdr.code_location = b_c.error ? b_c.error.code : b_c.data.bl_inv_mst_location.name;
                      return doc;
                    })
                  )
              ));
              return iif(() => b.data.length > 0,
                forkJoin(source).pipe(map(() => b)),
                of(b)
              );
            })
          ).pipe(
            map((a) => {
              return InternalDeliveryOrderActions.loadDeliveryOrderSuccess({
                deliveryOrders: a.data
              })
            }),
            catchError((err) => {
              console.log(err);
              return of(
                InternalDeliveryOrderActions.loadDeliveryOrderFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );


  createDeliveryOrder$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.createDeliveryOrderInit),
    withLatestFrom(
      this.store.select(HDRSelectors.selectHdr),
      this.draftStore.select(PNSSelectors.selectAll),
      this.draftStore.select(LinkSelectors.selectAll),
    ),
    map(([action, hdr, pns, link]) => {
      const container = new GenericDocContainerModel;

      hdr.guid = UUID.UUID().toLowerCase(),
      // Temporary Fix to if amount = "NaN"
      hdr.amount_discount = Number(hdr.amount_discount) ? hdr.amount_discount : null;
      hdr.amount_net = Number(hdr.amount_net) ? hdr.amount_net : null;
      hdr.amount_open_balance = Number(hdr.amount_open_balance) ? hdr.amount_open_balance : null;
      hdr.amount_std = Number(hdr.amount_std) ? hdr.amount_std : null;
      hdr.amount_txn = Number(hdr.amount_txn) ? hdr.amount_txn : null;
      hdr.amount_discount = Number(hdr.amount_discount) ? hdr.amount_discount : null;

      container.bl_fi_generic_doc_hdr = hdr;

      // link.forEach((l) => {
      //   l.guid = null;
      //   l.guid_doc_2_hdr = hdr.guid;
      //   if (l.status === 'DRAFT') { l.status = null; }
      // });

      pns.forEach((line) => {
        // line.guid = null;
        // line.generic_doc_hdr_guid = null;
        // line.status = null;
        // line.revision = null;
        // line.created_by_subject_guid = null;
        // line.updated_by_subject_guid = null;
        // line.created_date = null;
        // line.updated_date = null;
        line.guid_comp = hdr.guid_comp;
        line.guid_branch = hdr.guid_branch;
        line.guid_store = hdr.guid_store;

        if (line.line_property_json && (<any>line.line_property_json).delivery_instructions) {
          const ext = new bl_fi_generic_doc_ext_RowClass();
          ext.guid_doc_hdr = hdr.guid.toString();
          ext.guid_doc_line = line.guid;
          ext.param_code = 'REQUESTED_DELIVERY_DATE';
          ext.param_name = 'REQUESTED_DELIVERY_DATE';
          ext.param_type = 'DATE';
          ext.value_datetime = (<any>line.line_property_json).delivery_instructions.deliveryDate;
          ext.value_json = (<any>line.line_property_json).delivery_instructions;
          container.bl_fi_generic_doc_ext.push(ext);
        }

      });

      container.bl_fi_generic_doc_line = pns;
      // container.bl_fi_generic_doc_link = link;
      
      return container;
    }),
    exhaustMap((b) => this.branchService.getByGuid(b.bl_fi_generic_doc_hdr.guid_branch.toString(), this.apiVisa).pipe(
      map(b_inner => {
        b.bl_fi_generic_doc_hdr.guid_comp = b_inner.data.bl_fi_mst_branch.comp_guid;
        //  assign code_branch
        b.bl_fi_generic_doc_hdr.code_branch = b_inner.data.bl_fi_mst_branch.code;
        b.bl_fi_generic_doc_line.forEach((lineItem) =>{
          lineItem.guid_comp = b.bl_fi_generic_doc_hdr.guid_comp;
          lineItem.guid_branch = b.bl_fi_generic_doc_hdr.guid_branch;
          lineItem.guid_store = b.bl_fi_generic_doc_hdr.guid_store;
        })
        return b;
      })
    )),
    // to get the company code
    exhaustMap((b) =>
        this.compService
          .getByGuid(b.bl_fi_generic_doc_hdr.guid_comp.toString(), this.apiVisa)
          .pipe(
            map((c_inner) => {
              // assign the code_company
              b.bl_fi_generic_doc_hdr.code_company = c_inner.data.bl_fi_mst_comp.code;
              return b;
            })
          )
      ),
    
      // to get the location code
      exhaustMap((c) =>
      this.lctnService
        .getByGuid(
          c.bl_fi_generic_doc_hdr.guid_store.toString(),
          this.apiVisa
        )
        .pipe(
          map((d_inner) => {

            // assign the code_location
            c.bl_fi_generic_doc_hdr.code_location = d_inner.data.bl_inv_mst_location.code;
            return c;
          })
        )
    ),
    exhaustMap((d) => this.doService.post(d, this.apiVisa).pipe(
      map((a: any) => {
        this.toastr.success(
          'The delivery order created successfully',
          'Success',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        this.viewColFacade.updateInstance(0, {
          deactivateAdd: false,
          deactivateList: false
        });
        this.viewColFacade.resetIndex(0);
        return InternalDeliveryOrderActions.createDeliveryOrderSuccess({hdrGuid:a.data.bl_fi_generic_doc_hdr.guid});
      }),
      catchError(err => {
        this.toastr.error(
          err.message,
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return of(InternalDeliveryOrderActions.createDeliveryOrderFailed({error: err.message}));
      })
    ))
  ));

  createGenDocLink$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.createDeliveryOrderSuccess, InternalDeliveryOrderActions.editDeliveryOrderSuccess),
    withLatestFrom(
      this.draftStore.select(LinkSelectors.selectAll),
    ),
    map(([action, link]) => {
      let container: GenDocLinkContainerModel[] = [];

        link.forEach((l) => {
          if (l.status === "DRAFT") {
            let docLink: GenDocLinkContainerModel =
              new GenDocLinkContainerModel();
            l.guid = null;
            l.guid_doc_2_hdr = action.hdrGuid;
            l.status = "ACTIVE";
            docLink.bl_fi_generic_doc_link = l;
            container.push(docLink);
          }
        });

        console.log("Doc Link Container", container);
        return container;
    }),
    exhaustMap((d) => this.genDocLinkService.post(d, this.apiVisa).pipe(
      map((a: any) => {
        return InternalDeliveryOrderActions.createDeliveryOrderGenDocLinkSuccess();
      }),
      catchError(err => {
        this.toastr.error(
          err.message,
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return of(InternalDeliveryOrderActions.createDeliveryOrderFailed({error: err.message}));
      })
    ))
  ));

  deleteDeliveryOrder$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.deleteDeliveryOrderInit),
    withLatestFrom(this.store.select(InternalDeliveryOrderSelectors.selectEntity)),
    exhaustMap(([a, b]) => this.doService.delete(b.bl_fi_generic_doc_hdr.guid.toString(), this.apiVisa).pipe(
      map(() => {
        this.toastr.success(
          'The delivery order deleted successfully',
          'Success',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        this.viewColFacade.updateInstance(0, {
          deactivateAdd: false,
          deactivateList: false
        });
        this.viewColFacade.resetIndex(0);
        return InternalDeliveryOrderActions.deleteDeliveryOrderSuccess();
      }),
      catchError(err => {
        this.toastr.error(
          err.message,
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return of(InternalDeliveryOrderActions.deleteDeliveryOrderFailed({error: err.message}));
      })
    ))
  ));

  editDeliveryOrder$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.editDeliveryOrderInit),
    withLatestFrom(
      this.store.select(InternalDeliveryOrderSelectors.selectDraftEdit),
      this.draftStore.select(HDRSelectors.selectHdr),
      this.draftStore.select(PNSSelectors.selectAll),
      this.draftStore.select(LinkSelectors.selectAll),
      ),
    map(([action, cont, hdr, pns]) => {
      cont.bl_fi_generic_doc_hdr = hdr;

      // let updatedLink = link.filter(a => a.status != "DRAFT_TEMP");
      // updatedLink.forEach(l => {
      //   if (l.guid.toString().length != 36) {
      //     l.guid = null;
      //     l.guid_doc_2_hdr = hdr.guid;
      //     if (l.status === 'DRAFT') {
      //       l.status = null;
      //     }
      //   }
      // })

      cont.bl_fi_generic_doc_line = pns;
      cont.bl_fi_generic_doc_link = null;

      pns = pns.filter(function (props:any) {
        delete props.qtyArranged;
        delete props.qtyPending;
        delete props.ppStatus;
        return true;
      });

      pns.forEach((item: bl_fi_generic_doc_line_RowClass) => {
        // If condition doesn't matter
        if (item.line_property_json && (<any>item.line_property_json).delivery_instructions) {
          const extIndex = cont.bl_fi_generic_doc_ext.findIndex(x => x.param_code === 'REQUESTED_DELIVERY_DATE' && x.guid_doc_line === item.guid);
          if (extIndex >= 0) {
            console.log("extIndex",extIndex);
            cont.bl_fi_generic_doc_ext[extIndex].value_datetime = (<any>item.line_property_json).delivery_instructions.deliveryDate
            cont.bl_fi_generic_doc_ext[extIndex].value_json = (<any>item.line_property_json).delivery_instructions;
            cont.bl_fi_generic_doc_ext[extIndex].status = item.status;
          } else {
            const ext = new bl_fi_generic_doc_ext_RowClass();
            ext.guid_doc_hdr = cont.bl_fi_generic_doc_hdr.guid.toString();
            ext.guid_doc_line = item.guid;
            ext.param_code = 'REQUESTED_DELIVERY_DATE';
            ext.param_name = 'REQUESTED_DELIVERY_DATE';
            ext.param_type = 'DATE';
            ext.value_datetime = (<any>item.line_property_json).delivery_instructions.deliveryDate;
            ext.value_json = (<any>item.line_property_json).delivery_instructions;
            cont.bl_fi_generic_doc_ext.push(ext);
          }
        }  
      });
      return cont;
    }),
    exhaustMap((a) => this.branchService.getByGuid(a.bl_fi_generic_doc_hdr.guid_branch.toString(), this.apiVisa).pipe(
      map(b_inner => {
        a.bl_fi_generic_doc_hdr.guid_comp = b_inner.data.bl_fi_mst_branch.comp_guid;
        //  assign code_branch
        a.bl_fi_generic_doc_hdr.code_branch = b_inner.data.bl_fi_mst_branch.code;
        a.bl_fi_generic_doc_line.forEach(lineItem => {
          lineItem.guid_comp = a.bl_fi_generic_doc_hdr.guid_comp;
          lineItem.guid_branch = a.bl_fi_generic_doc_hdr.guid_branch;
          lineItem.guid_store = a.bl_fi_generic_doc_hdr.guid_store;
        });
        return a;
      })
    )),
    // to get the company code
    exhaustMap((b) =>
        this.compService
          .getByGuid(b.bl_fi_generic_doc_hdr.guid_comp.toString(), this.apiVisa)
          .pipe(
            map((c_inner) => {
              // assign the code_company
              b.bl_fi_generic_doc_hdr.code_company = c_inner.data.bl_fi_mst_comp.code;
              return b;
            })
          )
      ),
    
      // to get the location code
      exhaustMap((c) =>
      this.lctnService
        .getByGuid(
          c.bl_fi_generic_doc_hdr.guid_store.toString(),
          this.apiVisa
        )
        .pipe(
          map((d_inner) => {

            // assign the code_location
            c.bl_fi_generic_doc_hdr.code_location = d_inner.data.bl_inv_mst_location.code;
            return c;
          })
        )
    ),
    exhaustMap(c_inner => this.doService.put(c_inner, this.apiVisa).pipe(
      map((response) => {
        this.toastr.success(
          'The delivery order edited successfully',
          'Success',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        this.viewColFacade.updateInstance(0, {
          deactivateAdd: false,
          deactivateList: false
        });
        this.viewColFacade.resetIndex(0);
        return InternalDeliveryOrderActions.editDeliveryOrderSuccess({hdrGuid:response.data.bl_fi_generic_doc_hdr.guid.toString()});
      }),
      catchError(err => {
        this.toastr.error(
          err.message,
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return of(InternalDeliveryOrderActions.editDeliveryOrderFailed({error: err.message}));
      })
    )),
      catchError(err => {
        this.toastr.error(
          err.message,
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return of(InternalDeliveryOrderActions.editDeliveryOrderFailed({error: err.message}));
      })
    ));

    updateGenDocLink$ = createEffect(() => this.actions$.pipe(
      ofType(InternalDeliveryOrderActions.editDeliveryOrderSuccess),
      withLatestFrom(
        this.draftStore.select(HDRSelectors.selectHdr),
        this.draftStore.select(LinkSelectors.selectAll),
        ),
      map(([action, hdr, link]) => {

        let container: GenDocLinkContainerModel[] = [];

        // let updatedLink = link.filter((a) => a.status === "ACTIVE");
        link.forEach((l) => {
          if (l.status==='ACTIVE' && l.guid!==null) {
            let docLink: GenDocLinkContainerModel =
              new GenDocLinkContainerModel();
            l.guid_doc_2_hdr = hdr.guid;
            docLink.bl_fi_generic_doc_link = l;
            container.push(docLink);
          }
        });
        console.log("Updated Doc Link Container", container);
        return container;
      }),
      exhaustMap((d) => this.genDocLinkService.put(d, this.apiVisa).pipe(
        map((a: any) => {
          return InternalDeliveryOrderActions.editDeliveryOrderGenDocLinkSuccess();
        }),
        catchError(err => {
          this.toastr.error(
            err.message,
            'Error',
            {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 1300
            }
          );
          return of(InternalDeliveryOrderActions.editDeliveryOrderGenDocLinkFailed({error: err.message}));
        })
      ))
    ));
    
  // selectEntity$ = createEffect(() => this.actions$.pipe(
  //   ofType(InternalDeliveryOrderActions.selectEntityInit),
  //   mergeMap((action) => {

  //     switch (action.entityType){
  //       case "customer": {this.service = this.cstmrService; break;}
  //       case null: {this.service = this.cstmrService; break;}
  //       case "supplier": {this.service = this.suppService; break;}
  //       case "merchant": {this.service = this.mercService; break;}
  //       case "employee": {this.service = this.empService; break;}
  //     }

  //     return this.service.getByGuid(action.entity.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString(), this.apiVisa).pipe(
  //     map(a => InternalDeliveryOrderActions.selectEntitySuccess({entity: a.data})),
  //     catchError(err => of(InternalDeliveryOrderActions.selectEntityFailed({error: err.message})))
  //   )})
  // ));

  selectLineItem$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.selectLineItemInit),
    map((action) => {console.log("item_guid", action.lineItem.item_guid); return action}),
    mergeMap(action => this.fiService.getByGuid(action.lineItem.item_guid.toString(), this.apiVisa).pipe(
      map(a => {console.log("a", a); return a}),
      map(a => InternalDeliveryOrderActions.selectLineItemSuccess({entity: a.data})),
      map(a => {console.log("Successfully select line item", a.entity); return a}),
      catchError(err => of(InternalDeliveryOrderActions.selectLineItemFailed({error: err.message})))
    ))
  ));

  // If line item is successfully fetched.
  getInvItem$ = createEffect(() =>
    this.actions$.pipe(
      ofType(InternalDeliveryOrderActions.selectLineItemSuccess),
      map((action) => {
        let query = `
        SELECT inv.guid as requiredGuid
        FROM bl_inv_mst_item_hdr AS inv
        INNER JOIN bl_fi_mst_item_hdr AS fi
        ON inv.guid_fi_mst_item = fi.guid
        WHERE fi.guid = '${action.entity.bl_fi_mst_item_hdr.guid}'
      `;
        return {
          subquery: query,
          table: "bl_inv_mst_item_hdr",
        };
      }),
      switchMap((req) =>
        this.subQueryService.post(req, AppConfig.apiVisa).pipe(
          map((resolve) => {
            if (resolve.data.length)
              return InternalDeliveryOrderActions.selectInvItem({
                invItem: resolve.data[0],
              });
            else
              return InternalDeliveryOrderActions.selectInvItem({ invItem: null });
          })
        )
      )
    )
  );

  selectPricingLink$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.selectPricingSchemeLink),
    mergeMap(action => {
      const paging = new Pagination();
      paging.conditionalCriteria.push({ columnName: 'item_hdr_guid', operator: '=', value: action.item.item_guid.toString() });
      return this.pslService.getByCriteria(paging, this.apiVisa).pipe(
        mergeMap((result: any) => {
          let allIds = result.data.map(id => this.getPricing(id.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr.toString()));
          return forkJoin(...allIds).pipe(
            map((idDataArray) => {
              result.data.forEach((eachContact, index) => {
                eachContact.pricing_hdr = idDataArray[index]?.data.bl_fi_mst_pricing_scheme_hdr.name;
              })
              return InternalDeliveryOrderActions.selectPricingSchemeLinkSuccess({ pricing: result.data });
            })
          )
        })
      )
    })
  ));

  updatePostingStatus$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.updatePostingStatusInit),
    mergeMap(action => this.doService.updatePostingStatus(action.status, this.apiVisa, action.doc.bl_fi_generic_doc_hdr.guid.toString()).pipe(
      map(a => {
        this.viewColFacade.showSuccessToast('Posting Successfully');
        this.viewColFacade.updateInstance(0, {
          deactivateAdd: false,
          deactivateList: false
      });
      this.viewColFacade.resetIndex(0);
      return InternalDeliveryOrderActions.updatePostingStatusSuccess({doc: a.data});
    }),
    catchError(err => {
      this.viewColFacade.showFailedToast(err);
      return of(InternalDeliveryOrderActions.updatePostingStatusFailed({error: err.message}))
    })
    ))
  ));

  getPricing(guid: string) {
    return this.pricingService.getByGuid(guid, this.apiVisa)
  }

  printJasperPdf$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.printJasperPdfInit),
    withLatestFrom(
      this.draftStore.select(HDRSelectors.selectHdr),
      this.store.select(InternalDeliveryOrderSelectors.selectPrintableFormatGuid)
    ),
    exhaustMap(([action, hdr, printableGuid]) => this.doService.printJasperPdf(
      hdr.guid.toString(),
      'CP_COMMERCE_INTERNAL_SALES_ORDERS_JASPER_PRINT_SERVICE', printableGuid, AppConfig.apiVisa).pipe(
        map(a => {
          const downloadURL = window.URL.createObjectURL(a);
          const link = document.createElement('a');
          link.href = downloadURL;
          link.download = `${hdr.server_doc_1}.pdf`;
          link.click();
          link.remove();
          this.viewColFacade.showSuccessToast('Purchase GRN Exported Successfully');
          return InternalDeliveryOrderActions.printJasperPdfSuccess();
        }),
        catchError(err => {
          this.viewColFacade.showFailedToast(err);
          return of(InternalDeliveryOrderActions.printJasperPdfFailed());
        })
      )
    )
  ));

  pickPackQueueAllocation$ = createEffect(() => this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.pickPackQueueAllocationInit),
    map((action) => {
      console.log('Edited Gen Doc', action.pickPackQueueAllocation);
      return {"pickPackQueueLine":action.pickPackQueueAllocation};
    }),
    exhaustMap((d) => this.pickPackQService.pickPackQueueAllocation(d, this.apiVisa).pipe(
      map((a: any) => {
        this.toastr.success(
          'Pick pack queue allocation successful',
          'Success',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 2000
          }
        );
        return InternalDeliveryOrderActions.pickPackQueueAllocationSuccess();
      }),
      catchError(err => {
        this.toastr.error(
          err.message,
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 2000
          }
        );
        return of(InternalDeliveryOrderActions.pickPackQueueAllocationFailed({ error: err.message }));
      })
    ))
  ));

  loadInternalJobDoc$ = createEffect(() =>
    this.actions$.pipe(
      ofType(InternalDeliveryOrderActions.loadInternalJobDocsInit),
      switchMap(action =>
        this.jobDocService
        .post(action.dto, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              let result = [];
              b.data.forEach((element) => {
                for (
                  let i = 0;
                  i < element.bl_del_job_docline_links.length;
                  i++
                ) {
                  if (element.bl_del_job_docline_links[i] == null) {
                    continue;
                  }
                  for (
                    let j = 0;
                    j < element.bl_del_job_docline_links[i].length;
                    j++
                  ) {
                    if (
                      element.bl_del_job_docline_links[i][j].guid_job_hdr ==
                      element.bl_del_job_hdr.guid
                    ) {
                      result.push({
                        ...element,
                        bl_del_job_docline_links:
                          element.bl_del_job_docline_links[i][j],
                        bl_fi_generic_doc_lines:
                          element.bl_fi_generic_doc_lines[i],
                      });
                    }
                  }
                }
              });
              const source: Observable<any>[] = [];
              result.forEach((doc) =>
                source.push(
                  zip(
                    this.profileService
                      .getProfileName(
                        this.apiVisa,
                        doc.bl_del_job_hdr.created_by_subject_guid ?
                          doc.bl_del_job_hdr.created_by_subject_guid.toString() : null
                      )
                      .pipe(catchError((err) => of(err))),
                    this.profileService
                      .getProfileName(
                        this.apiVisa,
                        doc.bl_fi_generic_doc_hdr?.created_by_subject_guid ?
                          doc.bl_fi_generic_doc_hdr.created_by_subject_guid.toString() : null
                      )
                      .pipe(catchError((err) => of(err))),
                  ).pipe(
                    map(([b_a, b_b]) => {
                      doc = Object.assign(
                        {
                          job_created_by_name: b_a.error
                            ? b_a.error.code
                            : b_a.data,
                          gendoc_created_by_name: b_b.error
                            ? b_b.error.code
                            : b_b.data,
                        },
                        doc
                      );
                      return doc;
                    })
                  )
                )
              );
              return iif(
                () => b.data.length > 0,
                forkJoin(source).pipe(
                  map((b_inner) => {
                    b.data = b_inner;
                    return b;
                  })
                ),
                of(b)
              );
            })
          )
          .pipe(
            map((a) => {
              console.log(a.data)
              return InternalDeliveryOrderActions.loadInternalJobDocsSuccess({
                jobDocs: a.data,
              })
            }),
            catchError((err) => {
              console.log(err);
              return of(
                InternalDeliveryOrderActions.loadInternalJobDocsFailure({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  loadPickupJobDoc$ = createEffect(() =>
    this.actions$.pipe(
      ofType(InternalDeliveryOrderActions.loadPickupJobDocsInit),
      switchMap(action =>
        this.jobDocService
        .post(action.dto, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              let result = [];
              b.data.forEach((element) => {
                for (
                  let i = 0;
                  i < element.bl_del_job_docline_links.length;
                  i++
                ) {
                  if (element.bl_del_job_docline_links[i] == null) {
                    continue;
                  }
                  for (
                    let j = 0;
                    j < element.bl_del_job_docline_links[i].length;
                    j++
                  ) {
                    if (
                      element.bl_del_job_docline_links[i][j].guid_job_hdr ==
                      element.bl_del_job_hdr.guid
                    ) {
                      result.push({
                        ...element,
                        bl_del_job_docline_links:
                          element.bl_del_job_docline_links[i][j],
                        bl_fi_generic_doc_lines:
                          element.bl_fi_generic_doc_lines[i],
                      });
                    }
                  }
                }
              });
              const source: Observable<any>[] = [];
              result.forEach((doc) =>
                source.push(
                  zip(
                    this.profileService
                      .getProfileName(
                        this.apiVisa,
                        doc.bl_del_job_hdr.created_by_subject_guid ?
                          doc.bl_del_job_hdr.created_by_subject_guid.toString() : null
                      )
                      .pipe(catchError((err) => of(err))),
                    this.profileService
                      .getProfileName(
                        this.apiVisa,
                        doc.bl_fi_generic_doc_hdr?.created_by_subject_guid ?
                          doc.bl_fi_generic_doc_hdr.created_by_subject_guid.toString() : null
                      )
                      .pipe(catchError((err) => of(err))),
                  ).pipe(
                    map(([b_a, b_b]) => {
                      doc = Object.assign(
                        {
                          job_created_by_name: b_a.error
                            ? b_a.error.code
                            : b_a.data,
                          gendoc_created_by_name: b_b.error
                            ? b_b.error.code
                            : b_b.data,
                        },
                        doc
                      );
                      return doc;
                    })
                  )
                )
              );
              return iif(
                () => b.data.length > 0,
                forkJoin(source).pipe(
                  map((b_inner) => {
                    b.data = b_inner;
                    return b;
                  })
                ),
                of(b)
              );
            })
          )
          .pipe(
            map((a) => {
              console.log(a.data)
              return InternalDeliveryOrderActions.loadPickupJobDocsSuccess({
                jobDocs: a.data,
              })
            }),
            catchError((err) => {
              console.log(err);
              return of(
                InternalDeliveryOrderActions.loadPickupJobDocsFailure({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  loadExternalJobDoc$ = createEffect(() =>
    this.actions$.pipe(
      ofType(InternalDeliveryOrderActions.loadExternalJobDocsInit),
      switchMap(action =>
        this.jobDocService
        .post(action.dto, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              let result = [];
              b.data.forEach((element) => {
                for (
                  let i = 0;
                  i < element.bl_del_job_docline_links.length;
                  i++
                ) {
                  if (element.bl_del_job_docline_links[i] == null) {
                    continue;
                  }
                  for (
                    let j = 0;
                    j < element.bl_del_job_docline_links[i].length;
                    j++
                  ) {
                    if (
                      element.bl_del_job_docline_links[i][j].guid_job_hdr ==
                      element.bl_del_job_hdr.guid
                    ) {
                      result.push({
                        ...element,
                        bl_del_job_docline_links:
                          element.bl_del_job_docline_links[i][j],
                        bl_fi_generic_doc_lines:
                          element.bl_fi_generic_doc_lines[i],
                      });
                    }
                  }
                }
              });
              const source: Observable<any>[] = [];
              result.forEach((doc) =>
                source.push(
                  zip(
                    this.profileService
                      .getProfileName(
                        this.apiVisa,
                        doc.bl_del_job_hdr.created_by_subject_guid ?
                          doc.bl_del_job_hdr.created_by_subject_guid.toString() : null
                      )
                      .pipe(catchError((err) => of(err))),
                    this.profileService
                      .getProfileName(
                        this.apiVisa,
                        doc.bl_fi_generic_doc_hdr?.created_by_subject_guid ?
                          doc.bl_fi_generic_doc_hdr.created_by_subject_guid.toString() : null
                      )
                      .pipe(catchError((err) => of(err))),
                  ).pipe(
                    map(([b_a, b_b]) => {
                      doc = Object.assign(
                        {
                          job_created_by_name: b_a.error
                            ? b_a.error.code
                            : b_a.data,
                          gendoc_created_by_name: b_b.error
                            ? b_b.error.code
                            : b_b.data,
                        },
                        doc
                      );
                      return doc;
                    })
                  )
                )
              );
              return iif(
                () => b.data.length > 0,
                forkJoin(source).pipe(
                  map((b_inner) => {
                    b.data = b_inner;
                    return b;
                  })
                ),
                of(b)
              );
            })
          )
          .pipe(
            map((a) => {
              console.log(a.data)
              return InternalDeliveryOrderActions.loadExternalJobDocsSuccess({
                jobDocs: a.data,
              })
            }),
            catchError((err) => {
              console.log(err);
              return of(
                InternalDeliveryOrderActions.loadExternalJobDocsFailure({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  cancelJobs$ = createEffect(() =>
  this.actions$.pipe(
    ofType(InternalDeliveryOrderActions.cancelJobDocInit),

    mergeMap((action) => {
      const body = {
        jobs_guids: action.jobGuids
      }

      return this.jobDocService.cancelJobs(this.apiVisa, body)
      .pipe(
        map((response: any) => {
          this.toastr.success(
            'Cancel Jobs',
            'Success',
            {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 1300
            }
          );
          return InternalDeliveryOrderActions.cancelJobDocSuccess()
        }),
        catchError((err) => {
          console.log(err)
          this.toastr.error(
            err.message,
            'Error',
            {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 1300
            }
          );
          return of(InternalDeliveryOrderActions.cancelJobDocFailed({ error: err.messsage }))
        })
      )
    })
));



  service: CustomerService | SupplierService | MerchantService | EmployeeService;

  constructor(
    private actions$: Actions,
    private doService: InternalOutboundDeliveryOrderService,
    private genDocLinkService: GenericDocLinkBackofficeEPService,
    private toastr: ToastrService,
    private store: Store<InternalDeliveryOrderStates>,
    private draftStore: Store<DraftStates>,
    private branchService: BranchService,
    private viewColFacade: ViewColumnFacade,
    private cstmrService: CustomerService,
    private suppService: SupplierService,
    private mercService: MerchantService,
    private empService: EmployeeService,
    private pricingService: PricingSchemeService,
    private pslService: PricingSchemeLinkService,
    private fiService: FinancialItemService,
    private subQueryService: SubQueryService,
    private pickPackQService: PickPackQueueService,
    private jobDocService: JobSalesOrderkService,
    private profileService: TenantUserProfileService,
    protected brchService: BranchService,
    protected lctnService: LocationService,
    protected compService: CompanyService,
  ) {}
}
