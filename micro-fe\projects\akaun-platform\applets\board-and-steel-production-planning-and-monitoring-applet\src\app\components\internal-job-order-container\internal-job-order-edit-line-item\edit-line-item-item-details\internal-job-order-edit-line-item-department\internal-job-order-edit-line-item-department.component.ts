import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup, FormControl } from '@angular/forms';
import {
  bl_fi_generic_doc_line_RowClass,
  DimensionContainerModel,
  DimensionService,
  Pagination,
  ProfitCenterContainerModel,
  ProfitCenterService,
  ProjectCoaContainerModel,
  ProjectCoaService,
  SegmentCoaContainerModel,
  SegmentCoaService
} from 'blg-akaun-ts-lib';
import { AppConfig } from 'projects/shared-utilities/visa';
import { combineLatest, Observable } from 'rxjs';
import { SubSink } from 'subsink2';
import { AppletSettings } from '../../../../../models/applet-settings.model';
@Component({
  selector: 'app-internal-job-order-edit-line-item-department',
  templateUrl: './internal-job-order-edit-line-item-department.component.html',
  styleUrls: ['./internal-job-order-edit-line-item-department.component.css']
})
export class InternalJobOrderEditLineItemDepartmentComponent implements OnInit, OnDestroy {

  @Input() dept$: Observable<{
    guid_dimension: string,
    guid_profit_center: string,
    guid_project: string,
    guid_segment: string
  }>;
  @Input() line$: Observable<bl_fi_generic_doc_line_RowClass>;
  @Input() appletSettings$: Observable<AppletSettings>;

  private subs = new SubSink();

  form: FormGroup;

  leftColControls = [
    {label: 'Segment', formControl: 'segment', type: 'segment', readonly: false, hint: ''},
    {label: 'Profit Centre', formControl: 'profitCenter', type: 'profitCenter', readonly: false, hint: ''},
  ];

  rightColControls = [
    {label: 'Dimension', formControl: 'dimension', type: 'dimension', readonly: false, hint: ''},
    {label: 'Project', formControl: 'project', type: 'project', readonly: false, hint: ''},
  ];

  dimension: DimensionContainerModel[];
  profitCenter: ProfitCenterContainerModel[];
  project: ProjectCoaContainerModel[];
  segment: SegmentCoaContainerModel[];

  apiVisa = AppConfig.apiVisa;
  copyFromHdr = new FormControl();

  constructor(
    private dimensionService: DimensionService,
    private profitCenterService: ProfitCenterService,
    private projectCOAService: ProjectCoaService,
    private segmentCOAService: SegmentCoaService
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      segment: new FormControl(),
      profitCenter: new FormControl(),
      dimension: new FormControl(),
      project: new FormControl(),
    });
    this.subs.sink = this.dimensionService.getByCriteria(new Pagination(0, 100), this.apiVisa).subscribe(
      {next: resolve => this.dimension = resolve.data}
    );
    this.subs.sink = this.profitCenterService.getByCriteria(new Pagination(0, 100), this.apiVisa).subscribe(
      {next: resolve => this.profitCenter = resolve.data}
    );
    this.subs.sink = this.projectCOAService.getByCriteria(new Pagination(0, 100), this.apiVisa).subscribe(
      {next: resolve => this.project = resolve.data}
    );
    this.subs.sink = this.segmentCOAService.getByCriteria(new Pagination(0, 100), this.apiVisa).subscribe(
      {next: resolve => this.segment = resolve.data}
    );
    this.subs.sink = combineLatest([this.copyFromHdr.valueChanges, this.dept$]).subscribe({
      next: ([a, b]) => {
        if (a) {
          this.form.patchValue({
            segment: b.guid_segment,
            profitCenter: b.guid_profit_center,
            dimension: b.guid_dimension,
            project: b.guid_project,
          });
        } else {
          this.form.reset();
        }
    }});
    this.subs.sink = this.appletSettings$.subscribe({next: resolve => {
      resolve?.ENABLE_DIMENSION ? this.form.controls['dimension'].enable() : this.form.controls['dimension'].disable();
      resolve?.ENABLE_SEGMENT ? this.form.controls['segment'].enable() : this.form.controls['segment'].disable();
      resolve?.ENABLE_PROFIT_CENTER ? this.form.controls['profitCenter'].enable() : this.form.controls['profitCenter'].disable();
      resolve?.ENABLE_PROJECT ? this.form.controls['project'].enable() : this.form.controls['project'].disable();
    }});
    this.copyFromHdr.patchValue(true);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
