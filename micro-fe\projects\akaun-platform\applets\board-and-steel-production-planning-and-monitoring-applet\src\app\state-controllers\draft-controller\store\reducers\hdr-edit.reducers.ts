import { Action, createReducer, on } from '@ngrx/store';
import { bl_fi_generic_doc_hdr_RowClass, bl_mrp_job_order_hdr_RowClass } from 'blg-akaun-ts-lib';
import { InternalJobOrderActions } from '../../../internal-job-order-controller/store/actions';
import { HDREditActions, PlannedOutputActions } from '../actions';
import { initState } from '../states/hdr-edit.states';

export const hdrReducers = createReducer(
    initState,
    on(InternalJobOrderActions.selectEntity, (state, action) => ({
        ...state,
        ...action.entity.bl_mrp_job_order_hdr
      })),
    on(HDREditActions.updateMain, (state, action) => {
              // TODO: get confirmation on data storage
        // state.guid_branch = action.form.branch;
        // state.guid_store = action.form.location;
        // state.doc_desc = action.form.machineCode;
        // state.doc_remarks = action.form.remarks;
        // state.date_txn = action.form.date;
        state.date_txn = action.form.jobOrderDate
        state.item_code = action.form.itemCode,
        state.item_name = action.form.itemName,
        state.item_guid = action.form.itemGuid,
        state.process_status = action.form.process_status,
        state.qc_output_status = action.form.qcOutputStatus,
        state.uom = action.form.uom,
        state.total_container_measure = action.form.totalContainerMeasure,
        state.container_qty = action.form.containerQty,
        state.ad_hoc_qty = action.form.adHocQty,
        state.total_container_measure = action.form.totalContainerMeasure,
        state.estimated_pkg_date = action.form.estimatedPackingDate,
        state.completion_date = action.form.completionDate
        state.remarks = action.form.remarks

        state.uom_json = {
           'uom': action.form.uom,
        }
        return state;
    }),
    on(HDREditActions.updateGroupTemplate, (state, action) => {
        state.template_group_guid = action.groupGuid;
        return state;
    }),
    on(HDREditActions.resetHDRSuccess, (state, action) => ({
        ...state,
        ...action.hdr
    }))
);

export function reducers(state: bl_mrp_job_order_hdr_RowClass | undefined, action: Action) {
    return hdrReducers(state, action);
}
