import { FormControl, FormGroup } from '@angular/forms';
import { SearchModel } from 'projects/shared-utilities/models/search-model';
import { STATUS, CURRENCY, ENTITY_TYPE, EntityConstants } from '../constants/customer-constants';

export const customerCreditLimitSearchModel: SearchModel = {
  label: {
    modifiedDate: 'Modified Date',
    status: 'Status',
  },
  dataType: {

    modifiedDate: 'date',
    status: ['select', STATUS],
  },
  form: new FormGroup({
    listGuid: new FormControl(),
    modifiedDate: new FormGroup({
      from: new FormControl(),
      to: new FormControl()
    }),
    status: new FormControl()
  }),

  // SELECT  hdr.guid as requiredGuid FROM bl_fi_mst_label_hdr as hdr

  //  AND ( hdr.name ILIKE \'%' + text + '%\' OR hdr.code ILIKE \'%' + text + '%\')
  query: (query) => `SELECT DISTINCT (hdr.guid) AS requiredGuid FROM bl_fi_entity_credit_limit_hdr AS hdr WHERE ( hdr.code ILIKE '%${query}%' OR  hdr.name ILIKE '%${query}%')`,
  table: `bl_fi_entity_credit_limit_hdr`,
  queryCallbacks: {
    status: status => status ? ` AND hdr.status = '${status}'` : '',
    modifiedDate: modifiedDate => {
      if (modifiedDate.from || modifiedDate.to) {
        const from = modifiedDate.from ? modifiedDate.from : modifiedDate.to;
        const to = modifiedDate.to ? modifiedDate.to : modifiedDate.from;
        return `hdr.updated_date >= '${from.format(EntityConstants.DateTimeFormat)}' AND hdr.updated_date <= '${to.format(EntityConstants.DateTimeFormat)}'`;
      }
      return '';
    },
  },
};

