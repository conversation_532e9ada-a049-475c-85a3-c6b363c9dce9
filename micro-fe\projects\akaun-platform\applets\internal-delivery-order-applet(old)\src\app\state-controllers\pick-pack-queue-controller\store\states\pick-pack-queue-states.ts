import {
  bl_fi_generic_doc_line_RowClass, EntityContainerModel,
  FinancialItemContainerModel,
  GenericDocARAPContainerModel,
  GenericDocContainerModel,
  JsonDatatypeInterface,
  PickPackQueueContainerModel,
  PricingSchemeLinkContainerModel
} from 'blg-akaun-ts-lib';

export interface PickPackQueueState {
  selectedEntity: GenericDocContainerModel;
  loadedGenDocs: PickPackQueueContainerModel[];
  totalRecords: number;
  selectedCustomer: EntityContainerModel;
  selectedShippingAddress: JsonDatatypeInterface;
  selectedBillingAddress: JsonDatatypeInterface;
  selectedItem: FinancialItemContainerModel;
  selectedLineItem: bl_fi_generic_doc_line_RowClass;
  selectedContraDoc: GenericDocContainerModel;
  selectedContraLink: GenericDocARAPContainerModel;
  selectedPricingScheme: any;
  pricingSchemeLink: PricingSchemeLinkContainerModel[];
  refreshGenDocListing: boolean;
  selectedDetails: any;
  editMode: boolean;
  knockoffListingConfig: any;
  selectedOrder: GenericDocContainerModel;
  shopeeGetShipParam: any;
  selectedPricingSchemeGUID: string;

  // For Serial, Bin, Batch
  selectedInvItem: any;
  selectedSerial: any;
  selectedBin: any;
  selectedBatch: any;
  selectedCompGuid: string;
  selectedGuid: string;
  selectedMembershipGuid: string;
}

export const initState: PickPackQueueState = {
  selectedEntity: null,
  loadedGenDocs: null,
  totalRecords: 0,
  selectedCustomer: null,
  selectedShippingAddress: null,
  selectedBillingAddress: null,
  selectedItem: null,
  selectedLineItem: null,
  selectedContraDoc: null,
  selectedContraLink: null,
  refreshGenDocListing: false,
  selectedDetails: null,
  selectedPricingScheme: null,
  pricingSchemeLink: null,
  editMode: false,
  knockoffListingConfig: null,
  selectedOrder: null,
  shopeeGetShipParam: null,
  selectedPricingSchemeGUID: null,

  // For Serial, Bin, Batch
  selectedInvItem: null,
  selectedSerial: null,
  selectedBin: null,
  selectedBatch: null,
  selectedCompGuid: null,
  selectedGuid: null,
  selectedMembershipGuid: null
};
