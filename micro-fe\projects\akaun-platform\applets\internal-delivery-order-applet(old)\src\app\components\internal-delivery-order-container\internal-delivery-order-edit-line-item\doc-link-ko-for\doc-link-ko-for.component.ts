// import { Component, EventEmitter, OnInit, OnDestroy, Input, Output, ViewChild } from '@angular/core';
// import { SubSink } from 'subsink2';
// import { combineLatest, forkJoin, iif, Observable, of, Subject, zip } from 'rxjs';
// import { catchError, exhaustMap, filter, map, mergeMap, switchMap, tap } from 'rxjs/operators';
// import { Pagination, SubQueryService, bl_fi_generic_doc_link_RowClass, bl_fi_generic_doc_line_RowClass, bl_fi_generic_doc_hdr_RowClass, GenericDocHdrService } from 'blg-akaun-ts-lib';
// import { AppConfig } from 'projects/shared-utilities/visa';
// import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
// import { formatMoneyInList } from 'projects/shared-utilities/format.utils';
// import * as moment from 'moment';
// import { UUID } from 'angular2-uuid';

// @Component({
//   selector: 'app-internal-delivery-order-doc-link-ko-for',
//   templateUrl: './doc-link-ko-for.component.html',
//   styleUrls: ['./doc-link-ko-for.component.css']
// })
// export class DocLinkKoForComponent implements OnInit, OnDestroy {

//   @Input() localState: any;
//   @Input() hdr: bl_fi_generic_doc_hdr_RowClass;
//   @Input() service;
//   @Input() serverDocTypeDoc1: string;
//   @Input() store;
//   @Input() GenDocActions;
//   @Input() draftStore;
//   @Input() LinkActions;
//   @Input() LinkSelectors;
//   @Input() PNSActions;
//   @Input() PNSSelectors;
//   @Input() selectedLineItem$: Observable<any>;
//   @Input() links$: Observable<any>;
//   @Input() AppletConstants;

//   @Output() addKO = new EventEmitter();
//   @Output() showDetails = new EventEmitter();

//   protected subs = new SubSink();
  
//   gridApi;
//   enableListing;
//   draftLinks: Map<any, any>; draftPNS;
//   draftLinksIds = []; draftPNSIds = [];
//   knockoffTrigger = false; // An indicator to refresh server data
//   itemGuid;
  
//   apiVisa = AppConfig.apiVisa;

//   @ViewChild(PaginationComponent) paginationComp: PaginationComponent;

//   defaultColDef = {
//     filter: 'agTextColumnFilter',
//     floatingFilter: true,
//     floatingFilterComponentParams: { suppressFilterButton: true },
//     minWidth: 200,
//     flex: 2,
//     sortable: true,
//     resizable: true,
//     suppressCsvExport: true
//   };
  
//   columnsDefs = [
//     { headerName: 'Doc No.', field: 'doc_number', cellStyle: () => ({ 'text-align': 'left' }), maxWidth: 100 },
//     { headerName: 'Server Doc Type', field: 'server_doc_type', cellStyle: () => ({ 'text-align': 'left' }) },    
//     { headerName: 'Txn Date', field: 'date_txn', cellStyle: () => ({ 'text-align': 'left' }), maxWidth: 100, 
//       valueFormatter: params => params.value ? moment(params.value).format('YYYY-MM-DD') : null },
//     { headerName: 'UOM', field: 'uom', cellStyle: () => ({ 'text-align': 'left' }), maxWidth: 100 },
//     { headerName: 'Unit Price (Inc. of Tax)', field: 'unit_price_txn', type: 'numericColumn', maxWidth: 150,
//       valueFormatter: (params) => params.value ? formatMoneyInList(params.value) : null },
//     // { headerName: 'Base Qty.', field: 'order_qty', type: 'numericColumn', maxWidth: 100 },
//     // { headerName: 'Bal. Qty.', field: 'open_qty', type: 'numericColumn', maxWidth: 100 },
//     { headerName: 'Knocked Off Qty.', field: 'ko_qty', type: 'numericColumn' },
//     // { headerName: 'Knocked Off Qty.', field: 'knocked_off_qty', type: 'numericColumn', maxWidth: 100 },
//     // { headerName: 'Knockoff Qty.', type: 'numericColumn', maxWidth: 150,
//     //   editable: true,
//     //   valueGetter: (params) => { return params.data.ko_qty; },
//     //   valueSetter: (params) => {
//     //     let newKOvalue = parseInt(params.newValue);
//     //     let valueChanged = newKOvalue <= params.data.open_qty && newKOvalue >= 0;
//     //     if (valueChanged) {
//     //       params.data.ko_qty = newKOvalue;
//     //     }
//     //     return valueChanged;
//     //   }
//     // },
//     // { headerName: 'Status', field: 'status', cellStyle: () => ({ 'text-align': 'left' }) }
//   ];

//   constructor(
//     private subQueryService: SubQueryService,
//     private genDocHdrService: GenericDocHdrService,
//     ) {
//   }

//   ngOnInit() {
//     if(this.hdr.doc_entity_hdr_guid && this.hdr.guid_comp){
//       this.enableListing = true;
//     } else {
//       this.enableListing = false;
//     }
//     this.subs.sink = this.draftStore.select(this.LinkSelectors.selectLinkState).subscribe(
//       resolve => {
//         this.draftLinks = resolve.entities;
//         this.draftLinksIds = resolve.ids;
//       }
//     );
//     this.subs.sink = this.draftStore.select(this.PNSSelectors.selectPNSState).subscribe(
//       resolve => {
//         this.draftPNS = resolve.entities;
//         this.draftPNSIds = resolve.ids;
//       }
//     );
//   }

//   onGridReady(params) {
//     this.gridApi = params.api;
//     this.gridApi.closeToolPanel();
//     this.setGridData();
//   }

//   setGridData() {
//     const datasource = {
//       getRows: grid => {
//         this.subs.sink = combineLatest([this.selectedLineItem$, this.links$]).pipe(
//           mergeMap(([a, b]) => {
//             console.log("this.selectedLineItem$", a);
//             const source: Observable<any>[] = [];
//             let match = false;
//             b.forEach(link => {
//               if (
//                 link.status !== "DRAFT_TEMP" &&
//                 link.status !== "DELETED" &&
//                 link.guid_doc_2_line === a.guid &&
//                 link.server_doc_type_doc_2_line === this.AppletConstants.docType &&
//                 link.server_doc_type_doc_1_line === this.serverDocTypeDoc1
//               ) {
//                 match = true;
//                 const pagination = new Pagination();
//                 pagination.sortCriteria.push(
//                   // { columnName: 'doc_entity_hdr_guid', value: this.hdr.doc_entity_hdr_guid? this.hdr.doc_entity_hdr_guid.toString():'' },
//                   { columnName: 'guid_comp', value: this.hdr.guid_comp? this.hdr.guid_comp.toString():'' },
//                   { columnName: 'line_guids', value: link.guid_doc_1_line },
//                   { columnName: 'guids', value: link.guid_doc_1_hdr }
//                 )
//                 source.push(
//                   zip(
//                     // Get source document no.
//                     this.genDocHdrService.getByGuid(link.guid_doc_1_hdr.toString(), this.apiVisa).pipe(
//                       catchError((err) => of(err))
//                     ),
//                     // Get line item details
//                     <Observable<any>>this.service.getGenericDocHdrLineLinkByCriteria(pagination, this.apiVisa).pipe(
//                       catchError((err) => of(err))
//                     )
//                   ).pipe(map(([hdr, line]) => {
//                     let line1 = line.data.filter(a => a.bl_fi_generic_doc_line.guid === link.guid_doc_1_line)[0];
//                     this.itemGuid = line1.bl_fi_generic_doc_line.item_guid;
//                     let temp = {
//                       item_guid: line1.bl_fi_generic_doc_line.item_guid,
//                       doc_number: hdr.data.bl_fi_generic_doc_hdr.server_doc_1,
//                       server_doc_type: link.server_doc_type_doc_1_line,
//                       uom: line1.bl_fi_generic_doc_line.uom,
//                       unit_price_txn: line1.bl_fi_generic_doc_line.unit_price_txn,
//                       date_txn: link.date_txn,
//                       ko_qty: link.quantity_contra
//                     }

//                     return temp;
//                   }))
//                 );
//               }
//             });
//             return iif(() => match,
//               forkJoin(source).pipe(map((b_inner) => {
//                 b = b_inner;
//                 return b
//               })),
//               of([])
//             );
//           })
//         ).subscribe(resolved => {
//           grid.success({
//             rowData: resolved,
//             rowCount: resolved.length
//           });
//         }, err => {
//           console.log(err);
//         });
//       }
//     }
//     this.gridApi.setServerSideDatasource(datasource);
//   }

//   onRowClicked(item) {
//     console.log(item);
//     console.log(this.service);
//     console.log(this.serverDocTypeDoc1);

//     // TODO: to view the link and perform delete operation
    
//   }

//   onAdd() {
//     this.addKO.emit(
//       {
//         store: this.store,
//         GenDocActions: this.GenDocActions,
//         itemGuid: this.itemGuid,
//         service: this.service,
//         serverDocTypeDoc1: this.serverDocTypeDoc1
//       }
//     );
//   }

//   onKnockoff() {
//     const selectedLines = this.gridApi.getSelectedRows();
//     console.log(selectedLines);
//     selectedLines.filter(b => b.ko_qty > 0).forEach(a => {
//       // check if any line item in draft that is exactly the same as the line item to be knocked off
//       // console.log(this.findExistingLineItemDraft(a));
//       if(this.findExistingLineItemDraft(a)) {
//         this.editLineAndLink(a);
//       } else {
//         this.createNewLineAndLink(a);
//       }
//     })
//     // this.clear();
//     // this.setGridData();
//   }

//   findExistingLineItemDraft(draft: any) {
//     let isExist = false;

//     this.draftLinksIds.forEach(id => {
//       if(
//         this.draftLinks[id].status !== "DELETED" && this.draftLinks[id].status !== "DRAFT_TEMP" &&
//         this.draftLinks[id].server_doc_type_doc_1_line === this.serverDocTypeDoc1 &&
//         this.draftLinks[id].guid_doc_1_line === draft.bl_fi_generic_doc_line.guid &&
//         this.draftLinks[id].guid_doc_1_hdr === draft.bl_fi_generic_doc_hdr.guid
//       ) {
//         isExist = true;
//       }
//     })

//     return isExist;
//   }

//   createNewLineAndLink(a: any) {
//     const line = new bl_fi_generic_doc_line_RowClass();
//     line.guid = UUID.UUID().toLowerCase();

//     line.item_guid = a.item_guid;
//     line.item_code = a.item_code;
//     line.item_name = a.item_name;
//     line.quantity_base = a.ko_qty; // knock off qty
//     line.unit_price_std = a.unit_price_std;
//     line.unit_price_txn = a.unit_price_txn;
//     line.tax_gst_code = a.tax_gst_code;
//     line.tax_gst_rate = a.tax_gst_rate;
//     line.amount_tax_gst = a.amount_tax_gst;
//     line.tax_wht_code = a.tax_wht_code;
//     line.tax_wht_rate = a.tax_wht_rate;
//     line.amount_tax_wht = a.amount_tax_wht;
//     line.amount_discount = a.amount_discount;
//     line.amount_net = a.amount_net;
//     line.amount_std = a.amount_std;
//     line.amount_txn = a.amount_txn;
//     line.item_remarks = a.item_remarks;
//     line.item_txn_type = a.item_txn_type;
//     line.guid_dimension = a.guid_dimension;
//     line.guid_profit_center = a.guid_profit_center;
//     line.guid_project = a.guid_project;
//     line.guid_segment = a.guid_segment;
//     line.item_property_json = a.item_property_json;
//     line.line_property_json = a.line_property_json;
//     line.txn_type = a.txn_type;
//     line.uom = a.uom;
//     line.uom_to_base_ratio = a.uom_to_base_ratio;
//     line.qty_by_uom = a.qty_by_uom;
//     line.unit_price_std_by_uom = a.unit_price_std_by_uom;
//     line.unit_price_txn_by_uom = a.unit_price_txn_by_uom;
//     line.unit_disc_by_uom = a.unit_disc_by_uom;
//     line.quantity_signum = this.AppletConstants.quantity_signum;
//     line.amount_signum = this.AppletConstants.amount_signum;
//     line.server_doc_type = this.AppletConstants.docType;
//     line.client_doc_type = this.AppletConstants.docType;
//     line.date_txn = new Date();
//     line.status = 'DRAFT';

//     // To confirm whether to copy these values
//     // line.serial_no = a.serial_no;
//     // line.bin_no = a.bin_no;
//     // line.batch_no = a.batch_no;

//     const link = new bl_fi_generic_doc_link_RowClass();
//     link.guid_doc_2_line = line.guid;
//     link.guid_doc_1_hdr = a.hdr_guid;
//     link.guid_doc_1_line = a.line_guid;
//     link.server_doc_type_doc_1_hdr = a.server_doc_type_hdr;
//     link.server_doc_type_doc_1_line = a.server_doc_type_line;
//     link.server_doc_type_doc_2_hdr = this.AppletConstants.docType;
//     link.server_doc_type_doc_2_line = this.AppletConstants.docType;
//     // link.txn_type = 'ISO_IPO';
//     link.txn_type = 'KO';
//     link.quantity_signum = -1;
//     // link.quantity_contra = line.quantity_base;
//     link.quantity_contra = a.ko_qty;
//     link.date_txn = new Date();
//     link.status = 'DRAFT';

//     this.draftStore.dispatch(this.LinkActions.addLink({ link }));
//     this.draftStore.dispatch(this.PNSActions.addPNS({ pns: line }));
//   }

//   editLineAndLink(a: any) {
//     // check if DRAFT_TEMP is exist
//     let isExist = false;
//     let guid_doc_2_line;
//     let lineGuid;
//     // Find and update the affected Link
//     // TODO: create another temporary link for open_qty tracking
//     this.draftLinksIds.forEach(id => {
//       if(
//         this.draftLinks[id].status !== "DRAFT_TEMP" &&
//         this.draftLinks[id].server_doc_type_doc_1_line === this.serverDocTypeDoc1 &&
//         this.draftLinks[id].guid_doc_1_line === a.bl_fi_generic_doc_line.guid &&
//         this.draftLinks[id].guid_doc_1_hdr === a.bl_fi_generic_doc_hdr.guid
//       ) {
//         // update quantity_contra (on existing doc link in draft)
//         this.draftLinks[id].quantity_contra += a.ko_qty;
//         this.draftStore.dispatch(this.LinkActions.editLink({ link: this.draftLinks[id] }));
//         lineGuid = this.draftLinks[id].guid_doc_2_line;

//         if(this.draftLinks[id].status === "ACTIVE") {
//           this.draftLinksIds.forEach(id_2 => {
//             if(
//               this.draftLinks[id_2].status === "DRAFT_TEMP" &&
//               this.draftLinks[id_2].server_doc_type_doc_1_line === this.serverDocTypeDoc1 &&
//               this.draftLinks[id_2].guid_doc_1_line === a.bl_fi_generic_doc_line.guid &&
//               this.draftLinks[id_2].guid_doc_1_hdr === a.bl_fi_generic_doc_hdr.guid
//             ) {
//               // update quantity_contra (if DRAFT_TEMP is exist)
//               this.draftLinks[id_2].quantity_contra += a.ko_qty;
//               this.draftStore.dispatch(this.LinkActions.editLink({ link: this.draftLinks[id_2] }));
//               isExist = true;
//               guid_doc_2_line = this.draftLinks[id].guid_doc_2_line;
//             }
//           })          
//         }
//       }
//     })

//     if(!isExist) {
//       // create new temporary link (also need to composite DRAFT_TEMP)
//       const link = new bl_fi_generic_doc_link_RowClass();
//       link.guid_doc_2_line = guid_doc_2_line;
//       link.guid_doc_1_hdr = a.hdr_guid;
//       link.guid_doc_1_line = a.line_guid;
//       link.server_doc_type_doc_1_hdr = a.server_doc_type_hdr;
//       link.server_doc_type_doc_1_line = a.server_doc_type_line;
//       link.server_doc_type_doc_2_hdr = this.AppletConstants.docType;
//       link.server_doc_type_doc_2_line = this.AppletConstants.docType;
//       // link.txn_type = 'ISO_IPO';
//       link.txn_type = 'KO';
//       link.quantity_signum = -1;
//       // link.quantity_contra = line.quantity_base;
//       link.quantity_contra = a.ko_qty;
//       link.date_txn = new Date();
//       link.status = 'DRAFT_TEMP'; // Temporary draft, will not upsert to database

//       this.draftStore.dispatch(this.LinkActions.addLink({ link }));
//     }

//     // Find and update the affected PNS line item
//     this.draftPNSIds.forEach(id => {
//       if(
//         // this.draftPNS[id].server_doc_type_doc_1_line === this.serverDocTypeDoc1 &&
//         // this.draftPNS[id].guid_doc_1_line === a.bl_fi_generic_doc_line.guid &&
//         // this.draftPNS[id].guid_doc_1_hdr === a.bl_fi_generic_doc_hdr.guid
//         this.draftPNS[id].guid === lineGuid
//       ) {
//         // update quantity_base
//         this.draftPNS[id].quantity_base += a.ko_qty;
//         this.draftStore.dispatch(this.PNSActions.editPNS({ pns: this.draftPNS[id] }));
//       }
//     })
//   }

//   ngOnDestroy() {
//     this.subs.unsubscribe();
//   }

// }
