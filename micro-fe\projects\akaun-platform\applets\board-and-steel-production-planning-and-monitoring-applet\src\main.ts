import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { LicenseManager } from 'ag-grid-enterprise';
import { environment } from 'src/environments/environment';

LicenseManager.setLicenseKey
  ('CompanyName=WAVELET SOLUTIONS SDN. BHD.,LicensedApplication=akaun.com,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-026234,ExpiryDate=23_April_2023_[v2]_MTY4MjIwNDQwMDAwMA==674cd70d9fb70c2f7af064e2539295d0');

if (environment.production) {
  enableProdMode();
} else {
  sessionStorage.setItem('appletGuid', '97f0868a-5880-46f6-9770-5c3bbde840fb');
  sessionStorage.setItem('appletCode', 'MRP_INTERNAL_JOB_ORDER_APPLET');
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
