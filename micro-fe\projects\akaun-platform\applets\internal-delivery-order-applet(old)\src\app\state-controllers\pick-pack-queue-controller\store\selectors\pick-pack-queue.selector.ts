import { createFeatureSelector, createSelector } from '@ngrx/store';
import { pickPackQueueFeatureKey } from '../reducers/pick-pack-queue.reducers';

import { PickPackQueueStates } from '../states';
import { PickPackQueueState } from '../states/pick-pack-queue-states';


export const selectPickPackQueueFeature = createFeatureSelector<PickPackQueueState>(pickPackQueueFeatureKey);

export const selectEntity = (state: PickPackQueueStates) => state.pickPackQueue.selectedEntity;
export const selectTotalRecords = (state: PickPackQueueStates) => state.pickPackQueue.totalRecords;
export const selectPickPackQueues = (state: PickPackQueueStates) => {
    if (!state.pickPackQueue.loadedGenDocs) return
    return state.pickPackQueue.loadedGenDocs
    .sort((a, b) => {
        if (a.bl_fi_generic_doc_hdr.updated_date == null) return 1
        if (b.bl_fi_generic_doc_hdr.updated_date === null) return -1
        return a.bl_fi_generic_doc_hdr.updated_date < b.bl_fi_generic_doc_hdr.updated_date ? 1 : -1
    })
};
export const selectCustomer = (state: PickPackQueueStates) => state.pickPackQueue.selectedCustomer;
export const selectShippingAddress = (state: PickPackQueueStates) => state.pickPackQueue.selectedShippingAddress;
export const selectBillingAddress = (state: PickPackQueueStates) => state.pickPackQueue.selectedBillingAddress;
export const selectItem = (state: PickPackQueueStates) => state.pickPackQueue.selectedItem;
export const refreshGenDocListing = (state: PickPackQueueStates) => state.pickPackQueue.refreshGenDocListing;
export const selectLineItem = (state: PickPackQueueStates) => state.pickPackQueue.selectedLineItem;
export const selectContraDoc = (state: PickPackQueueStates) => state.pickPackQueue.selectedContraDoc;
export const selectContraLink = (state: PickPackQueueStates) => state.pickPackQueue.selectedContraLink;
export const selectDetails = (state: PickPackQueueStates) => state.pickPackQueue.selectedDetails;
export const selectPricingScheme = (state: PickPackQueueStates) => state.pickPackQueue.selectedPricingScheme;
export const selectEditMode = (state: PickPackQueueStates) => state.pickPackQueue.editMode;
export const selectKnockoffListingConfig = (state: PickPackQueueStates) => state.pickPackQueue.knockoffListingConfig;
export const selectOrder = (state: PickPackQueueStates) => state.pickPackQueue.selectedOrder;
export const selectShopeeGetShipParam = (state: PickPackQueueStates) => state.pickPackQueue.shopeeGetShipParam;
export const selectGuid = (state: PickPackQueueStates) => state.pickPackQueue.selectedGuid;
export const selectPricingSchemeGuid = (state: PickPackQueueStates) => state.pickPackQueue.selectedPricingSchemeGUID;

export const getPricingSchemeLinks = createSelector(
    selectPickPackQueueFeature,
    (state) => state.pricingSchemeLink
);

// For Serial, Bin, Batch
export const selectInvItem = (state: PickPackQueueStates) => state.pickPackQueue.selectedInvItem;
export const selectSerial = (state: PickPackQueueStates) => state.pickPackQueue.selectedSerial;
export const selectBatch = (state: PickPackQueueStates) => state.pickPackQueue.selectedBatch;
export const selectBin = (state: PickPackQueueStates) => state.pickPackQueue.selectedBin;
export const selectCompanyGuid = (state: PickPackQueueStates) => state.pickPackQueue.selectedCompGuid;
export const selectMembershipGuid = (state: PickPackQueueStates) => state.pickPackQueue.selectedMembershipGuid;
