import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import { LicenseManager } from 'ag-grid-enterprise';

LicenseManager.setLicenseKey
  ('CompanyName=WAVELET SOLUTIONS SDN. BHD.,LicensedApplication=akaun.com,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-026234,ExpiryDate=23_April_2023_[v2]_MTY4MjIwNDQwMDAwMA==674cd70d9fb70c2f7af064e2539295d0');

if (environment.production) {
  enableProdMode();
} else {
  sessionStorage.setItem('appletGuid', 'a998b86e-658d-4ce7-a3d3-1807b4d7ca6a');
}

platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
