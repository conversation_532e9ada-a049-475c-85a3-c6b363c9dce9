import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import {
  InternalSalesOrderService,
  SubQueryService,
  EmployeeService,
  PickPackQueueService,
  PickPackQueueContainerModel,
  EntityService,
  CreateJobsFromGenDocService,
} from "blg-akaun-ts-lib";
import { AppConfig } from "projects/shared-utilities/visa";
import { forkJoin, from, iif, Observable, of, zip } from "rxjs";
import {
  catchError,
  exhaustMap,
  map,
  mergeMap,
  switchMap,
} from "rxjs/operators";
import { PickPackQueueActions } from "../actions";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";

interface shippingProvider {
  value: string;
  viewValue: string;
}
@Injectable()
export class PickPackQueueEffects {
  apiVisa = AppConfig.apiVisa;

  loadPickPackQueue$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PickPackQueueActions.loadPickPackQueuesInit),
      map((action) => {
        var oneMonthAgo = moment()
          .subtract(1, "months")
          .format("YYYY-MM-DDTHH:mm:ss.SSS[Z]");
        const dto = {
          created_date_from: oneMonthAgo,
          server_doc_type: "INTERNAL_OUTBOUND_DELIVERY_ORDER",
        };
        return { ...action, dto: dto };
      }),
      switchMap((action) =>
        this.ppqService
          .getPickPackQueueByQuery(action.dto, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              const source: Observable<PickPackQueueContainerModel>[] = [];
              b.data.forEach((doc) => {
                const entityGuid =  doc.bl_fi_generic_doc_hdr.sales_entity_hdr_guid?.toString() ?? doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json["salesAgent"]?.toString()
                source.push(
                  zip(
                    this.entityService
                    .getByGuid(
                      entityGuid,
                        this.apiVisa
                      )
                      .pipe(catchError((err) => of(err)))
                  ).pipe(
                    map(([salesmanName]) => {
                      doc = Object.assign(
                        {
                          salesMan: salesmanName.error
                            ? salesmanName.error.code
                            : salesmanName.data.bl_fi_mst_entity_hdr.name,
                        },
                        doc
                      );
                      return doc;
                    })
                  )
                );
              });
              return iif(
                () => b.data.length > 0,
                forkJoin(source).pipe(
                  map((b_inner) => {
                    b.data = b_inner;
                    return b;
                  })
                ),
                of(b)
              );
            })
          )
          .pipe(
            map((a) => {
              let pickPackQueues = a.data.map((pickPackQueue) => ({
                ...pickPackQueue,
                qty_to_deliver:
                  pickPackQueue.bl_fi_pick_pack_queues[0].qty_balance,
              }));
              console.log("ABC", a.data);
              return PickPackQueueActions.loadPickPackQueueSuccess({
                PickPackQueues: pickPackQueues,
              });
            }),
            catchError((err) => {
              return of(
                PickPackQueueActions.loadPickPackQueueFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  searchPickPack$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PickPackQueueActions.searchPickPackQueuesInit),
      switchMap((action) =>
        this.ppqService
          .getPickPackQueueByQuery(action.searchDto, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              const source: Observable<PickPackQueueContainerModel>[] = [];
              b.data.forEach((doc) => {
                const entityGuid =  doc.bl_fi_generic_doc_hdr.sales_entity_hdr_guid?.toString() ?? doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json["salesAgent"]?.toString()
                source.push(
                  zip(
                    this.entityService
                    .getByGuid(
                      entityGuid,
                        this.apiVisa
                      )
                      .pipe(catchError((err) => of(err)))
                  ).pipe(
                    map(([salesmanName]) => {
                      doc = Object.assign(
                        {
                          salesMan: salesmanName.error
                            ? salesmanName.error.code
                            : salesmanName.data.bl_fi_mst_entity_hdr.name,
                        },
                        doc
                      );
                      return doc;
                    })
                  )
                );
              });
              return iif(
                () => b.data.length > 0,
                forkJoin(source).pipe(
                  map((b_inner) => {
                    b.data = b_inner;
                    return b;
                  })
                ),
                of(b)
              );
            })
          )
          .pipe(
            map((a) => {
              let pickPackQueues = a.data.map((pickPackQueue) => ({
                ...pickPackQueue,
                qty_to_deliver:
                  pickPackQueue.bl_fi_pick_pack_queues[0].qty_balance,
              }));
              console.log("ABC", a.data);
              return PickPackQueueActions.loadPickPackQueueSuccess({
                PickPackQueues: pickPackQueues,
              });
            }),
            catchError((err) => {
              return of(
                PickPackQueueActions.loadPickPackQueueFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  createJobFromDeliveryOrder$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PickPackQueueActions.createJobFromDeliveryOrderInit),
      exhaustMap((action) =>
        this.createJobsFromSalesOrderService
          .post(action.jobContainer, this.apiVisa)
          .pipe(
            map((company: any) => {
              this.toastr.success("Job successfully created", "Success", {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 1300,
              });
              return PickPackQueueActions.createJobFromDeliveryOrderSuccess();
            }),
            catchError((err) => {
              this.toastr.error(err.message, "Error", {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 1300,
              });
              return of(
                PickPackQueueActions.createJobFromDeliveryOrderFailed({
                  error: err.messsage,
                })
              );
            })
          )
      )
    )
  );

  DateConvert(date) {
    if (date != null && "" !== date) {
      const moments = moment(date).format("YYYY-MM-DD HH:mm:ss");
      return moments;
    } else {
      const notEdited = " ";
      return notEdited;
    }
  }

  constructor(
    private actions$: Actions,
    private empService: EmployeeService,
    private subQueryService: SubQueryService,
    private soService: InternalSalesOrderService,
    private ppqService: PickPackQueueService,
    private entityService: EntityService,
    private toastr: ToastrService,
    private createJobsFromSalesOrderService: CreateJobsFromGenDocService
  ) {}
}
