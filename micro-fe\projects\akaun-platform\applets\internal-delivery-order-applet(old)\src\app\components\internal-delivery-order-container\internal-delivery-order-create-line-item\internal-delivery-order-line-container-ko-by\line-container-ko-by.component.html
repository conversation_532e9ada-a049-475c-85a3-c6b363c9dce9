<div class="view-col-table no-tab" fxLayout="column">
    <ng-container *ngIf="enableListing; else error">
      <mat-card-title class="column-title">
        <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
            <div fxFlex="3 0 0">
              <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
                <app-advanced-search-lines fxFlex fxFlex.lt-sm="100" [id]="advSearchId" 
                  [advSearchModel]="searchModel" (search)="onSearch($event)">
                </app-advanced-search-lines>
              </div>
            </div>
            <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="space-between center">
              <app-pagination fxFlex #pagination [agGridReference]="agGrid"></app-pagination>
              <app-grid-toggle class="blg-button-icon"></app-grid-toggle>
            </div>
        </div>
      </mat-card-title>
      <div style="height: 80%;">
        <ag-grid-angular #agGrid
          style="height: 100%;"
          class="ag-theme-balham"
          rowSelection="single"
          rowModelType="serverSide"
          serverSideStoreType="partial"
          [getRowClass]="pagination.getRowClass"
          [columnDefs]="columnsDefs"
          [rowData]="[]"
          [paginationPageSize]="pagination.rowPerPage"
          [cacheBlockSize]="100"
          [pagination]="true"
          [animateRows]="true"
          [defaultColDef]="defaultColDef"
          [suppressRowClickSelection]="false"
          [sideBar]="true"
          (rowClicked)="onRowClicked($event.data)"
          (gridReady)="onGridReady($event)">
        </ag-grid-angular>
      </div>
    </ng-container>
    <ng-template #error>
      <mat-error>Please select an entity and a branch to use this feature</mat-error>
    </ng-template>
  </div>