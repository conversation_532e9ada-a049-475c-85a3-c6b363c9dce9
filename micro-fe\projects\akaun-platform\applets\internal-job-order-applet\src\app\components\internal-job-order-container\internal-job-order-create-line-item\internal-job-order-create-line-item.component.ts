import { Component, ChangeDetectionStrategy, ViewChild } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { LocationService, Pagination, FinancialItemService, FinancialItemContainerModel } from 'blg-akaun-ts-lib';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { SubSink } from 'subsink2';
import { internalJobOrderSearchModel } from '../../../models/advanced-search-models/internal-job-order.model';
import { InternalJobOrderStates } from '../../../state-controllers/internal-job-order-controller/store/states';
import { Store } from '@ngrx/store';
import { InternalJobOrderActions } from '../../../state-controllers/internal-job-order-controller/store/actions';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { MatTabGroup } from '@angular/material/tabs';
import { AppConfig } from 'projects/shared-utilities/visa';
import { InternalJobOrderSelectors } from '../../../state-controllers/internal-job-order-controller/store/selectors';

interface LocalState {
  deactivateReturn: boolean;
  deactivateFIList: boolean;
  // deactivateSearchItemList: boolean;
  // deactivateJobsheetItemList: boolean;
  // deactivateQuotationItemList: boolean;
  // deactivatePreviousSOList: boolean;
  selectedIndex: number;
}

@Component({
  selector: 'app-internal-job-order-create-line-item',
  templateUrl: './internal-job-order-create-line-item.component.html',
  styleUrls: ['./internal-job-order-create-line-item.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class InternalJobOrderCreateLineItemComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Select Line Item';
  protected readonly index = 3;
  protected localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deactivateFIList$ = this.componentStore.select(state => state.deactivateFIList);
  // readonly deactivateSearchItemList$ = this.componentStore.select(state => state.deactivateSearchItemList);
  // readonly deactivateJobsheetItemList$ = this.componentStore.select(state => state.deactivateJobsheetItemList);
  // readonly deactivateQuotationItemList$ = this.componentStore.select(state => state.deactivateQuotationItemList);
  // readonly deactivatePreviousSOList$ = this.componentStore.select(state => state.deactivatePreviousSOList);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);

  prevIndex: number;
  protected prevLocalState: any;

  searchModel = internalJobOrderSearchModel;

  defaultColDef = {
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true
  };

  gridApi;

  columnsDefs = [
    {headerName: 'Item Code', field: 'bl_fi_mst_item_hdr.code', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Item Name', field: 'bl_fi_mst_item_hdr.name', cellStyle: () => ({'text-align': 'left'})},
    // {headerName: 'Location', field: 'location', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Category', field: 'bl_fi_mst_item_hdr.category', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'UOM', field: 'bl_fi_mst_item_hdr.uom', cellStyle: () => ({'text-align': 'left'})},
    {headerName: 'Stock Balance', field: 'stockBalQty'},
  ];

  entity$ = this.store.select(InternalJobOrderSelectors.selectCustomer);

  @ViewChild(MatTabGroup, {static: true}) matTab: MatTabGroup;

  constructor(
    protected viewColFacade: ViewColumnFacade,
    protected fiService: FinancialItemService,
    protected lctnService: LocationService,
    private readonly store: Store<InternalJobOrderStates>,
    protected readonly componentStore: ComponentStore<LocalState>) {
    super();
  }

  ngOnInit() {
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.localState$.subscribe( a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
  }

  onGridReady(params) {
    const apiVisa = AppConfig.apiVisa;
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    const datasource = {
      getRows: grid => {
        // this.store.dispatch(InternalPackingOrderActions.loadPackingOrdersInit({request: grid.request}));
        this.subs.sink = this.fiService.getByCriteria(new Pagination(grid.request.startRow, grid.request.endRow, [
          {columnName: 'calcTotalRecords', operator: '=', value: 'true'}
        ]), apiVisa).pipe(
        ).subscribe( resolved => {
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderSuccess({totalRecords: resolved.totalRecords}));
          grid.success({
            rowData: resolved.data,
            rowCount: resolved.totalRecords
          });
        }, err => {
          // this.store.dispatch(InternalPackingOrderActions.loadPackingOrderFailed({error: err.message}));
          grid.fail();
        });
      }
    };
    this.gridApi.setServerSideDatasource(datasource);
  }

  onFinancialItem(entity: FinancialItemContainerModel) {
    if (entity) {
      this.store.dispatch(InternalJobOrderActions.selectItem({entity}));
    // if (!this.localState.deactivateSearchItemList) {
    // if (!this.localState.deactivateFIList) {
      // this.viewColFacade.updateInstance<LocalState>(this.index, {
      // ...this.localState,
      // deactivateReturn: true,
      // deactivateFIList: true
      // deactivateSearchItemList: true,
      // deactivateJobsheetItemList: false,
      // deactivateQuotationItemList: false,
      // deactivatePreviousSOList: false
    // });
      // this.viewColFacade.onNextAndReset(this.index, 7);
    // }
    }
    this.onReturn();
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateReturn: false
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex
      });
    }
    this.subs.unsubscribe();  }

}
