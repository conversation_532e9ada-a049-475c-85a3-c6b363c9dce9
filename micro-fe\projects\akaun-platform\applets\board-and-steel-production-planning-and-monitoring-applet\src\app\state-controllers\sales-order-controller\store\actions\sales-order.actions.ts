import { createAction, props } from '@ngrx/store';
import { GenericDocSingleLineContainer, bl_inv_bin_line_RowClass } from 'blg-akaun-ts-lib';

export const mapProcess = createAction('[Process] Map Process', props<{guid: string, process: string}>());

export const selectSalesOrder = createAction('[Sales Order] Select Sales Order', props<{salesOrder: any}>());
export const selectBinLine = createAction('[Sales Order] Select Bin Line', props<{binLine: bl_inv_bin_line_RowClass}>());
export const selectCurrentSalesOrder = createAction('[Sales Order] Select Current SalesOrder', props<{process: string}>());
export const resetAgGrid = createAction('[Sales Order] Reset Ag Grid Update');
export const updateForm = createAction('[Sales Order] Update Sales Order Form', props<{form: any}>());
export const updateSalesOrder = createAction('[Sales Order] Update Sales Order');
export const updateSalesOrderSuccess = createAction('[Sales Order] Sales Order Update Success');
